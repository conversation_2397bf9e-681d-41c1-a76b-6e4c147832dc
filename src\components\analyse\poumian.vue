<template>
  <div class="poumian">
    <div class="home_buffer_box">
      <div class="home_buffer_box1">
        <div class="buffer_btn_box">
          <div class="buffer_btn" @click="onDrawPolyline()">剖面绘制</div>
          <div class="buffer_btn buffer_cancel" @click="clear()">
            清除
          </div>
          <div class="buffer_btn buffer_cancel" v-show="loadEnd" @click="toImage()">
            导出图片
          </div>
        </div>
      </div>
    </div>
  </div>
  <div ref="sectionContainer" v-draggable v-loading="loading" class="sectionContainer"
    :class="{ 'open': isShowCanvas, 'close': !isShowCanvas }" @mousemove="handleMouseMove">
    <canvas v-show="isSection" ref="canvas" class="canvas" />
    <div v-show="isSection" ref="cursor" class="cursor" />
    <div v-show="isSection">
      <div v-show="selectCoal.name !== ''" ref="label" class="label">
        <div>
          <span class="selectLabel">名称：</span>
          <span class="selectValue">{{ selectCoal.name }}</span>
        </div>
        <div>
          <span class="selectLabel">剖面最高点：</span>
          <span class="selectValue">{{ selectCoal.maxHeight }} m</span>
        </div>
        <div>
          <span class="selectLabel">剖面最低点：</span>
          <span class="selectValue">{{ selectCoal.minHeight }} m</span>
        </div>
        <div>
          <span class="selectLabel">此处最高点：</span>
          <span class="selectValue">{{ selectCoal.thisMaxHeight }} m</span>
        </div>
        <div>
          <span class="selectLabel">此处最低点：</span>
          <span class="selectValue">{{ selectCoal.thisMinHeight }} m</span>
        </div>
        <!-- <div>
          <span class="selectLabel">总体积：</span>
          <span class="selectValue">{{ selectCoal.name === '地表' ? '--' : selectCoal.volume }} m³</span>
        </div> -->
        <div>
          <span class="selectLabel">此处厚度：</span>
          <span class="selectValue">{{ selectCoal.name === '地表' ? '--' : selectCoal.volume }} m</span>
        </div>
        <!--      <span>-->
        <!--        煤层：{{ selectCoal.name }}<br>最高点：{{ selectCoal.maxHeight }} m<br>-->
        <!--        最低点：{{ selectCoal.minHeight }} m<br>总体积：{{ selectCoal.volume }} m³<br>-->
        <!--        此处煤层厚度：{{ selectCoal.thickness }} m-->
        <!--      </span>-->
      </div>
    </div>
    <!-- <span v-show="isSection" class="uploadButton" @click="toImage">导出</span> -->
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef, reactive, getCurrentInstance, onMounted, onUnmounted } from "vue";
import titleImg from "@/assets/img/home/<USER>";
import * as echarts from "echarts";
import ClipPlane from "@/utils/map/clipPlane";
const { proxy } = getCurrentInstance()
//生命周期
onMounted(() => {
  let toolBox = document.getElementsByClassName('home_legend')[0]
  toolBox.style.right = '300px'
  toolBox.style.left = ''
  setTimeout(() => {
    console.log(window.underPlane)
    window.underPlane.plane.show = false
    eventHandler = new Cesium.ScreenSpaceEventHandler(viewer.canvas)
  }, 2000);
});
onUnmounted(() => {
  window.underPlane.plane.show = true
  removeTimeout()
  clear()
});
const removeTimeout = () => {
}


const showGround = (isShow) => {
  if (isShow) {
    coalTileset.style = new Cesium.Cesium3DTileStyle()
  } else {
    const groundContent = "${batchId} === \'" + groundBatchId.value + "\'"
    coalTileset.style = new Cesium.Cesium3DTileStyle({
      color: {
        conditions: [
          [groundContent, 'rgba(255, 255, 255, 0)']
        ]
      }
    })
  }
}
const showSingleCoal = (fileName) => {
  if (!fileName || fileName === '') {
    //     coalTileset.style = new Cesium.Cesium3DTileStyle()
    //
    //     // const ground = tileSet.find((x) => {
    //     //     return x.name === '地表'
    //     // })
    //     const groundContent = "${batchId} === \'" + groundBatchId + "\'"
    //    coalTileset.style = new Cesium.Cesium3DTileStyle({
    //         color: {
    //             conditions: [
    //                 [groundContent, 'rgba(255, 255, 255, 0)']
    //             ]
    //         }
    //     })

    // showGround(false)
    return
  }
  const coal = tileSet.find(x => {
    return x.file === fileName
  })
  if (!coal) {
    return
  }

  // console.log('coal',coal.file)
  // const selectContent = "${batchId} !== \'" + coal.batchId + "\'"
  // coalTileset.style = new Cesium.Cesium3DTileStyle({
  //   color: {
  //     conditions: [
  //       [selectContent, 'rgba(255, 255, 255, 0)']
  //     ]
  //   }
  // })
  // console.log(coalTileset.style)
}
const section = async (start, end) => {
  isShowCanvas.value = true
  console.time('section')
  isSection.value = true
  loading.value = true
  let interval = 5.0 // 分辨率：米s

  sectionLineStart.value = start
  sectionLineEnd.value = end

  const startC = Cesium.Cartographic.fromCartesian(start)
  const endC = Cesium.Cartographic.fromCartesian(end)

  const geodesic = new Cesium.EllipsoidGeodesic(startC, endC)
  const distance = geodesic.surfaceDistance
  let count = Math.round((distance / interval) + 0.5)
  count = count > 100 ? 100 : count
  interval = distance / count
  // count += 1

  let groundFinish = false
  let d3dmFinish = false
  const positions = []
  const positionsCartographic = []
  const crossPointsRs = {}
  const crossPointsRe = {}
  const groundHeights = []
  let maxHeight = -999999
  let minHeight = 999999
  for (let i = 0; i <= count; i++) {
    const clipPoint = Cesium.Cartesian3.lerp(start, end, i / count, new Cesium.Cartesian3())
    positions.push(clipPoint)
    positionsCartographic.push(Cesium.Cartographic.fromCartesian(clipPoint))
  }
  viewer.scene.globe.depthTestAgainstTerrain = false


  groundFinish = true
  console.log(positions)
  // 等待场景和模型准备就绪
  console.log('检查场景和模型准备状态...')

  // 等待场景瓦片加载
  if (!viewer.scene.globe.tilesLoaded) {
    console.log('等待场景瓦片加载完成...')
    await new Promise(resolve => {
      const checkTilesLoaded = () => {
        if (viewer.scene.globe.tilesLoaded) {
          console.log('场景瓦片加载完成')
          resolve()
        } else {
          setTimeout(checkTilesLoaded, 100)
        }
      }
      checkTilesLoaded()
    })
  }

  // 等待煤层模型准备就绪
  if (coalTileset && !coalTileset.ready) {
    console.log('等待煤层模型准备就绪...')
    await new Promise(resolve => {
      const checkModelReady = () => {
        if (coalTileset.ready) {
          console.log('煤层模型准备完成')
          resolve()
        } else {
          setTimeout(checkModelReady, 100)
        }
      }
      checkModelReady()
    })
  }

  // 强制渲染一帧，确保所有更新都已应用
  viewer.scene.render()
  console.log('场景渲染完成，开始射线检测')

  // 模型
  // 异步
  const allCrossPromise = getCrossPointsPromise(positions)
  console.time('3dtiles')
  console.log('allCrossPromise:', allCrossPromise)

  console.log('Promise数组长度:', allCrossPromise.length)
  console.log('第一个Promise类型:', allCrossPromise[0] ? allCrossPromise[0].constructor.name : 'undefined')

  if (allCrossPromise.length === 0) {
    console.error('没有生成任何Promise对象，剖面分析无法继续')
    loading.value = false
    return
  }

  Promise.all(allCrossPromise).then(crossResults => {
    console.timeEnd('3dtiles')
    console.log('crossResults', crossResults)
    console.log('crossResults.length', crossResults.length)
    for (let i = 0; i < crossResults.length; i++) {
      const crossResult = crossResults[i]
      if (i % 2 === 0) {
        const index = i / 2
        crossResult.forEach(crossPositon => {
          // console.log('crossPositon', crossPositon)
          // console.log(crossPositon.then(res=>console.log(res[0].object._content._tile.content.tile._header.content.uri)))
          // const b3dm = crossPositon.object._content._tile.content.tile._header.content.uri
          const b3dm = crossPositon.object.tileset.dataLabel
          const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
          const batchId = crossPositon.object.tileset.pmid
          // console.log(pnt)
          if (b3dm in crossPointsRs) {
            crossPointsRs[b3dm].push([index, pnt.height - 1900])
          } else {
            const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
            crossPointsRs[b3dm] = [[index, pnt.height - 1900]]

            //获取要采样的模型
            clipModes[b3dm] = crossPositon.object.tileset
            var color;
            if (modelInfo[b3dm]) {
              color = modelInfo[b3dm].color
            }
            else {
              var r = Math.round(Math.random() * 255)
              var g = Math.round(Math.random() * 255)
              var b = Math.round(Math.random() * 255)
              color = 'rgb(' + r + ', ' + g + ', ' + b + ')'
            }


            //设置tileSet
            tileSet.push({
              file: b3dm,
              name: b3dm,
              batchId: batchId,
              color: color,
              minHeight: 9999,
              maxHeight: -9999,
              volume: 9999,
            })


          }


          if (maxHeight < pnt.height) {
            maxHeight = pnt.height
          }
        })
      } else {
        const index = (i - 1) / 2
        crossResult.forEach(crossPositon => {
          // const b3dm = crossPositon.object._content._tile.content.tile._header.content.uri
          const b3dm = crossPositon.object.tileset.dataLabel
          const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
          const batchId = crossPositon.object.tileset.pmid
          if (b3dm in crossPointsRe) {
            crossPointsRe[b3dm].push([index, pnt.height - 1900])
          } else {
            const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
            crossPointsRe[b3dm] = [[index, pnt.height - 1900]]
          }

          if (minHeight > pnt.height) {
            minHeight = pnt.height
          }
        })
      }
    }
    d3dmFinish = true
    crossPointsRs2 = crossPointsRs
    crossPointsRe2 = crossPointsRe
    console.log('crossPointsRs', crossPointsRs)
    console.log('crossPointsRe', crossPointsRe)
    console.log('tileSet', tileSet)

    maxHeight -= 1900
    minHeight -= 1900
    // drawClippingPlane()
    if (groundFinish && d3dmFinish) {
      setHeight(tileSet, crossPointsRs, crossPointsRe)
      allFinished(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights)
    }
  }).catch(error => {
    console.error('剖面分析过程中发生错误:', error)
    ElMessage.error('剖面分析失败，请检查模型是否正确加载')
    loading.value = false
    loadEnd.value = false
  })


}

const setHeight = (tileSet, crossPointsRs, crossPointsRe) => {


  //获取最高最低点
  for (var tile of tileSet) {
    var name = tile.name
    if (crossPointsRs.hasOwnProperty(name)) {
      var arrRs = crossPointsRs[name]

      var arrRs2 = arrRs.map((val) => { return val[1] })

      tile.maxHeight = Math.max.apply(null, arrRs2).toFixed(2)
    }
    if (crossPointsRe.hasOwnProperty(name)) {
      var arrRe = crossPointsRe[name]
      var arrRe2 = arrRe.map((val) => { return val[1] })
      tile.minHeight = Math.min.apply(null, arrRe2).toFixed(2)
    }
  }

}
const allFinished = (start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights) => {
  // showGround(false)
  console.timeEnd('section')
  if (polyline) {
    viewer.entities.remove(polyline)
    polyline = null
  }

  makeSection(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights)
  drawAuxiliaryLineOnMap()
  drawClippingPlane()
  loading.value = false
  loadEnd.value = true
}
const getCrossPointsPromise = (positions) => {
  const allPromise = []

  // 检查viewer和scene是否可用
  if (!viewer || !viewer.scene) {
    console.error('Viewer或Scene未初始化')
    return allPromise
  }

  positions.forEach((position, index) => {
    try {
      const pnt = Cesium.Cartographic.fromCartesian(position)
      console.log(`位置${index} - 经纬度:`, Cesium.Math.toDegrees(pnt.longitude), Cesium.Math.toDegrees(pnt.latitude), '高度:', pnt.height)

      // 使用更大的高度范围，确保能够穿过地下模型
      const surfaceHeight = pnt.height || 0
      const startP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, surfaceHeight + 3000) // 地表上方3000米
      const endP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, surfaceHeight - 3000)   // 地表下方3000米

      console.log(`位置${index} - 射线起点高度:`, surfaceHeight + 3000, '终点高度:', surfaceHeight - 3000)

      // 使用简单的垂直向下射线
      const downDirection = new Cesium.Cartesian3(0, 0, -1) // 垂直向下
      const upDirection = new Cesium.Cartesian3(0, 0, 1)    // 垂直向上

      // 转换为世界坐标系的方向
      const transform = Cesium.Transforms.eastNorthUpToFixedFrame(position)
      const worldDownDirection = Cesium.Matrix4.multiplyByPointAsVector(transform, downDirection, new Cesium.Cartesian3())
      const worldUpDirection = Cesium.Matrix4.multiplyByPointAsVector(transform, upDirection, new Cesium.Cartesian3())

      Cesium.Cartesian3.normalize(worldDownDirection, worldDownDirection)
      Cesium.Cartesian3.normalize(worldUpDirection, worldUpDirection)

      const rayS = new Cesium.Ray(startP, worldDownDirection)
      const rayE = new Cesium.Ray(endP, worldUpDirection)

      console.log(`位置${index} - 世界坐标射线方向S:`, worldDownDirection, '射线方向E:', worldUpDirection)

      // 创建超时Promise，使用更长的超时时间
      const createTimeoutPromise = (delay, defaultValue, direction) => {
        return new Promise(resolve => {
          setTimeout(() => {
            console.warn(`位置${index} - ${direction}射线检测超时(${delay}ms)，返回默认值`)
            resolve(defaultValue)
          }, delay)
        })
      }

      // 先尝试简单的pick测试
      const screenPosition = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, position)
      let pickResults = []
      if (screenPosition) {
        const pickResult = viewer.scene.pick(screenPosition)
        console.log(`位置${index} - 屏幕坐标pick结果:`, pickResult)

        // 如果pick成功，创建一个模拟的射线检测结果
        if (pickResult && pickResult.primitive) {
          const mockResult = {
            object: pickResult,
            position: position // 使用当前位置作为交点
          }
          pickResults.push(mockResult)
          console.log(`位置${index} - 使用pick结果创建模拟射线检测结果`)
        }
      }

      // 尝试直接从地形获取高度
      const terrainHeight = viewer.scene.globe.getHeight(pnt)
      console.log(`位置${index} - 地形高度:`, terrainHeight)

      // 检查煤层模型是否在当前位置有数据
      if (coalTileset && coalTileset.ready) {
        console.log(`位置${index} - 煤层模型已准备就绪`)

        // 尝试使用更精确的射线起终点
        const modelBounds = coalTileset.boundingSphere
        if (modelBounds) {
          console.log(`位置${index} - 模型边界球:`, modelBounds.center, modelBounds.radius)
        }
      }

      // 必须使用drillPickFromRayMostDetailed来获取射线穿过的所有点
      console.log(`位置${index} - 开始射线检测...`)

      // 尝试调整射线参数，确保能够检测到模型
      // 使用更精确的射线起终点，基于模型边界
      let adjustedStartP = startP
      let adjustedEndP = endP

      if (coalTileset && coalTileset.boundingSphere) {
        const modelCenter = coalTileset.boundingSphere.center
        const modelRadius = coalTileset.boundingSphere.radius

        // 计算模型中心的地理坐标
        const modelCarto = Cesium.Cartographic.fromCartesian(modelCenter)
        const modelHeight = modelCarto.height

        console.log(`位置${index} - 模型中心高度:`, modelHeight, '模型半径:', modelRadius)

        // 基于模型实际高度调整射线范围
        adjustedStartP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, modelHeight + modelRadius + 500)
        adjustedEndP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, modelHeight - modelRadius - 500)

        console.log(`位置${index} - 调整后射线起点高度:`, modelHeight + modelRadius + 500, '终点高度:', modelHeight - modelRadius - 500)
      }

      // 重新计算射线方向
      const adjustedDownDirection = Cesium.Cartesian3.normalize(
        Cesium.Cartesian3.subtract(adjustedEndP, adjustedStartP, new Cesium.Cartesian3()),
        new Cesium.Cartesian3()
      )
      const adjustedUpDirection = new Cesium.Cartesian3(
        -1.0 * adjustedDownDirection.x,
        -1.0 * adjustedDownDirection.y,
        -1.0 * adjustedDownDirection.z
      )

      const adjustedRayS = new Cesium.Ray(adjustedStartP, adjustedDownDirection)
      const adjustedRayE = new Cesium.Ray(adjustedEndP, adjustedUpDirection)

      // 使用新的Cesium管理器的射线检测方法
      const cesiumManager = window.cesiumManager
      const rayPromiseS = cesiumManager ?
        cesiumManager.drillPickFromRay(adjustedRayS) :
        Promise.resolve(viewer.scene.drillPickFromRay ? viewer.scene.drillPickFromRay(adjustedRayS) : [])

      const rayPromiseE = cesiumManager ?
        cesiumManager.drillPickFromRay(adjustedRayE) :
        Promise.resolve(viewer.scene.drillPickFromRay ? viewer.scene.drillPickFromRay(adjustedRayE) : [])

      const resultsS = Promise.race([
        rayPromiseS,
        createTimeoutPromise(15000, [], '向下') // 15秒超时
      ]).then(result => {
        if (result && result.length > 0) {
          console.log(`位置${index} - 向下射线检测成功，找到${result.length}个交点:`, result)
        } else {
          console.log(`位置${index} - 向下射线检测完成，但没有找到交点`)
        }
        return result || []
      }).catch(error => {
        console.error(`射线检测失败 (位置${index}, 向下):`, error)
        return []
      })

      const resultsE = Promise.race([
        rayPromiseE,
        createTimeoutPromise(15000, [], '向上') // 15秒超时
      ]).then(result => {
        if (result && result.length > 0) {
          console.log(`位置${index} - 向上射线检测成功，找到${result.length}个交点:`, result)
        } else {
          console.log(`位置${index} - 向上射线检测完成，但没有找到交点`)
        }
        return result || []
      }).catch(error => {
        console.error(`射线检测失败 (位置${index}, 向上):`, error)
        return []
      })

      console.log(`位置${index} - resultsS:`, resultsS, 'resultsE:', resultsE)

      // 由于我们已经用Promise.resolve包装，这些都应该是有效的Promise
      allPromise.push(resultsS, resultsE)

    } catch (error) {
      console.error(`处理位置${index}时发生错误:`, error)
    }
  })

  console.log('生成的Promise数组:', allPromise)
  console.log('Promise数组长度:', allPromise.length)
  return allPromise
}
const getCrossPoints = (position) => {
  const pnt = Cesium.Cartographic.fromCartesian(position)
  const startP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, 1000)
  const endP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, -300)

  // 计算射线的方向，目标点 视域点
  const directionS = Cesium.Cartesian3.normalize(Cesium.Cartesian3.subtract(endP, startP, new Cesium.Cartesian3()), new Cesium.Cartesian3())
  const directionE = new Cesium.Cartesian3(-1.0 * directionS.x, -1.0 * directionS.y, -1.0 * directionS.z)
  const rayS = new Cesium.Ray(startP, directionS)
  const rayE = new Cesium.Ray(endP, directionE)

  const resultsS = viewer.scene.drillPickFromRay(rayS)
  const resultsE = viewer.scene.drillPickFromRay(rayE)

  return { rs: resultsS, re: resultsE }
}
const toImage = () => {
  const canvas = proxy.$refs.canvas
  canvas.toBlob(function (blob) {
    const a = document.createElement('a')
    const body = document.getElementsByTagName('body')
    document.body.appendChild(a)
    a.download = 'img' + '.jpg'
    a.href = window.URL.createObjectURL(blob)

    a.click()
    body.removeChild('a')
  })
}
const makeSection = (start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights) => {
  maxHeight = (parseInt(maxHeight / 10) + 1) * 10
  minHeight = (parseInt(minHeight / 10) - 1) * 10

  const container = proxy.$refs.sectionContainer
  const canvas = proxy.$refs.canvas
  canvas.width = container.offsetWidth
  canvas.height = container.offsetHeight
  const context = canvas.getContext('2d')
  const dataWidth = canvas.width - blank.value * 2
  const dataHeight = canvas.height - blankY.value * 2

  // background
  renderBackground(context, dataWidth, dataHeight)
  // renderTerrain(context, dataWidth, dataHeight, count, maxHeight, minHeight, groundHeights)
  renderCoal(context, dataWidth, dataHeight, count, maxHeight, minHeight, crossPointsRs, crossPointsRe)

  renderHeight(context, dataWidth, dataHeight, maxHeight, minHeight)
  renderDistance(context, dataWidth, dataHeight, distance)
}
const renderBackground = (context, width, height) => {
  context.save()
  context.beginPath()
  context.fillStyle = '#5A5A5AEE'
  context.strokeStyle = '#5A5A5AEE'
  context.fillRect(0, 0, width + blank.value * 2, height + blankY.value * 2)
  context.stroke()
  context.restore()
}
const renderTerrain = (context, width, height, count, maxHeight, minHeight, groundHeights) => {
  context.save()
  const xAvg = width / count
  let x = 0
  let y = 0
  let y1 = 0
  let terrain = minHeight
  context.lineWidth = 1
  context.strokeStyle = '#B7B3AB'
  const linearGrad = context.createLinearGradient(0, blankY.value, 0, height)
  linearGrad.addColorStop(0.0, '#EED6A044')
  linearGrad.addColorStop(1.0, '#FBF5E844')
  context.fillStyle = linearGrad
  context.beginPath()
  const deltaMeter = Math.abs(maxHeight - minHeight)
  for (let i = 0; i < count; i++) {
    terrain = groundHeights[i]
    terrain = terrain === 0 ? minHeight : terrain
    x = xAvg * i + xAvg / 2 + blank.value
    y = height - (terrain - minHeight) / deltaMeter * height + blankY.value
    if (i === 0) {
      context.moveTo(x, y)
      y1 = y
    } else {
      context.lineTo(x, y)
    }
  }
  context.lineTo(x, height + blankY.value)
  context.lineTo(blank.value, height + blankY.value)
  context.lineTo(blank.value, y1)
  context.stroke()
  context.fill()
  context.restore()
}
let allRegions;

const renderCoal = (context, width, height, count, maxHeight, minHeight, crossPointsRs, crossPointsRe) => {
  allRegions = []
  const xAvg = width / (count + 1)
  const deltaYMeter_ = Math.abs(maxHeight - minHeight) / height
  context.save()

  Object.keys(crossPointsRs).forEach(key => {
    const curRs = crossPointsRs[key]
    const curRe = crossPointsRe[key]

    console.log('curRs', key, curRs)
    console.log('curRe', key, curRe)

    context.lineWidth = 0.5
    const tile = tileSet.find(function (x) {
      return x.file === key
    })
    context.strokeStyle = tile.color
    context.fillStyle = context.strokeStyle

    if (!curRs || !curRe) {
      return
    }

    const crossPointsLength = curRs.length <= curRe.length ? curRs.length : curRe.length
    // console.log('crossPointsLength', crossPointsLength)
    let rsPos = []
    let rePos = []
    const savePos = []

    const isGround = key === groundFile.value

    for (let i = 0; i < crossPointsLength; ++i) {
      const xRs = Math.round(xAvg * curRs[i][0] + xAvg / 2 + blank.value)
      const yRs = Math.round(height - (curRs[i][1] - minHeight) / deltaYMeter_ + blankY.value)
      rsPos.push([xRs, yRs])

      const xRe = Math.round(xAvg * curRe[i][0] + xAvg / 2 + blank.value)
      const yRe = Math.round(height - (curRe[i][1] - minHeight) / deltaYMeter_ + blankY.value)
      rePos.push([xRe, yRe])

      // const isStop = (curRs[i + 1][0] - curRs[i][0] > 1 || curRe[i + 1][0] - curRe[i][0] > 1 || Math.abs(curRe[i + 1][1] - curRe[i][1]) > 18)
      if (isGround ? i === crossPointsLength - 1 : i === crossPointsLength - 1 || curRs[i + 1][0] - curRs[i][0] > 1 || curRe[i + 1][0] - curRe[i][0] > 1) {
        context.beginPath()
        context.moveTo(rsPos[0][0], rsPos[0][1])
        // savePos.push({ x: rsPos[0][0], y: rsPos[0][1] })
        savePos.push([rsPos[0][0], rsPos[0][1]])
        for (let m = 1; m < rsPos.length; ++m) {
          context.lineTo(rsPos[m][0], rsPos[m][1])
          // savePos.push({ x: rsPos[m][0], y: rsPos[m][1] })
          savePos.push([rsPos[m][0], rsPos[m][1]])
        }
        for (let m = rePos.length - 1; m >= 0; --m) {
          context.lineTo(rePos[m][0], rePos[m][1])
          // savePos.push({ x: rePos[m][0], y: rePos[m][1] })
          savePos.push([rePos[m][0], rePos[m][1]])
        }
        context.lineTo(rsPos[0][0], rsPos[0][1])
        // savePos.push({ x: rsPos[0][0], y: rsPos[0][1] })
        savePos.push([rsPos[0][0], rsPos[0][1]])
        context.stroke()
        context.fill()
        rsPos = []
        rePos = []

        allRegions.push({
          pos: savePos, name: tile.name, file: tile.file,
          minHeight: tile.minHeight, maxHeight: tile.maxHeight, volume: tile.volume
        })
      }
    }
    console.log('allRegions', allRegions)
    console.log('tileSet,', tileSet)
    context.restore()
  })
}
let deltaYMeter;
const renderHeight = (context, width, height, maxHeight, minHeight) => {
  context.save()
  context.lineWidth = 0.5
  context.strokeStyle = '#00000099'
  context.fillStyle = '#FFFFFF'
  context.font = '14px Arial'
  context.textBaseline = 'top'
  context.setLineDash([10, 10])
  deltaYMeter = Math.abs(maxHeight - minHeight) / height
  const tenCounts = (maxHeight - minHeight) / 10
  const sevenNum = tenCounts % 7
  if (sevenNum === 0) {
    for (let i = 1; i < 7; ++i) {
      const hY = (tenCounts / 7) * i * 10
      const y = Math.round(height - hY / deltaYMeter) + blankY.value

      context.beginPath()
      context.moveTo(blank.value, y)
      context.lineTo(blank.value + width, y)
      context.fillText(hY + minHeight + 'm ', 15, y - 5)
      context.stroke()
    }
  } else {
    for (let i = 0; i < 7; ++i) {
      const hY = (((tenCounts - sevenNum) / 7) * i + sevenNum) * 10
      const y = Math.round(height - hY / deltaYMeter) + blankY.value

      context.beginPath()
      context.moveTo(blank.value, y)
      context.lineTo(blank.value + width, y)
      context.save()
      context.fillText(hY + minHeight, 15, y - 5)
      context.save()
      context.stroke()
    }
  }
  context.beginPath()
  context.lineWidth = 1.0
  context.strokeStyle = '#FFFFFF'
  context.setLineDash([0])
  context.font = '14px Arial'
  context.textBaseline = 'bottom'
  context.moveTo(blank.value, height + blankY.value)
  context.lineTo(blank.value + width, height + blankY.value)
  context.moveTo(blank.value, height + blankY.value)
  context.lineTo(blank.value, blankY.value)
  context.fillText('高程(m)', 15, blankY.value - 5)
  context.fillText('距离(m)', blank.value + width - 25, blankY.value + height + 20)
  context.stroke()
  context.restore()
}
const renderDistance = (context, width, height, distance) => {
  context.save()
  context.lineWidth = 0.5
  context.strokeStyle = '#FFFFFF'
  context.fillStyle = '#FFFFFF'
  context.font = '14px Arial'
  context.textBaseline = 'top'

  const tenDistance = parseInt(distance / 10) * 10
  const deltaXMeter = distance / width
  const tenCounts = tenDistance / 10
  const num = tenCounts % 30

  if (num === 0) {
    for (let i = 1; i < 30; ++i) {
      const hX = (tenCounts / 30) * i * 10
      const x = Math.round(hX / deltaXMeter) + blank.value

      context.beginPath()
      context.moveTo(x, blankY.value + height)
      context.lineTo(x, blankY.value + height + 5)
      context.fillText(hX + 'm ', x, blankY.value + height + 6)
      context.stroke()
    }
  } else {
    for (let i = 0; i < 30; ++i) {
      const hX = (((tenCounts - num) / 30) * i + num) * 10
      const x = Math.round(hX / deltaXMeter) + blank.value

      context.beginPath()
      context.moveTo(x, blankY.value + height)
      context.lineTo(x, blankY.value + height + 5)
      context.fillText(hX, x - context.measureText(hX).width / 2, blankY.value + height + 6)
      context.stroke()
    }
  }
}
const getCurPoints = (time, result) => {
  return vlinePositions
}


const getMesByIndex = (MList, index) => {
  console.log(MList, index)
  for (var mes of MList) {
    if (mes[0] >= index) {
      return mes[1]
    }
  }

}

const handleMouseMove = (event) => {
  if (!isSection.value) {
    return
  }
  if (event.offsetX < 50 || event.offsetY < 0) {
    vlinePositions = []
    return
  }
  // console.log(event.offsetX, event.offsetY)
  proxy.$refs.cursor.style.marginLeft = event.offsetX + 'px'

  const start = sectionLineStart.value
  const end = sectionLineEnd.value

  const clipPoint = Cesium.Cartesian3.lerp(start, end, (event.offsetX - blank.value) / (proxy.$refs.sectionContainer.offsetWidth - blank.value * 2), new Cesium.Cartesian3())
  let topP = Cesium.Cartographic.fromCartesian(clipPoint)
  let bottomP = Cesium.Cartographic.fromCartesian(clipPoint)
  topP.height = 3000
  bottomP.height = -1000
  topP = Cesium.Cartographic.toCartesian(topP)
  bottomP = Cesium.Cartographic.toCartesian(bottomP)
  vlinePositions = [topP, bottomP]

  var index = Math.ceil(((event.offsetX - blank.value) / (proxy.$refs.sectionContainer.offsetWidth - blank.value * 2)) * 100)

  // console.log((event.offsetX - blank.value) / (proxy.$refs.sectionContainer.offsetWidth - blank.value * 2))
  if (Array.isArray(allRegions)) {
    // console.log('allRegions', allRegions)
    for (let i = allRegions.length - 1; i >= 0; i--) {
      const region = allRegions[i]
      const result = isInPolygon([event.offsetX, event.offsetY], region.pos)

      if (result.inner) {
        // console.log('selectCoal', selectCoal)
        selectCoal.name = region.name
        // console.log(region.name)
        // console.log(index)
        var maxHeight = Number(getMesByIndex(crossPointsRs2[region.name], index).toFixed(2))
        var minHeight = Number(getMesByIndex(crossPointsRe2[region.name], index).toFixed(2))
        selectCoal.thickness = (((result.height + 1) * deltaYMeter) + (((Math.random() - 0.5) * 0.2) * deltaYMeter)).toFixed(2)
        selectCoal.minHeight = region.minHeight
        selectCoal.maxHeight = region.maxHeight
        selectCoal.thisMaxHeight = maxHeight
        selectCoal.thisMinHeight = minHeight
        selectCoal.volume = (maxHeight - minHeight).toFixed(2)
        // selectCoal.volume = region.volume
        // selectCoal = {
        //   name: region.name,
        //   thickness: (((result.height + 1) * deltaYMeter) + (((Math.random() - 0.5) * 0.2) * deltaYMeter)).toFixed(2),
        //   minHeight: region.minHeight,
        //   maxHeight: region.maxHeight,
        //   volume: region.volume
        // }
        proxy.$refs.label.style.marginLeft = (proxy.$refs.sectionContainer.offsetWidth - event.offsetX < 200 ? event.offsetX - 200 : event.offsetX + 10) + 'px'
        proxy.$refs.label.style.top = (event.offsetY > 120 ? event.offsetY - 100 : event.offsetY - 25) + 'px'
        // var coalName = region.name + '  高度：' + (((result.height + 1) * deltaYMeter) + (((Math.random() - 0.5) * 0.2) * deltaYMeter)).toFixed(2) + 'm'
        // console.log(proxy.$refs.label.style.marginLeft)
        // console.log(proxy.$refs.label.style.top)

        showSingleCoal(region.file)
        viewer.scene.forceRender();
        return
      }
    }
  }

  showSingleCoal('')
  selectCoal.name = ''
  selectCoal.thickness = -1
  selectCoal.minHeight = -1
  selectCoal.maxHeight = -1
  selectCoal.thisMaxHeight = -1
  selectCoal.thisMinHeight = -1
  selectCoal.volume = -1
  // selectCoal = {
  //   name: '',
  //   thickness: -1,
  //   minHeight: -1,
  //   maxHeight: -1,
  //   volume: -1
  // }
}
const isInPolygon = (checkPoint, polygonPoints) => {
  let counter = 0
  let i
  let xInters
  let p1, p2
  const height = []
  let inner = false
  const pointCount = polygonPoints.length
  p1 = polygonPoints[0]

  for (i = 1; i <= pointCount; i++) {
    p2 = polygonPoints[i % pointCount]
    if (checkPoint[0] > Math.min(p1[0], p2[0]) && checkPoint[0] <= Math.max(p1[0], p2[0])) {
      height.push(p2[1])
      if (checkPoint[1] <= Math.max(p1[1], p2[1])) {
        if (p1[0] !== p2[0]) {
          xInters = ((checkPoint[0] - p1[0]) * (p2[1] - p1[1])) / (p2[0] - p1[0]) + p1[1]
          if (checkPoint[1] === xInters || Math.abs(checkPoint[1] - xInters) < 1.5) {
            inner = true
          }
          if (p1[1] === p2[1] || checkPoint[1] <= xInters) {
            counter++
          }
        }
      }
    }
    p1 = p2
  }
  if (inner) {
    return { inner: true, height: Math.abs(height[1] - height[0]) }
  }
  if (counter % 2 === 0) {
    return { inner: false, height: 0 }
  } else {
    return { inner: true, height: Math.abs(height[1] - height[0]) }
  }
}
const drawAuxiliaryLineOnMap = () => {
  groundLine = viewer.entities.add({
    polyline: {
      positions: groundLinePositions,
      width: 2,
      material: Cesium.Color.YELLOW
    }
  })

  footLine = viewer.entities.add({
    polyline: {
      positions: new Cesium.CallbackProperty(getCurPoints, false),
      width: 1,
      material: new Cesium.PolylineDashMaterialProperty({
        color: Cesium.Color.YELLOW,
        dashLength: 15
      })
    }
  })
}
const drawClippingPlane = () => {

  Object.keys(clipModes).forEach(key => {
    var model = clipModes[key]
    console.log(model)
    var inverseTransform = getInverseTransform(model)
    var clippingPlane = createPlane(sectionLineEnd.value, sectionLineStart.value, inverseTransform)
    var clippingPlanes = new Cesium.ClippingPlaneCollection({
      planes: [
        clippingPlane
      ],
      edgeWidth: 2.0,
      edgeColor: Cesium.Color.BLACK
    })
    model.clippingPlanes = clippingPlanes
    clippingPlanesList.push(clippingPlanes)

  })
  // const inverseTransform = getInverseTransform(coalTileset)
  // const clippingPlane = createPlane(sectionLineEnd.value, sectionLineStart.value, inverseTransform)
  // clippingPlanes = new Cesium.ClippingPlaneCollection({
  //   planes: [
  //     clippingPlane
  //   ],
  //   edgeWidth: 2.0,
  //   edgeColor: Cesium.Color.BLACK
  // })
  // coalTileset.clippingPlanes = clippingPlanes

  sectionWall = viewer.entities.add({
    name: 'SectionWall',
    wall: {
      positions: [sectionLineStart.value, sectionLineEnd.value],
      minimumHeights: [-1000.0, -1000.0],
      maximumHeights: [3000.0, 3000.0],
      material: Cesium.Color.BLACK.withAlpha(0.1)
    }
  })
  var clippingPlane = clippingPlanesList[0]._planes[0]
  let heading = Math.atan2(clippingPlane.normal.y, clippingPlane.normal.x) - Cesium.Math.PI_OVER_TWO
  heading = Cesium.Math.TWO_PI - Cesium.Math.zeroToTwoPi(heading)
  viewer.zoomTo(sectionWall, new Cesium.HeadingPitchRange(heading - 0.2, -0.4, 0.0))
  // viewer.scene.globe.translucency.frontFaceAlpha = 1.0
}
/**
 * 计算坐标转换矩阵
 */
const getInverseTransform = (tileSet) => {
  let transform
  const tmp = tileSet.root.transform
  if ((tmp && tmp.equals(Cesium.Matrix4.IDENTITY)) || !tmp) {
    // 如果root.transform不存在，则3DTiles的原点变成了boundingSphere.center
    transform = Cesium.Transforms.eastNorthUpToFixedFrame(tileSet.boundingSphere.center)
  } else {
    transform = Cesium.Matrix4.fromArray(tileSet.root.transform)
  }
  return Cesium.Matrix4.inverseTransformation(transform, new Cesium.Matrix4())
}
/**
 * 坐标点转换
 */
const getOriginCoordinateSystemPoint = (point, inverseTransform) => {
  return Cesium.Matrix4.multiplyByPoint(
    inverseTransform, point, new Cesium.Cartesian3(0, 0, 0))
}
const createPlane = (p1, p2, inverseTransform) => {
  // 将仅包含经纬度信息的p1,p2，转换为相应坐标系的cartesian3对象
  const p1C3 = getOriginCoordinateSystemPoint(p1, inverseTransform)
  const p2C3 = getOriginCoordinateSystemPoint(p2, inverseTransform)

  // 定义一个垂直向上的向量up
  const up = new Cesium.Cartesian3(0, 0, 10)
  //  right 实际上就是由p1指向p2的向量
  const right = Cesium.Cartesian3.subtract(p2C3, p1C3, new Cesium.Cartesian3())

  // 计算normal， right叉乘up，得到平面法向量，这个法向量指向right的右侧
  let normal = Cesium.Cartesian3.cross(right, up, new Cesium.Cartesian3())
  normal = Cesium.Cartesian3.normalize(normal, normal)

  // 由于已经获得了法向量和过平面的一点，因此可以直接构造Plane,并进一步构造ClippingPlane
  const planeTmp = Cesium.Plane.fromPointNormal(p1C3, normal)
  return Cesium.ClippingPlane.fromPlane(planeTmp)
}
let coalTileset;
const onDrawPolyline = () => {
  clear()

  // 检查必要的对象是否存在
  if (!window.earth || !window.layersManager) {
    console.error('Earth或LayersManager未初始化')
    ElMessage.error('系统未完全加载，请稍后再试')
    return
  }

  // 检查并设置模型的可检测性
  if (coalTileset) {
    console.log('煤层模型信息:', coalTileset)
    console.log('煤层模型可见性:', coalTileset.show)
    console.log('煤层模型准备状态:', coalTileset.ready)

    // 确保模型可以被射线检测
    if (coalTileset.style) {
      console.log('煤层模型样式:', coalTileset.style)
    }
  }

  // viewer._container.style.cursor = 'crosshair'
  var earth = window.earth;
  var meicengID = window.layersManager.getIdByLabel('7号煤层')

  if (!meicengID) {
    console.error('未找到7号煤层')
    ElMessage.error('未找到7号煤层，无法进行剖面分析')
    return
  }

  var layerItem = earth.layerManager[meicengID];
  if (!layerItem || !layerItem.layer) {
    console.error('7号煤层数据未加载')
    ElMessage.error('7号煤层数据未加载，请稍后再试')
    return
  }

  coalTileset = layerItem.layer
  viewer.zoomTo(coalTileset, new Cesium.HeadingPitchRange(0.0, Math.PI / -2, 0.0))
  let arr = []
  polyline = null

  eventHandler.setInputAction((movement) => {
    const position = viewer.camera.pickEllipsoid(movement.endPosition, viewer.scene.globe.ellipsoid)
    if (!Cesium.defined(position)) {
      return
    }
    if (arr.length >= 1) {
      if (!Cesium.defined(polyline)) {
        polyline = drawPolyline(arr)
      } else {
        arr.pop()
        arr.push(position)
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

  eventHandler.setInputAction((movement) => {
    const position = viewer.camera.pickEllipsoid(movement.position, viewer.scene.globe.ellipsoid)
    if (!Cesium.defined(position)) {
      return
    }
    if (arr.length === 2) {
      viewer._container.style.cursor = 'default'
      arr.pop()
      arr.push(position)

      // eventHandler.destroy()
      // eventHandler = null
      eventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      eventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      loading.value = true
      // showGround(true)
      setTimeout(() => {
        section(arr[0], arr[1])
        arr = []
        return
      }, 50)
    }
    if (arr.length === 0) {
      arr.push(position.clone())
    }
    arr.push(position)
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

}
const drawPolyline = (positions) => {
  const polylineGeometry = viewer.entities.add({
    polyline: {
      positions: new Cesium.CallbackProperty(() => {
        return positions
      }, false),
      width: 3,
      material: Cesium.Color.RED,
      classificationType: Cesium.ClassificationType.BOTH,
      clampToGround: true
    }
  })
  return polylineGeometry
}
let polyline;
let eventHandler = undefined;
let groundLine;
let footLine;
let sectionWall;
let clippingPlanes;
let clippingPlanesList = [];
let crossPointsRs2;
let crossPointsRe2;
const clear = () => {
  // viewer.scene.globe.translucency.frontFaceAlpha = 0.6
  // debugger
  isSection.value = false
  loading.value = false
  loadEnd.value = false
  if (eventHandler) {
    // eventHandler.destroy()
    eventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    eventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // eventHandler = null
  }

  if (polyline) {
    viewer.entities.remove(polyline)
    polyline = null
  }

  if (groundLine) {
    viewer.entities.remove(groundLine)
    groundLine = null
  }

  if (footLine) {
    viewer.entities.remove(footLine)
    footLine = null
  }

  if (sectionWall) {
    viewer.entities.remove(sectionWall)
    sectionWall = null
  }
  // if (clippingPlanes) {
  //   clippingPlanes.removeAll()
  //   clippingPlanes = null
  // }
  // if (clippingPlanesList.length > 0) {
  //   for (var clippingPlanes of clippingPlanesList) {
  //     clippingPlanes.removeAll()
  //     clippingPlanes = null
  //   }
  // }

  if (clipModes) {
    Object.keys(clipModes).forEach(key => {
      var model = clipModes[key]
      model.clippingPlanes = undefined
    })
    clipModes = {}
  }
  if (clippingPlanesList.length > 0) {
    clippingPlanesList = []
  }
  const canvas = proxy.$refs.canvas
  if (canvas) {
    const container = proxy.$refs.sectionContainer
    canvas.width = container.offsetWidth
    canvas.height = container.offsetHeight
    const context = canvas.getContext('2d')
    renderBackground(context, canvas.width, canvas.height)
  }



  isShowCanvas.value = false

  viewer._container.style.cursor = 'default'
  viewer.scene.forceRender();
  setTimeout(() => {
    viewer.scene.forceRender();
  }, 1000);


}

//data
let clipModes = {}
let tileSet = reactive([])
let vlinePositions = reactive([])
let groundLinePositions = reactive([])
let selectCoal = reactive({
  name: '',
  thickness: -1,
  minHeight: -1,
  maxHeight: -1,
  thisMinHeight: -1,
  thisMaxHeight: -1,
  volume: -1
})
let loading = ref(false)
let sectionLineStart = ref(null)
let sectionLineEnd = ref(null)
let blank = ref(50)
let blankY = ref(30)
let isSection = ref(false)
let groundBatchId = ref('')
let groundFile = ref('')
let isShowCanvas = ref(false)
let loadEnd = ref(false)
let modelInfo = reactive({
  '第四系': {
    color: '#a88a5d',
    index: 1
  }, '上侏罗～下白垩统': {
    color: '#777063',
    index: 1
  }, '二叠系地层': {
    color: '#565f65',
    index: 1
  }, '石炭系地层': {
    color: '#6e5747',
    index: 1
  }
  , '7号煤层': {
    color: '#242424',
    index: 2
  }
  , '8号煤层': {
    color: '#242424',
    index: 2
  }
  , '断层模型': {
    color: '#b61212',
    index: 4
  }
  , '井巷模型': {
    color: '#8c8c00',
    index: 4
  }
  , '回采工作面': {
    color: '#8595a4',
    index: 3
  }
  , '勘探钻孔': {
    color: '#87ff00',
    index: 5
  }
  , '积水区': {
    color: '#0ff1f1',
    index: 5
  }
  , '陷落柱': {
    color: '#ba8888',
    index: 5
  }
  , '7煤采空区': {
    color: '#9f9f9f',
    index: 3
  }
  , '8煤采空区': {
    color: '#9f9f9f',
    index: 3
  }
})
</script>
<style lang="less" scoped>
.poumian {
  width: 220px;
  height: 150px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  flex-direction: column;

  .home_buffer_box {
    width: 100%;
    background-color: RGBA(5, 31, 89, 0.3);
    //   background-image: url("../../assets/img/home/<USER>");
    // background-position: center center;
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    // padding-left: 18px;
    box-sizing: border-box;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    // margin-bottom: 10px;
    margin-top: 30px;
    border-radius: 6px;
    z-index: 999;

    .buffer_btn_box {
      width: 100%;
      // justify-content: space-around;
      height: 100%;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      padding-top: 10px;

      .buffer_btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 180px;
        height: 32px;
        cursor: pointer;
        border-radius: 4px;
        font-size: 16px;
        margin-bottom: 10px;
        background: rgba(54, 175, 255, 0.32);
        border-radius: 6px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;

        &:hover {
          border: 1px solid #73d897;
          transform: scale(1.05);
        }
      }

      .buffer_cancel {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

}




.sectionContainer {
  width: 80%;
  height: 250px;
  padding: 0px;
  float: left;
  // bottom: 0px;
  position: absolute;
  bottom: 70px;
  left: 10%;
  z-index: 998;
  background-image: url("../../assets/img/home/<USER>");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  &.open {
    height: 200px;
  }

  &.close {
    height: 0px;
  }

  .canvas {
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    bottom: 0px;
    pointer-events: none;
  }

  .cursor {
    position: absolute;
    width: 0;
    top: 5px;
    bottom: 5px;
    margin-left: 0px;
    border-left: #222222 1px dashed;
  }

  .label {
    user-select: none;
    position: absolute;
    width: 180px;
    min-height: 59px;
    max-height: 96px;
    top: 0px;
    margin-left: 0px;
    margin-bottom: 0px;
    border-left: #DDDDDD 1px dashed;
    border-right: #DDDDDD 1px dashed;
    background: rgba(234, 7, 7, 0.897);
    border-radius: 3px;
    font-size: 14px;
    z-index: 999;

    .selectLabel {
      color: #B5B5B5;
    }

    .selectValue {
      color: #DDDDDD;
    }
  }

  .float-section {
    position: absolute;
    bottom: 205px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 32px;
    height: 80px;
    cursor: pointer;
    pointer-events: auto;
    right: 10px;

    img {
      width: 34px;
      height: 34px;
    }
  }

  .uploadButton {
    border: 2px solid #1CDAFF;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
    background: #096D81;
    border-radius: 4px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    cursor: pointer;
    height: 40px;
    width: 65px;
    text-align: center;
    display: inline-block;
    line-height: 34px;

  }
}
</style>
