/**
 * 体积量算
 */
class MeasureVolume {

    upPolygonObj = null;
    downPolygonObj = null;

    constructor(viewer) {
        this.up = [{
                name: "a1",
                coords: "115.308846,39.302114,140.21",
            },
            {
                name: "b1",
                coords: "115.309067,39.302592,128.42",
            },
            {
                name: "c1",
                coords: "115.309595,39.302221,153.68",
            },
        ];
        this.down = [{
                name: "a2",
                coords: "115.308846,39.302114,64.54",
            },
            {
                name: "b2",
                coords: "115.309067,39.302592,72.47",
            },
            {
                name: "c2",
                coords: "115.309595,39.302221,51.16",
            },
        ];
        this.upPos = [];
        this.downPos = [];
        this.upClipPos = []; //上表面最低点集合
        this.downClipPos = []; //下表面最高点集合
        this.upMinHight = -Infinity; //上表面最低点高度值
        this.downMaxHight = Infinity; //下表面最高点高度值
        this.upPolygon = true;
        this.downPolygon = true;
        this.upVolume = 0;
        this.centerVolume = 0;
        this.downVolume = 0;
        this.viewer = viewer;
        this.initPoints();
    }

    //创建上下节点
    initPoints() {
        // 绘制上顶点
        for (let i = 0; i < this.up.length; i++) {
            const upEle = this.up[i];
            const coords = upEle.coords.split(",");
            const x = Number(coords[0]);
            const y = Number(coords[1]);
            const z = Number(coords[2]);
            this.upPos.push({
                x,
                y,
                z,
            });
            this.viewer.entities.add(
                new Cesium.Entity({
                    name: "上顶点-" + upEle.name,
                    show: true,
                    position: Cesium.Cartesian3.fromDegrees(x, y, z),
                    point: {
                        pixelSize: 10,
                        color: Cesium.Color.YELLOW,
                        outlineColor: Cesium.Color.RED,
                        outlineWidth: 3,
                    },
                    label: {
                        text: upEle.name,
                        font: "14pt Source Han Sans CN", //字体样式
                        fillColor: Cesium.Color.YELLOW, //字体颜色
                        outlineWidth: 2,
                        verticalOrigin: Cesium.VerticalOrigin.CENTER, //垂直位置
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER, //水平位置
                        pixelOffset: new Cesium.Cartesian2(0, -22), //偏移
                    },
                })
            );
        }
        // 绘制下顶点
        for (let n = 0; n < this.down.length; n++) {
            const downEle = this.down[n];
            const coords = downEle.coords.split(",");
            const x = Number(coords[0]);
            const y = Number(coords[1]);
            const z = Number(coords[2]);
            this.downPos.push({
                x,
                y,
                z
            });
            this.viewer.entities.add(
                new Cesium.Entity({
                    name: "下顶点-" + downEle.name,
                    show: true,
                    position: Cesium.Cartesian3.fromDegrees(x, y, z),
                    point: {
                        pixelSize: 10,
                        color: Cesium.Color.GREEN,
                        outlineColor: Cesium.Color.RED,
                        outlineWidth: 3,
                    },
                    label: {
                        text: downEle.name,
                        font: "14pt Source Han Sans CN", //字体样式
                        fillColor: Cesium.Color.GREEN, //字体颜色
                        outlineWidth: 2,
                        verticalOrigin: Cesium.VerticalOrigin.CENTER, //垂直位置
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER, //水平位置
                        pixelOffset: new Cesium.Cartesian2(0, 22), //偏移
                    },
                })
            );
        }
        this.viewer.flyTo(this.viewer.entities);
        this.initUpAndDownPolygon();
    }

    //绘制顶底面
    initUpAndDownPolygon() {
        //绘制上表面
        const upPositions = [];
        for (let i = 0; i < this.up.length; i++) {
            const upEle = this.up[i];
            const coords = upEle.coords.split(",");
            upPositions.push(
                Number(coords[0]),
                Number(coords[1]),
                Number(coords[2])
            );
        }
        this.upPolygonObj = this.viewer.entities.add({
            name: "上表面",
            polygon: {
                hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights(upPositions),
                perPositionHeight: true,
                material: Cesium.Color.YELLOW.withAlpha(0.5),
                outline: true,
                outlineColor: Cesium.Color.BLACK,
            },
        });
        // 绘制下表面
        const downPositions = [];
        for (let i = 0; i < this.down.length; i++) {
            const downEle = this.down[i];
            const coords = downEle.coords.split(",");
            downPositions.push(
                Number(coords[0]),
                Number(coords[1]),
                Number(coords[2])
            );
        }
        this.downPolygonObj = this.viewer.entities.add({
            name: "下表面",
            polygon: {
                hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights(downPositions),
                perPositionHeight: true,
                material: Cesium.Color.GREEN.withAlpha(0.5),
                outline: true,
                outlineColor: Cesium.Color.BLACK,
            },
        });
        this.clipUpByPoint();
    }
    // 上表面水平切割
    clipUpByPoint() {
        this.upPos.sort(this.compare("z"));
        this.upMinHight = this.upPos[0].z;
        const upClipPoints = [];
        for (let i = 0; i < this.upPos.length; i++) {
            const upEle = this.upPos[i];
            const x = upEle.x;
            const y = upEle.y;
            const z = this.upMinHight;
            this.upClipPos.push({
                x,
                y,
                z
            });
            upClipPoints.push(x, y, z);
            this.viewer.entities.add(
                new Cesium.Entity({
                    name: "上表面水平切割点-" + i,
                    show: true,
                    position: Cesium.Cartesian3.fromDegrees(x, y, z),
                    point: {
                        pixelSize: 20,
                        color: Cesium.Color.BLUE,
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 5,
                    },
                })
            );
        }
        this.viewer.entities.add({
            name: "上表面水平切割面",
            polygon: {
                hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights(upClipPoints),
                perPositionHeight: true,
                material: Cesium.Color.YELLOW.withAlpha(0.5),
                outline: true,
                outlineColor: Cesium.Color.BLACK,
            },
        });
        this.clipBottomByPoint();
    }

    // 下表面水平切割
    clipBottomByPoint() {
        this.downPos.sort(this.compare("z"));
        this.downMaxHight = this.downPos[this.downPos.length - 1].z;
        const downClipPoints = [];
        for (let i = 0; i < this.downPos.length; i++) {
            const downEle = this.downPos[i];
            const x = downEle.x;
            const y = downEle.y;
            const z = this.downMaxHight;
            this.downClipPos.push({
                x,
                y,
                z
            });
            downClipPoints.push(x, y, z);
            this.viewer.entities.add(
                new Cesium.Entity({
                    name: "下表面水平切割点-" + i,
                    show: true,
                    position: Cesium.Cartesian3.fromDegrees(x, y, z),
                    point: {
                        pixelSize: 20,
                        color: Cesium.Color.BLUE,
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 5,
                    },
                })
            );
        }
        this.viewer.entities.add({
            name: "下表面水平切割面",
            polygon: {
                hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights(downClipPoints),
                perPositionHeight: true,
                material: Cesium.Color.GREEN.withAlpha(0.5),
                outline: true,
                outlineColor: Cesium.Color.BLACK,
            },
        });
        this.createUpSolid();
    }

    // 创建上半部分空间体
    createUpSolid() {
        const vertex = this.upPos[0];
        const upSolidPolygonPoints = [];
        const fourpoints0 = [];
        const fourpoints1 = [];
        for (let i = 1; i < this.upPos.length; i++) {
            const upEle = this.upPos[i];
            const upClipEle = this.upClipPos[i];
            const upClipEle1 = this.upClipPos[this.upPos.length - i];
            fourpoints0.push(upEle.x, upEle.y, upEle.z);
            fourpoints1.push(upClipEle1.x, upClipEle1.y, upClipEle1.z);
            const curPolygonPoints = [
                vertex.x,
                vertex.y,
                vertex.z,
                upEle.x,
                upEle.y,
                upEle.z,
                upClipEle.x,
                upClipEle.y,
                upClipEle.z,
            ];
            upSolidPolygonPoints.push(curPolygonPoints);
        }
        const fourpoints = fourpoints0.concat(fourpoints1); //上半部分顶点对应的那个面的四个顶点
        upSolidPolygonPoints.push(fourpoints);
        for (let n = 0; n < upSolidPolygonPoints.length; n++) {
            const element = upSolidPolygonPoints[n];

            this.viewer.entities.add({
                name: "上表面水平切割后形成的侧面",
                polygon: {
                    hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights(element),
                    perPositionHeight: true,
                    material: Cesium.Color.YELLOW.withAlpha(0.5),
                    outline: true,
                    outlineColor: Cesium.Color.RED,
                },
            });
        }
        this.createDownSolid();
    }

    //创建下半部分空间体
    createDownSolid() {
        const vertex = this.downPos[this.downPos.length - 1];
        const downSolidPolygonPoints = [];
        const fourpoints0 = [];
        const fourpoints1 = [];
        for (let i = 0; i < this.downPos.length - 1; i++) {
            const downEle = this.downPos[i];
            const downClipEle = this.downClipPos[i];
            const downClipEle1 = this.downClipPos[this.downPos.length - 2 - i];
            fourpoints0.push(downEle.x, downEle.y, downEle.z);
            fourpoints1.push(downClipEle1.x, downClipEle1.y, downClipEle1.z);
            const curPolygonPoints = [
                vertex.x,
                vertex.y,
                vertex.z,
                downEle.x,
                downEle.y,
                downEle.z,
                downClipEle.x,
                downClipEle.y,
                downClipEle.z,
            ];
            downSolidPolygonPoints.push(curPolygonPoints);
        }
        const fourpoints = fourpoints0.concat(fourpoints1); //下半部分顶点对应的那个面的四个顶点
        downSolidPolygonPoints.push(fourpoints);
        for (let n = 0; n < downSolidPolygonPoints.length; n++) {
            const element = downSolidPolygonPoints[n];
            this.viewer.entities.add({
                name: "下表面水平切割后形成的侧面",
                polygon: {
                    hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights(element),
                    perPositionHeight: true,
                    material: Cesium.Color.GREEN.withAlpha(0.5),
                    outline: true,
                    outlineColor: Cesium.Color.RED,
                },
            });
        }
        this.calculateUpVolume();
    }

    // 计算上半部分体积//四棱锥体积计算公式：体积=底面积*高/3
    calculateUpVolume() {
        // 底面积=（上底+下底）*高/2
        // 上底长度和下底长度
        console.log("计算上半部分的底面积：", this.upPos, this.upClipPos, turf);
        const clipLine = [];
        let upAndDownDistance = 0;
        for (let i = 1; i < this.upPos.length; i++) {
            const upEle = this.upPos[i];
            const upClipEle = this.upClipPos[i];
            clipLine.push(upClipEle);
            const distance = this.getSpaceDistance(upEle, upClipEle);
            console.log("底面上下底边长度" + i, distance);
            upAndDownDistance += distance;
        }
        const height = this.getSpaceDistance(clipLine[0], clipLine[1]);
        console.log("底面的高", height);
        const bottomArea = (upAndDownDistance * height) / 2;
        console.log("底面积", bottomArea);
        //高
        const pt = turf.point([this.upClipPos[0].x, this.upClipPos[0].y]);
        const line = turf.lineString([
            [this.upClipPos[1].x, this.upClipPos[1].y],
            [this.upClipPos[2].x, this.upClipPos[2].y],
        ]);

        const heightDistance = turf.pointToLineDistance(pt, line) * 1000;
        console.log("顶点到底面的高：", heightDistance);
        this.upVolume = (bottomArea * heightDistance) / 3;
        console.log("上半部分体积（黄色）：", this.upVolume);
        this.calculateDownVolume();
    }


    // 计算下半部分体积//四棱锥体积计算公式：体积=底面积*高/3
    calculateDownVolume() {
        // 底面积=（上底+下底）*高/2
        // 上底长度和下底长度
        console.log(
            "计算下半部分的底面积：",
            this.downPos,
            this.downClipPos,
            turf
        );
        const clipLine = [];
        let upAndDownDistance = 0;
        for (let i = 0; i < this.downPos.length - 1; i++) {
            const upEle = this.downPos[i];
            const upClipEle = this.downClipPos[i];
            clipLine.push(upClipEle);
            const distance = this.getSpaceDistance(upEle, upClipEle);
            console.log("底面上下底边长度" + i, distance);
            upAndDownDistance += distance;
        }
        const height = this.getSpaceDistance(clipLine[0], clipLine[1]);
        console.log("底面的高", height);
        const bottomArea = (upAndDownDistance * height) / 2;
        console.log("底面积", bottomArea);
        //高
        const pt = turf.point([this.downClipPos[2].x, this.downClipPos[2].y]);
        const line = turf.lineString([
            [this.downClipPos[0].x, this.downClipPos[0].y],
            [this.downClipPos[1].x, this.downClipPos[1].y],
        ]);

        const heightDistance = turf.pointToLineDistance(pt, line) * 1000;
        console.log("顶点到底面的高：", heightDistance);
        this.downVolume = (bottomArea * heightDistance) / 3;
        console.log("下半部分体积（绿色）：", this.downVolume);
        this.calculateCenterVolume(heightDistance, height);
    }


    // 计算中间部分体积//三棱柱体积计算公式：体积=底面积*高
    //height：三棱柱底面三角形的高，bottom：三棱柱底面三角形的底边
    calculateCenterVolume(height, bottom) {
        //底面积
        const area = (height * bottom) / 2;
        console.log("底面三角形面积：", area);
        const h = Math.abs(this.upClipPos[0].z - this.downClipPos[2].z); //三棱柱高
        this.centerVolume = area * h;
        console.log("中间部分三棱柱体积：", this.centerVolume);
        const volume = this.upVolume + this.downVolume + this.centerVolume;
        console.log("当前空间立方体的体积是(m³)：", volume);
    }


    // 空间两点间距离，单位m
    getSpaceDistance(pos1, pos2) {
        const h = Math.abs(pos1.z - pos2.z);
        const from = turf.point([pos1.x, pos1.y]);
        const to = turf.point([pos2.x, pos2.y]);
        const distance = turf.distance(from, to); //这里计算出来的单位是km，要转换成m，所以后面开平方的时候*1000
        const result = Math.sqrt(Math.pow(distance * 1000, 2) + Math.pow(h, 2));
        return result;
    }


    //数组根据某一字段从高到低排序
    compare(property) {
        return function (a, b) {
            const value1 = a[property];
            const value2 = b[property];
            return value1 - value2;
        };
    }


    initCamera() {
        this.viewer.camera.flyTo({
            destination: window.Cesium.Cartesian3.fromDegrees(
                110.62898254394531,
                40.02804946899414,
                1000.0
            ), //相机飞入点
        });
    }

    getLocation() {
        let handler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas);
        handler.setInputAction(function (event) {
            let earthPosition = this.viewer.scene.pickPosition(event.position);
            if (Cesium.defined(earthPosition)) {
                let cartographic = Cesium.Cartographic.fromCartesian(earthPosition);
                let lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(5);
                let lat = Cesium.Math.toDegrees(cartographic.latitude).toFixed(5);
                let height = cartographic.height.toFixed(2);
                console.log(earthPosition, {
                    lon: lon,
                    lat: lat,
                    height: height,
                });
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

}
export default MeasureVolume