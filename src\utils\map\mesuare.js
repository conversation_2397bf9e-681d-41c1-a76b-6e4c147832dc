
/**
 * 测量工具类
 * <AUTHOR>
 * @time 2022年10月17日
 */
class Measure {
    constructor(_viewer) {
        this.viewer = _viewer;
        this.init();
    }

    init() {
        this.measure = new Geowin3D.GwMicroApp.GwAnalysisManager(this.viewer);
        this.volumeMesuare = new Geowin3D.GwModel.GwModelManager(this.viewer);
    }

    on(callback) {
        this.measure.on(function(eventType, eventArg) {
            callback(eventType, eventArg);
        })
    }

    singlePoint() {
        this.measure.start('SINGLE_POINT');
        this.measure.setSingleMode(false);
    }

    singlePoint2() {
        this.measure.start('SINGLE_POINT');
        this.measure.setSingleMode(true);
    };

    lineDistance() {
        this.measure.start('LINE_DISTANCE');
        this.measure.setSumPointMode(false);
    };

    segmentsDistance() {
        this.measure.start('SEGMENTS_DISTANCE');
        this.measure.setSumPointMode(false);
    };

    segmentsDistance2() {
        this.measure.start('SEGMENTS_DISTANCE');
        this.measure.setSumPointMode(true);
    };

    area() {
        this.measure.start('AREA');
        this.measure.setSumPointMode(false);
    };

    elevation() {
        this.measure.start('ELEVATION');
        this.measure.setSumPointMode(true);
    };

    height() {
        this.measure.start('HEIGHT');
        this.measure.setSumPointMode(false);
    };

    angle() {
        this.measure.start('ANGLE');
        this.measure.setSumPointMode(false);
    };

    volume(fillColor){
        let _fillColor = Geowin3D.defined(fillColor) ? fillColor : [0, 255, 255, 0.7]
        this.volumeEnt = this.volumeMesuare.add('Volume', {
            fillColor: _fillColor
        });
        this.viewer.scene.requestRender();
    }
    getVolumeData(){
        if(!this.volumeEnt){
            console.log('请先绘制体积');
            return;
        }
        let _data = this.volumeEnt._data;
    }

    getData() {
        return this.measure.getData();
    };

    removeAll() {
        this.measure.removeAll();
        if(this.volumeEnt){
            this.volume.remove(this.volumeEnt);
            this.volumeEnt = null;
        }
    };

    cancel() {
        this.measure.cancel();
    };
}
export default Measure