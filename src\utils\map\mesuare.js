/**
 * 测量工具类
 * <AUTHOR>
 * @time 2022年10月17日
 */
class Measure {
    constructor(_viewer) {
        this.viewer = _viewer || window.viewer;
        this.handler = null;
        this.activePoints = [];
        this.activeEntity = null;
        this.measurementType = null;
        this.callbacks = [];
        this.init();
    }

    init() {
        if (!this.viewer) {
            console.warn('Viewer未初始化，测量工具无法使用');
            return;
        }

        console.log('Cesium测量工具初始化完成');
    }

    on(callback) {
        if (typeof callback === 'function') {
            this.callbacks.push(callback);
        }
    }

    // 触发回调
    _triggerCallback(eventType, eventArg) {
        this.callbacks.forEach(callback => {
            try {
                callback(eventType, eventArg);
            } catch (error) {
                console.error('测量回调执行失败:', error);
            }
        });
    }

    singlePoint() {
        this.startPointMeasurement();
    }

    singlePoint2() {
        this.startPointMeasurement(true);
    }

    lineDistance() {
        this.startDistanceMeasurement();
    }

    segmentsDistance() {
        this.startSegmentsMeasurement(false);
    }

    segmentsDistance2() {
        this.startSegmentsMeasurement(true);
    }

    area() {
        this.startAreaMeasurement();
    }

    elevation() {
        this.startElevationMeasurement();
    }

    height() {
        this.startHeightMeasurement();
    }

    angle() {
        this.startAngleMeasurement();
    }

    volume(fillColor) {
        console.warn('体积测量功能暂未实现，请使用其他测量工具');
    }

    getVolumeData() {
        console.warn('体积数据获取功能暂未实现');
        return null;
    }

    getData() {
        return {
            type: this.measurementType,
            points: [...this.activePoints],
            entity: this.activeEntity
        };
    }

    removeAll() {
        this.cleanup();
        if (this.viewer && this.viewer.entities) {
            this.viewer.entities.removeAll();
        }
    }

    cancel() {
        this.cleanup();
    }

    // 清理当前测量
    cleanup() {
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }
        this.measurementType = null;
        this.activePoints = [];
        this.activeEntity = null;
    }

    // 开始点测量
    startPointMeasurement(singleMode = false) {
        this.cleanup();
        this.measurementType = 'SINGLE_POINT';

        if (!this.viewer) return;

        this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (!position) return;

            const cartographic = window.Cesium.Cartographic.fromCartesian(position);
            const longitude = window.Cesium.Math.toDegrees(cartographic.longitude);
            const latitude = window.Cesium.Math.toDegrees(cartographic.latitude);
            const height = cartographic.height;

            const entity = this.viewer.entities.add({
                position: position,
                point: {
                    pixelSize: 10,
                    color: window.Cesium.Color.YELLOW,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                },
                label: {
                    text: `经度: ${longitude.toFixed(6)}\n纬度: ${latitude.toFixed(6)}\n高度: ${height.toFixed(2)}m`,
                    font: '12pt sans-serif',
                    fillColor: window.Cesium.Color.WHITE,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    style: window.Cesium.LabelStyle.FILL_AND_OUTLINE,
                    pixelOffset: new window.Cesium.Cartesian2(0, -40),
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                }
            });

            this._triggerCallback('SINGLE_POINT', {
                position: position,
                longitude: longitude,
                latitude: latitude,
                height: height,
                entity: entity
            });

            if (singleMode) {
                this.cleanup();
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    // 开始距离测量
    startDistanceMeasurement() {
        this.cleanup();
        this.measurementType = 'LINE_DISTANCE';
        this.activePoints = [];

        if (!this.viewer) return;

        this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (!position) return;

            this.activePoints.push(position);

            this.viewer.entities.add({
                position: position,
                point: {
                    pixelSize: 8,
                    color: window.Cesium.Color.YELLOW,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                }
            });

            if (this.activePoints.length >= 2) {
                const distance = window.Cesium.Cartesian3.distance(
                    this.activePoints[this.activePoints.length - 2],
                    this.activePoints[this.activePoints.length - 1]
                );

                const midpoint = window.Cesium.Cartesian3.midpoint(
                    this.activePoints[this.activePoints.length - 2],
                    this.activePoints[this.activePoints.length - 1],
                    new window.Cesium.Cartesian3()
                );

                const entity = this.viewer.entities.add({
                    polyline: {
                        positions: [this.activePoints[this.activePoints.length - 2], this.activePoints[this.activePoints.length - 1]],
                        width: 3,
                        color: window.Cesium.Color.YELLOW,
                        clampToGround: true
                    },
                    position: midpoint,
                    label: {
                        text: `${distance.toFixed(2)} m`,
                        font: '14pt sans-serif',
                        fillColor: window.Cesium.Color.WHITE,
                        outlineColor: window.Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: window.Cesium.LabelStyle.FILL_AND_OUTLINE,
                        pixelOffset: new window.Cesium.Cartesian2(0, -40),
                        heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                });

                this._triggerCallback('LINE_DISTANCE', {
                    distance: distance,
                    points: [...this.activePoints],
                    entity: entity
                });
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction(() => {
            this.cleanup();
        }, window.Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    // 其他测量方法的简化实现
    startSegmentsMeasurement(sumMode = false) {
        console.log('分段距离测量功能');
        this.startDistanceMeasurement();
    }

    startAreaMeasurement() {
        console.log('面积测量功能');
    }

    startElevationMeasurement() {
        console.log('高程测量功能');
        this.startPointMeasurement(true);
    }

    startHeightMeasurement() {
        console.log('高度测量功能');
        this.startPointMeasurement(false);
    }

    startAngleMeasurement() {
        console.log('角度测量功能');
    }
}

export default Measure