/**
 * 测量工具类
 * <AUTHOR>
 * @time 2022年10月17日
 */
class Measure {
    constructor(_viewer) {
        this.viewer = _viewer || window.viewer;
        this.handler = null;
        this.activePoints = [];
        this.activeEntity = null;
        this.measurementType = null;
        this.callbacks = [];
        this.init();
    }

    init() {
        if (!this.viewer) {
            console.warn('Viewer未初始化，测量工具无法使用');
            return;
        }

        console.log('Cesium测量工具初始化完成');
    }

    on(callback) {
        if (typeof callback === 'function') {
            this.callbacks.push(callback);
        }
    }

    // 触发回调
    _triggerCallback(eventType, eventArg) {
        this.callbacks.forEach(callback => {
            try {
                callback(eventType, eventArg);
            } catch (error) {
                console.error('测量回调执行失败:', error);
            }
        });
    }

    singlePoint() {
        this.startPointMeasurement();
    }

    singlePoint2() {
        this.startPointMeasurement(true);
    }

    lineDistance() {
        this.startDistanceMeasurement();
    }

    segmentsDistance() {
        this.startSegmentsMeasurement(false);
    }

    segmentsDistance2() {
        this.startSegmentsMeasurement(true);
    }

    area() {
        this.startAreaMeasurement();
    }

    elevation() {
        this.startElevationMeasurement();
    }

    height() {
        this.startHeightMeasurement();
    }

    angle() {
        this.startAngleMeasurement();
    }

    volume(fillColor) {
        console.warn('体积测量功能暂未实现，请使用其他测量工具');
    }

    getVolumeData() {
        console.warn('体积数据获取功能暂未实现');
        return null;
    }

    getData() {
        return {
            type: this.measurementType,
            points: [...this.activePoints],
            entity: this.activeEntity
        };
    }

    removeAll() {
        this.cleanup();
        if (this.viewer && this.viewer.entities) {
            this.viewer.entities.removeAll();
        }
    }

    cancel() {
        this.cleanup();
    }

    // 清理当前测量
    cleanup() {
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }
        this.measurementType = null;
        this.activePoints = [];
        this.activeEntity = null;
    }

    // 开始点测量
    startPointMeasurement(singleMode = false) {
        this.cleanup();
        this.measurementType = 'SINGLE_POINT';

        if (!this.viewer) return;

        this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (!position) return;

            const cartographic = window.Cesium.Cartographic.fromCartesian(position);
            const longitude = window.Cesium.Math.toDegrees(cartographic.longitude);
            const latitude = window.Cesium.Math.toDegrees(cartographic.latitude);
            const height = cartographic.height;

            const entity = this.viewer.entities.add({
                position: position,
                point: {
                    pixelSize: 10,
                    color: window.Cesium.Color.YELLOW,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                },
                label: {
                    text: `经度: ${longitude.toFixed(6)}\n纬度: ${latitude.toFixed(6)}\n高度: ${height.toFixed(2)}m`,
                    font: '12pt sans-serif',
                    fillColor: window.Cesium.Color.WHITE,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    style: window.Cesium.LabelStyle.FILL_AND_OUTLINE,
                    pixelOffset: new window.Cesium.Cartesian2(0, -40),
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                }
            });

            this._triggerCallback('SINGLE_POINT', {
                position: position,
                longitude: longitude,
                latitude: latitude,
                height: height,
                entity: entity
            });

            if (singleMode) {
                this.cleanup();
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    // 开始距离测量
    startDistanceMeasurement() {
        this.cleanup();
        this.measurementType = 'LINE_DISTANCE';
        this.activePoints = [];

        if (!this.viewer) return;

        this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (!position) return;

            this.activePoints.push(position);

            this.viewer.entities.add({
                position: position,
                point: {
                    pixelSize: 8,
                    color: window.Cesium.Color.YELLOW,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                }
            });

            if (this.activePoints.length >= 2) {
                const distance = window.Cesium.Cartesian3.distance(
                    this.activePoints[this.activePoints.length - 2],
                    this.activePoints[this.activePoints.length - 1]
                );

                const midpoint = window.Cesium.Cartesian3.midpoint(
                    this.activePoints[this.activePoints.length - 2],
                    this.activePoints[this.activePoints.length - 1],
                    new window.Cesium.Cartesian3()
                );

                const entity = this.viewer.entities.add({
                    polyline: {
                        positions: [this.activePoints[this.activePoints.length - 2], this.activePoints[this.activePoints.length - 1]],
                        width: 3,
                        color: window.Cesium.Color.YELLOW,
                        clampToGround: true
                    },
                    position: midpoint,
                    label: {
                        text: `${distance.toFixed(2)} m`,
                        font: '14pt sans-serif',
                        fillColor: window.Cesium.Color.WHITE,
                        outlineColor: window.Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: window.Cesium.LabelStyle.FILL_AND_OUTLINE,
                        pixelOffset: new window.Cesium.Cartesian2(0, -40),
                        heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                });

                this._triggerCallback('LINE_DISTANCE', {
                    distance: distance,
                    points: [...this.activePoints],
                    entity: entity
                });
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction(() => {
            this.cleanup();
        }, window.Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    // 开始分段距离测量
    startSegmentsMeasurement(sumMode = false) {
        this.cleanup();
        this.measurementType = 'SEGMENTS_DISTANCE';
        this.activePoints = [];
        let totalDistance = 0;

        if (!this.viewer) return;

        this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (!position) return;

            this.activePoints.push(position);

            this.viewer.entities.add({
                position: position,
                point: {
                    pixelSize: 8,
                    color: window.Cesium.Color.YELLOW,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                }
            });

            if (this.activePoints.length >= 2) {
                const distance = window.Cesium.Cartesian3.distance(
                    this.activePoints[this.activePoints.length - 2],
                    this.activePoints[this.activePoints.length - 1]
                );

                totalDistance += distance;

                const midpoint = window.Cesium.Cartesian3.midpoint(
                    this.activePoints[this.activePoints.length - 2],
                    this.activePoints[this.activePoints.length - 1],
                    new window.Cesium.Cartesian3()
                );

                this.viewer.entities.add({
                    polyline: {
                        positions: [this.activePoints[this.activePoints.length - 2], this.activePoints[this.activePoints.length - 1]],
                        width: 3,
                        color: window.Cesium.Color.YELLOW,
                        clampToGround: true
                    },
                    position: midpoint,
                    label: {
                        text: sumMode ? `总距离: ${totalDistance.toFixed(2)} m` : `${distance.toFixed(2)} m`,
                        font: '14pt sans-serif',
                        fillColor: window.Cesium.Color.WHITE,
                        outlineColor: window.Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: window.Cesium.LabelStyle.FILL_AND_OUTLINE,
                        pixelOffset: new window.Cesium.Cartesian2(0, -40),
                        heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                });

                this._triggerCallback('SEGMENTS_DISTANCE', {
                    segmentDistance: distance,
                    totalDistance: totalDistance,
                    points: [...this.activePoints]
                });
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction(() => {
            this.cleanup();
        }, window.Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    // 开始面积测量
    startAreaMeasurement() {
        this.cleanup();
        this.measurementType = 'AREA';
        this.activePoints = [];

        if (!this.viewer) return;

        this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (!position) return;

            this.activePoints.push(position);

            this.viewer.entities.add({
                position: position,
                point: {
                    pixelSize: 8,
                    color: window.Cesium.Color.YELLOW,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                }
            });

            if (this.activePoints.length >= 3) {
                if (this.activeEntity) {
                    this.viewer.entities.remove(this.activeEntity);
                }

                const area = this.calculatePolygonArea(this.activePoints);
                const center = this.calculatePolygonCenter(this.activePoints);

                this.activeEntity = this.viewer.entities.add({
                    polygon: {
                        hierarchy: this.activePoints,
                        material: window.Cesium.Color.YELLOW.withAlpha(0.3),
                        outline: true,
                        outlineColor: window.Cesium.Color.YELLOW,
                        height: 0
                    },
                    position: center,
                    label: {
                        text: `${area.toFixed(2)} m²`,
                        font: '14pt sans-serif',
                        fillColor: window.Cesium.Color.WHITE,
                        outlineColor: window.Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: window.Cesium.LabelStyle.FILL_AND_OUTLINE,
                        pixelOffset: new window.Cesium.Cartesian2(0, -40),
                        heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                });

                this._triggerCallback('AREA', {
                    area: area,
                    points: [...this.activePoints],
                    entity: this.activeEntity
                });
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction(() => {
            this.cleanup();
        }, window.Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    startElevationMeasurement() {
        this.startPointMeasurement(true);
    }

    startHeightMeasurement() {
        this.startPointMeasurement(false);
    }

    // 开始角度测量
    startAngleMeasurement() {
        this.cleanup();
        this.measurementType = 'ANGLE';
        this.activePoints = [];

        if (!this.viewer) return;

        this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (!position) return;

            this.activePoints.push(position);

            this.viewer.entities.add({
                position: position,
                point: {
                    pixelSize: 8,
                    color: window.Cesium.Color.YELLOW,
                    outlineColor: window.Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                }
            });

            if (this.activePoints.length === 3) {
                const angle = this.calculateAngle(this.activePoints[0], this.activePoints[1], this.activePoints[2]);

                this.viewer.entities.add({
                    position: this.activePoints[1],
                    label: {
                        text: `${angle.toFixed(2)}°`,
                        font: '14pt sans-serif',
                        fillColor: window.Cesium.Color.WHITE,
                        outlineColor: window.Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: window.Cesium.LabelStyle.FILL_AND_OUTLINE,
                        pixelOffset: new window.Cesium.Cartesian2(0, -40),
                        heightReference: window.Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                });

                this.viewer.entities.add({
                    polyline: {
                        positions: [this.activePoints[0], this.activePoints[1], this.activePoints[2]],
                        width: 3,
                        color: window.Cesium.Color.YELLOW,
                        clampToGround: true
                    }
                });

                this._triggerCallback('ANGLE', {
                    angle: angle,
                    points: [...this.activePoints]
                });

                this.cleanup();
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    // 计算多边形面积
    calculatePolygonArea(positions) {
        if (positions.length < 3) return 0;

        const cartographics = positions.map(pos => window.Cesium.Cartographic.fromCartesian(pos));
        let area = 0;

        for (let i = 0; i < cartographics.length; i++) {
            const j = (i + 1) % cartographics.length;
            area += cartographics[i].longitude * cartographics[j].latitude;
            area -= cartographics[j].longitude * cartographics[i].latitude;
        }

        area = Math.abs(area) / 2.0;
        area = area * 6378137 * 6378137;

        return area;
    }

    // 计算多边形中心点
    calculatePolygonCenter(positions) {
        let x = 0, y = 0, z = 0;

        positions.forEach(pos => {
            x += pos.x;
            y += pos.y;
            z += pos.z;
        });

        return new window.Cesium.Cartesian3(
            x / positions.length,
            y / positions.length,
            z / positions.length
        );
    }

    // 计算角度
    calculateAngle(point1, point2, point3) {
        const vector1 = window.Cesium.Cartesian3.subtract(point1, point2, new window.Cesium.Cartesian3());
        const vector2 = window.Cesium.Cartesian3.subtract(point3, point2, new window.Cesium.Cartesian3());

        const dot = window.Cesium.Cartesian3.dot(vector1, vector2);
        const magnitude1 = window.Cesium.Cartesian3.magnitude(vector1);
        const magnitude2 = window.Cesium.Cartesian3.magnitude(vector2);

        const cosAngle = dot / (magnitude1 * magnitude2);
        const angle = Math.acos(window.Cesium.Math.clamp(cosAngle, -1.0, 1.0));

        return window.Cesium.Math.toDegrees(angle);
    }
}

export default Measure