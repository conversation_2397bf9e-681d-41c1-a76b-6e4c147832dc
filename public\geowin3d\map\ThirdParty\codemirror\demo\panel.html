<!doctype html>

<title>CodeMirror: Panel Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/javascript/javascript.js"></script>
<script src="../mode/xml/xml.js"></script>
<script src="../mode/htmlmixed/htmlmixed.js"></script>
<script src="../addon/display/panel.js"></script>
<style type="text/css">
  .border {
    border: 1px solid #f7f7f7;
  }
  .add-panel {
    background: orange;
    padding: 3px 6px;
    color: white !important; 
    border-radius: 3px;
  }
  .add-panel, .remove-panel {
    cursor: pointer;
  }
  .remove-panel {
    float: right;
  }
  .panel {
    background: #f7f7f7;
    padding: 3px 7px;
    font-size: 0.85em;
  }
  .panel.top, .panel.after-top {
    border-bottom: 1px solid #ddd;
  }
  .panel.bottom, .panel.before-bottom {
    border-top: 1px solid #ddd;
  }
</style>

<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Panel</a>
  </ul>
</div>

<article>

<h2>Panel Demo</h2>

<div class="border">
    <textarea id="code" name="code"></textarea>
</div>

<p>
  The <a href="../doc/manual.html#addon_panel"><code>panel</code></a>
  addon allows you to display panels above or below an editor.
  <br>
  Click the links below to add panels at the given position:
</p>

<div id="demo">
<p>
  <a class="add-panel" onclick="addPanel('top')">top</a>
  <a class="add-panel" onclick="addPanel('after-top')">after-top</a>
  <a class="add-panel" onclick="addPanel('before-bottom')">before-bottom</a>
  <a class="add-panel" onclick="addPanel('bottom')">bottom</a>
</p>
<p>
  You can also replace an existing panel:
</p>
<form onsubmit="return replacePanel(this);" name="replace_panel">
  <input type="submit" value="Replace panel n°" />
  <input type="number" name="panel_id" min="1" value="1" />
</form>

<script>
var textarea = document.getElementById("code");
var demo = document.getElementById("demo");
var numPanels = 0;
var panels = {};
var editor;

textarea.value = demo.innerHTML.trim();
editor = CodeMirror.fromTextArea(textarea, {
  lineNumbers: true,
  mode: "htmlmixed"
});

function makePanel(where) {
  var node = document.createElement("div");
  var id = ++numPanels;
  var widget, close, label;

  node.id = "panel-" + id;
  node.className = "panel " + where;
  close = node.appendChild(document.createElement("a"));
  close.setAttribute("title", "Remove me!");
  close.setAttribute("class", "remove-panel");
  close.textContent = "✖";
  CodeMirror.on(close, "click", function() {
    panels[node.id].clear();
  });
  label = node.appendChild(document.createElement("span"));
  label.textContent = "I'm panel n°" + id;
  return node;
}
function addPanel(where) {
  var node = makePanel(where);
  panels[node.id] = editor.addPanel(node, {position: where, stable: true});
}

addPanel("top");
addPanel("bottom");

function replacePanel(form) {
  var id = form.elements.panel_id.value;
  var panel = panels["panel-" + id];
  var node = makePanel("");

  panels[node.id] = editor.addPanel(node, {replace: panel, position: "after-top", stable: true});
  return false;
}
</script>

</div>

</article>
