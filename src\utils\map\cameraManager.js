/*
 * @Description: 摄像机范围管理
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2022-11-23 09:39:08
 * @LastEditors: wbw
 * @LastEditTime: 2022-12-01 17:23:31
 */

import {
    ref
} from 'vue'
class CameraManager {

    /**
     * @description: 初始化CameraManager
     * @param {Object} earth 通过earth.js创建的对象
     * @return {*}
     * @author: wbw
     */
    constructor(earth) {
        this.lng = ref(0);
        this.lat = ref(0);
        this.height = ref(0);
        this.heading = ref(0);
        this.pitch = ref(0);
        this.roll = ref(0);
        this._updateInfo()
        this.earth = earth
        this.maxHeight=300000 + 1900
        this.minHeight= -85000 + 1900

    }

    /**
     * @description: 设置摄像机视角范围
     * @param {Object} option centrePoint中心点经纬度坐标，limitLength范围长度
     * @return {*}
     * @author: wbw
     */
    setExtent(option) {
        this.centrePoint = option.centrePoint
        this.limitLength = option.limitLength
    }

    /**
     * @description: 开启摄像机范围限制
     * @return {*}
     * @author: wbw
     */
    inputExtent() {
        this.lastCameraPosition = undefined
        this.lastCameraHeading = undefined
        this.lastCameraPitch = undefined
        this.lastCameraRoll = undefined
        this.cameraTimer = setInterval(() => {
            if (this.centrePoint && this.limitLength) {
                var centrePointPosition = new Cesium.Cartesian3.fromDegrees(this.centrePoint[0], this.centrePoint[1])
                var cameraPosition = new Cesium.Cartesian3.fromDegrees(Number(this.lng.value), Number(this.lat.value))
                var distance = Cesium.Cartesian3.distance(centrePointPosition, cameraPosition)
                var currentCameraPosition = new Cesium.Cartesian3.fromDegrees(Number(this.lng.value), Number(this.lat.value), Number(this.height.value))
                if (this.lastCameraPosition != undefined && distance > this.limitLength) {
                    // viewer.scene.screenSpaceCameraController.enableZoom = false;
                    viewer.camera.setView({
                        destination: this.lastCameraPosition, //赤道上空1000km高度  
                        orientation: {
                            heading: this.lastCameraHeading, // east, default value is 0.0 (north)
                            pitch: this.lastCameraPitch, // default value (looking down)
                            roll: this.lastCameraRoll

                        }  
                    });
                    // window.cameraVisual.flytoDefault()
                    // console.log(viewer.scene.screenSpaceCameraController)
                    // viewer.scene.screenSpaceCameraController._minimumZoomRate=0
                }
                if (this.lastCameraPosition != undefined && Number(this.height.value) < this.minHeight) {
                    viewer.camera.setView({
                        destination: this.lastCameraPosition, //赤道上空1000km高度 
                         orientation: {
                            heading: this.lastCameraHeading, // east, default value is 0.0 (north)
                            pitch: this.lastCameraPitch, // default value (looking down)
                            roll: this.lastCameraRoll

                        }   
                    });

                }
                if (this.lastCameraPosition != undefined && Number(this.height.value) > this.maxHeight) {

                    viewer.camera.setView({
                        destination: this.lastCameraPosition, //赤道上空1000km高度   
                        orientation: {
                            heading: this.lastCameraHeading, // east, default value is 0.0 (north)
                            pitch: this.lastCameraPitch, // default value (looking down)
                            roll: this.lastCameraRoll

                        }
                    });

                }
                if (distance < this.limitLength && Number(this.height.value) > this.minHeight && Number(this.height.value) < this.maxHeight) {
                    // viewer.scene.screenSpaceCameraController.enableZoom = true;
                    this.lastCameraPosition = currentCameraPosition
                    this.lastCameraHeading = viewer.camera.heading
                    this.lastCameraPitch = viewer.camera.pitch
                    this.lastCameraRoll = viewer.camera.roll
                }
               

            }

        }, 100)
    }

    /**
     * @description: 关闭摄像机范围限制
     * @return {*}
     * @author: wbw
     */
    removeExtent() {
        clearInterval(this.cameraTimer)
    }

    /**
     * @description: 每100ms更新一次摄像机的位置
     * @return {*}
     * @author: wbw
     */
    _updateInfo() {
        this.cameraInfoTimer = setInterval(() => {
            try {
                let cameraInfoData = null;

                if (this.earth && typeof this.earth.getCameraInfo === 'function') {
                    // 使用原有的earth对象
                    cameraInfoData = this.earth.getCameraInfo();
                } else if (window.viewer && window.viewer.camera) {
                    // 使用Cesium viewer直接获取相机信息
                    const camera = window.viewer.camera;
                    const position = camera.position;
                    const cartographic = window.Cesium.Cartographic.fromCartesian(position);

                    cameraInfoData = {
                        lontitude: window.Cesium.Math.toDegrees(cartographic.longitude),
                        latitude: window.Cesium.Math.toDegrees(cartographic.latitude),
                        height: cartographic.height,
                        heading: window.Cesium.Math.toDegrees(camera.heading),
                        pitch: window.Cesium.Math.toDegrees(camera.pitch),
                        roll: window.Cesium.Math.toDegrees(camera.roll)
                    };
                }

                if (cameraInfoData) {
                    this.lng.value = cameraInfoData.lontitude || cameraInfoData.longitude || 0;
                    this.lat.value = cameraInfoData.latitude || 0;
                    this.height.value = cameraInfoData.height || 0;
                    this.heading.value = cameraInfoData.heading || 0;
                    this.pitch.value = cameraInfoData.pitch || 0;
                    this.roll.value = cameraInfoData.roll || 0;
                }
            } catch (error) {
                console.warn('CameraManager获取相机信息失败:', error);
            }
        }, 100)
    }

    /**
     * @description: 获取当前摄像机的位置
     * @return {*}
     * @author: wbw
     */
    getCameraInfo() {
        return {
            lng: this.lng,
            lat: this.lat,
            height: this.height,
            heading: this.heading,
            pitch: this.pitch,
            roll: this.roll
        }
    }

    /**
     * @description: 关闭更新一次摄像机的位置
     * @return {*}
     * @author: wbw
     */
    destory() {
        clearInterval(this.cameraInfoTimer)
    }

}
export default CameraManager;