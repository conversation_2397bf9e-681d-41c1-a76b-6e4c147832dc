{"type": "Topology", "arcs": [[[13365, 10890], [46, 23], [97, -17], [34, -91], [38, -18], [30, -69], [112, -70], [61, -109], [-44, -50], [22, -81], [54, -94], [22, -73], [-3, -84], [-38, -28], [-14, -70], [49, -101], [111, -54], [62, -95], [47, 17], [56, -20], [20, -36], [43, -17], [44, -69], [28, -7], [39, 41], [13, 125], [-30, 83], [19, 16], [129, -12], [63, 47], [-31, 52], [32, 14], [13, 96], [-57, 77], [-30, -3], [-61, 41], [-47, -5], [0, 95], [-25, 44], [24, 26], [4, 82], [43, 12], [20, 61], [-32, 55], [26, 38], [4, 104], [64, 5], [62, -47], [110, 6], [43, -28]], [[14637, 10702], [6, -91], [87, -9], [22, -30], [63, -8], [83, -40], [67, -89], [137, -30], [37, -48], [72, -16], [42, -41], [-23, -76], [21, -57], [-10, -67], [-79, -71], [7, -102], [40, -87], [7, -88], [-54, -69], [-95, -5], [-53, 16], [-76, -38], [-25, 24], [-78, 30], [-30, -51], [69, -142], [-53, -16], [-61, 8], [-32, -22], [-76, 9], [-48, -29], [-69, 84], [-59, 29], [-153, -5], [-31, -29], [25, -95], [-25, -68], [-76, -90], [22, -36], [57, 0], [50, -35], [26, -42], [-8, -74], [-44, 16], [14, -82], [-31, -17], [-37, -94], [22, -41], [1, -96], [33, -57], [-27, -32], [-45, 43], [-97, -32], [-42, 31], [-104, -67], [-38, -116], [72, -35], [-1, -114], [-10, -30], [-108, -53], [-61, 12]], [[13860, 8342], [-29, 3], [-20, -50], [22, -28], [-72, -75], [-53, -18], [-50, 16], [-58, -14], [-26, 15], [-84, 10], [-39, 18], [-66, 68], [-51, 22], [14, 60], [37, 62], [-33, 44], [-21, 75], [5, 51], [-51, 72], [-8, 76], [-27, 25], [-152, 5], [-96, 22], [15, 52], [-90, -29], [-72, 37], [-18, 80], [4, 83], [-14, 34], [-52, 26], [-31, 46], [-64, -12], [-80, -62], [9, -47], [-78, -13], [-52, -41], [-65, -26], [75, -122], [6, -66], [55, -40], [0, -42], [-89, -50], [-64, -52], [-14, -36], [-41, -2], [-61, -54], [-14, 83], [40, 78], [-18, 79], [-46, -9]], [[12243, 8696], [-71, -10], [-16, 66], [-36, 42], [-41, -33], [-104, 11], [-13, 105], [-79, 45], [-68, 128], [10, 79], [52, 52], [44, -27], [96, -21], [29, -24], [125, -38], [16, -32], [58, -38], [35, 41], [65, 21], [29, 51], [59, 48], [-28, 81], [-94, 55], [-8, 40], [-52, -18], [-18, 41], [74, 48], [-4, 32], [41, 55], [112, 65], [47, 83], [-24, 41], [-14, 75], [60, 16], [47, 57], [73, -7], [9, 55], [-19, 77], [9, 47], [60, 13], [60, -17], [11, 90], [-30, 54], [47, 74], [-63, 52], [-34, 10], [-20, 98], [-32, 62], [-37, 28], [27, 82], [-93, 87], [16, 75], [23, 43], [-54, 65], [-78, 58], [-46, 14], [-47, 42], [16, 39], [-57, 103], [-75, -116], [-51, 49], [-154, 106], [-106, 93], [-180, 81], [-36, 83], [-85, 32], [-94, 101], [-25, -52], [31, -52], [-33, -14], [-121, 49], [-105, 85], [-27, 49], [-173, 164], [-2, 38], [-58, 14], [-78, 48], [-77, -67], [-69, 5], [-52, 38], [-27, -52], [-84, -74], [-307, 197], [-98, 30], [-48, -4], [-28, -52], [13, -61], [-5, -143], [-17, -59], [13, -41], [-12, -119], [-43, -1], [-146, 65], [-3, 45], [-81, 44], [-113, 29], [-21, 41], [-50, 19], [-74, 51], [-68, 80], [-46, 18], [-67, 77], [-108, -21], [-144, 52], [-41, 32], [-66, 14], [-158, 2], [-90, -21], [-81, 12], [-95, -20], [-99, -47], [-59, 6], [-89, -27]], [[8401, 11883], [6, 171], [-77, 203], [2, 54], [62, 120], [3, 173], [48, 38], [108, -12], [127, 68], [80, 133], [108, 159], [248, 273], [207, 115], [189, 39], [122, -16], [39, 15], [76, 79], [-9, 82], [12, 205], [20, 49], [120, 77]], [[9892, 13908], [358, 36]], [[10250, 13944], [285, -661], [-86, -93], [26, -61], [105, -130], [164, -150], [-40, -178], [122, -4], [56, 19], [78, 90], [145, 63], [146, 3], [74, 34], [150, -3], [73, -92], [12, -67], [-29, -56], [-54, -66], [-50, -111], [-119, -78], [-84, -107], [111, -2], [155, -84], [63, -19], [42, -39], [10, -43], [64, -31], [38, -49], [107, 0], [18, -17], [10, -119], [15, -36], [69, -59], [58, 11], [30, -85], [72, -69], [72, -34], [109, 1], [82, 94], [1, 20], [-86, 97], [247, 99], [87, -34], [161, -33], [139, 98], [78, 37], [124, 38], [119, 9], [17, -76], [71, -122], [-24, -75], [-56, -48], [-27, -66], [-80, -95], [-141, -88], [23, -167], [-58, -22], [-1, -58], [22, -89], [105, -38], [80, -85], [106, -93], [67, -41], [42, 6]], [[12243, 8696], [-37, -62], [-11, -57], [26, -30], [-35, -79], [-87, 62], [-98, -38], [-24, -24], [-14, -107], [43, -46], [-26, -96], [-117, -33], [-90, 32], [-40, -133], [-31, -28], [-25, 46], [37, 45], [-26, 59], [-44, 51], [-62, -18], [-14, -51], [-45, 19], [16, 64], [-47, 87], [-90, 12], [-32, -20], [-30, -101], [-82, 52], [-96, 29], [-28, 64], [-53, 41], [-109, 59], [-34, 72], [-16, 106], [-45, 67], [5, 39], [-81, 92], [-68, 4], [-39, 53], [-77, 12], [-94, -55], [-32, 0], [-21, 80], [-38, -42], [-98, -34], [3, -147], [37, -28], [28, -62], [80, -26], [-48, -44], [-49, -76], [2, -85], [-62, -84], [29, -111], [124, -89]], [[10478, 8137], [-36, -57], [-30, 23], [-87, 9], [-16, -44], [29, -21], [-15, -71], [-45, -32], [0, -73], [-57, -37], [-46, 7], [-115, -27], [0, -77], [18, -70], [-135, 22], [-90, 88], [-41, -22], [-15, -52], [28, -24], [1, -74], [-52, 53], [-107, 8], [-112, 27], [-40, -11], [-40, 39], [-22, 81], [30, 80], [-18, 43], [-54, 42], [-86, 9], [-47, 43], [-7, 52], [-79, 61], [-14, 32], [-61, 45], [-115, -74], [-61, -11], [-29, -24], [-102, 9], [-62, 44], [-60, -5], [-37, -27], [-71, 35], [-55, 46], [-82, 2], [-11, 41], [-68, -4], [-53, 18], [-34, -11], [-80, 17], [-106, -15], [-11, 71], [-48, 9], [-48, -20], [-71, 67], [-40, 8], [-77, 56], [-72, 108], [-7, 38], [-75, 0], [-26, -47], [-77, -9], [-64, -56], [-63, 8], [-97, 72], [-49, 4], [-51, 64], [-1, 56], [-27, 44], [-73, 29], [-41, 110], [-59, 49], [-46, 104], [65, 60], [23, 105], [-19, 13], [-2, 83], [-30, 106], [44, 60], [-16, 46], [-89, 8], [-5, 75], [-51, 79], [14, 73], [106, 57], [-17, 37], [10, 76], [33, 105], [-91, 7], [-41, 23], [-22, 61], [35, 37], [70, 17]], [[6985, 10143], [83, -10], [45, 23], [19, 84], [66, -71], [198, -10], [90, -56], [37, 1], [86, 34], [10, 38], [-33, 98], [-23, 108], [-87, 31], [-51, 79], [10, 70], [42, 50], [107, 20], [101, 39], [12, 60], [-35, 43], [-69, 166], [-51, 18], [-54, 50], [-173, 97], [-11, 39], [13, 84], [-107, 187], [-19, 60], [70, 20], [72, 38], [69, 51], [7, 28], [156, 15], [123, 25], [86, 41], [60, 5], [214, 66], [91, 61], [262, 58]], [[16684, 3710], [-28, -22], [0, -55], [32, -27], [-6, -50], [24, -48], [-31, -59], [-46, -26], [12, -136], [-40, -46], [-6, -34], [-59, -4], [-24, -105], [-56, -25], [-1, -31], [-34, -72], [-13, -90], [3, -75], [18, -49], [-26, -82], [-73, -91], [-50, -4], [-18, -59], [-117, -32], [-49, -225], [-77, -19], [-58, 23], [10, -52], [0, -119], [-60, -8], [-33, 12], [-76, -17], [-22, -102], [-73, -63]], [[15707, 1918], [-43, -32], [-33, 35], [-19, -51], [-40, -21], [-53, 2], [-44, -23], [-71, 11], [40, 42], [-4, 52], [-29, 20], [-42, -18], [-30, 34], [-65, -9], [-79, 92], [-7, 52], [-46, -21], [25, -100], [-27, -26], [-53, 37], [-30, -65], [-51, -8], [-63, -54], [-58, 65], [-49, 32], [-57, -29], [-115, -4], [-32, 68], [-44, -2], [-47, 46], [-19, 54], [-69, 44], [-61, 21], [-22, 48], [-4, 75], [-19, 56], [-28, 5], [-12, 55], [18, 83], [49, 3], [24, 66], [-3, 59], [-27, 38], [-78, 33], [-75, -41], [-35, 6], [-13, 47], [-89, 17], [-63, -40], [-71, 78], [-60, 10], [-14, 58]], [[13870, 2818], [142, 201], [59, -27], [63, 40], [10, 119], [18, 67], [-16, 59], [-80, 65], [-43, -35], [-37, -3], [-111, 60], [-37, -50], [-55, 45], [-30, -18], [-35, 47], [2, 80], [-50, 69], [-44, -8], [-90, 28], [-10, -58], [-27, -14], [-29, 36], [-43, 99], [-3, 68], [20, 25]], [[13444, 3713], [82, -62], [53, 41], [11, 28], [59, 25], [38, 89], [46, 27], [37, -40], [39, 14], [28, -19], [27, -60], [73, -15], [48, -42], [56, 7], [38, -46], [75, 75], [11, 55], [-19, 47], [65, 12], [122, 70], [35, 38], [95, 14], [47, 33], [9, 56], [-18, 49], [22, 33], [95, 34], [67, -97], [44, -98], [62, 30], [59, -65], [37, -4], [46, 36], [43, 2], [27, 32], [2, 66], [70, 87], [48, -13], [71, -57], [55, 106], [35, -45], [72, 14], [37, 27], [26, 119], [28, -31], [66, -4], [42, 145], [25, 22]], [[15580, 4448], [64, 9], [29, -39], [3, -55], [41, 12], [4, 65], [25, 11], [32, 78], [65, -17], [-12, -65], [45, 9], [48, -42], [40, 76], [63, 34], [23, 30], [17, 71], [48, -10], [16, -33], [67, 0], [25, 62], [81, -44], [56, -15], [2, -39], [-23, -159], [23, -35], [66, 22], [1, -60], [-37, -41], [-4, -128], [-27, -60], [-38, -19], [-23, -72], [-52, -49], [-17, -71], [22, -52], [32, 5], [20, 58], [65, 58], [67, -23], [15, -50], [-14, -43], [19, -85], [-20, -64], [38, -21], [65, 77], [57, -11], [56, 8], [31, -21]], [[15494, 5848], [2, -105], [40, -32], [-24, -97], [27, -36], [-22, -34], [7, -92], [38, -40], [14, -50], [-12, -51], [-93, -66], [-82, -103], [-50, -42], [-48, -59], [37, -37], [32, 5], [63, 60], [27, -30], [32, 40], [90, -12], [26, -39], [4, -48], [-27, -43], [26, -43], [-47, -40], [-44, -15], [35, -46], [11, -62], [-37, -64], [-8, -63], [44, -25], [25, -131]], [[13444, 3713], [2, 41], [70, 115], [-8, 47], [13, 63], [-21, 49], [-45, 47], [-6, 59], [-52, 13], [-5, 41], [-50, 49], [50, 108], [-12, 52], [46, 47], [20, 135], [56, 63], [-50, 116], [-87, 36], [-61, -12], [-29, 14], [-57, -78], [-83, 19], [-31, 46], [7, 58], [-25, 38], [26, 46], [0, 43], [-33, 55], [-2, 56], [93, 135], [31, 6], [83, -73], [75, 96], [42, -53], [67, -18], [82, 10], [38, -13], [91, 58], [50, -7], [44, 183]], [[13773, 5403], [27, 21], [71, 8], [53, -54], [100, 38], [71, 6], [28, 19], [63, -8], [43, 34], [-21, 93], [-36, 32], [-7, 39], [-39, 28], [-44, -19], [-45, 6], [-25, 67], [-78, 28], [-21, 93], [28, 58], [90, 14], [40, 82], [89, -96], [35, -16], [49, 9]], [[14244, 5885], [47, -18], [31, 128], [29, -74], [-29, -71], [82, -4], [6, 50], [28, 10], [3, 68], [24, 40], [36, 2], [25, 47], [57, -3], [59, -42], [61, 39], [27, 67], [-33, 47], [3, 60], [28, 36], [27, -34], [98, -27], [2, -56], [38, -31], [78, 45], [100, 7], [36, -170], [-17, -50], [110, -50], [0, -128], [64, 57], [24, -28], [-25, -105], [94, -23], [66, 3], [27, 131], [44, 40]], [[15640, 7680], [57, -58], [3, -43], [85, -23], [79, -69], [28, -118], [-29, -38], [9, -123], [-27, -44], [-30, -1], [-55, 35], [-53, -30], [-47, -66], [-107, -77], [-64, 8], [-29, -17], [-94, 23], [-42, -33], [-68, -15], [-18, 29], [-42, -43], [-61, -6], [-22, -50], [65, -47], [3, -245], [-60, -31], [20, -54], [29, -7], [44, 77], [35, -91], [78, -33], [0, -78], [20, -87], [41, -23], [27, -53], [57, -63]], [[15472, 6186], [30, -31], [-14, -67], [1, -82], [15, -33], [-30, -56], [32, -32], [-12, -37]], [[14244, 5885], [-37, 57], [-16, 111], [-109, 60], [-57, -31], [-58, 43], [-19, 129], [-25, 26], [-96, 36], [-7, 55], [-40, 23], [-10, 65], [53, 67], [61, 25], [2, 32], [66, 36], [13, 94], [-79, 65], [66, 72], [35, 84], [145, -45], [50, -70], [63, 25], [64, 42], [38, -39], [42, -119], [24, -13], [114, 12], [85, 103], [62, 148], [46, 62], [-9, 86], [22, 34], [100, 35], [49, -41], [34, 36], [1, 43], [33, 51], [11, 103], [70, 62], [14, 77], [79, 56], [48, 81], [-7, 47], [-93, 74], [-8, 29], [33, 57], [32, 15], [-13, 48], [57, 7]], [[15173, 7940], [117, -86], [116, -59], [58, -57], [27, -54], [37, -23], [112, 19]], [[19013, 12510], [-19, -83], [-32, -11]], [[18962, 12416], [-109, -32], [-69, 1], [-37, -44], [17, -39], [61, -66], [-21, -50]], [[18804, 12186], [-35, -47]], [[18769, 12139], [-83, 5], [-76, -51], [-18, -45], [-60, 36], [-17, 38], [-116, 2], [-81, -38], [-60, 53], [-67, 23], [-12, 60], [32, 51], [-6, 48], [-31, 22], [66, 75], [105, 32], [54, 81], [-93, 139], [63, 40], [53, -8], [58, 36], [33, 52], [37, -6], [73, 29], [-9, 62], [94, 74], [9, -52], [103, -108], [64, -41], [61, 8], [99, -12], [-7, -41], [-73, -16], [-25, -41], [22, -26], [6, -65], [46, -45]], [[20066, 4209], [17, -40], [-11, -47], [-43, 27], [37, 60]], [[20344, 5110], [-62, 63], [-23, -27], [86, -66], [-28, -31], [-32, 8], [-21, -105], [-47, 1], [-44, -52], [45, -23], [-22, -85], [-41, 4], [40, 75], [-57, 6], [-38, -81], [-19, 82], [-35, -19], [-64, 17], [-15, -26], [1, -75], [52, 2], [48, -24], [15, -70], [-72, -15], [12, -63], [29, 6], [34, 56], [24, -36], [-42, -37], [-48, -10], [-93, -168], [-115, 59], [22, -69], [44, -15], [40, 13], [55, 49], [58, -43], [-36, -45], [-10, -114], [-25, 7], [-33, -41], [43, -39], [38, -97], [-71, -12], [10, 58], [-61, 27], [-27, 47], [-28, -70], [-56, -42], [49, -51], [28, 5], [25, -47], [-25, -30], [-49, -8], [-14, 26], [-67, 0], [-48, -102], [44, -17], [8, -33], [-122, -63], [13, -69], [-76, -86], [-65, 31], [-31, -23], [-95, 21], [-38, -25], [-21, -73], [57, -105], [-60, -39], [-20, 11], [-21, -105], [-87, -42], [-48, -36], [-21, 17], [-31, -74], [-75, -15], [-13, -61], [-38, -22]], [[18934, 3075], [-61, 69], [-47, 193], [18, 60], [-37, 119], [-59, 90], [-1, 67], [-61, -9], [-32, -23], [-68, 128], [-112, 3], [-35, 27], [-64, 13]], [[18375, 3812], [0, 47], [-19, 111], [28, 24], [31, 65], [21, 161], [42, 73], [5, 57], [81, 51], [32, 61], [10, 51], [-9, 77], [44, 67], [41, -6], [10, 54], [-33, 40], [-16, 73], [18, 101], [54, 67], [110, 19], [51, 51], [49, 94], [-23, 40], [10, 47], [-5, 78], [49, 89], [26, 27], [-1, 48], [113, 63], [27, -57], [61, -30], [34, 69], [103, 25], [31, 35], [102, 32], [-8, 69], [43, 39]], [[19487, 5724], [22, -14], [103, 35], [27, -64], [-14, -20], [-8, -116], [32, -36], [30, -106], [15, -97], [35, -40], [79, -34], [34, 2], [55, 50], [34, 7], [19, 64], [45, 16], [30, -88], [3, -56], [34, -54], [128, 15], [36, 33], [85, -13], [27, -47], [6, -51]], [[20001, 7337], [-24, -163], [-32, -44], [-26, -73], [-39, 20], [-48, -71], [35, -18], [18, -64], [-66, -51], [-66, -2], [-54, 23], [-24, -15], [12, -59], [-24, -35], [18, -101], [-32, -58], [-34, -29], [-8, -56], [-72, -59], [-81, -98], [-85, -25]], [[19369, 6359], [-27, 62], [-55, 31], [-38, -13], [-83, 3], [-26, 29], [-53, 9], [-54, 86], [1, 32], [-57, -1], [-21, 44], [-44, 2], [-24, -40], [5, -73], [-54, -32], [-30, -53], [-61, -8], [-42, 46], [11, 46], [54, 27], [38, 94], [-41, 57], [-61, 16], [-70, -94], [-44, -5], [-66, -53], [-39, 4]], [[18488, 6575], [-31, 101], [-16, 144], [-56, 54], [-15, 41], [30, 56], [-77, 113], [3, 38], [38, 17], [-2, 43], [77, 73], [-39, 26], [-30, 54], [-47, 8], [-44, 42], [-51, -21], [-77, 134]], [[18151, 7498], [7, 54], [62, 127], [57, 27], [104, 6], [11, 130], [-17, 210], [-43, 43], [-42, 12], [-20, -50], [-35, 0], [-87, 89], [-72, 17], [-6, 153], [-113, 56], [-18, 71], [37, 22], [75, -22], [47, 21], [41, 74], [-12, 69], [13, 45], [42, 54], [83, 25], [-26, 90], [24, 50], [-31, 34], [41, 85], [35, 9], [47, -33], [51, -4], [5, -54], [47, -72], [45, -33], [103, 41], [11, 30], [86, 62], [-43, 95], [13, 41], [-9, 53], [-66, -6], [-106, 97], [-3, 67]], [[18489, 9283], [51, 6], [48, 36]], [[18588, 9325], [22, 0], [85, -88], [62, -18], [15, -30], [64, -6], [9, -64], [27, -69], [55, -36], [71, -7], [30, -27], [49, 11], [64, -39], [41, -56], [3, -90], [162, 23], [22, -41], [-106, -260], [64, -48], [60, 18], [9, -146], [27, -83], [43, -26], [140, 0], [11, 60], [48, 76], [59, -5], [86, -142], [4, -67], [-52, -83], [-82, 84], [-61, 10], [-86, -22], [50, -68], [7, -65], [-14, -58], [-59, -22], [-8, -34], [-49, -48], [-15, -61], [56, -50], [-1, -39], [90, -50], [-1, -31], [77, -11], [9, -59], [-23, -72], [-33, -21], [28, -75], [113, 7], [38, 27], [67, -11], [20, -42], [116, -34]], [[15986, 1646], [59, -8], [-2, -62], [-120, 31], [63, 39]], [[17060, 2027], [-56, -79], [-10, 41], [30, 40], [36, -2]], [[17399, 2500], [-66, 58], [15, 23], [48, -50], [3, -31]], [[17523, 4114], [37, -8], [6, -94], [39, 22], [71, -9], [65, 53], [61, 12], [5, -26], [55, -40], [-11, -72], [20, -25], [-78, -37], [-59, -42], [-13, -54], [-36, -72], [-47, -36], [42, -56], [59, -54], [32, 32], [105, 38], [53, -20], [36, 46], [45, 21], [39, -13], [44, 35], [78, 22], [79, -100], [46, -36], [40, 16], [-21, 99], [16, 64], [44, 32]], [[18934, 3075], [-66, 24], [-45, -12], [-28, -64], [5, -42], [-51, -71], [-8, -39], [-85, -84], [12, -51], [-30, -45], [-49, -6], [-36, 19], [-55, -56], [-42, 2], [-51, -36], [-64, 2], [-68, 50], [-30, -9], [-27, -78], [-65, -27], [-18, 53], [-37, 27], [-78, -23], [-88, -109], [-53, 4], [4, 80], [-15, 23], [-67, -34], [-17, -25], [6, -68], [34, -30], [-43, -35], [-17, 53], [-35, 38], [-72, -33]], [[17655, 2473], [-65, -14]], [[17590, 2459], [-57, -3], [-26, -41], [-51, 125], [-13, 50], [-35, 21], [-34, 103], [-35, 6], [-59, -63], [55, -59], [-24, -20], [69, -94], [-23, -77], [19, -114], [-46, -47], [-78, 17], [-32, -80], [-31, -10], [-21, 41], [-36, 26], [1, -103], [-32, -49], [-34, 0], [-23, 51], [-78, -66], [-17, -42], [-71, 30], [-34, -54], [-52, -14], [-32, 60], [-78, -29], [-131, -90], [-26, -42], [-83, 2], [-60, -19], [-41, 18], [-147, -84], [-34, 12], [-50, -54], [-17, -46], [-67, -7], [-27, 91], [-25, -51], [14, -70], [-55, -42], [-49, -59], [0, -77], [100, -14], [-21, -56], [31, -80], [31, -10], [24, -50], [-59, -101], [-52, -27], [-66, -3], [-44, 30], [-5, 66], [-84, 56], [-9, 57], [-24, 8], [12, 108], [-36, 8], [-13, 53], [21, 73], [-17, 37], [45, 52], [14, 80], [43, 19], [5, 57], [-50, -9], [-23, 58]], [[16684, 3710], [30, 34], [34, -1], [9, 66], [-19, 22], [40, 142], [106, -21], [90, -28], [21, -65], [42, -32], [78, 12], [-13, 52], [4, 70], [-34, 35], [12, 47], [75, 51], [55, 52], [89, -69], [44, 1], [26, -34], [57, 20], [50, -2], [43, 52]], [[6460, 10351], [110, -46], [50, 3], [57, -49], [115, -13], [197, -103], [-70, -17], [-35, -37], [22, -61], [42, -23], [90, -7], [-32, -105], [-11, -76], [18, -37], [-107, -57], [-13, -73], [50, -79], [6, -75], [88, -8], [16, -46], [-43, -60], [29, -106], [2, -83], [19, -13], [-23, -105], [-65, -60], [46, -104], [59, -49], [41, -110], [73, -29], [27, -44], [1, -56], [51, -64], [49, -4], [98, -72], [62, -8], [64, 56], [77, 9], [27, 47], [74, 0], [7, -38], [72, -108], [78, -56], [39, -8], [71, -67], [48, 20], [48, -9], [11, -71], [106, 15], [80, -17], [34, 11], [53, -18], [68, 4], [11, -41], [82, -2], [55, -46], [71, -35], [38, 27], [60, 5], [62, -44], [101, -9], [29, 24], [61, 11], [115, 74], [61, -45], [14, -32], [80, -61], [6, -52], [47, -43], [86, -9], [54, -42], [18, -43], [-30, -80], [22, -81], [40, -39], [40, 11], [112, -27], [107, -8], [52, -53], [-1, 74], [-27, 24], [14, 52], [42, 22], [89, -88], [135, -22], [-18, 70], [0, 77], [115, 27], [46, -7], [57, 37], [0, 73], [46, 32], [14, 71], [-29, 21], [16, 44], [87, -9], [30, -23], [36, 57], [131, -45], [82, -62], [7, -59], [39, -70], [62, -81], [-14, -61], [11, -42], [49, -72], [14, -44], [127, -139], [-51, -70], [-36, 48], [-31, -34], [3, -38], [79, -112], [-11, -50], [78, -88], [-22, -47], [25, -116], [11, -179], [20, -51], [10, -79], [-22, -54], [-8, -96], [24, -50], [8, -144], [21, -47], [-52, -14], [7, -72], [-57, -149], [-39, 108], [-53, -15], [-11, -55], [17, -68], [-38, -49], [22, -140], [-28, -61], [-37, -39], [-35, 1], [-47, 60], [-31, -86], [-50, -34], [-54, 40], [1, 31], [-41, 49], [-69, 15], [-18, 60], [-36, -4], [-38, 28], [-36, -84], [-4, -54], [-49, -39], [0, 0], [0, 0], [2, -16], [-13, -34], [-2, -20], [6, -14], [21, -27], [3, -16], [-3, -8], [-7, -16], [-2, -9], [1, -13], [0, -6], [-2, -5], [-8, -5], [-9, -4], [-2, 4], [0, 7], [-5, 8], [-7, 0], [-9, -2], [-8, -5], [-5, -5], [-6, -10], [-6, -9], [-22, -22], [-4, -3], [-5, -2], [-3, -2], [-3, -8], [-3, -3], [-8, -4], [-3, -3], [-2, -6], [0, -7], [-2, -2], [-1, 0], [0, 2], [-5, 41], [-24, 0], [-31, 35], [-62, 28], [-46, 16], [-24, 42], [-26, 15], [-20, 11], [-39, 50], [-16, -28], [-34, 15], [-19, 55], [-23, -6], [-46, -13], [-33, -13], [-56, 43], [-11, 19], [-35, -7], [-51, -20], [-66, -36], [-34, -23], [-32, -22], [-28, -83], [-20, -12], [-40, -27], [-17, -12], [-1, 0], [-14, -8], [-58, -34], [-94, -59], [-7, -5], [-14, -6], [-24, -10], [-21, -9], [-44, -19], [-24, -12], [-34, 3], [-11, 1], [-14, -4], [-41, -36], [-55, -70], [-52, -61], [-25, -35], [-17, -23], [-7, -77], [-42, -21], [-38, -22], [-37, -21], [-106, -14], [-88, -22], [-83, 20], [-58, -8], [-60, 31], [-50, -3], [-53, -21], [-99, -22], [-72, -9], [16, 19], [4, 19], [1, 9], [-7, 32], [0, 0], [-8, 19], [-21, 25], [-6, 18], [0, 11], [2, 10], [2, 10], [3, 8], [1, 2], [1, 2], [6, 7], [2, 5], [2, 5], [1, 10], [2, 5], [6, 9], [13, 13], [3, 10], [-1, 7], [-38, 82], [-10, 13], [-5, -2], [-13, -11], [-6, -2], [-16, 1], [-11, -2], [-34, -14], [-13, -2], [-2, 5], [-1, 8], [-7, 12], [0, 0], [-10, 5], [-10, 2], [-10, 4], [-7, 9], [-4, 9], [-12, 12], [-4, 8], [-10, 42], [3, 21], [20, 34], [3, 24], [0, 0], [0, 0], [-1, 14], [-3, 95], [-89, 24], [-65, 45], [-41, -9], [-35, -49], [-52, 6], [-29, 29], [-97, 30], [-91, -8], [49, 91], [-141, 58], [-84, 7], [-90, -29], [-46, -63], [-85, -28], [-51, -59], [-47, -91], [-48, -35], [-43, -103], [-32, -11], [-35, -51], [-9, -48], [20, -58], [-46, -8], [-52, 52], [-14, 77], [29, 63], [21, 107], [-16, 85], [-90, 64], [-48, -44], [-52, -26], [-110, -18], [-14, -48], [-118, 22], [-29, -48], [-98, 9], [-63, -18], [-101, 12], [-54, 63], [-60, 10], [-52, 32], [-48, 54], [-51, -8], [-21, -88], [-25, -15], [-103, 52], [-20, 87], [-38, -45], [20, -85], [-58, -23], [-57, 154], [-40, 26], [-36, 58], [-27, -32], [-78, 27], [-76, -17], [-77, 29], [-1, 67], [39, 83], [-49, 31], [-59, -58], [-52, 6], [-53, 33], [-31, 53], [-68, 20], [-42, 73], [-51, 22], [-2, 71], [-43, 67], [-14, 58], [-68, 26], [-97, -33], [-28, -41], [-61, 17], [-81, 166], [-72, 80], [-56, -3], [-58, 39], [-28, -17], [-94, 102], [-34, 48], [-64, 46], [-109, 36], [5, 64], [-26, 18], [-3, 67], [-135, 15], [-81, 32], [-47, -46], [-42, 23], [-13, -113], [-35, -24], [-36, -72], [-42, 7], [-53, 135], [-84, 38], [-26, 39], [-64, 42], [-21, -4], [-119, 61], [-37, 55], [28, 36], [-27, 33], [-47, 0], [-99, 101], [-41, 10], [-92, -15], [-101, 97], [-42, 113], [-59, 63], [-38, -65], [-50, -16], [-52, 24], [10, 63], [-28, 37], [52, 55], [-39, 34], [-25, 58], [32, 110], [-42, 41], [-59, 97], [-24, 13], [-4, 111], [-29, 58], [28, 21], [80, 7], [36, 34], [26, -100], [74, -66], [64, 26], [9, 46], [72, 10], [87, 88], [10, 57], [-38, 80], [-34, 40], [20, 96], [-177, 103], [-65, 86], [-18, 53], [5, 108], [-32, 142], [4, 48], [76, 45], [31, 33], [-8, 71], [108, 56], [58, -5], [75, 23], [90, -34], [32, 11], [72, 144], [-32, 133], [70, 55], [-14, 39], [71, 95], [62, 145], [73, -59], [161, -33], [52, -30], [36, 54], [65, -41], [64, 9], [81, -46], [73, -3], [126, 42], [37, 73], [88, 55], [13, 50], [47, 32], [123, -23], [32, 20], [66, -7], [18, -42], [-3, -62], [56, -44], [64, 5], [139, -35], [132, 9], [50, 23], [64, -21], [35, 6], [152, 97], [94, 22], [63, 52], [98, 33], [48, -11], [56, 15], [100, -46], [43, 47], [92, 3], [68, 64], [54, 136], [82, 31], [26, 27], [111, 7], [32, 17], [177, 24], [65, 40], [99, -22], [153, 27], [33, 26], [199, 12]], [[6985, 10143], [-197, 103], [-115, 13], [-57, 49], [-50, -3], [-110, 46], [-199, -12], [-33, -26], [-154, -27], [-98, 22], [-65, -40], [-177, -24], [-32, -17], [-111, -7], [-26, -27], [-82, -31], [-54, -136], [-68, -64], [-92, -3], [-43, -47], [-100, 46], [-56, -15], [-48, 11], [-98, -33], [-64, -52], [-93, -22], [-152, -97], [-35, -6], [-64, 21], [-50, -23], [-133, -9], [-138, 35], [-64, -5], [-56, 44], [3, 62], [-18, 42], [-66, 7], [-32, -20], [-123, 23], [-47, -32], [-13, -50], [-89, -55], [-36, -73], [-126, -42], [-73, 3], [-81, 46], [-64, -9], [-65, 41], [-36, -54], [-52, 30], [-162, 33], [-72, 59], [-62, -145], [-71, -95], [14, -39], [-70, -55], [31, -133], [-71, -144], [-32, -11], [-90, 34], [-75, -23], [-58, 5], [-108, -56], [-67, 41], [-85, 75], [-99, 19], [-46, 45], [-28, 107], [-27, 54], [-7, 64], [-56, 120], [23, 105], [-30, 23], [-45, -36], [-36, 37], [-61, -21], [-108, 7], [-70, 35], [-63, 4], [-28, 26], [-62, 2], [-142, 92], [-3, 65], [-57, -3], [-53, -29], [-63, -2], [-22, 77], [-24, 28], [-60, 23], [-12, 38], [52, 74], [-19, 41], [3, 91], [-44, 89], [-37, 39], [-95, 45], [-35, -26], [-35, 15], [-26, 95], [-109, 45], [-82, -18], [-108, 52], [-53, -19], [-76, 72], [53, 44], [59, 3], [42, 38], [27, -44], [124, 52], [42, 50], [-118, 113], [-3, 30], [34, 65], [-35, 44], [4, 85], [-44, 50], [-9, 119], [29, 50], [-27, 73], [-192, 89], [-84, 1], [-91, -78], [-59, 41], [-48, 143], [57, 78], [-51, 48], [-42, 89], [17, 58], [-4, 63], [82, 11], [46, 71], [-15, 68], [-30, 53], [79, 145], [70, 26], [89, 0], [145, 127], [55, -7], [-19, 57], [21, 53], [89, -40], [67, 0], [125, 97], [34, 11], [46, -103], [-12, -47], [19, -43], [103, 9], [68, 47], [33, -13], [40, 42], [37, -53], [52, 37], [77, 121], [2, 57], [61, 79], [-2, 44], [41, 39], [63, 17], [49, -18], [93, 5], [62, -22], [82, 11], [88, 36], [90, -15], [44, 38], [81, 103], [0, 52], [112, 56], [23, 35], [132, 58], [143, 83], [46, -5], [87, 50], [59, 7], [42, 70], [38, 12], [109, -2], [33, 36], [-21, 60], [26, 28], [-56, 217], [37, 96], [109, 41], [-46, 94], [89, 51], [87, -3], [-18, 99], [-39, 21], [30, 75], [-103, 257], [-57, 108], [13, 95], [-19, 84], [20, 143], [43, 37], [-112, 47], [-98, -14], [-54, 32], [-4, 30], [88, 66], [132, 14], [26, 28], [118, 28], [68, -9], [126, 44], [259, 76], [29, -85], [62, -25], [105, 44], [45, 3], [79, -63], [47, 35], [21, 92], [-8, 54], [-110, 26], [-30, 29], [35, 115], [50, 94], [35, 168], [46, 76], [72, 227], [63, 119], [12, 143], [60, 11], [162, -82], [166, -54], [71, -5], [40, 17], [219, -6], [29, -21], [3, -75], [78, 20], [125, 104], [128, 10], [24, 72], [45, 16], [5, 101], [-40, 79], [8, 57], [-35, 167], [31, 126], [58, 124], [27, 28], [97, 12], [81, -7], [61, 41], [101, 21], [78, 100], [20, 77], [-32, 41], [4, 45], [70, 66], [99, 13], [124, -34], [54, 28], [126, 26], [3, -77], [22, -35], [-50, -27], [21, -68], [99, -40], [13, -27], [-53, -47], [98, -60], [29, 0], [80, -57], [64, -28], [7, -75], [98, -60], [55, -5], [48, -59], [87, -7], [40, 30], [89, -1], [92, -118], [60, 0], [67, 32], [16, -71], [107, -74], [49, -74], [12, -104], [106, -168], [51, -17], [44, -62], [32, -76], [-4, -82], [22, -17], [-65, -150], [43, -106], [3, -53], [-126, -152], [-27, -147], [38, -29], [25, -85], [48, -60], [30, 16], [70, -10], [49, -38], [90, 4], [29, -34], [61, -10], [151, 9], [80, -34], [122, -11], [121, 24], [84, -18], [95, 1], [157, -32], [81, -44], [70, -91], [44, -31], [96, -3], [62, -81], [108, -43], [48, -52], [147, -50], [149, 17], [-25, -77], [2, -81], [80, -15], [34, -63], [45, -135], [61, -126], [9, -70], [24, -40], [168, -160], [31, -119]], [[16114, 1112], [11, -45], [34, -37], [64, -8], [27, -156], [-14, -48], [-39, -55], [-24, 8], [-27, -69], [-26, -9], [-48, -136], [-11, -68], [-43, -98], [2, -60], [-18, -55], [-87, -21], [-54, -59], [-22, -85], [-60, 16], [-34, -20], [-39, 8], [-29, -85], [-48, -30], [-56, 54], [-97, 9], [-23, -10], [-32, 46], [-39, -1], [-89, 69], [-44, 10], [-1, 122], [-28, 65], [11, 48], [-16, 104], [20, 30], [-13, 74], [27, 53], [33, 8], [74, 68], [33, 50], [61, 26], [5, 89], [59, 66], [70, -49], [26, 15], [0, 51], [31, 24], [53, 9], [38, -33], [34, 19], [56, -16], [34, 12], [28, 51], [46, -20], [53, 27], [85, -57], [-4, 57], [50, 42]], [[14798, 11147], [-22, -49], [-42, -1], [-32, -57], [-51, -42], [2, -35], [-37, -129], [38, -78], [-17, -54]], [[13365, 10890], [29, 55], [42, 19], [54, -13], [129, 22], [61, 65], [117, 52], [79, -16], [53, 9], [57, 36], [16, 114], [-30, 75], [32, 51], [5, 195], [24, 93], [71, 138], [23, 81], [74, 19], [-3, 65], [96, 9], [51, 41], [76, 2], [8, -92], [46, -74], [28, -19], [-20, -64], [-99, -161], [-25, -101], [-76, -95], [155, -80], [101, -21], [45, 14], [70, -41], [35, -82], [109, -39]], [[16298, 12015], [50, -42], [-46, -138], [-54, -35], [-6, -107], [-47, -110], [12, -43], [-17, -44], [-37, -24], [-45, -62], [-40, -17], [-29, -49], [1, -140], [32, -19], [28, -61], [44, -38], [15, -105], [-19, -46], [-49, -47], [14, -64], [-89, -132], [-30, -17], [3, -136], [38, -107], [5, -61], [-17, -77], [-5, -118], [29, -144], [21, -21], [26, -143], [-85, -171], [-39, -56], [-33, -143], [-9, -82], [6, -77], [36, -28]], [[15962, 9311], [-4, -77], [67, -87], [-20, -37], [74, -51], [-5, -60], [22, -48], [-22, -57], [68, -43], [19, -56], [76, -60], [18, -56], [3, -72], [-18, -61]], [[16240, 8546], [-66, -38], [-49, -51], [-46, 25], [-25, 53], [-50, -41], [-85, -11], [-61, 33], [-190, 29], [-62, -15], [-11, -53], [116, -36], [-8, -85], [69, -10], [86, -40], [26, -64], [5, -61], [-41, -3], [-31, -37], [-53, 22], [-107, 0], [-23, -28], [-8, -77], [-33, -39], [27, -58], [31, -26], [20, -83], [-24, -65], [-7, -107]], [[15173, 7940], [-17, 30], [-106, 7], [-81, -20], [-39, -39], [-51, 40], [-80, 91], [-87, 11], [-4, 64], [-35, -1], [-36, -37], [-36, -1], [-58, 39], [11, 83], [-13, 23], [-154, 16], [-46, -35], [-104, -21], [-59, -26], [-76, 69], [12, 91], [-90, -16], [-27, -47], [-79, -31], [-27, 12], [-31, 100]], [[14798, 11147], [99, -30], [46, -43], [21, -52], [127, 0], [72, 21], [117, 7], [11, 147], [31, 27], [40, -36], [32, 105], [-33, 50], [14, 77], [77, 118], [66, 44], [48, 85], [49, 9], [43, 68], [16, 60], [65, 24], [62, 72], [92, 59], [-29, 81], [51, -2], [48, -60], [68, 31], [47, -60], [36, -2], [70, 114], [72, 57], [54, 0], [-41, -81], [29, -22]], [[17434, 10298], [-22, -76], [6, -94], [-25, -58], [-19, -151], [11, -23], [-52, -68], [-70, -36], [-87, -63], [-48, -13], [-50, -50], [-42, 1], [-54, 39], [-68, -51], [-204, 7], [2, -71], [-100, -15], [-91, -77], [-27, -47], [-72, -23], [-27, 10], [-133, -53], [-61, -46], [-154, -46], [-85, 17]], [[16298, 12015], [102, 34], [34, 44], [2, 58], [28, 16], [80, -9], [38, -22], [69, 52], [78, 179], [28, 31], [50, 96], [52, 34], [85, -29], [60, -37], [48, 30], [51, 74], [55, 27], [62, 7], [45, -55], [80, 15], [69, 48], [57, 3], [102, 30], [14, 35], [-21, 48], [55, 62]], [[17621, 12786], [-1, -40], [47, -70], [12, -97], [64, -10], [7, -38], [-159, -61], [-38, -31], [-27, -66], [65, -56], [130, -32], [8, -114], [61, -73], [-53, -131], [-11, -73], [-78, -58], [-55, 3], [-33, 26], [-69, -27], [-41, -93], [29, -55], [-69, -86], [-25, -8], [-33, -61], [-7, -90], [18, -77], [100, -51], [44, -116], [59, -112], [33, -30], [-22, -111], [-72, -117], [-12, -49], [-45, -53], [9, -128], [-42, -10], [-31, -59], [-67, -18], [3, -57], [37, -50], [-5, -36], [82, -73]], [[18488, 6575], [-65, -26], [-62, -3], [-65, 70], [-96, 2], [-30, -87], [-27, -37], [-93, -34], [-73, -15], [-34, -23], [-33, -71], [-48, -3], [-104, -40], [-70, 20], [-26, -60], [-80, -26], [-70, -77]], [[17512, 6165], [-56, 33], [-52, -1], [-6, 56], [-22, 41], [61, 81], [-41, 141], [-43, 1], [-8, 88], [-45, -44], [-99, -132], [-18, -34], [-60, 26], [-34, -13], [-8, 60], [15, 62], [-26, 46], [-103, -93], [-89, 8], [-34, -39], [-85, 43], [-19, 33], [-88, 85], [-67, 36], [-80, -9], [-73, 13], [-74, 59], [-220, 37], [-11, -38], [-75, 14], [-35, -22], [17, -86], [39, -52], [-63, -26], [-36, -43], [-112, 77], [-140, -29], [-36, -45], [-99, -50], [-17, -19], [-41, -138], [-31, -27], [-26, -79]], [[16240, 8546], [12, -33], [46, -34], [47, -97], [-3, -26], [67, -60], [62, -80], [10, -41], [79, -7], [29, -45], [119, -36], [40, -35], [62, -25], [108, 15], [93, -16], [138, 39], [51, -2], [110, -70], [39, 4], [39, 50], [49, 19], [23, -34], [-1, -94], [-12, -31], [18, -94], [30, -63], [42, -17], [6, -48], [48, 21], [26, 36], [54, -23], [41, -42], [84, 3], [-3, -94], [39, -3], [56, -44], [100, -7], [57, 67], [37, -28], [2, -56], [29, -30], [38, 13]], [[17512, 6165], [1, -63], [47, -24], [57, -71], [-13, -98], [-25, -29], [52, -32], [23, -56], [-20, -65], [-40, -21], [-33, -85], [-43, -49], [-78, -22], [-15, -92], [-60, -130], [16, -126], [29, -21], [50, 22], [30, -13], [-13, -67], [-29, -50], [60, -110], [-26, -68], [14, -97], [95, -44], [-7, -70], [-23, -39], [-7, -59], [-26, -40], [53, -19], [44, 25], [22, -29], [-75, -57], [-10, -77], [-48, -104], [5, -51], [28, -44], [-24, -76]], [[13773, 5403], [-31, 75], [25, 83], [-49, 34], [-50, 15], [-39, -22], [-4, -52], [-26, -32], [-85, -10], [-42, -21], [-100, 66], [-17, 48], [47, 30], [-29, 104], [-47, 28], [-8, 112], [82, 43], [-11, 19], [-155, -5], [-96, -15], [-17, -41], [36, -117], [-74, -62], [-19, 27], [-41, -12], [-49, -61], [-10, -38], [34, -55], [-3, -61], [-40, -68], [-86, -100], [-42, -88], [-63, -14], [-37, -55], [10, -101], [-15, -45], [10, -61], [31, -54], [37, -155], [-27, -40], [3, -49], [-54, -20], [-69, -72], [-48, 13], [7, 48], [-26, 19], [-137, -85], [-60, -82], [-83, 8], [-31, -36], [-33, 41], [-5, 39], [-37, 30], [-36, -11], [-7, 56], [29, 23], [-44, 83], [-57, 42], [39, 112], [-58, 5], [-12, 54], [-87, 99], [14, 83], [-74, 3], [-5, 76], [-23, 59], [-31, 34], [2, 38], [-92, 178], [-56, -34], [-103, 20], [-40, 27], [-62, 95], [-5, 69], [49, 27], [-8, 61], [-62, 64], [-29, 64], [-38, 20], [-8, 39], [-42, 41], [-11, 56], [-34, -16], [-40, -76], [-15, -58], [-46, -21], [-2, -119], [11, -45], [-14, -39], [-47, 53], [-49, 91], [3, 93], [-22, 60], [-9, 84], [10, 61], [-5, 148]], [[11084, 6255], [-21, 47], [-8, 144], [-24, 50], [8, 96], [22, 54], [-10, 79], [-20, 51], [-12, 179], [-24, 116], [21, 47], [-77, 88], [11, 50], [-79, 112], [-3, 38], [30, 34], [37, -48], [51, 70], [-127, 139], [-15, 44], [-48, 72], [-12, 42], [15, 61], [-62, 81], [-39, 70], [-7, 59], [-83, 62], [-130, 45]], [[13870, 2818], [-39, 59], [-43, 29], [-50, -30], [-68, -19], [-50, -41], [-32, 2], [-32, -44], [12, -75], [-42, -65], [-29, -16], [-48, 15], [-92, -85], [-39, 23], [-16, 56], [-61, -25], [-22, -36], [-16, -109], [-52, 27], [-96, 127], [-24, -17], [-17, -73], [-34, -27], [-31, 83], [-40, 28], [-24, -62], [-49, -40], [1, -40], [-67, -56], [-42, 27], [-21, 57], [-66, 48], [-68, 14], [-41, 39], [-28, -85], [-54, -68], [-15, -48], [-89, 20], [-85, -23], [-46, 49], [-25, -25], [-21, -92], [-38, -32], [38, -148], [29, -21], [34, -70], [-10, -50], [29, -74], [-26, -19], [-6, -142], [45, -39], [-30, -53], [-29, 2], [-46, 44], [-39, 11], [-95, -39], [-29, 41], [11, 66], [-27, 23], [1, 118], [-46, 85], [-118, -77], [-41, -62], [-35, -27], [-71, -8], [-57, 40], [-60, -55], [-43, 57], [11, 58], [-14, 30], [-58, 22], [-12, 73], [19, 64], [-10, 46], [-53, -16], [-36, 28], [-130, 33], [-33, -15], [-85, 19], [0, 42], [50, 117], [32, 47], [-1, 65], [-20, 62], [47, 78], [6, 45], [46, 7], [-25, 67], [-56, 30], [-58, -35], [-61, 25], [-21, 33], [-73, 11], [22, 68], [-15, 84], [-41, 49], [34, 41], [-35, 93], [-50, 15], [0, 98], [84, 65], [5, 33], [-66, -12], [-57, -25], [-36, 25], [-74, -12], [-65, 11], [-127, -55], [-109, -90], [-53, 45], [83, 103], [4, 87], [-33, 91], [-53, -4], [7, 178], [56, 48], [36, -2], [-19, 76], [14, 73], [38, 98], [28, -32], [69, 60], [27, 44], [-4, 59], [22, 68], [55, -35], [25, 8], [56, 130], [20, 25], [44, -22], [35, 44], [-47, 60], [-16, 69], [70, 39], [-22, 78], [27, 55], [2, 74], [14, 49], [-2, 83], [-15, 51], [-19, 178], [14, 72], [-13, 47], [2, 109], [-122, 55], [-7, -67], [-39, -12], [-38, 109], [-12, 97], [-31, 42], [4, 97], [50, 34], [31, 86], [47, -60], [35, -1], [37, 39], [28, 61], [-22, 140], [38, 49], [-17, 68], [11, 55], [53, 15], [39, -108], [57, 149], [-7, 72], [52, 14]], [[19575, 11822], [-58, -71], [-20, 29], [78, 42]], [[18962, 12416], [-40, -81], [25, -106], [-23, -81], [-120, 38]], [[19828, 13086], [-30, -55], [-86, -57], [-48, -144], [93, -97], [53, -17], [44, -51], [118, 5], [8, -94], [23, -93], [47, -2], [-2, -61], [49, -64]], [[20097, 12356], [-137, -53], [-85, -100], [-34, -103], [18, -57], [-51, -45], [-74, -93], [-32, -17], [-36, 27], [-27, -32], [-78, 6], [-119, -74], [-48, 12], [-40, 69], [-57, 25]], [[19297, 11921], [-1, 71], [-48, 32], [-2, 94], [-114, 6], [-3, 51], [-39, 48], [-14, 135], [115, -14], [-7, 56], [-130, 93], [-41, 17]], [[18769, 12139], [-1, -62], [26, -86], [-11, -71], [28, -64], [-78, -52], [-14, -55], [13, -54], [74, -68], [61, 5], [32, -54], [77, -31], [118, 36]], [[19094, 11583], [76, -138], [51, -61]], [[19221, 11384], [-24, -60], [-41, -52], [-51, -14], [-65, -115], [-252, -7], [-41, -20], [-59, -90], [-86, -88], [-27, 77], [-56, -90], [16, -42], [-122, -38], [-53, -149], [-99, -149], [-64, -24], [-59, -87], [-30, -70], [30, -70], [46, -58], [5, -52]], [[18189, 10186], [-48, -43], [-46, 45], [-60, 13], [-31, -47], [-45, -26], [-17, 36], [-80, 18], [-69, -7], [-90, 60], [-81, 1], [-40, 17], [-32, 47], [-79, -11], [-37, 9]], [[17621, 12786], [-41, 39], [-17, 63], [-49, 59], [-11, 40], [36, 31], [9, 50], [-33, 30], [8, 70], [43, 65], [74, 34], [7, 99], [19, 53], [71, 54], [26, 67], [50, 38], [68, -1], [23, 23], [51, -137], [-20, -72], [5, -113], [91, 3], [56, -18], [53, 22], [-3, 55], [81, 29], [144, 88], [34, 0], [44, -72], [73, 47], [75, 72], [38, -35], [58, -14], [112, 55], [9, 113], [-22, 50], [26, 50], [74, 37], [81, 12], [73, -14], [17, 51], [62, 1], [65, 23], [25, -12], [97, -106], [7, -53], [-22, -38], [42, -23], [21, -90], [58, 29], [24, -119], [-19, -63], [-52, -19], [37, -72], [30, -13], [24, -145], [47, 23], [293, -24], [35, -22]], [[18189, 10186], [-16, -93], [-39, -35], [-7, -80], [43, 18], [23, 50], [117, 32], [39, 36], [43, -5], [65, 46], [-19, -78], [-30, 11], [-88, -77], [-47, -59], [-65, -13], [-48, -60], [-21, -68], [-52, -46], [-68, -20], [-55, -91], [-34, -18], [-13, -98], [71, -4], [77, -37], [18, -61], [44, 4], [48, -36], [4, -58], [45, -57], [73, 14], [46, -17], [99, 17], [47, -20]], [[20750, 12071], [38, -12], [-17, -52], [-46, 12], [25, 52]], [[22640, 12851], [-62, -46], [-12, -32], [-73, -42], [-148, -64], [1, -43], [-57, 14], [-79, -98], [-31, -9], [-124, -117], [-2, -51], [-65, -67], [-95, -39], [-157, 35], [-14, -51], [-78, 8], [-64, -17], [1, -38], [-40, -19], [-72, 8], [-162, -97], [-21, -24], [-83, -31], [-123, -120], [7, -34], [-138, -95], [-17, 28], [-54, -16], [3, -77], [-74, -28], [-61, 3], [-83, -49], [-27, 97], [253, 108], [-31, 74], [59, 52], [-9, 34], [-73, -6], [-25, 15], [-25, 69], [-56, -4], [0, 54], [62, 10], [26, 45], [-22, 31], [34, 63], [74, 38], [30, -4], [96, 116], [-1, 36], [42, 22], [7, 47], [45, 40], [40, 70], [-71, 68], [-39, 70], [-86, 48], [-120, 6], [-93, 55], [-27, -22], [-54, 12], [-80, -51], [-21, -80], [-50, -9], [-12, -43], [-78, -71], [-52, -84], [2, -44], [-30, -30], [-211, -79], [-43, -40]], [[19828, 13086], [56, 58], [12, 55], [-31, 105], [-8, 68], [10, 107], [19, 76], [-52, 52], [39, 55], [98, 47], [30, -68], [83, -25], [-6, -50], [37, -69], [29, -15], [31, -63], [-2, -72], [50, 32], [86, 100], [56, 41], [18, 62], [51, 8], [124, 76], [53, -20], [146, 136], [76, 19], [44, -46], [34, 2], [67, 47], [17, 61], [55, 42], [111, -47], [55, 48], [-26, 44], [48, 28], [109, -62], [71, 3], [104, 46], [22, 66], [76, 39], [112, 39], [36, 155]], [[21768, 14266], [48, 57], [87, -82], [120, -48], [6, -39], [53, -43], [-19, -54], [62, -64], [107, 133], [54, -12], [-12, -112], [36, -48], [33, -89], [33, -40], [3, -47], [47, -49], [32, -98], [86, -47], [-81, -110], [4, -157], [62, -1], [9, -59], [79, -164], [37, -23], [14, -59], [-33, -77], [-50, -52], [55, -31]], [[19849, 9573], [-46, 16], [-42, -32], [-92, -7], [-47, -163], [-33, -37], [-89, 3], [-37, -139], [-98, -26], [-51, 143], [-58, 15], [-55, -15], [-7, -70], [-52, 0], [-27, -31], [-53, 2], [-57, 64], [-28, -14], [-39, -64], [-53, 146], [-52, 97], [-50, 44], [-85, -11], [-70, -22], [-22, -36], [-18, -111]], [[19221, 11384], [46, -21], [62, -54], [57, 4], [102, -22], [41, -22], [49, 44], [80, 4], [68, -108], [107, -109], [-90, -28], [-26, -62], [-14, -132], [12, -55], [75, -59], [128, -32], [144, 17], [55, 55], [-16, 64], [87, 45], [103, 89], [6, 50], [185, 84], [82, -8], [46, -62], [46, -5], [24, -72], [82, -5], [42, -56], [50, -24], [29, 27], [205, 6], [72, -37], [69, 0], [86, -19], [-45, -42], [23, -57], [-68, -35], [6, -66], [29, -50], [-90, -68], [-52, 11], [4, 43], [-74, 38], [-110, -53], [-21, -30], [-78, -41], [-174, -56], [-59, -48], [-44, -17], [13, -54], [-87, 0], [-45, -72], [22, -106], [-63, -17], [-87, -42], [-26, 15], [29, 71], [-16, 19], [-95, -14], [-2, -59], [62, -70], [-88, -73], [-16, -66], [-45, 5], [2, -63], [-40, 13], [-71, -37], [-42, -115], [-56, -38], [-28, -123], [-34, -11]], [[19297, 11921], [-54, -10], [-72, -61], [-4, -64], [-55, -96], [-18, -107]], [[19369, 6359], [-19, -62], [-35, -14], [-22, -74], [27, -52], [72, -56], [32, -67], [30, -21], [24, -58], [-4, -52], [17, -49], [8, -106], [-12, -24]], [[20745, 7539], [-67, -18], [-27, -102], [-29, -17], [-11, -59], [-66, -2], [11, -73]], [[20556, 7268], [-93, -20], [7, -48], [-57, -26], [-38, -47], [-19, 72], [-37, 27], [-65, -10], [-36, 13], [-81, 81], [-5, 36], [-83, 15], [-48, -24]], [[19849, 9573], [-38, -43], [4, -123], [63, -2], [128, -131], [80, -45], [49, -7], [79, -56], [60, -27], [49, -156], [16, -101], [39, -60], [6, -54], [50, -101], [16, -80], [32, -33], [-3, -43], [34, -38], [11, -63], [27, -7], [5, -96], [-20, -71], [21, -50], [91, -70], [102, -53], [28, -93], [10, -77], [93, -29], [72, -63], [44, -112], [-46, -36], [-61, 18], [-170, 95], [-74, -53], [-59, 11], [-14, 38], [-59, 79], [-77, 31], [-62, 3], [-57, -32], [-40, -44], [-59, -3], [-60, 41], [-37, 70], [5, 32], [-44, 67], [-55, 4], [-1, -48], [43, -21], [-2, -48], [31, -15], [66, -78], [96, -23], [125, 62], [102, -23], [32, -85], [30, -37], [109, -44], [86, -111]], [[20745, 7539], [88, -70], [79, -48], [66, -104], [43, -104], [-30, -33], [-120, -11], [-96, -40], [-59, -43]], [[20716, 7086], [-8, 30], [-66, 51], [-54, -1], [-5, 100], [-27, 2]], [[20993, 7520], [-27, -9], [-234, 123], [-60, 72], [39, 32], [92, -40], [81, -67], [79, -25], [59, -38], [-29, -48]], [[20720, 5618], [-42, -33], [-25, 35], [44, 65], [23, -67]], [[21106, 6752], [51, -20], [2, -75], [-48, 34], [-75, 22], [-17, 65], [50, 0], [37, -26]], [[20716, 7086], [-63, -52], [-66, -32], [-17, -68], [-66, -73], [-44, -13], [89, -57], [45, 23], [67, 65], [46, 13], [53, -13], [57, -60], [74, -124], [103, -46], [29, -55], [-121, -142], [-106, -26], [3, -60], [195, 125], [22, -23], [-26, -180], [14, -41], [-43, -28], [-40, 22], [-57, 3], [-11, 49], [-52, -57], [11, -50], [55, 5], [25, -49], [-5, -113], [-27, -58], [-42, -38], [43, -102], [20, -79], [-107, -19], [-53, -71], [-22, 72], [-40, 22], [-11, -71], [-48, -67], [-19, -65], [-56, -17], [-12, -69], [-61, -120], [-31, -30], [35, -43], [-24, -65], [-33, 13], [-13, -114], [-42, 2]], [[25052, 14331], [12, -50], [-50, -113], [10, -24], [-46, -55], [9, -75], [-48, -35], [-98, 10], [-79, -27], [-78, -47], [72, -65], [-61, -33], [-82, 103], [-2, 90], [-43, 3], [-8, 42], [-61, -8], [-45, 16], [-50, -142], [-26, -172], [-80, -30], [-67, 31], [-52, -37], [-15, -90], [-39, -41], [-68, -33], [-11, -31], [-94, 7], [-96, -29], [-104, 21], [-99, -23], [3, -64], [52, -95], [62, -73], [-51, -101], [-53, -9], [-52, 46], [-42, -23], [-91, 0], [-72, 39], [-89, 3], [-97, 93], [-5, 36], [-64, 30], [-43, -50], [-74, -17], [-50, -132], [-105, -164], [-49, -29], [-60, -109], [-133, -24]], [[21768, 14266], [-39, 6], [-99, 50], [-7, 49], [56, 30], [13, 59], [-87, 196], [17, 62], [-45, 29], [-58, 165], [-35, 11], [-38, -38], [-104, -63], [-148, -65], [-53, 15], [13, 103], [-85, 109], [-18, 43], [-4, 135], [-11, 104], [78, 47], [18, 49], [-42, 92], [-47, 20], [-28, 47], [-13, 69], [-65, -8], [-51, 23], [64, 112], [-17, 55], [97, -17], [105, -94], [69, 77], [43, -71], [65, -56], [55, 35], [11, 171], [118, 11], [39, 63]], [[21535, 15891], [43, 26], [42, -18], [245, 22], [50, -145], [-24, -22], [36, -60], [-19, -52], [54, -85], [35, 3], [74, -105], [74, -20], [21, 18], [113, 0], [67, 29], [34, -58], [88, 12], [54, 34], [116, 19], [2, -86], [59, -71], [136, -53], [117, 49], [60, 11], [99, -56], [72, -8], [53, -113], [-42, -62], [23, -54], [6, -75], [44, 32], [38, -21], [62, 18], [79, -43], [-28, -92], [96, -141], [12, -43], [46, -17], [89, 39], [-4, 122], [49, 7], [22, 50], [66, 34], [26, -28], [14, -77], [-2, -91], [35, -42], [45, -95], [51, -63], [7, -53], [52, -87], [57, -9], [93, 39], [9, 101], [124, 47], [94, 3], [22, 51], [49, 32], [37, -21], [15, -73], [30, -5], [76, 67], [24, -21], [35, -146], [76, -24], [142, -100], [35, 32], [67, -40], [45, 18]], [[20776, 19915], [84, -43], [84, -124], [-48, -65], [-81, -76], [-144, -101], [139, -86], [87, -32], [60, -67], [82, 8], [67, 111], [72, -15], [66, -90], [104, -26], [19, -27], [-59, -60], [50, -141], [-17, -82], [53, -76], [2, -32], [51, -71], [57, -42], [79, -26], [72, 4], [58, -39], [95, 16], [34, 45], [129, 10], [81, -29], [66, 46], [71, -14], [68, 17], [103, 96], [30, 46], [89, -18], [67, -43], [101, -144], [54, -27], [61, -58], [32, -70], [-25, -45], [-77, -76], [-6, -116], [-121, -96], [-49, -113], [-67, -50], [14, -52], [-30, -54], [4, -207], [14, -65], [-19, -124], [-29, -39], [-128, 13], [-18, -64], [-87, -181], [-36, -96], [-8, -116], [18, -75], [-59, -35], [-43, 90], [-11, 90], [-30, 22], [-67, -47], [-153, -145], [-65, -78], [-123, -61], [-37, -76], [-178, -96], [-101, -63], [-70, -114], [73, -114], [98, -55], [86, -109], [82, -45], [63, 41], [22, 37], [99, -71], [-92, -100], [-46, -6], [-118, 14], [-13, -117], [74, -105]], [[10250, 13944], [286, -36], [353, -45], [353, -45], [236, 64], [522, -84], [84, 9], [98, -17], [43, -31], [65, -103], [64, -53], [452, -102], [269, -140], [13, -2], [339, 66], [-1, -117], [45, -11], [126, 0], [53, -28], [81, 83], [32, 7], [258, 135], [391, 167], [612, 95], [62, -13], [94, 2], [114, -20], [85, 28], [113, -13], [100, 13], [79, 56], [107, 45], [70, 9], [145, 71], [18, 39], [69, 60], [32, 65], [110, 131], [70, 44], [219, 89], [67, 80], [78, 18], [5, 51], [-45, 86], [-90, 73], [-50, 69], [-49, 129], [60, 105], [4, 53], [81, 154], [46, 42], [72, 27], [42, -12], [145, -8], [75, -82], [58, -24], [155, -40], [161, -13], [70, -15], [110, 92], [72, 19], [48, 56], [111, 91], [50, 104], [88, 23], [88, -29], [173, 11], [130, 29], [163, 133], [68, 17], [36, 54], [-17, 59], [62, 113], [108, 114], [87, 42], [216, -18], [23, 16], [17, 109], [82, 12], [49, -45], [44, 4], [36, 44], [156, 67], [86, -14], [75, 8], [48, -16], [43, 46], [65, -10], [42, -45], [110, -28], [59, 9], [98, -20], [98, 66], [0, 76], [-58, 85], [-31, 95], [-164, 140], [-6, 39], [-66, 23], [-31, 82], [-138, 53], [-97, 120], [-157, 35], [-92, -18], [-68, 5], [-57, -28], [-140, -173], [-19, 2], [-107, 88], [-95, 36], [-154, -21], [-110, 16], [-70, -32], [-48, -56], [-52, -2], [-127, 130], [-21, 106], [112, 58], [16, 28], [-3, 146], [99, 134], [4, 59], [281, 540], [162, -85], [85, -29], [96, -5], [124, -57], [59, 19], [119, 71], [154, 126], [53, 34], [194, 19], [51, 16], [47, 44], [16, 143], [-93, 18], [45, 38], [21, 87], [89, 79], [9, 95], [101, 102], [4, 65], [68, 75], [83, 189], [21, 0], [147, 120], [67, 20], [25, 85], [32, 32], [-9, 68], [-58, 62], [43, 110], [-127, 54], [-111, -29], [-52, 9], [-10, 91], [108, 59], [73, 81], [89, 64], [95, 90], [144, -2], [91, 23]], [[20776, 19915], [99, 39], [108, 25], [83, -1], [111, 36], [58, -15], [169, 5], [128, 22], [43, 31], [102, -24], [66, 15], [41, -27], [103, -27], [65, -59], [49, 17], [77, -83], [117, -21], [18, -28], [130, 34], [47, -2], [171, -93], [44, 12], [93, -88], [86, -103], [-27, -54], [104, -56], [57, -80], [-12, -43], [46, -16], [-45, -49], [92, -27], [-7, -62], [-29, -35], [156, -254], [-3, -65], [68, -51], [-22, -93], [11, -58], [93, -91], [62, -86], [34, -111], [-33, -50], [31, -30], [-13, -49], [108, -60], [-4, -53], [-36, -59], [21, -58], [-11, -56], [59, -32], [33, -68], [47, -30], [70, 3], [44, -33], [110, 4], [16, 17], [123, 18], [57, -22], [36, -52], [74, -58], [63, 7], [73, -22], [61, 34], [30, -74], [60, -3], [20, -52], [69, -81], [145, -100], [60, 18], [78, -23], [47, 9], [-61, -133], [80, -78], [44, -123], [-68, -94], [8, -37], [98, -95], [15, -94], [36, -20], [172, 34], [68, -42], [55, 16], [84, -15], [115, 12], [75, 26], [83, -16], [58, 91], [7, 46], [63, -4], [118, 93], [41, 10], [121, -20], [42, 13], [70, 75], [114, 23], [32, 24], [143, 30], [68, -13], [68, -54], [-7, -72], [-51, -54], [90, -187], [-39, -70], [-86, -89], [-84, -12], [-60, -86], [32, -79], [-45, -27], [-45, -134], [11, -35], [-25, -83], [-60, -83], [32, -40], [-26, -40], [16, -45], [-92, -66], [13, -36], [-26, -82], [-62, -31], [-13, -110], [-31, -64], [-93, -35], [-26, -43], [-19, -108], [8, -69], [-71, -56], [-48, 19], [-19, 75], [-33, 34], [-132, 50], [-96, -2], [-90, -60], [-75, 59], [-41, -67], [-41, -9], [-9, -62], [-82, -74], [-39, 5], [-75, -38], [-46, 4], [-52, -38], [58, -91], [87, -355], [-41, -155], [9, -109], [-11, -38], [38, -30]], [[17577, 2344], [-54, -66], [-15, 46], [69, 20]], [[17655, 2473], [46, -61], [-26, -32], [1, -68], [-43, 13], [-30, 41], [-86, 6], [37, 63], [36, 24]], [[17363, 2251], [5, -2], [2, 0], [0, -2], [-1, -2], [-2, -1], [-2, 0], [-1, -5], [3, -2], [2, -1], [2, 2], [0, -1], [-1, -2], [2, -2], [3, 0], [0, -1], [0, -1], [0, -2], [-1, 0], [-3, 1], [0, -3], [-1, -1], [-2, 1], [-1, 2], [0, -4], [-1, -2], [0, -3], [-1, -1], [-2, 3], [-4, -2], [0, -1], [-1, 0], [-1, 0], [-1, 1], [-1, 3], [1, 1], [-1, 14], [-3, 5], [-1, 2], [1, 5], [3, 0], [0, 1], [5, 1], [3, -1]], [[17359, 2271], [4, 2], [-3, -3], [-1, 1]], [[17359, 2271], [0, -1], [0, -1], [0, -1], [0, -1], [0, -1], [-1, -1], [-3, -2], [-1, -1], [-2, 0], [-1, -3], [-2, 0], [-1, -2], [-2, 6], [0, 1], [1, 3], [1, 1], [0, 1], [2, 4], [2, 3], [0, 2], [-1, 0], [0, -1], [-2, -1], [-1, 3], [1, 1], [3, 0], [1, 2], [1, 2], [2, -2], [0, -1], [3, -1], [1, -2], [1, -2], [0, -2], [-2, 1], [-1, -2], [0, -1], [1, -1]], [[20873, 3986], [35, -41], [76, -21], [6, -92], [-36, -55], [-4, -119], [19, -53], [-34, -67], [-3, -71], [-48, -65], [-23, -66], [9, -29], [-47, -208], [-25, -179], [-24, -50], [-4, -62], [-44, -106], [-47, -65], [-21, -58], [-53, -54], [-31, -68], [-32, -134], [2, -133], [-17, -74], [-59, 32], [-3, 75], [-34, 103], [-47, 74], [-78, 53], [-68, 206], [-2, 74], [-49, 78], [32, 119], [-3, 90], [28, 145], [45, 77], [20, 63], [67, 118], [34, 99], [33, 45], [24, 69], [63, 60], [23, 71], [69, 121], [139, 61], [24, 47], [46, 27], [42, -37]]], "transform": {"scale": [0.002301279977427492, 0.0017637038514733002], "translate": [73.60225630700012, 18.19318268400005]}, "objects": {"China": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4, 5, 6]], "type": "Polygon"}, {"arcs": [[7, 8, 9, -4]], "type": "Polygon"}, {"arcs": [[10, 11, 12, 13, 14]], "type": "Polygon"}, {"arcs": [[15, -14, 16, 17, 18]], "type": "Polygon"}, {"arcs": [[19, 20, -19, 21, 22]], "type": "Polygon"}, {"arcs": [[23, 24, 25, 26]], "type": "Polygon"}, {"arcs": [[[27]], [[28, 29, 30, 31]]], "type": "MultiPolygon"}, {"arcs": [[32, 33, 34, 35, 36, 37]], "type": "Polygon"}, {"arcs": [[[38]], [[39]], [[40]], [[41, -30, 42, 43, 44, -11, 45]]], "type": "MultiPolygon"}, {"arcs": [[46]], "type": "Polygon"}, {"arcs": [[-5, -10, 47]], "type": "Polygon"}, {"arcs": [[48]], "type": "Polygon"}, {"arcs": [[49, -1, 50]], "type": "Polygon"}, {"arcs": [[51, 52, 53, -23, 54, -2, -50, 55]], "type": "Polygon"}, {"arcs": [[56, -52, 57, 58]], "type": "Polygon"}, {"arcs": [[-35, 59, 60, -20, -54, 61]], "type": "Polygon"}, {"arcs": [[62, -46, -15, -16, -21, -61]], "type": "Polygon"}, {"arcs": [[-55, -22, -18, 63, 64, -8, -3]], "type": "Polygon"}, {"arcs": [[-17, -13, 65, -64]], "type": "Polygon"}, {"arcs": [[[66]], [[-25, 67]], [[68, 69, 70, -27, 71, 72, 73, 74, -59, 75]]], "type": "MultiPolygon"}, {"arcs": [[76, -36, -62, -53, -57, -75]], "type": "Polygon"}, {"arcs": [[[77]], [[78, -69, 79, 80]]], "type": "MultiPolygon"}, {"arcs": [[81, -37, -77, -74, 82]], "type": "Polygon"}, {"arcs": [[83, -72, -26, -68, -24, -71]], "type": "Polygon"}, {"arcs": [[84, -31, -42, -63, -60, -34]], "type": "Polygon"}, {"arcs": [[85, 86, -38, -82, 87]], "type": "Polygon"}, {"arcs": [[[88, 89, -86]], [[90]]], "type": "MultiPolygon"}, {"arcs": [[[91]], [[92]], [[-90, 93, -32, -85, -33, -87]]], "type": "MultiPolygon"}, {"arcs": [[94, -81, 95, 96]], "type": "Polygon"}, {"arcs": [[97, -96, -80, -76, -58, -56, -51, -7, 98]], "type": "Polygon"}, {"arcs": [[-97, -98, 99]], "type": "Polygon"}, {"arcs": [[[100]], [[101, -44]]], "type": "MultiPolygon"}, {"arcs": [[[102]], [[103, 104]]], "type": "MultiPolygon"}, {"arcs": [[105]], "type": "Polygon"}, {"type": null}]}}}