<template>
  <div class="lujing" v-draggable>
    <div class="right" v-show="show">
      <div class="right_box1">
        <div class="box1_title">
          <img :src="titleImg" class="box_img" />
          <span>路径规划</span>
        </div>
        <div class="right_content">
          <div class="right_btn_box">
            <div class="right_content_btn BTN" @click="paintPoint(1)">
              选择起点
            </div>
            <el-input v-model="startPointS" disabled></el-input>
          </div>
          <div class="right_btn_box">
            <div class="right_content_btn BTN" @click="paintPoint(2)">
              选择终点
            </div>
            <el-input v-model="endPointS" disabled></el-input>
          </div>
          <ul class="right_content_ul">
            <li class="right_content_li BTN" @click="changeBtn(index)" v-for="(item, index) in btnList" :key="index">
              {{ item }}
            </li>
          </ul>
          <div class="right_bottom_btn_box">
            <div class="right_bottom_btn" @click="pathAnalysis()">
              逃生路线规划
            </div>
            <div class="right_bottom_btn" @click="clearAll()">
              清除所有
            </div>
          </div>
          <div class="right_res">{{ '逃生路线规划结果:\n'+resultDistance }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { computed, ref, toRef, toRaw } from "vue";
import titleImg from "@/assets/img/home/<USER>";
import * as echarts from "echarts";
import allLines from "@/utils/map/lujing/connectedGraph.json";
import {
  urgentManageSchedulingLine
} from "@/utils/map/lujing/urgentManageSchedulingLine.js";
import { useLoadStore } from "@/store/load";

const panelStore = usePanelStore();
const loadStore = useLoadStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
const { isLoadEnd } = storeToRefs(loadStore);

//生命周期
onMounted(() => {
  let toolBox = document.getElementsByClassName('home_legend')[0]
  toolBox.style.right = '320px'
  toolBox.style.left = ''
  if (isLoadEnd.value == true) {
    init();
  }
  // addPolyline()
});
watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);

onUnmounted(() => {
  clearAll()
})

var mm;
var umsl;
var startPointS = ref('未选择起点');
var endPointS = ref('未选择终点');
const init = () => {
  // viewer.showLogo = false;
  // viewer.scene.backgroundColor = new Cesium.Color(0.0, 0.0, 0.0, 0.0);
  // viewer.scene.backgroundColor = Cesium.Color.GRAY;

  mm = new Geowin3D.GwModel.GwModelManager(viewer);

  mm.on((eventType, eventArg) => {
    // console.log(eventType, eventArg)
    if (eventType == "LabelAdded") {
      let data = eventArg.getData();
      let text = data.option.text;
      let position = data.option.position;

      if (text == "起点") {
        startPoint = position;
        startPointS.value = position[0].toFixed(2) + "|" + position[1].toFixed(2) + "|" + position[2].toFixed(2);
        console.log("起点:", position);
      } else if (text == "终点") {
        endPoint = position;
        endPointS.value = position[0].toFixed(2) + "|" + position[1].toFixed(2) + "|" + position[2].toFixed(2);
        console.log("终点:", position);
      }
    }
  });
  umsl = new urgentManageSchedulingLine(viewer);

}

const addPolyline = () => {
  let path = [];
  let points = allLines.points;
  for (var i = 0; i < points.length; i++) {
    path.push(Cesium.Cartesian3.fromDegrees(points[i][0], points[i][1], points[i][2] + 1900));
  }

  setTimeout(() => {
    var model = window.viewer.entities.add({
      name: "巷道",
      polyline: {
        positions: path,
        width: 2,
        material: Cesium.Color.GREEN,
        depthFailMaterial: Cesium.Color.GREEN,
        clampToGround: false, //折线固定在地面
        arcType: Cesium.ArcType.GEODESIC, //定义连接点采用的路径
      },
    });
    
    viewer.zoomTo(model);
  }, 2000);
  

}

var startPoint;
var endPoint;

const paintPoint = (type) => {
  if (type == 1) {
    startPoint = mm.add('Label', {
      text: '起点',
      maxDistance: 1000,
      style: "5",
      offset: 0
    });
  } else if (type == 2) {
    endPoint = mm.add('Label', {
      text: '终点',
      maxDistance: 1000,
      style: "5",
      offset: 0
    });
  }
}

var resultDistance = ref('')
const pathAnalysis = () => {
  if (startPoint == null || endPoint == null) {
    return;
  }
  clearPathAnalysis();
  console.log("startPoint, endPoint",startPoint, endPoint)
  let result = umsl.drawLineC2(startPoint, endPoint, true);
  resultDistance.value = '距离：' + result.distance.toFixed(2) + '米'

}
const clearPathAnalysis = () => {
  umsl.clearUMSLC();
}
const clearAll = () => {
  startPointS.value= '未选择起点';
  endPointS.value= '未选择终点';
  mm.removeAll();
  umsl.clearUMSLC();
}
</script>
<style lang="less" scoped>
.lujing {
  width: 300px;
  top: 90px;
  right: 10px;
  display: flex;
  position: fixed;
  // background: rgba(11, 24, 36,.7);
  background-image: url("../../assets/img/home/<USER>");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 18px;
  .right {
    z-index: 1;
    width: 300px;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .right_box1 {
      width: 100%;
      height: 350px;
      box-sizing: border-box;
      padding: 0px 22px 0 0px;
      display: flex;
      flex-direction: column;
      .right_content {
        width: 100%;
        height: 100%;
        padding-top: 23px;
        padding-left: 20px;
        .right_btn_box {
          display: flex;
          width: 100%;
          align-items: center;
          margin-bottom: 10px;
          height: 40px;
          webkit-text-fill-color: red;
          /* 设置字体颜色 */
        }

        :deep(.el-input__wrapper) {
          input::input-placeholder {
            color: red;
          }
        }

        .right_content_btn {
          width: 100px;
          height: 36px;
          font-size: 14px;
          margin-right: 10px;
        }

        .right_content_ul {
          width: 100%;
          display: flex;
          margin-top: 20px;

          .right_content_li {
            width: 90px;
            height: 36px;
            margin-right: 10px;
          }
        }

        .right_bottom_btn_box {
          width: 100%;
          display: flex;
          margin-top: 30px;
          justify-content: space-around;

          .right_bottom_btn {
            display: flex;
            justify-content: center;
            align-items: center;
            background: #039ec4;
            color: #ffffff;
            width: 110px;
            height: 36px;
            border-radius: 6px;
            font-size: 15px;
            cursor: pointer;

            &:hover {
              background: #ffd200;
              color: #000;
            }
          }
        }

        .BTN {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #039ec4;
          border-radius: 6px;
          background-repeat: no-repeat;
          font-size: 14px;
          font-family: Source Han Sans SC;
          font-weight: 600;
          color: #ffffff;
          cursor: pointer;

          &:hover {
            transform: scale(1.03);
            border: 1px solid #ffd200;
          }
        }

        .right_res {
          margin-top: 20px;
          font-size: 15px;
          color: #ffd200;
        }
      }

      .left_box1_li_active {
        border: 1px solid #ffd200 !important;
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>
