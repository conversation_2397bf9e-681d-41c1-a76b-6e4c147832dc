var CGeowin = {};
function CCModuleScript($) {
    document.write("<script src='" + $ + "' charset='utf-8' type='module'> </script>")
}
function CCScript($) {
    document.write("<script src='" + $ + "' charset='utf-8' type='text/javascript'> </script>")
}
function CCCss(_, A) {
    if (document.getElementById(A))
        return;
    var $ = document.createElement("LINK");
    $.href = _;
    $.id = A;
    $.type = "text/css";
    $.rel = "Stylesheet";
    document.getElementsByTagName("HEAD")[0].appendChild($)
}
;(function(_) {
    var B = _.document
      , $ = {}
      , A = /((?:http|https|file):\/\/.*?\/[^:]+)(?::\d+)?:\d+/
      , C = ("" + B.querySelector).indexOf("[native code]") === -1;
    _.getCurrAbsPath = function() {
        if (B.currentScript)
            return B.currentScript.src;
        var _;
        try {
            $.b()
        } catch (D) {
            _ = D.stack || D.stacktrace || D.fileName || D.sourceURL
        }
        if (_) {
            var E = A.exec(_)[0];
            if (E)
                return E
        }
        for (var G = B.scripts, F = G.length - 1, H; H = G[F--]; )
            if (H.readyState === "interactive")
                return C ? H.getAttribute("src", 4) : H.src
    }
    ;
    _.CGetAbrPath = function() {
        var $ = getCurrAbsPath();
        return $.substr(0, $.indexOf("/api?"))
    }
    ;
    _.CGetApiHost = function() {
        var $ = CGetAbrPath();
        console.log('APIHost=' + $);
        if ($.substr($.length - 1, 1) == '/')
            $ = $.substr(0, $.length - 1);
        return $.substr(0, $.lastIndexOf("/"))
    }
}(window));
CGeowin.AbrPath = CGetAbrPath();
CGeowin.APIHost = CGetApiHost();
CGeowin.ServerList = [CGeowin.AbrPath];
CGeowin.BaseUrl = '';
if (CGeowin.AbrPath.slice(0, 4) === 'http')
    CGeowin.BaseUrl = CGeowin.AbrPath.substr(0, CGeowin.AbrPath.indexOf('/', 9) - 1);
CGeowin.BaseMapServer = [CGeowin.AbrPath];
CGeowin.ApiPath = CGeowin.AbrPath + '/geowin3d';

var CESIUM_BASE_URL = "/geowin3d/map";

CCCss("/geowin3d/map/Widgets/widgets.css", "widgets.css");

CCScript('/geowin3d/map/Geowin3D.js');
CCScript('/geowin3d/map/lodash.min.js');
CCScript('/geowin3d/map/Geowin3DX.min.js');
CCScript('/geowin3d/map/Geowin3DAPP.min.js');

function $C(element) { return document.getElementById(element); }
function $CC(id) { return document.createElement(id); }
function $CV(element, evalue) { if (element == null || element == '') return ''; if ($C(element) == null) return ''; if (evalue != null) $C(element).value = evalue; return $C(element).value; }
function WinMax() { try { var windowWidth = window.screen.availWidth; var windowHeight = window.screen.availHeight; window.moveTo(0, 0); window.resizeTo(windowWidth, windowHeight); } catch (e) { } }
function IsBlack(element) { if ($C(element) == null) return true; if ($C(element).value == null || $C(element).value == "") return true; return false; }
function IsNull(_value) { if (_value == null || _value == "") return true; return false; }

function CEvalObj(str) { var obj = null; try { obj = eval('(' + str + ')'); } catch (e) { } return obj };
function CParseObj(str) {
    try {
        return JSON.parse(str);
    }
    catch (e) {
        return CEvalObj(str);
    }
};
function CEncode(str) {    
    return encodeURIComponent(encodeURIComponent(str));
}

function CDecode(str) { return decodeURIComponent(decodeURIComponent(str)); }
function CResourceUrl(path) { return path; }
function CCResourceUrl(path) { return CGeowin.ApiPath + path; }


function CGetAttrsStr(obj) {
    if (obj == null) return;
    var attr = '';

    for (var x in obj) {
        if (obj[x] == null) obj[x] = "";
        if (attr == '')
            attr += String(x) + '=' + obj[x];
        else
            attr += '&' + String(x) + '=' + obj[x];
    }

    return attr;
};
function CAttributeCount(obj) {
    var count = 0;
    for (var x in obj) {
        if (obj.hasOwnProperty(x)) count++;
    }
    return count;
}
function CEncodeObj(obj) {
    var eobj = {};
    for (var attr in obj) {
        eobj[attr] = CEncode(obj[attr]);
    }
    return eobj;
}




(function () {
    var DataSvrUrl = '/ds?r=' + Math.random();



    function AjaxRequest() { try { if (typeof ActiveXObject != "undefined") return new ActiveXObject("Microsoft.XMLHTTP"); else if (window.XMLHttpRequest) return new XMLHttpRequest } catch (a) { } return null }
    function __wl(a) { var b = -1, c = null; try { b = a.status; c = a.responseText } catch (d) { } return { status: b, responseText: c } }


    function CDownload(url, callback, postBody, postContentType) {
        
        var e = AjaxRequest();
        if (!e) return false;
        if (callback) e.onreadystatechange = function () {
            if (e.readyState == 4) {
                var g = __wl(e), h = g.status, i = g.responseText;
                e.onreadystatechange = function () { };
                delete e["onreadystatechange"];
                e = null;;
                callback(i, h);
            }
        };
        if (postBody) {
            e.open('POST', url, true);
            var f = postContentType;
            if (!f) f = 'application/x-www-form-urlencoded';
            e.setRequestHeader("Content-Type", f);
            e.send(postBody);
        }
        else {
            e.open("GET", url, true);
            e.send(null);
        }
        return true;
    }

    function CDownloadSyn(url, postBody) {
        var e = AjaxRequest();
        if (!e) return false;
        if (postBody) {
            e.open('POST', url, false);
            var f = 'application/x-www-form-urlencoded';
            e.setRequestHeader("Content-Type", f);
            e.send(postBody);
        }
        else {
            e.open("GET", url, false);
            e.send(null)
        }
        var responseText = __wl(e).responseText;
        e = null;
        return responseText;
    }

    function CRequestUrl(transobj) {
        var url = DataSvrUrl;
        if (transobj == null) return url;

        if (!IsNull(transobj.url)) {
            if (transobj.url instanceof Array)
                url = transobj.url[0] + '/ds?r=' + Math.random();
            else
                url = transobj.url + '/ds?r=' + Math.random();
        }

        if (transobj.serviceProviderId)
            url += "&serviceproviderid=" + transobj.serviceProviderId;
        if (transobj.serviceId)
            url += "&serviceid=" + transobj.serviceId;
        if (transobj.params)
            url += "&" + CGetAttrsStr(transobj.params);

        if (transobj.requestType)
            url += "&requesttype=" + transobj.requestType;
        else
            url += "&requesttype=json";
        return url;
    }

    function CGetData(transobj, callback, postbody, postContentType) {
        return CDownload(CRequestUrl(transobj), callback, postbody, postContentType);
    }

   
    var GLastUniqueID = 0;
    var GStartTimeStamp = Number(new Date());
    function CGetUniqueId() {
        return ++GLastUniqueID;
    }
    function CGetUID() {
        return Number(GStartTimeStamp.toString() + CGetUniqueId().toString());
    }

    CGeowin.DataSvrUrl = DataSvrUrl;
    window.CDownload = CDownload;

    window.CRequestUrl = CRequestUrl;
    window.CGetData = CGetData;

    window.CGetUniqueId = CGetUniqueId;
    window.CGetUID = CGetUID;
    CGeowin.AjaxRequest = AjaxRequest;
})();





(function () {

    function encodeSignedNumber(num) {
        var sgn_num = num << 1;
        if (num < 0) {
            sgn_num = ~(sgn_num);
        }
        return (encodeNumber(sgn_num));
    }

    function encodeNumber(num) {
        var encodeString = "";
        while (num >= 0x20) {
            encodeString += (String.fromCharCode((0x20 | (num & 0x1f)) + 63));
            num >>= 5;
        }
        encodeString += (String.fromCharCode(num + 63));
        return encodeString;
    }

    function geoEncodings(points) {
        var i = 0;
        var plat = 0;
        var plng = 0;
        var encoded_points = "";

        for (i = 0; i < points.length; ++i) {
            var point = points[i];
            var lat = point[1];
            var lng = point[0];

            var late5 = Math.floor(lat * 1e5);
            var lnge5 = Math.floor(lng * 1e5);

            dlat = late5 - plat;
            dlng = lnge5 - plng;

            plat = late5;
            plng = lnge5;

            encoded_points += encodeSignedNumber(dlng) + encodeSignedNumber(dlat);
        }
        return encoded_points;
    }


    function geoDecodings(encoded) {
        var index = 0;
        var array = [];
        var lat = 0;
        var lng = 0;
        while (index < encoded.length) {
            var b;
            var shift = 0;
            var result = 0;
            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);
            var dlat = ((result & 1) ? ~(result >> 1) : (result >> 1));
            lat += dlat;

            shift = 0;
            result = 0;
            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);
            var dlng = ((result & 1) ? ~(result >> 1) : (result >> 1));
            lng += dlng;

            array.push([lat * 1e-5, lng * 1e-5]);
        }
        return array;
    }
    function geoDecodeDistrct(data) {

        if (data instanceof Array) {
            var list = [];
            for (var i = 0; i < data.length; i++) {
                list.push(geoDecodePoly(data[i]));
            }
            return list;
        }
        return geoDecodePoly(data);
    }

    function geoDecodePoly(district) {

        if (district.encodepoly) {
            district.poly = [];
            for (var i = 0; i < district.encodepoly.length; i++) {
                district.poly.push(geoDecodings(district.encodepoly[i]));
            }
        }
        return district;
    }

    CGeowin.geoEncode = geoEncodings;
    CGeowin.geoDecode = geoDecodings;
    CGeowin.geoDecodeDistrct = geoDecodeDistrct;
})();

