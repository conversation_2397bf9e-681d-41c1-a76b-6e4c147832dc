/*
 * @Description: 透明掘进页面掘进车的控制
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2022-11-19 19:16:15
 * @LastEditors: wbw
 * @LastEditTime: 2022-12-01 16:51:12
 */

import ClipModels from "@/utils/map/clipModels";

class Animation {

  /**
   * @description: 初始化Animation
   * @param {Cesium.viewer} viewer Cesium的视图对象
   * @return {*}
   * @author: wbw
   */
  constructor(viewer) {
    this.viewer = viewer;
    //未采样的点路径列表
    this.pathList = [];
    //每两点之间采样范围列表
    this.intervalList = []
    //标注线entity列表
    this.measurePathLineList = [];
    //标注线Label entity列表
    this.measurePathLabelList = [];
    //每米的采样间隔数
    this.intervalPerMeter = 1000;
    //模型状态index
    this.modelPositionIndex = 0;
    //模型状态最大index
    this.maxIndex = 0
    //是否循环
    this.loop = false;
    //小车速度，单位 米/小时
    this.speed = 1;
    //是否显示模型行进路径
    this.isShowPathLine = false;
    //量测线偏移长度 单位：米
    this.offsetLength = 3;
    //量测标注this.viewer间隔 单位：米
    this.labelDuration = 50;
    //掘进面entity列表
    this.drivingPlaneList = [];
    //每个掘进面之间的间隔 单位：米
    this.drivingPlaneinterval = 5;
    //规划掘进距离
    this.planningDistance = 0;
    //规划掘进距离线entity列表
    this.PlanningPathList = [];
    //路径线entity列表
    this.pathLineList = [];
    //剖切煤层
    this.clipModelsArr = [];
  }

  /**
   * @description: 清除本类中的所有创建的实体
   * @return {*}
   * @author: wbw
   */
  destroy() {
    this.pause();
    this.unTracked();
    //删除路径线
    for (var i = 0; i < this.pathLineList.length; i++) {
      this.viewer.entities.remove(this.pathLineList[i]);
    }
    this.pathLineList = [];
    //删除掘进车
    this.viewer.entities.remove(this.model);
    //删除量测线
    for (var i = 0; i < this.measurePathLineList.length; i++) {
      this.viewer.entities.remove(this.measurePathLineList[i]);
    }
    this.measurePathLineList = [];
    //删除量测线Label
    for (var i = 0; i < this.measurePathLabelList.length; i++) {
      this.viewer.entities.remove(this.measurePathLabelList[i]);
    }
    this.measurePathLabelList = [];
    //删除掘进面
    for (var i = 0; i < this.drivingPlaneList.length; i++) {
      this.viewer.entities.remove(this.drivingPlaneList[i].entity);
    }
    this.drivingPlaneList = [];
    //删除规划掘进距离线列表
    for (var i = 0; i < this.PlanningPathList.length; i++) {
      this.viewer.entities.remove(this.PlanningPathList[i]);
    }
    this.PlanningPathList = [];
    //取消剖分
    if (this.clipModelsArr) {
      for (var clipModels of this.clipModelsArr) {
        clipModels.destroy();
      }

    }
    this.viewer.scene.forceRender();
  }

  /**
   * @description: 设置掘进车模型
   * @param {Object} options 属性url为模型的地址
   * @return {*}
   * @author: wbw
   */
  setModel(options) {
    if (this.model) {
      this.viewer.entities.remove(this.model);
    }
    this.model = this.viewer.entities.add({
      id: "tunnelingMachine",
      model: {
        uri: options.url,
      },
    });
    return this.model;
  }

  /**
   * @description: 获取模型
   * @return {*}
   * @author: wbw
   */
  getModel() {
    return this.model;
  }


  /**
   * @description: 播放动画
   * @return {*}
   * @author: wbw
   */
  play() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    //设置模型开始播放动画效果
    this.model.model.runAnimations = true;
    //按一定时间间隔更新模型状态
    this.timer = setInterval(() => {
      if (this.maxIndex != 0) {
        if (this.modelPositionIndex < this.maxIndex) {
          //设置模型的位置
          this.model.position = this.getPositionByIndex(this.modelPositionIndex)
          var groll = Cesium.Math.toRadians(0);
          var gheading = this._getHeading(
            this.getPositionByIndex(this.modelPositionIndex),
            this.getPositionByIndex(this.modelPositionIndex + 1)
          );
          var gpitch = this._getPitch(
            this.getPositionByIndex(this.modelPositionIndex),
            this.getPositionByIndex(this.modelPositionIndex + 1)
          );
          var hpr = new Cesium.HeadingPitchRoll(gheading, gpitch, groll);
          var orientation = Cesium.Transforms.headingPitchRollQuaternion(
            this.getPositionByIndex(this.modelPositionIndex),
            hpr
          );
          //设置模型的姿态
          this.model.orientation = orientation;
        }
        this.modelPositionIndex += 1;
        if (this.modelPositionIndex >= this.maxIndex) {
          this.modelPositionIndex = 0;
          //如果不循环则停止动画
          if (this.loop == false) {
            this.pause();
          }
        }
      }
    }, (1 / this.speed) * 3600);

    //每隔20毫秒，强制重新渲染页面
    this.modelRenderTimer = setInterval(() => {
      this.viewer.scene.forceRender();
    }, 20);

    //监听掘进面，掘进车经过后的掘进面消失
    this.drivingPlaneTimer = setInterval(() => {
      for (var i = 0; i < this.drivingPlaneList.length; i++) {
        if (this.modelPositionIndex >= this.drivingPlaneList[i].index) {
          this.drivingPlaneList[i].entity.show = false;
        } else {
          this.drivingPlaneList[i].entity.show = true;
        }
      }
    }, (1 / this.speed) * 3600);
  }

  /**
   * @description: 暂停动画
   * @return {*}
   * @author: wbw
   */
  pause() {
    clearInterval(this.timer);
    clearInterval(this.modelRenderTimer);
    clearInterval(this.drivingPlaneTimer);
    this.model.model.runAnimations = false;
    this.timer = undefined;
  }

  /**
   * @description: 设置模型的运行速度
   * @param {number} speed 模型速度的值 单位:米/小时
   * @return {*}
   * @author: wbw
   */
  setSpeed(speed) {
    this.speed = speed;
  }



  /**
   * @description: 设置模型的行进路径
   * @param {array} pathList 模型行进路径列表，里面存储的值为Cesium.Cartesian3(x,y,z)坐标
   * @return {*}
   * @author: wbw
   */
  setPath(pathList) {
    if (pathList.length < 2) throw "setPath:路径列表长度应大于等于2";
    this.pathList = pathList;
    this.intervalList = []
    var firstInterval = 0
    for (var i = 0; i < pathList.length - 1; i++) {
      var startP = pathList[i];
      var endP = pathList[i + 1];
      var interval = this._getSamplesNumber(startP, endP);
      this.intervalList.push([firstInterval, firstInterval + interval])
      firstInterval = firstInterval + interval
    }
    this.maxIndex = firstInterval
    this._setEntity();
  }

  /**
   * @description: 摄像机飞到模型处
   * @return {*}
   * @author: wbw
   */
  flyToModel() {
    this.viewer.flyTo(this.model, {
      duration: 1,
    });
  }

  /**
   * @description: 获取模型当前位置
   * @return {*}
   * @author: wbw
   */
  getCurrentPosition() {
    return this.getPositionByIndex(this.modelPositionIndex);
  }

  /**
   * @description: 设置每米间采样的间隔数
   * @param {number} interval 每米的采样间隔数
   * @return {*}
   * @author: wbw
   */
  setInterval(interval) {
    this.intervalPerMeter = interval;
  }


  /**
   * @description: 获取模型循环状态
   * @return {*}
   * @author: wbw
   */
  getLoop() {
    return this.loop;
  }



  /**
   * @description: 设置模型循环状态
   * @param {boolean} bool true为启用循环，false为关闭循环
   * @return {*}
   * @author: wbw
   */
  setLoop(bool) {
    this.loop = bool;
  }

  /**
   * @description: 显示模型的行进路径线
   * @return {*}
   * @author: wbw
   */
  showPathLine() {
    this.isShowPathLine = true;
    for (var i = 0; i < this.pathLineList.length; i++) {
      this.pathLineList[i].show = this.isShowPathLine;
    }
  }


  /**
   * @description: 隐藏模型的行进路径线
   * @return {*}
   * @author: wbw
   */
  unShowPathLine() {
    this.isShowPathLine = false;
    for (var i = 0; i < this.pathLineList.length; i++) {
      this.pathLineList[i].show = this.isShowPathLine;
    }
  }

  /**
   * @description: 设置量测路径和掘进面
   * @return {*}
   * @author: wbw
   */
  _setMeasurePathLine() {
    this._setMeasurePathEntity();
    this._setDrivingPlaneEntity();
  }

  /**
   * @description: 设置摄像机的模型视角
   * @param {number} value 0:顶面剖切 1:正面剖切 2:侧面剖切 3:固定视角的侧面剖切
   * @return {*}
   * @author: wbw
   */
  setViewer(value) {
    var modelPosition = this.getCurrentPosition();
    //顶面剖切
    if (value == 0) {
      var viwerPosition = this._getMoveUpCoordinate(modelPosition, 100);
      var heading =
        this._getHeading(
          this.getPositionByIndex(this.modelPositionIndex + 1),
          this.getPositionByIndex(this.modelPositionIndex)

        ) - Cesium.Math.toRadians(90);
      var pitch = this._getPitch(viwerPosition, modelPosition);
      this.viewer.camera.setView({
        destination: viwerPosition,
        orientation: {
          heading: heading,
          pitch: pitch,
          roll: 0,
        },
      });
    }
    //正面剖切
    if (value == 1) {
      var L1 = Cesium.Cartesian3.subtract(
        this.getPositionByIndex(this.modelPositionIndex),
        this.getPositionByIndex(this.modelPositionIndex + 1),
        new Cesium.Cartesian3()
      );
      var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3());
      var vector = Cesium.Cartesian3.multiplyByScalar(
        L1N,
        30,
        new Cesium.Cartesian3()
      );
      var viwerPosition = this._getMoveUpCoordinate(modelPosition, 1.5);
      var viwerPosition = Cesium.Cartesian3.add(
        viwerPosition,
        vector,
        new Cesium.Cartesian3()
      );
      var heading =
        this._getHeading(viwerPosition, modelPosition) +
        Cesium.Math.toRadians(90);
      var pitch = this._getPitch(viwerPosition, modelPosition);
      this.viewer.camera.setView({
        destination: viwerPosition,
        orientation: {
          heading: heading,
          pitch: pitch,
          roll: 0,
        },
      });
    } //侧面剖切
    if (value == 2) {
      var offsetLength = 50;
      var L1 = Cesium.Cartesian3.subtract(
        this.getPositionByIndex(this.modelPositionIndex + 1),
        this.getPositionByIndex(this.modelPositionIndex),
        new Cesium.Cartesian3()
      );
      L1.Z = 0;
      var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3());
      var L2SP = this._getMoveUpCoordinate(modelPosition, -1);
      var L2 = Cesium.Cartesian3.subtract(
        modelPosition,
        L2SP,
        new Cesium.Cartesian3()
      );
      var L2N = Cesium.Cartesian3.normalize(L2, new Cesium.Cartesian3());
      var L3 = Cesium.Cartesian3.cross(L2N, L1N, new Cesium.Cartesian3());
      var L3N = Cesium.Cartesian3.normalize(L3, new Cesium.Cartesian3());
      var L3vector = Cesium.Cartesian3.multiplyByScalar(
        L3N,
        -offsetLength,
        new Cesium.Cartesian3()
      );
      var viwerPosition = Cesium.Cartesian3.add(
        modelPosition,
        L3vector,
        new Cesium.Cartesian3()
      );

      var heading =
        this._getHeading(viwerPosition, modelPosition) +
        Cesium.Math.toRadians(90);
      var pitch = this._getPitch(viwerPosition, modelPosition);
      this.viewer.camera.setView({
        destination: viwerPosition,
        orientation: {
          heading: heading,
          pitch: pitch,
          roll: 0,
        },
      });
    }
    //固定视角的侧面剖切
    if (value == 3) {
      var offsetLength = 50;
      var L1 = Cesium.Cartesian3.subtract(
        this.getPositionByIndex(this.modelPositionIndex + 1),
        this.getPositionByIndex(this.modelPositionIndex),
        new Cesium.Cartesian3()
      );
      var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3());

      var L2SP = this._getMoveUpCoordinate(modelPosition, -1);
      var L2 = Cesium.Cartesian3.subtract(
        modelPosition,
        L2SP,
        new Cesium.Cartesian3()
      );
      var L2N = Cesium.Cartesian3.normalize(L2, new Cesium.Cartesian3());

      var L3 = Cesium.Cartesian3.cross(L2N, L1N, new Cesium.Cartesian3());
      var L3N = Cesium.Cartesian3.normalize(L3, new Cesium.Cartesian3());
      var L3vector = Cesium.Cartesian3.multiplyByScalar(
        L3N,
        -offsetLength,
        new Cesium.Cartesian3()
      );

      var viwerPosition = Cesium.Cartesian3.add(
        modelPosition,
        L3vector,
        new Cesium.Cartesian3()
      );
      var heading =
        this._getHeading(viwerPosition, modelPosition) +
        Cesium.Math.toRadians(90);
      var pitch = this._getPitch(viwerPosition, modelPosition);
      this.viewer.camera.setView({
        destination: viwerPosition,
        orientation: {
          heading: heading,
          pitch: pitch,
          roll: 0,
        },
      });
    }
  }

  /**
   * @description: 获取pathList右侧的偏移路径
   * @param {array} pathList 需要偏移的路径
   * @return {array} 向右偏移后的路径，偏移距离由this.offsetLength决定
   * @author: wbw
   */
  _getOffsetVector(pathList) {
    var startP = pathList[0];
    var endP = pathList[1];
    //路径的前进向量
    var L1 = Cesium.Cartesian3.subtract(endP, startP, new Cesium.Cartesian3());
    var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3());
    //用于做叉乘的向量
    var L2SP = this._getMoveUpCoordinate(startP, -1);
    var L2 = Cesium.Cartesian3.subtract(L2SP, startP, new Cesium.Cartesian3());
    var L2N = Cesium.Cartesian3.normalize(L2, new Cesium.Cartesian3());
    //叉乘，获取前进向量的右侧向量
    var L3 = Cesium.Cartesian3.cross(L1N, L2N, new Cesium.Cartesian3());
    var L3N = Cesium.Cartesian3.normalize(L3, new Cesium.Cartesian3());
    var offsetVector = Cesium.Cartesian3.multiplyByScalar(
      L3N,
      this.offsetLength,
      new Cesium.Cartesian3()
    );

    var startP2 = Cesium.Cartesian3.add(
      startP,
      offsetVector,
      new Cesium.Cartesian3()
    );
    var endP2 = Cesium.Cartesian3.add(
      endP,
      offsetVector,
      new Cesium.Cartesian3()
    );

    return [startP2, endP2];
  }

  /**
   * @description: 创建量测路径线和距离标注
   * @return {*}
   * @author: wbw
   */
  _setMeasurePathEntity() {
    //删除量测线
    for (var i = 0; i < this.measurePathLineList.length; i++) {
      this.viewer.entities.remove(this.measurePathLineList[i]);
    }
    this.measurePathLineList = [];
    //删除量测线Label
    for (var i = 0; i < this.measurePathLabelList.length; i++) {
      this.viewer.entities.remove(this.measurePathLabelList[i]);
    }
    this.measurePathLabelList = [];
    var allDistan = 0;
    //上一轮循环中余下的距离
    var remainingDistance = 0;
    for (var i = 0; i < this.pathList.length - 1; i++) {
      var startP = this.pathList[i];
      var endP = this.pathList[i + 1];
      var [startP, endP] = this._getOffsetVector([startP, endP]);
      var line2 = this.viewer.entities.add({
        name: "PathLine2",
        polyline: {
          positions: [startP, endP],
          width: 1,
          material: Cesium.Color.WHITE,
        },
      });
      this.measurePathLineList.push(line2);
      var L1 = Cesium.Cartesian3.subtract(
        endP,
        startP,
        new Cesium.Cartesian3()
      );
      var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3());
      var LDistan = Cesium.Cartesian3.distance(startP, endP);
      for (var j = remainingDistance; j < LDistan; j += this.labelDuration) {
        var newL = Cesium.Cartesian3.multiplyByScalar(
          L1N,
          j,
          new Cesium.Cartesian3()
        );
        var newP = Cesium.Cartesian3.add(startP, newL, new Cesium.Cartesian3());
        var label = this.viewer.entities.add({
          position: newP,
          point: {
            color: Cesium.Color.WHITE, //点位颜色
            pixelSize: 10, //像素点大小
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              1000
            ),
          },
          label: {
            text: allDistan.toString() + "米", //文本内容，\n标识换行
            font: "14pt Source Han Sans CN", //字体样式
            fillColor: Cesium.Color.BLACK, //字体颜色
            backgroundColor: Cesium.Color.WHITE, //背景颜色
            showBackground: true, //是否显示背景颜色
            style: Cesium.LabelStyle.FILL, //label样式
            outlineWidth: 2,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            verticalOrigin: Cesium.VerticalOrigin.CENTER, //垂直位置
            horizontalOrigin: Cesium.HorizontalOrigin.LEFT, //水平位置
            pixelOffset: new Cesium.Cartesian2(10, 0), //偏移
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              1000
            ),
          },
        });
        allDistan += this.labelDuration;
        this.measurePathLabelList.push(label);
      }
      //计算本轮两点间余下的距离
      while (LDistan > 0) {
        LDistan -= this.labelDuration;
      }
      remainingDistance = -LDistan;
    }
  }

  /**
   * @description: 显示量测路径线和距离标注
   * @return {*}
   * @author: wbw
   */
  showMeasurePathLine() {
    for (var i = 0; i < this.measurePathLineList.length; i++) {
      this.measurePathLineList[i].show = true;
    }
    for (var i = 0; i < this.measurePathLabelList.length; i++) {
      this.measurePathLabelList[i].show = true;
    }
  }

  /**
   * @description: 隐藏量测路径线和距离标注
   * @return {*}
   * @author: wbw
   */
  unShowMeasurePathLine() {
    for (var i = 0; i < this.measurePathLineList.length; i++) {
      this.measurePathLineList[i].show = false;
    }
    for (var i = 0; i < this.measurePathLabelList.length; i++) {
      this.measurePathLabelList[i].show = false;
    }
  }

  /**
   * @description: 创建掘进面
   * @return {*}
   * @author: wbw
   */
  _setDrivingPlaneEntity() {
    //删除已有的掘进面
    for (var i = 0; i < this.drivingPlaneList.length; i++) {
      this.viewer.entities.remove(this.drivingPlaneList[i].entity);
    }
    this.drivingPlaneList = [];
    //向量掘进面的index步长
    var idx = this.drivingPlaneinterval * this.intervalPerMeter;
    for (var j = 0; j < this.maxIndex; j += idx) {

      var position = this._getMoveUpCoordinate(this.getPositionByIndex(j), 1.5);

      if (j + 1 >= this.maxIndex) {
        var startP = this.getPositionByIndex(j - 1);
        var endP = this.getPositionByIndex(j);
      } else {
        var startP = this.getPositionByIndex(j);
        var endP = this.getPositionByIndex(j + 1);
      }

      var plane = this.viewer.entities.add({
        name: "plane",
        position: position,
        plane: {
          plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, 0), //指定平面的法线和距离
          dimensions: new Cesium.Cartesian2(5, 3), //指定平面的宽度和高度。
          fill: true, //是否填充
          material: Cesium.Color.WHITE.withAlpha(0.1),
          outline: true, //显示边框
          outlineWidth: 4,
          outlineColor: Cesium.Color.YELLOW, //边框颜色
        },
        box: {
          dimensions: new Cesium.Cartesian3(5, 5, 5),
          material: Cesium.Color.WHITE.withAlpha(0.2),
        },
      });

      var groll = Cesium.Math.toRadians(0); //滚转角
      var gheading = this._getHeading(startP, endP);
      var gpitch = this._getPitch(startP, endP);
      var hpr = new Cesium.HeadingPitchRoll(gheading, gpitch, groll);
      var orientation = Cesium.Transforms.headingPitchRollQuaternion(
        startP,
        hpr
      );
      plane.orientation = orientation;
      this.drivingPlaneList.push({
        index: j,
        entity: plane,
      });
    }
  }

  /**
   * @description: 显示掘进面
   * @return {*}
   * @author: wbw
   */
  showDrivingPlaneEntity() {
    for (var i = 0; i < this.drivingPlaneList.length; i++) {
      this.drivingPlaneList[i].entity.show = true;
    }
  }

  /**
   * @description: 隐藏掘进面
   * @return {*}
   * @author: wbw
   */
  unShowDrivingPlaneEntity() {
    for (var i = 0; i < this.drivingPlaneList.length; i++) {
      this.drivingPlaneList[i].entity.show = false;
    }
  }

  /**
   * @description: 获取掘进进尺
   * @return {*}
   * @author: wbw
   */
  getDriftingFootage() {
    var idx = 0;
    var pathIndex = 0;
    var distance = 0;
    for (var i = 0; i < this.pathList.length - 1; i++) {
      var startP = this.pathList[i];
      var end = this.pathList[i + 1];
      var LDistan = Cesium.Cartesian3.distance(end, startP);
      var interval = this._getSamplesNumber(startP, end);
      idx += interval * LDistan;
      if (idx >= this.modelPositionIndex) {
        pathIndex = i;
        break;
      }
    }
    for (var i = 0; i < pathIndex; i++) {
      var startP = this.pathList[i];
      var end = this.pathList[i + 1];
      var LDistan = Cesium.Cartesian3.distance(startP, end);
      distance += LDistan;
    }
    var startP = this.pathList[pathIndex];
    var newP = this.getPositionByIndex(this.modelPositionIndex);
    var LDistan = Cesium.Cartesian3.distance(startP, newP);
    distance += LDistan;
    return distance;
  }

  /**
   * @description: 获取待掘进进尺
   * @return {*}
   * @author: wbw
   */
  getAwaitingDriftingFootage() {
    if (this.getPlanningDistance() < this.getDriftingFootage()) {
      return 0;
    } else {
      return this.getPlanningDistance() - this.getDriftingFootage();
    }
  }

  /**
   * @description: 获取总掘进距离
   * @return {*}
   * @author: wbw
   */
  getAllowableDistance() {
    var distance = 0;
    for (var i = 0; i < this.pathList.length - 1; i++) {
      var startP = this.pathList[i];
      var end = this.pathList[i + 1];
      var LDistan = Cesium.Cartesian3.distance(end, startP);
      distance += LDistan;
    }
    return distance;
  }

  /**
   * @description: 获取规划掘进距离
   * @return {*}
   * @author: wbw
   */
  getPlanningDistance() {
    return this.planningDistance;
  }

  /**
   * @description: 获取超期距离
   * @return {*}
   * @author: wbw
   */
  getOverdueDistance() {
    if (this.getDriftingFootage() > this.getPlanningDistance()) {
      return this.getDriftingFootage() - this.getPlanningDistance();
    } else {
      return 0;
    }
  }

  /**
   * @description: 获取航向
   * @return {*}
   * @author: wbw
   */
  getModelHeading() {
    var gheading = this._getHeading(
      this.getPositionByIndex(this.modelPositionIndex),
      this.getPositionByIndex(this.modelPositionIndex + 1)
    );
    var heading = Cesium.Math.toDegrees(gheading);
    if (heading < 0) {
      heading += 360;
    }
    return heading;
  }

  /**
   * @description: 设置当前掘进进度
   * @param {number} value 掘进进度的数据，范围0~1
   * @return {*}
   * @author: wbw
   */
  setRate(value) {
    if (value >= 0 && value <= 1) {
      var newIdx = this.maxIndex * value;
      this.modelPositionIndex = newIdx;
      this.model.position = this.getPositionByIndex(this.modelPositionIndex);
      for (var i = 0; i < this.drivingPlaneList.length; i++) {
        if (this.modelPositionIndex >= this.drivingPlaneList[i].index) {
          this.drivingPlaneList[i].entity.show = false;
        } else {
          this.drivingPlaneList[i].entity.show = true;
        }
      }
    }
  }


  getCurrentPath() {
    if (this.modelPositionIndex < this.maxIndex - 1) {
      return [this.getPositionByIndex(this.modelPositionIndex), this.getPositionByIndex(this.modelPositionIndex + 1)]
    } else {
      return [this.getPositionByIndex(this.modelPositionIndex-1), this.getPositionByIndex(this.modelPositionIndex)]
    }
  }

  /**
   * @description: 设置规划掘进距离
   * @param {number} value 规划掘进距离 单位:米
   * @return {*}
   * @author: wbw
   */
  setPlanningDistance(value) {
    this.planningDistance = value;
    this._setEntity();
  }


  /**
   * @description: 视角跟随模型,轮询
   * @return {*}
   * @author: wbw
   */
  tracked2() {
    var trackedTimer = setInterval(() => {
      if (this.viewer.trackedEntity != this.model) {
        this.viewer.trackedEntity = this.model;
      } else {
        this.unTracked();
        clearInterval(trackedTimer);
      }
    }, 1000);
  }

  /**
   * @description: 视角跟随模型
   * @return {*}
   * @author: wbw
   */
  tracked() {
    this.viewer.trackedEntity = this.model;
  }

  /**
   * @description: 视角取消跟随模型
   * @return {*}
   * @author: wbw
   */
  unTracked() {
    this.viewer.trackedEntity = null;
  }

  /**
   * @description: 创建规划距离线
   * @return {*}
   * @author: wbw
   */
  _setPlanningPathLine() {
    //删除规划距离线
    for (var i = 0; i < this.PlanningPathList.length; i++) {
      this.viewer.entities.remove(this.PlanningPathList[i]);
    }
    this.PlanningPathList = [];
    var distance = 0;
    var excessDistance = 0;
    var flag = true;
    for (var i = 0; i < this.pathList.length - 1; i++) {
      var startP = this.pathList[i];
      var endP = this.pathList[i + 1];
      var LDistan = Cesium.Cartesian3.distance(startP, endP);
      distance += LDistan;
      if (this.planningDistance > distance && flag == true) {
        var line = this.viewer.entities.add({
          name: "PathLine3",
          polyline: {
            positions: [startP, endP],
            width: 1,
            material: Cesium.Color.BLUE,
          },
        });
        this.PlanningPathList.push(line);
        var excessDistance = this.planningDistance - distance;
      } else if (this.planningDistance <= distance && flag == true) {
        var L1 = Cesium.Cartesian3.subtract(
          endP,
          startP,
          new Cesium.Cartesian3()
        );
        var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3());
        var newL = Cesium.Cartesian3.multiplyByScalar(
          L1N,
          excessDistance,
          new Cesium.Cartesian3()
        );
        var newP = Cesium.Cartesian3.add(startP, newL, new Cesium.Cartesian3());

        var line = this.viewer.entities.add({
          name: "PathLine3",
          polyline: {
            positions: [startP, newP],
            width: 1,
            material: Cesium.Color.BLUE,
          },
        });
        this.PlanningPathList.push(line);
        flag = false;
      }
      if (flag == false) {
        for (var j = 0; j < this.pathLineList.length; j++) {
          this.viewer.entities.remove(this.pathLineList[j]);
        }
        this.pathLineList = [];
        if (newP) {
          var startP = newP;
          newP = undefined;
        } else {
          var startP = this.pathList[i];
        }
        var endP = this.pathList[i + 1];
        var line = this.viewer.entities.add({
          name: "PathLine",
          polyline: {
            positions: [startP, endP],
            width: 1,
            material: Cesium.Color.YELLOW,
          },
        });
        this.pathLineList.push(line);
      }
    }
  }

  /**
   * @description: 获得某点向上平移的坐标
   * @param {Cesium.Cartesian3} cartesian 需要向上平移的坐标
   * @param {number} height 向上平移的高度
   * @return {*}
   * @author: wbw
   */
  _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }


  /**
   * @description: 重新设置各个entity和模型的属性
   * @return {*}
   * @author: wbw
   */
  _setEntity() {
    this._setPathLine();
    this._setMeasurePathLine();
    this.model.position = this.getPositionByIndex(this.modelPositionIndex);
    var groll = Cesium.Math.toRadians(0); //滚转角
    var gheading = this._getHeading(

      this.getPositionByIndex(this.modelPositionIndex),
      this.getPositionByIndex(this.modelPositionIndex + 1)
    );
    var gpitch = this._getPitch(

      this.getPositionByIndex(this.modelPositionIndex),
      this.getPositionByIndex(this.modelPositionIndex + 1)
    );
    var hpr = new Cesium.HeadingPitchRoll(gheading, gpitch, groll);
    var orientation = Cesium.Transforms.headingPitchRollQuaternion(

      this.getPositionByIndex(this.modelPositionIndex),
      hpr
    );
    this.model.orientation = orientation;
    for (var i = 0; i < this.drivingPlaneList.length; i++) {
      if (this.modelPositionIndex >= this.drivingPlaneList[i].index) {
        this.drivingPlaneList[i].entity.show = false;
      } else {
        this.drivingPlaneList[i].entity.show = true;
      }
    }
  }

  /**
   * @description: 创建路径线
   * @return {*}
   * @author: wbw
   */
  _setPathLine() {
    if (this.pathLineList.length != 0) {
      for (var i = 0; i < this.pathLineList.length; i++) {
        this.viewer.entities.remove(this.pathLineList[i]);
      }
      this.pathLineList = [];
    }
    var line = this.viewer.entities.add({
      name: "PathLine",
      polyline: {
        positions: this.pathList,
        width: 1,
        material: Cesium.Color.YELLOW,
      },
    });
    this.pathLineList.push(line);
    for (var i = 0; i < this.pathLineList.length; i++) {
      this.pathLineList[i].show = this.isShowPathLine;
    }
  }

  /**
   * @description: 获取模型前进Heading
   * @param {Cesium.Cartesian3} pointA 起点坐标
   * @param {Cesium.Cartesian3} pointB 终点坐标
   * @return {number} Heading值
   * @author: wbw
   */
  _getHeading(pointA, pointB) {
    //建立以点A为原点，X轴为east,Y轴为north,Z轴朝上的坐标系
    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
    //向量AB
    const positionvector = Cesium.Cartesian3.subtract(
      pointB,
      pointA,
      new Cesium.Cartesian3()
    );
    //因transform是将A为原点的eastNorthUp坐标系中的点转换到世界坐标系的矩阵
    //AB为世界坐标中的向量
    //因此将AB向量转换为A原点坐标系中的向量，需乘以transform的逆矩阵。
    const vector = Cesium.Matrix4.multiplyByPointAsVector(
      Cesium.Matrix4.inverse(transform, new Cesium.Matrix4()),
      positionvector,
      new Cesium.Cartesian3()
    );
    //归一化
    const direction = Cesium.Cartesian3.normalize(
      vector,
      new Cesium.Cartesian3()
    );
    //heading
    const heading =
      Math.atan2(direction.y, direction.x) - Cesium.Math.PI_OVER_TWO;
    return (
      Cesium.Math.TWO_PI -
      Cesium.Math.zeroToTwoPi(heading) -
      Cesium.Math.toRadians(90)
    );
  }

  /**
   * @description: 获取模型前进Pitch
   * @param {Cesium.Cartesian3} pointA 起点坐标
   * @param {Cesium.Cartesian3} pointB 终点坐标
   * @return {number} Pitch值
   * @author: wbw
   */
  _getPitch(pointA, pointB) {
    let transfrom = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
    const vector = Cesium.Cartesian3.subtract(
      pointB,
      pointA,
      new Cesium.Cartesian3()
    );
    let direction = Cesium.Matrix4.multiplyByPointAsVector(
      Cesium.Matrix4.inverse(transfrom, transfrom),
      vector,
      vector
    );
    Cesium.Cartesian3.normalize(direction, direction);
    //因为direction已归一化，斜边长度等于1，所以余弦函数等于direction.z
    return Cesium.Math.PI_OVER_TWO - Cesium.Math.acosClamped(direction.z);
  }

  /**
   * @description: 获得该线段采样的个数
   * @param {Cesium.Cartesian3} startP 起点坐标
   * @param {Cesium.Cartesian3} endP 终点坐标
   * @return {number} 采样个数
   * @author: wbw
   */
  _getSamplesNumber(startP, endP) {
    var distan = Cesium.Cartesian3.distance(startP, endP);
    var interval = distan * this.intervalPerMeter;
    return interval;
  }

  /**
   * @description: 设置小车路径的其他模型剖切
   * @param {array} clipModelsList 需要剖切的模型列表：里面存储的对象是3dtiles
   * @param {number} distance 剖切平面到小车的距离
   * @param {Object} earth 通过earth.js创建的对象
   * @return {*}
   * @author: wbw
   */
  setClipModels(clipModelsList, distance, earth) {

    var clipModels = new ClipModels(
      clipModelsList,
      this.pathList,
      distance, earth
    );
    clipModels.clipStart();
    this.clipModelsArr.push(clipModels)

  }


  /**
   * @description: 通过index获取路径上的位置
   * @param {*} index 模型所处的index
   * @return {*}
   * @author: wbw
   */
  getPositionByIndex(index) {
    if (this.pathList.length == 0) {
      return
    }
    if (index > this.maxIndex) {
      return this.pathList[this.pathList.length - 1]
    }
    for (var i = 0; i < this.intervalList.length; i++) {
      if (index >= this.intervalList[i][0] && index < this.intervalList[i][1]) {
        var startP = this.pathList[i];
        var endP = this.pathList[i + 1];
        var samplesIndex = index - this.intervalList[i][0]
        var samplesLength = this.intervalList[i][1] - this.intervalList[i][0]
        var pos = Cesium.Cartesian3.lerp(
          startP,
          endP,
          samplesIndex / samplesLength,
          new Cesium.Cartesian3())
        return pos
      }
    }
    return this.pathList[0]
  }
}

export default Animation;