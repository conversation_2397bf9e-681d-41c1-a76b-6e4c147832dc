<!doctype html>

<title>CodeMirror: Tag Matcher <PERSON></title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/fold/xml-fold.js"></script>
<script src="../addon/edit/matchtags.js"></script>
<script src="../mode/xml/xml.js"></script>
<style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Tag Matcher</a>
  </ul>
</div>

<article>
<h2>Tag Matcher Demo</h2>


    <div id="editor"></div>

    <script>
window.onload = function() {
  editor = CodeMirror(document.getElementById("editor"), {
    value: "<html>\n  " + document.documentElement.innerHTML + "\n</html>",
    mode: "text/html",
    matchTags: {bothTags: true},
    extraKeys: {"Ctrl-J": "toMatchingTag"}
  });
};
    </script>

    <p>Put the cursor on or inside a pair of tags to highlight them.
    Press Ctrl-J to jump to the tag that matches the one under the
    cursor.</p>
  </article>
