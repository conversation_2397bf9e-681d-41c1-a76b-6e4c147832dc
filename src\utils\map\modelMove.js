
import modelMoveHeight from "@/utils/map/modelMoveHeight"
class ModelMove {
    up(model, height) {
        const cartographic = Cesium.Cartographic.fromCartesian(
            model.boundingSphere.center
        );
        const surface = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            0.0
        );
        const offset = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            height+modelMoveHeight
        );
        const translation = Cesium.Cartesian3.subtract(
            offset,
            surface,
            new Cesium.Cartesian3()
        );
        model.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
    }

    up2(models, height) {
        for(var model of models){
            const cartographic = Cesium.Cartographic.fromCartesian(
                model.boundingSphere.center
            );
            const surface = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                0.0
            );
            const offset = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                height+modelMoveHeight
            );
            const translation = Cesium.Cartesian3.subtract(
                offset,
                surface,
                new Cesium.Cartesian3()
            );
            model.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
        }     
    }

    recovery(model){
        const cartographic = Cesium.Cartographic.fromCartesian(
            model.boundingSphere.center
        );
        const surface = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            0.0
        );
        const offset = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            0+modelMoveHeight
        );
        const translation = Cesium.Cartesian3.subtract(
            offset,
            surface,
            new Cesium.Cartesian3()
        );
        model.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
    }

    recoveryAll(){
        var entities =window.earth.getEntities();
        for (var i = 0; i < entities.length; i++) {
            const cartographic = Cesium.Cartographic.fromCartesian(
                entities[i].boundingSphere.center
            );
            const surface = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                0.0
            );
            const offset = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                0+modelMoveHeight
            );
            const translation = Cesium.Cartesian3.subtract(
                offset,
                surface,
                new Cesium.Cartesian3()
            );
            entities[i].modelMatrix = Cesium.Matrix4.fromTranslation(translation);

        }
    }


}

export default ModelMove;