import {  toRaw } from "vue";
class ForecastSort {

    bubbleSort(forecastList) {
        var sortList = toRaw(forecastList.value)
        // console.log(sortList)
        var resultList = bubble(sortList)
        forecastList.value=resultList
        function bubble(arr)  {
            if (arr.length < 2) {
                return arr;
            }
            let left = [];
            let right = [];
            let numIndex = Math.floor(arr.length / 2);
            let num = arr.splice(numIndex, 1);
            // console.log(num[0].distance)
            //这里注意不能像下面这样直接写，因为splice会直接改变数组，这样在最后面连上num，才没问题
            // let num = arr[Math.floor(arr.length / 2)];
            arr.forEach((item) => {
                if (Number(item.distance) <= Number(num[0].distance)) {
                    left.push(item);
                } else if (Number(item.distance) >Number (num[0].distance)) {
                    right.push(item);
                }
            });
            return bubble(left).concat(num, bubble(right));
        };
    }

}
export default ForecastSort