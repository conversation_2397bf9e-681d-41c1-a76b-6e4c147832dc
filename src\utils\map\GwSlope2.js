
/**
 * 坡度坡向分析
 */
import {openLoading,closeLoading} from '@/utils/loading.js'
class GwSlope2{
  /**
   * @param {Viewer} viewer 
   * @param {Object} [option={}] 可选，包含下面列出的属性
   * @param {String} [option.id=GUID] 实例id属性，不同实例需设置不同的id值
   * @param {Number} [option.interpolations=20] 矩形短边分割的单元格的数量，长边依此数分割等比例的单元格数
   * @param {Number} [option.samples=10] 小单元格中一行（一列）的采样点数，总采样点数 = samples * samples
   * @param {Number} [option.arrowWidth=10] 坡向箭头的宽度
   * @param {Color} [option.arrowColor=Geowin3D.Color.WHITE] 坡向箭头的颜色
   * @param {Number} [option.mapRadius=80] 坡面填充色渐变半径
   * @param {Object} [option.mapGradient={'0.25': 'rgb(0, 0 ,255)','0.5': 'rgb(0, 255, 255)','0.75': 'rgb(0, 255, 0)','1': 'rgb(255, 255, 0)'}] 坡面渐变填充颜色
   */
  constructor(viewer, options = {}) {
    this._viewer = viewer;
    this._options = options;

    this._points = undefined;
    this._rectangle = undefined;
    this._slopes = [];

    this._pge = new Geowin3D.GwMicroApp.GwPolygonEditor(viewer);
    this._pge.setAutoDel(true);
    this._lsnr = undefined;

    this._event = new Geowin3D.Event();
    this._eventHelper = new Geowin3D.EventHelper();
    this._excludeLayers=[]

    // 可配置参数
    this.id = Geowin3D.createGuid();
    this._interpolations = 20;
    this._samples = 10;
    this._arrowWidth = 10;
    this._arrowColor = Geowin3D.Color.WHITE;
    this._mapRadius = 80;
    this._mapGradient = {
      '0.25': 'rgb(0, 0 ,255)',
      '0.5': 'rgb(0, 255, 255)',
      '0.75': 'rgb(0, 255, 0)',
      '1': 'rgb(255, 255, 0)'
    };
    this._showArrows = true;
    this._showHeatmap = true;
    this._showLegend = true;

    this._arrowsLayer = undefined;
    this._heatmapLayer = undefined;
    this._heatmapContainer = undefined;
    this._legend = undefined;

    this._pmtAdd = false;
    this._tooltip = undefined;


    this.set(options);
  }

  /**
   * 配置参数
   * @type {Object}
   */
  get options() {
    return this._options;
  }

  /**
   * 显示坡向箭头
   * @type {Boolean}
   */
  set showArrows(newValue) {
    this.setShowArrows(newValue);
  }

  /**
   * 显示坡度热力图
   * @type {Boolean}
   */
  set showHeatmap(newValue) {
    this.setShowHeatmap(newValue);
  }

  /**
   * 显示箭头和热力图
   * @type {Boolean}
   */
  set show(newValue) {
    this.setShowAll(newValue);
  }

  /**
   * 显示图例
   * @type {Boolean}
   */
  set showLegend(newValue) {
    this.setShowLegend(newValue);
  }

  /**
   * 设置参数
   * @param {Object} options 配置参数
   */
  set(options = {}) {
    options.id && (this.id = options.id);

    if (options.interpolations && parseInt(options.interpolations)) {
      this._interpolations = parseInt(options.interpolations);
    }

    if (options.samples && parseInt(options.samples)) {
      this._samples = parseInt(options.samples);
    }

    if (options.arrowWidth && parseInt(options.arrowWidth)) {
      this._arrowWidth = parseInt(options.arrowWidth);
    }

    if (options.arrowColor) {
      if (options.arrowColor instanceof Geowin3D.Color) {
        this._arrowColor = options.arrowColor;
      } else if (typeof options.arrowColor === 'string') {
        this._arrowColor = Geowin3D.Color.fromCssColorString(options.arrowColor, new Geowin3D.Color());
      }
    }

    if (options.mapRadius && parseInt(options.mapRadius)) {
      this._mapRadius = parseInt(options.mapRadius);
    }

    if (options.mapGradient && Object.prototype.toString.call(options.mapGradient) === '[object Object]') {
      this._mapGradient = options.mapGradient;
    }
  }

  /**
   * 重置可配置参数为默认值
   */
  resetOptions() {
    this._interpolations = 20;
    this._samples = 10;
    this._arrowWidth = 10;
    this._arrowColor = Geowin3D.Color.WHITE;
    this._mapRadius = 80;
    this._mapGradient = {
      '0.25': 'rgb(0, 0 ,255)',
      '0.5': 'rgb(0, 255, 255)',
      '0.75': 'rgb(0, 255, 0)',
      '1': 'rgb(255, 255, 0)'
    };
    this._showArrows = true;
    this._showHeatmap = true;
    this._showLegend = true;
  }

  /**
   * 开始选取多边形区域
   */
  start() {
    this.run();
  }

  /**
   * 开始选取多边形区域
   */
  run () {
    this._reset();

    this._addLsnr();
    this._pge.start();

    this._registerHandlers();
    this._createTooltipContainer();

    this._event.raiseEvent('Start', {});
  }

  /**
   * 设置坡向箭头的显示/隐藏
   * @param {Boolean} [val=true] true则显示
   */
  setShowArrows(val) {
    this._showArrows = !!Geowin3D.defaultValue(val, true);

    this._arrowsLayer && (this._arrowsLayer.show = this._showArrows);

    this._viewer.scene.requestRender();
  }

  /**
   * 设置坡面填充色的显示/隐藏
   * @param {Boolean} [val=true] true则显示
   */
  setShowHeatmap(val) {
    this._showHeatmap = !!Geowin3D.defaultValue(val, true);

    this._heatmapLayer && (this._heatmapLayer.show = this._showHeatmap);

    this._viewer.scene.requestRender();
  }

  /**
   * 设置全部显示/隐藏
   * @param {Boolean} [val=true] true则显示
   */
  setShowAll(val=true) {
    this.setShowArrows(val);
    this.setShowHeatmap(val);
    this.setShowLegend(val);
  }

  /**
   * 设置显示/隐藏图例
   * @param {Boolean} [val=true] true则显示
   */
  setShowLegend(val) {
    this._showLegend = !!Geowin3D.defaultValue(val, true);
    if (this._legend) {
      this._legend.style.display = this._showLegend ? "block" : "none";
    }
  }

  /**
   * 销毁
   */
  destroy() {
    this._reset();

    this._lsnr = this._lsnr && this._lsnr();
    this._pge.destroy();
    this._handler = this._handler && this._handler.destroy();

    this._viewer.scene.requestRender();

    this._event.raiseEvent('Destroy', {});
  }

  removeAll() {
    this._removeHeatmap();
    this._removeArrows();
    this._removeTooltip();
  }

  /**
   * 注册事件捕捉
   * @param {GwTools.GwSlope~GwSlopeCallback} listener 当有事件触发时被执行的函数。
   * @param {Object} [scope] listener函数执行时的绑定的对象。
   * @returns {Geowin3D.Event~RemoveCallback} 返回一个函数，调用该函数可以取消监听。
   */
  on(listener, scope) {
    return this._eventHelper.add(this._event, listener, scope);
  }

  /**
   * @callback GwTools.GwSlope~GwSlopeCallback
   * @param {String} eventType 事件类型有以下几种：Start/ AnaFinished（分析完成）/PMTAdd
   * @param {Object} eventArg AnaFinished 事件返回分析结果
   */

  _addLsnr() {
    this._lsnr = this._lsnr && this._lsnr();

    this._lsnr = this._pge.on((eventType, param) => {
      if (eventType === 'PGAdd') {
        this._points = Geowin3D.clone(param.polygon.pts, true);
        console.log("开始绘制")
        
        this._build();
      }
    });
  }

  _createTooltipContainer() {
    let tip = document.createElement('div');
    tip.setAttribute('id', 'slopeTips_' + this.id);

    tip.style.position = 'absolute';
    tip.style.display = 'block';
    tip.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    tip.style.padding = '6px 14px';
    tip.style.borderRadius = '4px';
    tip.style.color = 'white';
    tip.style.fontSize = '12px';
    tip.style.lineHeight = '24px';
    tip.style.display = 'none';
    tip.style.pointerEvents = 'none';
    tip.style.zIndex = '999';
    
    document.body.appendChild(tip);
    this._tooltip = tip;
  }

  _showTooltip() {
    this._tooltip.style.display = 'block';
  }

  _hideTooltip() {
    this._tooltip.style.display = 'none';
  }

  _setTooltipAttribute(pickSlope, position) {
    let point = this._positionToCartesian3(position);
    let radians = Geowin3D.Cartographic.fromCartesian(point);

    let lng = Geowin3D.Math.toDegrees(radians.longitude).toFixed(6);
    let lat = Geowin3D.Math.toDegrees(radians.latitude).toFixed(6);
    let alt = (radians.height-1900).toFixed(2);

    let slope = Geowin3D.Math.toDegrees(pickSlope.slope).toFixed(2);
    let direction = Geowin3D.Math.toDegrees(pickSlope.direction).toFixed(2);

    let html = `<span>经度：${lng}</span></br><span>纬度：${lat}</span></br><span>高程：${alt} 米</span></br><span>倾角：${slope}&#176;</span></br><span>倾向：${direction}&#176;</span>`;

    this._tooltip.innerHTML = html;
    this._tooltip.style.left = position.x + 10 + 'px';
    this._tooltip.style.top = position.y + 10 + 'px';
  }

  _registerHandlers() {
    this._handler && this._handler.destroy();
    this._handler = new Geowin3D.ScreenSpaceEventHandler(this._viewer.scene.canvas);
    this._handler.setInputAction(this._onMouseMove.bind(this), Geowin3D.ScreenSpaceEventType.MOUSE_MOVE);
  }

  // 绘制结束
  _endDraw() {
    this._pmtAdd = true;
    this._pge.cancel();

    this._viewer.scene.requestRender();

    this._event.raiseEvent('PMTAdd', {
      points: this._points,
      slopes: this._slopes,
      arrowsLayer: this._arrowsLayer,
      heatmapLayer: this._heatmapLayer,
      interpolations: this._interpolations,
      samples: this._samples
    });

    console.log('_endDraw')
  }

  _onMouseMove(movement) {
    if (this._pmtAdd) {
      // 显示信息提示框
      let result = this._viewer.scene.pick(movement.endPosition);
      
      if(result == undefined)
        return;
      if(result.hasOwnProperty("id")){
        if (result && result.id && result.id.split('__')[0] === this.id) {
          let pickSlope = this._slopes[result.id.split('__')[1]];
          if (pickSlope) {
            this._setTooltipAttribute(pickSlope, movement.endPosition)
            this._showTooltip();
          } else {
            this._hideTooltip();
          }
        } else {
          this._hideTooltip();
        }
      }else{
        this._hideTooltip();
      }
      
    }
  }

  _build(pts) {
    pts = Geowin3D.defaultValue(pts, this._points);

    if (!pts || !pts.length) {
      return;
    }
    this._rectangle = Geowin3D.PolygonGeometry.computeRectangle({
      polygonHierarchy: new Geowin3D.PolygonHierarchy(this._points)
    }, new Geowin3D.Rectangle());

    let promises = this._section(this._rectangle);
    console.log('插值成功');
    this._analysis(promises, 0, promises.length);
  }

  _section(rect) {
    let gridSize = Math.min(rect.east - rect.west, rect.north - rect.south) / this._interpolations; // 网格边长 经纬度 弧度值

    let rows = Math.ceil((rect.east - rect.west) / gridSize); // 网格行数
    let cols = Math.ceil((rect.north - rect.south) / gridSize); // 网格列数

    let gzLon = (rect.east - rect.west) / rows; // 每个单元格宽度 经度 弧度
    let gzLat = (rect.north - rect.south) / cols; // 每个单元格高德 纬度 弧度

    let result = [];

    // 从左下 -》 右上
    for (let i = 0; i < cols; i++) {
      for (let j = 0; j < rows; j++) {
        let r1 = { // 左上
          longitude: rect.west + gzLon * j,
          latitude: rect.south + gzLat * (i + 1)
        };

        let r2 = { // 右上
          longitude: rect.west + gzLon * (j + 1),
          latitude: rect.south + gzLat * (i + 1)
        };

        let r3 = { // 右下
          longitude: rect.west + gzLon * (j + 1),
          latitude: rect.south + gzLat * (i)
        };

        let r4 = { // 左下
          longitude: rect.west + gzLon * j,
          latitude: rect.south + gzLat * i
        };

        let promise = this._getSamples(r1, r2, r3, r4);
        promise && result.push(promise);
      }
    }
    console.log('slope: promise length ='+result.length)
    return result;
  }

  _analysis(promises, i, len) {
    console.log('slope:第'+i + "个")
    Geowin3D.when(promises[i], (updatedPositions) => {
      let high = undefined;
      let low = undefined;
      
      updatedPositions.forEach(item => {
        item = Cesium.Cartographic.fromCartesian(item);

        (!high || item.height > high.height) && (high = item);
        (!low || item.height < low.height) && (low = item);
      });

      let slope = this._slopeFromRadians(high, low);

      // 计算坡向：与正北向的夹角
      let vector1 = new Geowin3D.Cartesian2(0, 1);
      let vector2 = new Geowin3D.Cartesian2(low.longitude - high.longitude, low.latitude - high.latitude);

      let direction = this._angleFromVectors(vector1, vector2);
      direction < 0 && (direction += Math.PI * 2); // 保存坡向为正值

      this._slopes.push({ high, low, slope, direction });

      if (i < len - 1) {
        this._analysis(promises, i + 1, len)
      } else {
        this._event.raiseEvent('AnaFinished', {
          slopes: this._slopes,
          samples: this._samples,
          interpolations: this._interpolations
        });

        this._startDraw();
      }
    });
  }

  _startDraw() {
    let dataPoints = [];
    let max = undefined;
    let min = undefined;
    let rect = this._rectangle;

    let width = window.innerWidth;
    let height = window.innerHeight;

    let slopeLineInstances = [];
    
    this._slopes.forEach((item, i) => {
      (!min || item.slope < min) && (min = item.slope);
      (!max || item.slope > max) && (max = item.slope);

      let pos1 = item.high;
      let pos2 = item.low;

      let mid = {
        x: (pos1.longitude + pos2.longitude) / 2,
        y: (pos1.latitude + pos2.latitude) / 2,
      };

      let point = {
        x: Math.round((mid.x - rect.west) / (rect.east - rect.west) * width),
        y: Math.round((rect.north - mid.y) / (rect.north - rect.south) * height),
        value: item.slope
      };

      dataPoints.push(point);
      item.id = i;
      slopeLineInstances.push(this._getArrowInstance(pos1, pos2, i));
    });

    this._drawArrow(slopeLineInstances);

    this._drawHeatmap(width, height, {
      max: max,
      min: min,
      data: dataPoints
    });

    this._addLegend(min, max);
    
    setTimeout(() => {
      
      this._endDraw();
    }, 0);
  }

  setExcludeLayers(layers){
    this._excludeLayers=layers
  }

  // 获取单元格中的采样点（需要通过 sampleTerrainMostDetailed 获得采样点的高度值）
  // return {Promise<>}
  _getSamples(r1, r2, r3, r4) {
    let result = undefined;

    if (this._radiansInPolygon(r1, this._points) && this._radiansInPolygon(r2, this._points) && this._radiansInPolygon(r3, this._points) && this._radiansInPolygon(r4, this._points)) {
      // 单元格所有顶点都在多边形区域内
      let samplePoints = [];
      let samplePoints2 = [];

      let sLon = (r2.longitude - r1.longitude) / this._samples;
      let sLat = (r3.latitude - r2.latitude) / this._samples;

      for (let i = 0; i < this._samples; i++) {
        for (let j = 0; j < this._samples; j++) {
          samplePoints.push(Geowin3D.Cartographic.fromRadians(r4.longitude + sLon * j, r4.latitude + sLat * i));
          samplePoints2.push(Geowin3D.Cartesian3.fromRadians(r4.longitude + sLon * j, r4.latitude + sLat * i));

        }
      }
      // result = Geowin3D.sampleTerrainMostDetailed(this._viewer.terrainProvider, samplePoints);
      result = this._viewer.scene.clampToHeightMostDetailed(samplePoints2,this._excludeLayers)
    }
    return result;
  }

  _getArrowInstance(pos1, pos2, id) {
    let eLng = (pos2.longitude - pos1.longitude) / 4 + pos1.longitude;
    let eLat = (pos2.latitude - pos1.latitude) / 4 + pos1.latitude;

    return new Geowin3D.GeometryInstance({
      id: this.id + '__' + id,
      geometry: new Geowin3D.GroundPolylineGeometry({
        positions: Geowin3D.Cartesian3.fromRadiansArray([pos1.longitude, pos1.latitude, eLng, eLat]),
        width: this._arrowWidth
      })
    });
  }

  _drawArrow(slopeLineInstances) {
    this._arrowsLayer = new Geowin3D.GroundPolylinePrimitive({
      geometryInstances: slopeLineInstances,
      appearance: new Geowin3D.PolylineMaterialAppearance({
        translucent: true,
        material: new Geowin3D.Material({
          fabric: {
            type: 'PolylineArrow',
            uniforms: {
              color: this._arrowColor
            }
          }
        })
      }),
      show: this._showArrows,
      asynchronous: false,
      releaseMaterials: true,
      classificationType:Geowin3D.ClassificationType.BOTH,
      debugShowBoundingVolume:false
    });

    this._viewer.scene.primitives.add(this._arrowsLayer);
  }

  _drawHeatmap(width, height, data) {
    let heatmapContainer = document.createElement('div');
    heatmapContainer.setAttribute('class', 'heatmapContainer');
    heatmapContainer.style.visibility = 'hidden';
    heatmapContainer.style.width = width + 'px';
    heatmapContainer.style.height = height + 'px';

    document.body.appendChild(heatmapContainer);

    let heatmapInstance = h337.create({
      container: heatmapContainer,
      radius: this._mapRadius,
      maxOpacity: 0.7,
      gradient: this._mapGradient
    });

    heatmapInstance.setData(data);

    this._heatmapLayer = this._viewer.imageryLayers.addImageryProvider(new Geowin3D.SingleTileImageryProvider({
      url: heatmapInstance.getDataURL(),
      rectangle: this._rectangle
    }));
    this._heatmapLayer.clampTo3DTiles = true;
    this._heatmapLayer.show = this._showHeatmap;
    this._heatmapContainer = heatmapContainer;
  }

  /**
   * 添加图例
   * @private
   * @param {Number} min 最小坡度（弧度）
   * @param {Number} max 最大坡度（弧度）
   */
  _addLegend(min, max) {
    const el = document.createElement("div");
    el.setAttribute("class", "slope-legend");
    el.style.width = "20px";
    el.style.height = "100px"; // 高度
    el.style.position = "absolute";
    el.style.bottom = "6px";
    el.style.left = "6px";
    el.style.zIndex = 1000;

    el.style.display = this._showLegend ? "block" : "none";

    // 计算图例背景渐变色
    const colorData = [];

    for (let k in this._mapGradient) {
      if (this._mapGradient.hasOwnProperty(k)) {
        const GwColor = Geowin3D.Color.fromCssColorString(this._mapGradient[k]);
        colorData.push({
          name: Number(k) * 100,
          color: GwColor.withAlpha(0.7).toCssColorString()
        });
      }
    }

    // 需要按从小到大进行排序
    colorData.sort((a, b) => a.name - b.name);

    // 拼接颜色字符串
    const background = colorData.map(e => `${e.color} ${e.name}%`);

    // 线性渐变默认是从上到下，需要设置为从下到上
    el.style.background = `linear-gradient(to top, ${background.join(",")})`;

    // 鼠标移动到图例上时，显示鼠标指针所指向的颜色所对应的坡度值
    const tip = document.createElement("div");
    tip.setAttribute("class", "slope-legend_tip")
    tip.style.position = "absolute";
    tip.style.left = "24px";
    tip.style.top = "90px";
    tip.style.padding = "0 6px";
    tip.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
    tip.style.borderRadius = "2px";
    tip.style.lineHeight = "20px";
    tip.style.display = "none";
    tip.style.whiteSpace = "nowrap";
    el.appendChild(tip);

    // 显示坡度文字
    const text = document.createElement("span");
    text.setAttribute("class", "slope-legend_text");
    text.innerText = "0度";
    text.style.fontSize = "12px";
    text.style.color = "#fff";
    tip.appendChild(text);

    // 显示小箭头
    const arrow = document.createElement("div");
    arrow.setAttribute("class", "slope-legend_arrow")
    arrow.style.position = "absolute";
    arrow.style.width = "0";
    arrow.style.height = "0";
    arrow.style.top = "6px";
    arrow.style.left = "-8px";
    arrow.style.background = "transparent";
    arrow.style.borderTop = "4px solid transparent";
    arrow.style.borderLeft = "4px solid transparent";
    arrow.style.borderBottom = "4px solid transparent";
    arrow.style.borderRight = "4px solid rgba(0, 0, 0, 0.8)";
    tip.appendChild(arrow);

    // 计算每像素对应的坡度弧度值
    const unit = (max - min) / (100 - 0);

    el.addEventListener("mouseenter", e => {
      tip.style.display = "block";
    });
    el.addEventListener("mouseleave", e => {
      tip.style.display = "none";
    });
    el.addEventListener("mousemove", e => {
      if (e.target === el) {
        // 计算弧度，并转为角度
        const radian = (100 - e.offsetY) * unit + min;
        const degree = Math.round(radian * 180 / Math.PI);
        tip.style.top = e.offsetY - 10 + "px";
        text.innerText = degree + "度";
      }
    });

    this._viewer.container.appendChild(el);
    this._legend = el;
  }

  // 根据角度和判断点是否在多边形内部
  // 按照顺时针方向的角度为正，逆时针方向的角度为负，则：
  // 多边形内的点，与多边形每两个相邻端点之间的角度相加的和为 360 度或 -360 度
  // 多边形外的点，与多边形每两个相邻端点之间的角度相加的和为 0 度
  _radiansInPolygon(radians, points) {
    let angles = 0;

    for (let i = 0, len = points.length; i < len; i++) {
      let point1 = points[i];
      let point2 = i + 1 < len ? points[i + 1] : points[0];

      let lng = radians.longitude;
      let lat = radians.latitude;

      let radians1 = Geowin3D.Cartographic.fromCartesian(point1);
      let radians2 = Geowin3D.Cartographic.fromCartesian(point2);

      let vector1 = new Geowin3D.Cartesian2(radians1.longitude - lng, radians1.latitude - lat);
      let vector2 = new Geowin3D.Cartesian2(radians2.longitude - lng, radians2.latitude - lat);

      angles += this._angleFromVectors(vector1, vector2);
    }
    return Math.abs(Math.abs(angles) - Math.PI * 2) < 0.02 ? true : false;
  }

  // 根据两点的弧度计算坡度
  _slopeFromRadians(radians1, radians2) {
    let point1 = Geowin3D.Cartesian3.fromRadians(radians1.longitude, radians1.latitude, radians1.height);
    let point2 = Geowin3D.Cartesian3.fromRadians(radians2.longitude, radians2.latitude, radians2.height);

    let distance = Geowin3D.Cartesian3.distance(point1, point2);
    return Math.asin((radians1.height - radians2.height) / distance);
  }

  // 计算坡向（夹角）
  // 从向量1到向量2的夹角，顺时针为正，逆时针为负
  _angleFromVectors(vector1, vector2) {
    let result = Geowin3D.Cartesian2.angleBetween(vector1, vector2);

    let k1 = Math.atan(vector1.y / vector1.x);
    let k2 = Math.atan(vector2.y / vector2.x);

    if (vector1.x < 0) {
      k1 += Math.PI;
    } else if (vector1.x >= 0 && vector1.y < 0) {
      k1 += Math.PI * 2;
    }

    if (vector2.x < 0) {
      k2 += Math.PI;
    } else if (vector2.x >= 0 && vector2.y < 0) {
      k2 += Math.PI * 2;
    }

    if ((k1 > k2 && (k1 - k2 > Math.PI)) || (k1 < k2 && (k1 - k2 > -Math.PI))) {
      result *= -1;
    }
    return result;
  }

  // 屏幕坐标转换cartesian3
  _positionToCartesian3(position) {
    return Geowin3D.GwMisc.GwGeometric.pickPosition(this._viewer, position);
  }

  _reset (){
    this.removeAll();
    this._slopes.length = 0;
    this._rectangle = undefined;
    this._points = undefined;
    this._pmtAdd = false;

    if (this._legend) {
      this._legend.remove();
      this._legend = undefined;
    }
    this._viewer.scene.requestRender();
  }

  _removeHeatmap() {
    if (this._heatmapLayer) {
      this._viewer.imageryLayers.remove(this._heatmapLayer, true);
      this._heatmapLayer = undefined;
    }

    if (this._heatmapContainer) {
      this._heatmapContainer.remove();
      this._heatmapContainer = undefined;
    }
  }

  _removeArrows() {
    if (this._arrowsLayer) {
      this._viewer.scene.primitives.remove(this._arrowsLayer);
      this._arrowsLayer = undefined;
    }
  }

  _removeTooltip() {
    if (this._tooltip) {
      document.body.removeChild(this._tooltip);
      this._tooltip = undefined;
    }
  }
}

export default GwSlope2
