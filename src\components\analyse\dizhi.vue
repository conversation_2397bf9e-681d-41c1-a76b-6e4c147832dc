<template>
  <div class="dizhi">
    <div class="right_box1">
      <div class="box1_title">
        <img :src="titleImg" class="box_img" />
        <span>测量工具</span>
      </div>
      <ul class="right_box1_content">
        <li
          class="left_box1_btn"
          @click="changeCeLiang(index)"
          :class="toolData[index].active ? 'left_box1_li_active' : ''"
          v-for="(item, index) in toolData"
          :key="index"
        >
          <img :src="toolData[index].active?item.activeImg:item.img" class="left_box1_btn_img" />
          <div class="left_box1_btn_title">{{item.name }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef } from "vue";
import titleImg from "@/assets/img/home/<USER>";
import leftImg1 from "@/assets/img/analyse/1.png";
import leftImg2 from "@/assets/img/analyse/2.png";
import leftImg3 from "@/assets/img/analyse/3.png";
import leftImg4 from "@/assets/img/analyse/4.png";
import leftImg5 from "@/assets/img/analyse/5.png";
import leftImg6 from "@/assets/img/analyse/6.png";
import leftImgA1 from "@/assets/img/analyse/1-1.png";
import leftImgA2 from "@/assets/img/analyse/2-1.png";
import leftImgA3 from "@/assets/img/analyse/3-1.png";
import leftImgA4 from "@/assets/img/analyse/4-1.png";
import leftImgA5 from "@/assets/img/analyse/5-1.png";
import leftImgA6 from "@/assets/img/analyse/6-1.png";
import * as echarts from "echarts";
import Measure from "@/utils/map/mesuare";
import Buffer from "@/utils/map/buffer3";
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
//生命周期
onMounted(() => {
  let toolBox = document.getElementsByClassName("home_legend")[0];
  toolBox.style.right = "260px";
  toolBox.style.left = "";
});

onUnmounted(() => {
  if(measure){
    measure.removeAll();
  measure.cancel();
  } 
  removeTimeout()
});
let timer1
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  } 
}
// 按钮
let measure;
let toolData = ref([
  {
    name: "距离测量",
    img: leftImg1,
    activeImg:leftImgA1,
    active: false,
    addFun: function () {
      measure.removeAll();
      measure.cancel();
      measure.segmentsDistance2();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "高程测量",
    img: leftImg2,
    activeImg:leftImgA2,
    active: false,
    addFun: function () {
      measure.removeAll();
      measure.cancel();
      measure.elevation();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "面积测量",
    img: leftImg3,
    activeImg:leftImgA3,
    active: false,
    addFun: function () {
      measure.removeAll();
      measure.cancel();
      measure.area();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "角度测量",
    img: leftImg3,
    activeImg:leftImgA3,
    active: false,
    addFun: function () {
      measure.removeAll();
      measure.cancel();
      measure.angle();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "清除",
    img: leftImg6,
    activeImg:leftImgA6,
    active: false,
    addFun: function () {
      measure.removeAll();
      measure.cancel();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
]);

var celiangIndex=ref(-1)
const changeCeLiang = (item) => {
  if (item == toolData.value.length - 1) {
    for (let i in toolData.value) {
      try {
        toolData.value[i].delFun();
      } catch {}
      toolData.value[i].active = false;
    }
    viewer.scene.forceRender();
  } else {

    if(celiangIndex.value!=-1){
      toolData.value[celiangIndex.value].delFun();
      toolData.value[celiangIndex.value].active=false
    }

    // 切换选择状态
    toolData.value[item].active = !toolData.value[item].active;
    // 如果选中就执行功能
    if (toolData.value[item].active) {
      toolData.value[item].addFun();
    } else {
      try {
        toolData.value[item].delFun();
      } catch {}
      viewer.scene.forceRender();
    }
    // if (toolData.value[item].name == "模型恢复") {
    //   toolData.value[item].active = false;
    // }
  }
  celiangIndex.value=item
};
timer1=setTimeout(() => {
  measure = new Measure(viewer);
}, 1000);


</script>
<style lang="less" scoped>
.dizhi {
  width: 240px;
  // height: 320px;
  display: flex;
  justify-content: space-between;
  position: relative;
  flex-direction: column;
  background: rgba(11, 24, 36,.7);
  // background-image: url("../../assets/img/home/<USER>");
  // background-position: center center;
  // background-size: 100% 100%;
  // background-repeat: no-repeat;
  // padding-left: 18px;
  .right_box1 {
    width: 100%;
    flex: 1;
    box-sizing: border-box;
    padding: 0px 22px 0 0px;
    display: flex;
    flex-direction: column;
    .right_box1_content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px 0;
      box-sizing: border-box;
      align-items: center;

      .left_box1_btn {
        width: 193px;
          height:54px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        margin-top: 20px;
        font-family: Source Han Sans SC;
        font-weight: 800;
        color: #ffffff;
        // background-image: url("../../assets/img/tunnel/btn.png");
        // background-position: center center;
        // background-size: 100% 100%;
        // background-repeat: no-repeat;
        cursor: pointer;
        .left_box1_btn_img{
          width: 53px;
        }
        .left_box1_btn_title{
          width: 129px;
            height: 38px;
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            background: rgba(54, 175, 255, 0.32);
            border-radius: 6px;
        }
        &:hover {
          transform: scale(1.1);
        }
      }

      .left_box1_li_active {
        .left_box1_btn_title{
          background: rgba(255, 242, 118, 0.32);
        }
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>
