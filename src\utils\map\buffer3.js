/*
 * @Description: 缓冲区分析
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2022-11-11 09:16:38
 * @LastEditors: wbw
 * @LastEditTime: 2022-12-01 17:39:46
 */

import {
  ref,
  toRaw
} from "vue";

class Buffer {
  constructor(_viewer, _bufferShow, _options) {
    this.viewer = _viewer;
    this.options = _options;
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.bufferShow = _bufferShow;
    this.init();
  }

  init() {
    if (this.options.color == undefined) {
      this.r = 1;
      this.g = 1;
      this.b = 0;
      this.a = 0.5;
    } else {
      this.r = this.options.color[0];
      this.g = this.options.color[1];
      this.b = this.options.color[2];
      this.a = this.options.color[3];
    }
    if (this.options.radius == undefined) {
      this.radius = 100;
    } else {
      this.radius = this.options.radius;
    }
    this.butterAnalyseFlag = false;
    this.butterDataList = ref([])
  }

  setRadius(radius) {
    this.radius = radius;
    this.clear();
    this.addPrimitive();
    if (this.butterAnalyseFlag) {
      this._butterAnalyse();
    }
    this.viewer.scene.forceRender();
  }

  setColor(color) {
    this.r = color[0];
    this.g = color[1];
    this.b = color[2];
    this.a = color[3];
    this.clear();
    this.addPrimitive();
    this.viewer.scene.forceRender();
  }

  //添加
  add() {
    this.init();
    this.handler.setInputAction((movement) => {
      let pick = viewer.scene.pickPosition(movement.position);
      this.position = this._degreeArrayFromCartesian(pick);
      this.addPrimitive();
      this.bufferShow.value = true;
      this.viewer.scene.forceRender();
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  //添加
  add2(func, dataList) {
    this.dataList = dataList;
    this.init();
    this.handler.setInputAction((movement) => {
      let pick = viewer.scene.pickPosition(movement.position);
      this.position = this._degreeArrayFromCartesian(pick);
      func();
      this.clear();
      this.addPrimitive();
      this.bufferShow.value = true;
      this._initButterDataList()
      this._butterAnalyse();
      this.viewer.scene.forceRender();
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  add3(dataList, type) {
    this.dataList = dataList;
    this.pointType = type
    this.init();
    this.butterAnalyseFlag = true
    this.handler.setInputAction((movement) => {
      let pick = viewer.scene.pickPosition(movement.position);
      this.position = this._degreeArrayFromCartesian(pick);
      this.clear();
      this.addPrimitive();
      this.bufferShow.value = true;
      this._initButterDataList()
      this._butterAnalyse()
      this.viewer.scene.forceRender();
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  addPrimitive() {
    let ellipsoid = new Cesium.EllipsoidGeometry({
      vertexFormat: Cesium.VertexFormat.POSITION_ONLY,
      radii: new Cesium.Cartesian3(this.radius, this.radius, this.radius),
    });
    let ellipsoidInstance = new Cesium.GeometryInstance({
      geometry: ellipsoid,
      modelMatrix: Cesium.Matrix4.multiplyByTranslation(
        Cesium.Transforms.eastNorthUpToFixedFrame(
          Cesium.Cartesian3.fromDegrees(this.position[0], this.position[1])
        ),
        new Cesium.Cartesian3(0.0, 0.0, this.position[2]),
        new Cesium.Matrix4()
      ),
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(
          new Cesium.Color(this.r, this.g, this.b, this.a)
        ),
      },
    });
    this.primitive = new Cesium.Primitive({
      releaseGeometryInstances: false,
      geometryInstances: [ellipsoidInstance],
      asynchronous: false,
      appearance: new Cesium.PerInstanceColorAppearance({
        flat: true,
      }),
    });
    this.viewer.scene.primitives.add(this.primitive);
  }

  //清除
  clear() {
    this.viewer.scene.primitives.remove(this.primitive);
  }
  //取消操作
  cancel() {
    this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  //获取风险点数据
  getButterData(label) {
    this.butterLabel = label;
    this.butterAnalyseFlag = true;
  }

  //地质分析界面
  _initButterDataList() {
    this.butterDataList.value = []
    var position = new Cesium.Cartesian3.fromDegrees(...this.position);
    switch (this.butterLabel) {
      case "异常区":
        var ycq = window.ycq;
        for (var i = 0; i < ycq.anomalousArea.value.length; i++) {
          var distance = Cesium.Cartesian3.distance(
            position,
            toRaw(ycq.anomalousArea.value[i].centre)
          );
          this.butterDataList.value.push({
            label: ycq.anomalousArea.value[i].title,
            distance: distance,
            position: toRaw(ycq.anomalousArea.value[i].centre)
          })
        }
        break;
      case "断层":
        var dc = window.dc;
        for (var i = 0; i < dc.DCMList.value.length; i++) {
          var distance = Cesium.Cartesian3.distance(
            position,
            toRaw(dc.DCMList.value[i].position)
          );
          this.butterDataList.value.push({
            label: dc.DCMList.value[i].title,
            distance: distance,
            position: toRaw(dc.DCMList.value[i].position)
          })
        }
        break;
      case "异常区和断层":
        var dc = window.dc;
        for (var i = 0; i < dc.DCMList.value.length; i++) {
          var distance = Cesium.Cartesian3.distance(
            position,
            toRaw(dc.DCMList.value[i].position)
          );
          this.butterDataList.value.push({
            label: dc.DCMList.value[i].title,
            distance: distance,
            position: toRaw(dc.DCMList.value[i].position)
          })
        }
        var ycq = window.ycq;
        for (var i = 0; i < ycq.anomalousArea.value.length; i++) {
          var distance = Cesium.Cartesian3.distance(
            position,
            toRaw(ycq.anomalousArea.value[i].centre)
          );
          this.butterDataList.value.push({
            label: ycq.anomalousArea.value[i].title,
            distance: distance,
            position: toRaw(ycq.anomalousArea.value[i].centre)
          })
        }
        break;
      case "实时监测":
        var monitoringPoint = window.monitoringPoint
        var monitoringPointDataList = monitoringPoint.dataList.value[this.pointType]
        for (var i = 0; i < monitoringPointDataList.length; i++) {
          var distance = Cesium.Cartesian3.distance(
            toRaw(monitoringPointDataList[i].position), position
          )
          this.butterDataList.value.push(monitoringPointDataList[i])
          this.butterDataList.value[i]['distance'] = distance
        }
        // console.log(this.butterDataList.value)
        break
    }
  }
  //地质分析界面
  _butterAnalyse() {
    this.dataList.value = [];
    if (this.butterAnalyseFlag) {
      switch (this.butterLabel) {
        case "异常区":
          for (var i = 0; i < this.butterDataList.value.length; i++) {
            var distance = this.butterDataList.value[i].distance
            if (this.radius > distance) {
              this.dataList.value.push({
                label: this.butterDataList.value[i].label,
                distance: this.butterDataList.value[i].distance.toFixed(2),
                position: this.butterDataList.value[i].position,
              })
            }
          }
          break;
        case "断层":
          for (var i = 0; i < this.butterDataList.value.length; i++) {
            var distance = this.butterDataList.value[i].distance
            if (this.radius > distance) {
              this.dataList.value.push({
                label: this.butterDataList.value[i].label,
                distance: this.butterDataList.value[i].distance.toFixed(2),
                position: this.butterDataList.value[i].position,
              })
            }
          }
          break;
        case "异常区和断层":
          for (var i = 0; i < this.butterDataList.value.length; i++) {
            var distance = this.butterDataList.value[i].distance
            if (this.radius > distance) {
              this.dataList.value.push({
                label: this.butterDataList.value[i].label,
                distance: this.butterDataList.value[i].distance.toFixed(2),
                position: this.butterDataList.value[i].position,
              })
            }
          }
          break;
        case "实时监测":
          for (var i = 0; i < this.butterDataList.value.length; i++) {
            var distance = this.butterDataList.value[i].distance
            if (this.radius > distance) {
              this.dataList.value.push(this.butterDataList.value[i])
            }
          }
          break
      }
    }
  }

  _degreeArrayFromCartesian(cartesian) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return [lon, lat, rad.height];
  }
}
export default Buffer;