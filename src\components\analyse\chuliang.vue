<template>
  <div class="chuliang">
    <div class="right_box2">
      <div class="box1_title">
        <img :src="titleImg" class="box_img" />
        <span>储量计算</span>
      </div>
      <div class="left_box2_content2">
        <div class="left_box2_content2_input">
          <div class="left_box2_content2_title">平均煤厚(m):</div>
          <el-input v-model="value1" type="number" />
        </div>
        <div class="left_box2_content2_input">
          <div class="left_box2_content2_title">容重(t/m³):</div>
          <el-input v-model="value2" type="number" />
        </div>
        <div class="left_box2_content2_btn_box">
          <div class="left_box2_content2_btn" @click="polygon">绘制投影面</div>
          <div class="left_box2_content2_btn" @click="clear">清除</div>
        </div>
      </div>
    </div>
    <div class="right_box1">
      <div class="box1_title">
        <img :src="titleImg" class="box_img" />
        <span>属性展示窗口</span>
      </div>
      <ul class="right_ul">
        <li class="right_li">
          <span>投影面积(m²)：</span>{{value3}}
        </li>
        <li class="right_li">
          <span>体积(m³)：</span>{{value4}}
        </li>
        <li class="right_li">
          <span>资源量(t)：</span>{{value5}}
        </li>
      </ul>
    </div>

  </div>
</template>
    
<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { computed, ref, toRef } from "vue";
import titleImg from "@/assets/img/home/<USER>";
import * as echarts from "echarts";
import Measure from "@/utils/map/mesuare";
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);

//生命周期
onMounted(() => {
  let toolBox = document.getElementsByClassName('home_legend')[0]
  toolBox.style.right = '270px'
  toolBox.style.left = ''
});

onUnmounted(() => {
  if(measure){
    measure.removeAll();
  measure.cancel();
  }
  removeTimeout()
})
let timer1
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  } 
}

// 储量
const value1 = ref(1)
const value2 = ref(1)
const value3 = ref(0)
const value4 = computed(() => {
  return (Number(value2.value) * Number(value3.value)).toFixed(2)
})
const value5 = computed(() => {
  return (Number(value4.value) * Number(value1.value)).toFixed(2)
})


let measure;
timer1=setTimeout(() => {
  measure = new Measure(viewer);
}, 1000)
const polygon = () => {
  measure.removeAll();
  measure.area();
  measure.on(getArea)
}
const getArea = () => {
  try {
    var text = measure.getData().area[measure.getData().area.length - 1].text
    if (text.indexOf('平方米') > -1) {
      value3.value = measure.getData().area[measure.getData().area.length - 1].area
    }
    if (text.indexOf('平方公里') > -1) {
      value3.value = measure.getData().area[measure.getData().area.length - 1].area * 1000000
    }

  } catch {
    value3.value = 0
  }
  //console.log(measure.getData())
}

const clear = () => {
  value1.value = 1
  value2.value = 1
  value3.value = 0
  measure.removeAll();
  measure.cancel();
}




</script>
<style lang="less" scoped>
.chuliang {
  width: 260px;
  // height: 100%;
  display: flex;
  justify-content: flex-start;
  position: relative;
  flex-direction: column;
  // background: rgba(11, 24, 36,.7);
  background-image: url("../../assets/img/home/<USER>");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 18px;
  .right_box1 {
    margin-top: 30px;
    width: 100%;
    height: 240px;
    box-sizing: border-box;
    padding: 0px 12px 0 0px;
    display: flex;
    flex-direction: column;
    .right_ul {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 20px 10px 0 0px;
      padding-left: 10px;
      .right_li {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #EBEBEB;
        line-height: 40px;
        display: flex;

        span {
          color: #959ABB;
          display: block;
          width: 210px;
        }
      }
    }
  }

  .right_box2 {
    width: 100%;
    height: 300px;
    box-sizing: border-box;
    padding: 0px 0 10px 0px;
    display: flex;
    flex-direction: column;
    .left_box2_content2 {
      font-size: 14px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #00f0e8;
      margin-top: 40px;
      padding-right: 20px;
      padding-left: 10px;

      .left_box2_content2_title {
        font-size: 16px;
        color: #fff;
        margin-right: 20px;
        width: 200px;
      }

      .left_box2_content2_input {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        width: 100%;
        justify-content: space-between;

        :deep(input) {
          font-size: 18px;
        }

        span {
          font-size: 24px;
          margin-left: 10px;
        }
      }

      .left_box2_content2_btn_box {
        width: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;

        .left_box2_content2_btn {
          cursor: pointer;
          width: 100px;
          height: 32px;
          background: #03afa3;
          // border: 1px solid #255990;
          border-radius: 6px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;

          &:hover {
            filter: brightness(1.1);
          }
        }
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>
    