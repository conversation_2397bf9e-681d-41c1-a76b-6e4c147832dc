import allLines from './connectedGraph'

export class urgentManageSchedulingLine {
	constructor(viewer) {
		this.viewer = viewer;
		this.callback = viewer.callback;
		this.pathC = null;
		this.firePointC = null;
	}
	//清除
	clearUMSLC() {
		if (this.updatEvent) {
			this.updatEvent()
			this.updatEvent = null
		}
		
		if (this.pathC) {
			this.viewer.entities.remove(this.pathC)
			console.log('REMOVE',this.pathC);
			this.pathC = null
		}
	}
	//这里是视角移动到事故发生点
	drawWarnPoint(lng, lat, h) {
		this.$viewer.camera.setView({
			destination: Cesium.Cartesian3.fromDegrees(lng, lat - 0.013, 3000.0),
			orientation: {
				heading: Cesium.Math.toRadians(0.0), // east, default value is 0.0 (north)
				pitch: Cesium.Math.toRadians(-45), // default value (looking down)
				roll: 0.0 // default value
			}
		})
	}
	//初步划线
	drawLineC() {
		if (this.pathC) {
			this.clearUMSLC()
			return
		}
		var result = this.getShortestRoad(1, 10, allLines)
		return result;
	}
	drawLineC2(startPosition,endPosition,isDraw) {
		if (this.pathC) {
			this.clearUMSLC()
			return
		}
		let startID = this.getStartPoint(startPosition,allLines);
		let endID = this.getStartPoint(endPosition,allLines);
		
		let result = this.getShortestRoad(startID, endID, allLines)
		console.log(result);
		if(isDraw){
			this.drawPath(result, allLines)
		}
		this.drawPath(result, allLines)
		return result;
	}
	//获取点位的函数
	getStartPoint(startPosition, roads) {
		let shortDistance = Infinity
		let beginPoint = 0
		roads.points.forEach((item, index) => {
			const point = item
			const curDistance = this.getDistanceByPointToPoint_Coordinate(startPosition, point)
			if (curDistance < shortDistance) {
				beginPoint = index
				shortDistance = curDistance
			}
		})
		// console.log(beginPoint)
		return beginPoint
	}
	/**
	 * 计算最短路径
	 * @param startPosition 起点坐标
	 * @param endPoint 出口点索引
	 * @param roads 点、线信息
	 * @returns {{distance: *, roads: *, points: string[]}}
	 */
	//路径算法
	getShortestRoad(beginPoint, endPoint, roads) {
		const graph = {}
		roads.lines.forEach((item, index) => {
			if (item.open !== 1) {
				return
			}
			const sp = item.points[0]
			const ep = item.points[item.points.length - 1]
			if (!graph[sp]) {
				graph[sp] = {}
			}
			if (!graph[ep]) {
				graph[ep] = {}
			}

			if (item.direction === 0) {
				graph[sp][ep] = [item.distance, index]
				graph[ep][sp] = [item.distance, index]
			} else if (item.direction === 1) {
				graph[sp][ep] = [item.distance, index]
			} else if (item.direction === 2) {
				graph[ep][sp] = [item.distance, index]
			}
		})
		const sp = beginPoint.toString()
		const ep = endPoint.toString()
		const costs = {}
		const parents = {}
		for (const i in graph) {
			if (i !== sp) {
				costs[i] = [Infinity, '']
				parents[i] = null
			}
		}
		for (const j in graph[sp]) {
			costs[j] = graph[sp][j]
			parents[j] = sp
		}
		// 计算路径
		const processed = []

		function findLowestCostNode() {
			const lowestCost = [Infinity, '']
			let lowestCostNode = null
			for (const i in costs) {
				if (costs[i][0] <= lowestCost[0] && processed.indexOf(i) < 0) {
					lowestCost[0] = costs[i][0]
					lowestCostNode = i
				}
			}
			return lowestCostNode
		}

		function dijkstra() {
			const node = findLowestCostNode()
			if (!node) {
				return
			}
			const cost = costs[node] // 记录起点到当前节点的开销
			const neiber = graph[node]
			for (const i in neiber) {
				const newCost = [neiber[i][0] + cost[0]].concat(cost.slice(1).concat(neiber[i].slice(1)))
				if (!costs[i]) {
					continue
				}

				if (costs[i][0] > newCost[0]) {
					costs[i] = newCost
					parents[i] = node
				}
			}
			processed.push(node)
			dijkstra()
		}
		dijkstra()
		const arr = [ep]

		function printPath(node) {
			if (parents[node] !== undefined) {
				arr.unshift(parents[node])
				printPath(parents[node])
			}
		}
		printPath(ep)
		const points = []
		for (let i = 1; i < costs[ep].length; ++i) {
			const line = roads.lines[costs[ep][i]]
			if (!line) {
				continue
			}
			if (i === 1) {
				if (parseInt(arr[i - 1]) === line.points[0]) {
					points.push.apply(points, line.points)
				} else if (parseInt(arr[i]) === line.points[0]) {
					points.push.apply(points, line.points.slice(0).reverse())
				}
			} else {
				if (parseInt(arr[i - 1]) === line.points[0]) {
					points.push.apply(points, line.points.slice(1))
				} else if (parseInt(arr[i]) === line.points[0]) {
					points.push.apply(points, line.points.slice(0, -1).reverse())
				}
			}
		}
		// 返回最短路径
		const result = {
			points: points,
			distance: costs[ep].shift(),
			roads: costs[ep]
		}
		if (result.points.length === 0) return false
		console.log(result + "---------dji")
		return result
	}
	createLineEntity(shortRoad, roads) {
		var positionList = []

		for (var i = 0; i < shortRoad.length; i = i + 1) {
			positionList.push({
				x: roads[i * 3],
				y: allLines.points[i * 3 + 1],
				z: allLines.points[i * 3 + 2]
			})
		}
		this.lineEntity = this.viewer.entities.add({
			polyline: {
				positions: positionList,
				width: 2,
				material: Cesium.Color.YELLOW,
				depthFailMaterial: Cesium.Color.YELLOW,
				clampToGround: true,
				classificationType: Cesium.ClassificationType.BOTH,
			}
		})
	}


	starPositions(arms, rOuter, rInner) {
		const angle = Math.PI / arms;
		const pos = [];
		for (let i = 0; i < 2 * arms; i++) {
			const r = i % 2 === 0 ? rOuter : rInner;
			const p = new Cesium.Cartesian2(
				Math.cos(i * angle) * r,
				Math.sin(i * angle) * r
			);
			pos.push(p);
		}
		return pos;
	}

	//进一步划线
	drawPath(shortRoad, roads) {
		const points = shortRoad.points.map(item => {
			return [roads.points[item][0], roads.points[item][1], roads.points[item][2] + 10]
		})
		var positionList = []
		var x=116.94672164369393-116.94666487131315
		var y=34.837594812625326 -34.837639711087185
		var z=1511.5414834904009-1512.2404698442133

		for (var i = 0; i < points.length; i = i + 1) {
			positionList.push(new Cesium.Cartesian3.fromDegrees(points[i][0]+x,points[i][1]+y,points[i][2]+1900+z));
		}
		if (this.pathC) {
			this.viewer.entities.remove(this.pathC)
			console.log('REMOVE',this.pathC);
			this.pathC = null
		}

		this.pathC = this.viewer.entities.add({
			polyline: {
				positions: positionList,
				width: 3,
				material: Geowin3D.Color.RED,
				depthFailMaterial: Geowin3D.Color.RED,
				clampToGround: false, //折线固定在地面
				arcType: Geowin3D.ArcType.GEODESIC, //定义连接点采用的路径
			},
		});
		console.log('this.pathC',this.pathC)
		this.viewer.trackedEntity = this.pathC;
	}
	
	//两转换函数 还没使用
	getDistanceByPointToLine_Coordinate(point, pnt1, pnt2) {
		if (pnt1.length < 2 || pnt1.length < 2 || point.length < 2) {
			return -1
		}
		let dis = 0
		if (pnt1[0] === pnt2[0]) {
			if (pnt1[1] === pnt2[1]) {
				const dx = point[0] - pnt1[0]
				const dy = point[1] - pnt1[1]
				dis = Math.sqrt(dx * dx + dy * dy)
			} else {
				dis = Math.abs(point[0] - pnt1[0])
			}
		} else {
			const lineK = (pnt2[1] - pnt1[1]) / (pnt2[0] - pnt1[0])
			const lineC = (pnt2[0] * pnt1[1] - pnt1[0] * pnt2[1]) / (pnt2[0] - pnt1[0])
			dis = Math.abs(lineK * point[0] - point[1] + lineC) / (Math.sqrt(lineK * lineK + 1))
		}
		return dis
	}
	//转换函数
	getDistanceByPointToPoint_Coordinate(pnt1, pnt2) {
		if (pnt1.length < 2 || pnt2.length < 2) {
			return -1
		}
		let dis = 0
		const dx = pnt1[0] - pnt2[0]
		const dy = pnt1[1] - pnt2[1]
		dis = Math.sqrt(dx * dx + dy * dy)
		return dis
	}


}
