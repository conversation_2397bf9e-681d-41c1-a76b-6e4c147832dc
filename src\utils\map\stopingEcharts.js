 //myChartData用于显示数据
 //myChartData2用于显示y轴
 //myChartData3用于显示单位和图例
 var myChartData = {
   // title: {
   //   text: "单位(m)",
   //   textStyle: {
   //     color: "#FFFFFF",
   //     fontSize: 14,
   //   },
   // },
   textStyle: {
     color: "#FFFFFF",
     fontSize: 14,
   },
   tooltip: {
     trigger: "axis",
   },
   boundaryGap: [0, 0.2],
   color: ["#0DD6E3", "#FFD200"],
   // legend: {
   //   data: ["底板高度", "顶板高度"],
   //   left: 'right',
   //   textStyle: {
   //     color: "#FFFFFF",
   //     fontSize: 12,
   //   },
   //   right: 20,
   // },
   grid: {
     left: "2%",
     right: "3%",
     bottom: "4%",
     top: '15%',
     containLabel: true,
   },
   xAxis: {
     type: "category",
     boundaryGap: true,
     // position: 'top',
     min: function (value) {

       return value.min;
     },
     max: function (value) {
       return value.max
     },
     interval: 25,
     data: ["0", "0", "0", "0", "0"],
     axisLine: {
       lineStyle: {
         color: "#0DD6E3",
       },
     },
     axisLabel: {
       // interval:0,
       // rotate:"5",
       // align:'',
       overflow: "breakAll",
     },
   },
   yAxis: {
     type: "value",
     axisLine: {
       show: false, //隐藏y轴
     },
     axisLabel: {
       show: false, //隐藏刻度值
     },
     min: function (value) {
       var min = (Math.ceil(value.min / 10) - 1) * 10
       return min;
     },
     max: function (value) {
       var max = Math.ceil(value.max / 10) * 10
       return max
     },
     scale: false,
     interval: 10,
     splitLine: {
       lineStyle: {
         color: "#0DD6E3",
         type: "dashed",
       },
     },
   },
   series: [{
       name: "顶板高度",
       type: "line",
       data: [0, 0, 0, 0, 0],
     },
     {
       name: "底板高度",
       type: "line",
       data: [0, 0, 0, 0, 0],
     }
   ],
 }
 var myChartData2 = {
   // title: {
   //   text: "单位\n(m)",
   //   textStyle: {
   //     color: "#FFFFFF",
   //     fontSize: 10,
   //   },
   // },
   textStyle: {
     color: "#FFFFFF",
     fontSize: 14,
   },
   tooltip: {
     trigger: "axis",
   },
   boundaryGap: [0, 0.2],
   color: ["#0DD6E3", "#FFD200"],
   legend: {
     data: ["底板高度", "顶板高度"],
     left: 'right',
     textStyle: {
       color: "#FFFFFF",
       fontSize: 12,
     },
     right: 20,
   },
   grid: {
     left: "2%",
     right: "3%",
     bottom: "4%",
     top: '15%',
     containLabel: true,
   },
   xAxis: {
     type: "category",
     boundaryGap: true,
     // position: 'top',
     min: function (value) {

       return value.min;
     },
     max: function (value) {
       return value.max
     },
     interval: 25,
     data: ["0", "0", "0", "0", "0"],
     axisLine: {
       lineStyle: {
         color: "#0DD6E3",
       },
     },
     axisLabel: {
       // interval:0,
       // rotate:"5",
       // align:'',
       overflow: "breakAll",
     },
   },
   yAxis: {
     type: "value",
     // axisLine: {
     //           show: false, //隐藏y轴
     //         },
     //         axisLabel: {
     //           show: false, //隐藏刻度值
     //         },
     min: function (value) {
       var min = (Math.ceil(value.min / 10) - 1) * 10
       return min;
     },
     max: function (value) {
       var max = Math.ceil(value.max / 10) * 10
       return max
     },
     scale: false,
     interval: 10,
     splitLine: {
       lineStyle: {
         color: "#0DD6E3",
         type: "dashed",
       },
     },
   },
   series: [{
       name: "顶板高度",
       type: "line",
       data: [0, 0, 0, 0, 0],
     },
     {
       name: "底板高度",
       type: "line",
       data: [0, 0, 0, 0, 0],
     }
   ],
 }
 var myChartData3 = {
   title: {
     text: "单位(m)",
     textStyle: {
       color: "#FFFFFF",
       fontSize: 12,
     },
   },
   textStyle: {
     color: "#FFFFFF",
     fontSize: 14,
   },
   tooltip: {
     trigger: "axis",
   },
   boundaryGap: [0, 0.2],
   color: ["#0DD6E3", "#FFD200"],
   legend: {
     data: ["底板高度", "顶板高度"],
     left: 'right',
     textStyle: {
       color: "#FFFFFF",
       fontSize: 12,
     },
     right: 20,
   },
   grid: {
     left: "2%",
     right: "3%",
     bottom: "4%",
     top: '15%',
     containLabel: true,
   },
   xAxis: {
     type: "category",
     boundaryGap: true,
     // position: 'top',
     min: function (value) {

       return value.min;
     },
     max: function (value) {
       return value.max
     },
     interval: 25,
     data: ["0", "0", "0", "0", "0"],
     axisLine: {
       lineStyle: {
         color: "#0DD6E3",
       },
     },
     axisLabel: {
       // interval:0,
       // rotate:"5",
       // align:'',
       overflow: "breakAll",
     },
   },
   yAxis: {
     type: "value",
     // axisLine: {
     //           show: false, //隐藏y轴
     //         },
     //         axisLabel: {
     //           show: false, //隐藏刻度值
     //         },
     min: function (value) {
       var min = (Math.ceil(value.min / 10) - 1) * 10
       return min;
     },
     max: function (value) {
       var max = Math.ceil(value.max / 10) * 10
       return max
     },
     scale: false,
     interval: 10,
     splitLine: {
       lineStyle: {
         color: "#0DD6E3",
         type: "dashed",
       },
     },
   },
   series: [{
       name: "顶板高度",
       type: "line",
       data: [0, 0, 0, 0, 0],
     },
     {
       name: "底板高度",
       type: "line",
       data: [0, 0, 0, 0, 0],
     }
   ],
 }
 export {
   myChartData,
   myChartData2,
   myChartData3
 };