/**
 * geowin3d兼容性层
 * 提供原有geowin3d功能的兼容性实现
 */
import * as Cesium from 'cesium'

// 模拟的LayerManager类
class MockLayerManager {
  constructor() {
    this.layers = new Map()
    this.layerCounter = 0
  }

  addLayer(label, layer) {
    const id = `layer_${this.layerCounter++}`
    this.layers.set(id, {
      id,
      label,
      layer,
      visible: true
    })
    return id
  }

  getIdByLabel(label) {
    for (const [id, layerInfo] of this.layers) {
      if (layerInfo.label === label) {
        return id
      }
    }
    return null
  }

  getLayer(id) {
    const layerInfo = this.layers.get(id)
    return layerInfo ? layerInfo.layer : null
  }

  setLayerVisible(id, visible) {
    const layerInfo = this.layers.get(id)
    if (layerInfo) {
      layerInfo.visible = visible
      if (layerInfo.layer.show !== undefined) {
        layerInfo.layer.show = visible
      }
    }
  }

  removeLayer(id) {
    this.layers.delete(id)
  }

  // 兼容原有接口
  [Symbol.iterator]() {
    return this.layers.values()
  }
}

// 模拟的Earth类
class MockEarth {
  constructor(viewer) {
    this.viewer = viewer
    this.layerManager = new MockLayerManager()
  }

  getCameraInfo() {
    if (!this.viewer || !this.viewer.camera) {
      return {
        lontitude: 0,
        latitude: 0,
        height: 0,
        heading: 0,
        pitch: 0,
        roll: 0
      }
    }

    const camera = this.viewer.camera
    const position = camera.position
    const cartographic = Cesium.Cartographic.fromCartesian(position)

    return {
      lontitude: Cesium.Math.toDegrees(cartographic.longitude),
      latitude: Cesium.Math.toDegrees(cartographic.latitude),
      height: cartographic.height,
      heading: Cesium.Math.toDegrees(camera.heading),
      pitch: Cesium.Math.toDegrees(camera.pitch),
      roll: Cesium.Math.toDegrees(camera.roll)
    }
  }

  flyTo(target, options = {}) {
    if (this.viewer) {
      return this.viewer.flyTo(target, options)
    }
  }

  zoomTo(target, options = {}) {
    if (this.viewer) {
      return this.viewer.zoomTo(target, options)
    }
  }
}

// 创建基础的Cesium viewer
function createBasicViewer(containerId) {
  const viewer = new Cesium.Viewer(containerId, {
    animation: false,
    baseLayerPicker: false,
    fullscreenButton: false,
    geocoder: false,
    homeButton: false,
    infoBox: false,
    sceneModePicker: false,
    selectionIndicator: false,
    timeline: false,
    navigationHelpButton: false,
    scene3DOnly: true,
    shouldAnimate: true
  })

  // 禁用默认的双击行为
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)
  
  // 设置地球参数
  viewer.scene.globe.enableLighting = false
  viewer.scene.globe.depthTestAgainstTerrain = true
  
  // 设置相机参数
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 1.0
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 50000000.0

  return viewer
}

// 初始化兼容性层
export function initCompatibility() {
  // 如果已经有viewer，不重复创建
  if (window.viewer) {
    console.log('Viewer已存在，跳过初始化')
    return
  }

  try {
    // 创建viewer
    const viewer = createBasicViewer('cesiumContainer')
    
    // 创建earth对象
    const earth = new MockEarth(viewer)
    
    // 创建layersManager
    const layersManager = earth.layerManager
    
    // 设置全局变量
    window.viewer = viewer
    window.earth = earth
    window.layersManager = layersManager
    window.Cesium = Cesium
    
    console.log('兼容性层初始化完成')
    
    return {
      viewer,
      earth,
      layersManager
    }
  } catch (error) {
    console.error('兼容性层初始化失败:', error)
    
    // 创建空的兼容对象，防止错误
    window.viewer = {
      camera: {
        position: new Cesium.Cartesian3(0, 0, 0),
        heading: 0,
        pitch: 0,
        roll: 0,
        setView: () => {},
        pickEllipsoid: () => null
      },
      scene: {
        camera: {
          heading: 0,
          pitch: 0,
          roll: 0
        },
        forceRender: () => {},
        globe: {
          ellipsoid: Cesium.Ellipsoid.WGS84
        },
        pick: () => null,
        pickFromRay: () => null,
        drillPickFromRay: () => []
      },
      entities: {
        add: () => {},
        remove: () => {},
        removeAll: () => {}
      },
      flyTo: () => Promise.resolve(),
      zoomTo: () => Promise.resolve()
    }
    
    window.earth = {
      getCameraInfo: () => ({
        lontitude: 0,
        latitude: 0,
        height: 0,
        heading: 0,
        pitch: 0,
        roll: 0
      }),
      layerManager: new MockLayerManager()
    }
    
    window.layersManager = window.earth.layerManager
    window.Cesium = Cesium
  }
}

// 等待DOM加载完成后初始化
export function waitForContainerAndInit() {
  return new Promise((resolve) => {
    const checkContainer = () => {
      const container = document.getElementById('cesiumContainer')
      if (container) {
        const result = initCompatibility()
        resolve(result)
      } else {
        // 如果容器不存在，创建一个隐藏的容器
        const hiddenContainer = document.createElement('div')
        hiddenContainer.id = 'cesiumContainer'
        hiddenContainer.style.display = 'none'
        document.body.appendChild(hiddenContainer)
        
        const result = initCompatibility()
        resolve(result)
      }
    }
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', checkContainer)
    } else {
      checkContainer()
    }
  })
}

export { MockEarth, MockLayerManager }
