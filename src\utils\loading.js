/**
 * 使用案例：
 * import withLoading from './loading'
 * 将await queryXXXX(params)变成下方写法
 * await withLoading(queryXXXX)(params)
 */

import {
  ElLoading
} from 'element-plus';
// 定义一个默认配置
const defaultOptions = {
  body: true,
  lock: true, // 是否锁屏
  text: '加载中',
  background: 'rgba(0, 0, 0, 0.7)'
}
/**
 * 传入一个方法fn,在它执行周期内,加上全屏loading
 * 如果：
 * 1. fn是同步方法，结束后隐藏loading
 * 2. 如果是异步方法，resolve后隐藏loading
 * 3. 报错后隐藏loading并抛出错误
 * @param {*} fn 函数
 * @returns Function 一个新的函数，去执行它吧
 */

export const withLoading = (fn, options = {}) => {
  let loading;
  const showLoading = () => {
    loading = ElLoading.service()
  }
  const hideLoading = () => {
    if (loading) {
      loading.close()
    }
  }
  // 进行assign
  const _options = Object.assign(defaultOptions, options)
  const newFn = (...args) => {
    console.log("开始loading")
    try {
      // 当新方法被执行时，执行老方法，并返回它的返回
      // 等于新方法完全继承了老方法的入参、能力、返回值
      showLoading(_options)
      let result = fn(...args)
      const isPromise = result instanceof Promise
      if (!isPromise) {
        hideLoading()
        return result
      }
      return result
        .then((res) => {
          hideLoading()
          return res
        })
        .catch((err) => {
          hideLoading()
          throw err
        })
    } catch (err) {
      hideLoading()
      throw err
    }
  }
  return newFn
}



/**
 * 
 * @param {loading加载文案} text 
 */
let loading;
export const openLoading = (text = 'Loading…') => {
  loading = ElLoading.service({
    body: true,
    lock: true,
    text: text,
    background: 'rgba(0, 0, 0, 0.7)'
  })
}
export const closeLoading = () => {
  loading.close();
}
// 直接调用 openLoading() 开启loading