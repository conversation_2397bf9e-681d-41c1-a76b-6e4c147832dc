Ext.define("Ext.ux.Gauge",{extend:"Ext.Gadget",xtype:"gauge",requires:["Ext.util.Region"],config:{baseCls:Ext.baseCSSPrefix+"gauge",padding:10,trackStart:135,trackLength:270,angleOffset:0,minValue:0,maxValue:100,value:50,clockwise:true,textTpl:['<tpl>{value:number("0.00")}%</tpl>'],textAlign:"c-c",trackStyle:{outerRadius:"100%",innerRadius:"100% - 20",round:false},valueStyle:{outerRadius:"100% - 2",innerRadius:"100% - 18",round:false},animation:true},template:[{reference:"innerElement",children:[{reference:"textElement",cls:Ext.baseCSSPrefix+"gauge-text"}]}],defaultBindProperty:"value",pathAttributes:{fill:true,fillOpacity:true,stroke:true,strokeOpacity:true,strokeWidth:true},easings:{linear:Ext.identityFn,"in":function(a){return a*a*a},out:function(a){return(--a)*a*a+1},inOut:function(a){return a<0.5?4*a*a*a:(a-1)*(2*a-2)*(2*a-2)+1}},resizeDelay:0,resizeTimerId:0,size:null,svgNS:"http://www.w3.org/2000/svg",svg:null,defs:null,trackArc:null,valueArc:null,trackGradient:null,valueGradient:null,fx:null,fxValue:0,fxAngleOffset:0,constructor:function(a){var b=this;b.fitSectorInRectCache={startAngle:null,lengthAngle:null,minX:null,maxX:null,minY:null,maxY:null};b.interpolator=b.createInterpolator();b.callParent([a]);b.on("resize","onElementResize",b)},doDestroy:function(){var a=this;a.un("resize","onElementResize",a);a.stopAnimation();a.callParent()},afterComponentLayout:function(c,a,b,d){this.callParent([c,a,b,d]);if(Ext.isIE9){this.handleResize()}},onElementResize:function(b,a){this.handleResize(a)},handleResize:function(b,a){var d=this,c=d.element;if(!(c&&(b=b||c.getSize())&&b.width&&b.height)){return}clearTimeout(d.resizeTimerId);if(a||d.resizeDelay){d.resizeTimerId=0}else{d.resizeTimerId=Ext.defer(d.handleResize,d.resizeDelay,d,[b,true]);return}d.size=b;d.resizeHandler(b)},updateMinValue:function(b){var a=this;a.interpolator.setDomain(b,a.getMaxValue());if(!a.isConfiguring){a.render()}},updateMaxValue:function(b){var a=this;a.interpolator.setDomain(a.getMinValue(),b);if(!a.isConfiguring){a.render()}},updateAngleOffset:function(a,b){var c=this,d=c.getAnimation();c.fxAngleOffset=a;if(!c.isConfiguring){if(d.duration){c.animate(b,a,d.duration,c.easings[d.easing],function(e){c.fxAngleOffset=e;c.render()})}else{c.render()}}},updateTrackStart:function(b){var a=this;if(!a.isConfiguring){a.render()}},updateTrackLength:function(a){var b=this;b.interpolator.setRange(0,a);if(!b.isConfiguring){b.render()}},applyPadding:function(b){if(typeof b==="string"){var a=parseFloat(b)/100;return function(c){return c*a}}return function(){return b}},updatePadding:function(){if(!this.isConfiguring){this.render()}},applyValue:function(b){var a=this.getMinValue(),c=this.getMaxValue();return Math.min(Math.max(b,a),c)},updateValue:function(d,a){var b=this,c=b.getAnimation();b.fxValue=d;if(!b.isConfiguring){b.writeText();if(c.duration){b.animate(a,d,c.duration,b.easings[c.easing],function(e){b.fxValue=e;b.render()})}else{b.render()}}},applyTextTpl:function(a){if(a&&!a.isTemplate){a=new Ext.XTemplate(a)}return a},updateTextTpl:function(){this.writeText();if(!this.isConfiguring){this.centerText()}},writeText:function(a){var c=this,e=c.getValue(),d=c.getMinValue(),f=c.getMaxValue(),g=f-d,b=c.getTextTpl();b.overwrite(c.textElement,{value:e,percent:(e-d)/g*100,minValue:d,maxValue:f,delta:g})},centerText:function(e,d,c,g,f){var b=this.textElement,a=this.getTextAlign(),i,h;if(Ext.Number.isEqual(g,0,0.1)||c.isOutOfBound({x:e,y:d})){i=b.getRegion().alignTo({align:a,target:c});b.setLeft(i.left);b.setTop(i.top)}else{h=b.getBox();b.setLeft(e-h.width/2);b.setTop(d-h.height/2)}},camelCaseRe:/([a-z])([A-Z])/g,camelToHyphen:function(a){return a.replace(this.camelCaseRe,"$1-$2").toLowerCase()},applyTrackStyle:function(a){var c=this,b;a.innerRadius=c.getRadiusFn(a.innerRadius);a.outerRadius=c.getRadiusFn(a.outerRadius);if(Ext.isArray(a.fill)){b=c.getTrackGradient();c.setGradientStops(b,a.fill);a.fill="url(#"+b.getAttribute("id")+")"}return a},updateTrackStyle:function(a){var d=this,c=Ext.fly(d.getTrackArc()),b;for(b in a){if(b in d.pathAttributes){c.setStyle(d.camelToHyphen(b),a[b])}}},applyValueStyle:function(a){var c=this,b;a.innerRadius=c.getRadiusFn(a.innerRadius);a.outerRadius=c.getRadiusFn(a.outerRadius);if(Ext.isArray(a.fill)){b=c.getValueGradient();c.setGradientStops(b,a.fill);a.fill="url(#"+b.getAttribute("id")+")"}return a},updateValueStyle:function(a){var d=this,c=Ext.fly(d.getValueArc()),b;for(b in a){if(b in d.pathAttributes){c.setStyle(d.camelToHyphen(b),a[b])}}},getRadiusFn:function(c){var b,e,d,a=0;if(Ext.isNumber(c)){b=function(){return c}}else{if(Ext.isString(c)){c=c.replace(/ /g,"");d=parseFloat(c)/100;e=c.search("%");if(e<c.length-1){a=parseFloat(c.substr(e+1))}b=function(f){return f*d+a};b.ratio=d}}return b},getSvg:function(){var b=this,a=b.svg;if(!a){a=b.svg=Ext.get(document.createElementNS(b.svgNS,"svg"));b.innerElement.append(a)}return a},getTrackArc:function(){var b=this,a=b.trackArc;if(!a){a=b.trackArc=document.createElementNS(b.svgNS,"path");b.getSvg().append(a);a.setAttribute("class",Ext.baseCSSPrefix+"gauge-track")}return a},getValueArc:function(){var b=this,a=b.valueArc;b.getTrackArc();if(!a){a=b.valueArc=document.createElementNS(b.svgNS,"path");b.getSvg().append(a);a.setAttribute("class",Ext.baseCSSPrefix+"gauge-value")}return a},getDefs:function(){var b=this,a=b.defs;if(!a){a=b.defs=document.createElementNS(b.svgNS,"defs");b.getSvg().append(a)}return a},setGradientSize:function(e,b,d,a,c){e.setAttribute("x1",b);e.setAttribute("y1",d);e.setAttribute("x2",a);e.setAttribute("y2",c)},resizeGradients:function(f){var h=this,e=h.getTrackGradient(),c=h.getValueGradient(),b=0,g=f.height/2,a=f.width,d=f.height/2;h.setGradientSize(e,b,g,a,d);h.setGradientSize(c,b,g,a,d)},setGradientStops:function(f,d){var c=d.length,b,a,e;while(f.firstChild){f.removeChild(f.firstChild)}for(b=0;b<c;b++){a=d[b];e=document.createElementNS(this.svgNS,"stop");f.appendChild(e);e.setAttribute("offset",a.offset);e.setAttribute("stop-color",a.color);("opacity" in a)&&e.setAttribute("stop-opacity",a.opacity)}},getTrackGradient:function(){var b=this,a=b.trackGradient;if(!a){a=b.trackGradient=document.createElementNS(b.svgNS,"linearGradient");a.setAttribute("gradientUnits","userSpaceOnUse");b.getDefs().appendChild(a);Ext.get(a)}return a},getValueGradient:function(){var b=this,a=b.valueGradient;if(!a){a=b.valueGradient=document.createElementNS(b.svgNS,"linearGradient");a.setAttribute("gradientUnits","userSpaceOnUse");b.getDefs().appendChild(a);Ext.get(a)}return a},getArcPoint:function(d,b,a,e){var c=e/180*Math.PI;return[d+a*Math.cos(c),b+a*Math.sin(c)]},isCircle:function(b,a){return Ext.Number.isEqual(Math.abs(a-b),360,0.001)},getArcPath:function(d,c,h,e,f,a,o){var i=this,g=i.isCircle(f,a),a=a-0.01,n=i.getArcPoint(d,c,h,f),b=i.getArcPoint(d,c,h,a),m=i.getArcPoint(d,c,e,f),j=i.getArcPoint(d,c,e,a),l=a-f<=180?0:1,p=["M",n[0],n[1],"A",h,h,0,l,1,b[0],b[1]],k=(e-h)/2;if(g){p.push("M",j[0],j[1])}else{if(o){p.push("A",k,k,0,0,0,j[0],j[1])}else{p.push("L",j[0],j[1])}}p.push("A",e,e,0,l,0,m[0],m[1]);if(o&&!g){p.push("A",k,k,0,0,0,n[0],n[1])}p.push("Z");return p.join(" ")},resizeHandler:function(b){var c=this,a=c.getSvg();a.setSize(b);c.resizeGradients(b);c.render()},createInterpolator:function(f){var b=0,a=1,d=0,c=1;var e=function(g,i){var h=0;if(a){h=(g-b)/a;if(f){h=Math.max(0,h);h=Math.min(1,h)}if(i){h=1-h}}return(1-h)*d+h*c};e.setDomain=function(h,g){b=h;a=g-h;return this};e.setRange=function(h,g){d=h;c=g;return this};e.getDomain=function(){return[b,b+a]};e.getRange=function(){return[d,c]};return e},applyAnimation:function(a){if(true===a){a={}}else{if(false===a){a={duration:0}}}if(!("duration" in a)){a.duration=1000}if(!(a.easing in this.easings)){a.easing="out"}return a},updateAnimation:function(){this.stopAnimation()},animate:function(h,i,c,d,g,j){var f=this,a=Ext.now(),e=f.createInterpolator().setRange(h,i);function b(){var k=Ext.AnimationQueue.frameStartTime,l=Math.min(k-a,c)/c,m=e(d(l));if(j){if(typeof g==="string"){j[g].call(j,m)}else{g.call(j,m)}}else{g(m)}if(l>=1){Ext.AnimationQueue.stop(b,j);f.fx=null}}f.stopAnimation();Ext.AnimationQueue.start(b,j);f.fx={frame:b,scope:j}},stopAnimation:function(){var a=this;if(a.fx){Ext.AnimationQueue.stop(a.fx.frame,a.fx.scope);a.fx=null}},unitCircleExtrema:{0:[1,0],90:[0,1],180:[-1,0],270:[0,-1],360:[1,0],450:[0,1],540:[-1,0],630:[0,-1]},getUnitSectorExtrema:function(a,d){var c=this.unitCircleExtrema,b=[],e;for(e in c){if(e>a&&e<a+d){b.push(c[e])}}return b},fitSectorInRect:function(m,h,n,o,d){if(Ext.Number.isEqual(o,360,0.001)){return{cx:m/2,cy:h/2,radius:Math.min(m,h)/2,region:new Ext.util.Region(0,m,h,0)}}var y=this,p,b,k,w,t,s,r,g=y.fitSectorInRectCache,q=g.startAngle===n&&g.lengthAngle===o;if(q){w=g.minX;t=g.maxX;s=g.minY;r=g.maxY}else{p=y.getUnitSectorExtrema(n,o).concat([y.getArcPoint(0,0,1,n),y.getArcPoint(0,0,d,n),y.getArcPoint(0,0,1,n+o),y.getArcPoint(0,0,d,n+o)]);b=p.map(function(A){return A[0]});k=p.map(function(A){return A[1]});w=Math.min.apply(null,b);t=Math.max.apply(null,b);s=Math.min.apply(null,k);r=Math.max.apply(null,k);g.startAngle=n;g.lengthAngle=o;g.minX=w;g.maxX=t;g.minY=s;g.maxY=r}var a=t-w,i=r-s,x=m/a,u=h/i,z=Math.min(x,u),l=new Ext.util.Region(s*z,t*z,r*z,w*z),c=new Ext.util.Region(0,m,h,0),j=l.alignTo({align:"c-c",target:c}),f=j.left-w*z,e=j.top-s*z;return{cx:f,cy:e,radius:z,region:j}},fitSectorInPaddedRect:function(e,b,g,c,f,d){var a=this.fitSectorInRect(e-g*2,b-g*2,c,f,d);a.cx+=g;a.cy+=g;a.region.translateBy(g,g);return a},normalizeAngle:function(a){return(a%360+360)%360},render:function(){if(!this.size){return}var A=this,n=A.getTrackArc(),w=A.getValueArc(),x=A.getClockwise(),r=A.fxValue,h=A.fxAngleOffset,q=A.getTrackLength(),p=A.size.width,m=A.size.height,d=A.getPadding(),l=d(Math.min(p,m)),z=A.normalizeAngle(A.getTrackStart()+h),i=z+q,s=A.interpolator(r),k=A.getTrackStyle(),u=A.getValueStyle(),t=A.fitSectorInPaddedRect(p,m,l,z,q,k.innerRadius.ratio),c=t.cx,b=t.cy,e=t.radius,f=Math.max(0,k.innerRadius(e)),o=Math.max(0,k.outerRadius(e)),a=Math.max(0,u.innerRadius(e)),j=Math.max(0,u.outerRadius(e)),y=A.getArcPath(c,b,f,o,z,i,k.round),g=A.getArcPath(c,b,a,j,x?z:i-s,x?z+s:i,u.round);A.centerText(c,b,t.region,f,o);n.setAttribute("d",y);w.setAttribute("d",g)}});Ext.define("Ext.ux.ajax.Simlet",function(){var d=/([^?#]*)(#.*)?$/,a=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2}(?:\.\d*)?)Z$/,b=/^[+-]?\d+$/,c=/^[+-]?\d+\.\d+$/;function e(g){var f;if(Ext.isDefined(g)){g=decodeURIComponent(g);if(b.test(g)){g=parseInt(g,10)}else{if(c.test(g)){g=parseFloat(g)}else{if(!!(f=a.exec(g))){g=new Date(Date.UTC(+f[1],+f[2]-1,+f[3],+f[4],+f[5],+f[6]))}}}}return g}return{alias:"simlet.basic",isSimlet:true,responseProps:["responseText","responseXML","status","statusText","responseHeaders"],status:200,statusText:"OK",constructor:function(f){Ext.apply(this,f)},doGet:function(f){return this.handleRequest(f)},doPost:function(f){return this.handleRequest(f)},doRedirect:function(f){return false},doDelete:function(f){var h=this,i=f.xhr,g=i.options.records;h.removeFromData(f,g)},exec:function(i){var h=this,f={},j="do"+Ext.String.capitalize(i.method.toLowerCase()),g=h[j];if(g){f=g.call(h,h.getCtx(i.method,i.url,i))}else{f={status:405,statusText:"Method Not Allowed"}}return f},getCtx:function(h,f,g){return{method:h,params:this.parseQueryString(f),url:f,xhr:g}},handleRequest:function(f){var h=this,g={},i;Ext.Array.forEach(h.responseProps,function(j){if(j in h){i=h[j];if(Ext.isFunction(i)){i=i.call(h,f)}g[j]=i}});return g},openRequest:function(l,h,g,i){var f=this.getCtx(l,h),k=this.doRedirect(f),j;if(g.action==="destroy"){l="delete"}if(k){j=k}else{j=new Ext.ux.ajax.SimXhr({mgr:this.manager,simlet:this,options:g});j.open(l,h,i)}return j},parseQueryString:function(o){var g=d.exec(o),l={},q,p,k,f;if(g&&g[1]){var j,h=g[1].split("&");for(k=0,f=h.length;k<f;++k){if((j=h[k].split("="))[0]){q=decodeURIComponent(j.shift());p=e((j.length>1)?j.join("="):j[0]);if(!(q in l)){l[q]=p}else{if(Ext.isArray(l[q])){l[q].push(p)}else{l[q]=[l[q],p]}}}}}return l},redirect:function(h,f,g){switch(arguments.length){case 2:if(typeof f=="string"){break}g=f;case 1:f=h;h="GET";break}if(g){f=Ext.urlAppend(f,Ext.Object.toQueryString(g))}return this.manager.openRequest(h,f)},removeFromData:function(f,g){var j=this,k=j.getData(f),i=(f.xhr.options.proxy&&f.xhr.options.proxy.getModel())||{},h=i.idProperty||"id";Ext.each(g,function(l){var n=l.get(h);for(var m=k.length;m-->0;){if(k[m][h]===n){j.deleteRecord(m);break}}})}}}());Ext.define("Ext.ux.ajax.DataSimlet",function(){function b(f,e){var c=f.direction,d=(c&&c.toUpperCase()==="DESC")?-1:1;return function(h,i){var g=h[f.property],k=i[f.property],j=(g<k)?-1:((k<g)?1:0);if(j||!e){return j*d}return e(h,i)}}function a(c,e){for(var f=e,d=c&&c.length;d;){f=b(c[--d],f)}return f}return{extend:"Ext.ux.ajax.Simlet",buildNodes:function(g,l){var k=this,d={data:[]},j=g.length,f,h,e,c;k.nodes[l]=d;for(h=0;h<j;++h){d.data.push(e=g[h]);c=e.text||e.title;e.id=l?l+"/"+c:c;f=e.children;if(!(e.leaf=!f)){delete e.children;k.buildNodes(f,e.id)}}},deleteRecord:function(c){if(this.data&&typeof this.data!=="function"){Ext.Array.removeAt(this.data,c)}},fixTree:function(d,c){var g=this,f=d.params.node,e;if(!(e=g.nodes)){g.nodes=e={};g.buildNodes(c,"")}f=e[f];if(f){if(g.node){g.node.sortedData=g.sortedData;g.node.currentOrder=g.currentOrder}g.node=f;g.data=f.data;g.sortedData=f.sortedData;g.currentOrder=f.currentOrder}else{g.data=null}},getData:function(k){var i=this,f=k.params,e=(f.filter||"")+(f.group||"")+"-"+(f.sort||"")+"-"+(f.dir||""),l=i.tree,c,g,h,j;if(l){i.fixTree(k,l)}g=i.data;if(typeof g==="function"){c=true;g=g.call(this,k)}if(!g||e==="--"){return g||[]}if(!c&&e==i.currentOrder){return i.sortedData}k.filterSpec=f.filter&&Ext.decode(f.filter);k.groupSpec=f.group&&Ext.decode(f.group);h=f.sort;if(f.dir){h=[{direction:f.dir,property:h}]}else{h=Ext.decode(f.sort)}if(k.filterSpec){var d=new Ext.util.FilterCollection();d.add(this.processFilters(k.filterSpec));g=Ext.Array.filter(g,d.getFilterFn())}j=a((k.sortSpec=h));if(k.groupSpec){j=a([k.groupSpec],j)}g=Ext.isArray(g)?g.slice(0):g;if(j){Ext.Array.sort(g,j)}i.sortedData=g;i.currentOrder=e;return g},processFilters:Ext.identityFn,getPage:function(d,g){var e=g,f=g.length,h=d.params.start||0,c=d.params.limit?Math.min(f,h+d.params.limit):f;if(h||c<f){e=e.slice(h,c)}return e},getGroupSummary:function(d,e,c){return e[0]},getSummary:function(m,g,h){var j=this,c=m.groupSpec.property,k,f={},i=[],d,e;Ext.each(h,function(n){d=n[c];f[d]=true});function l(){if(k){i.push(j.getGroupSummary(c,k,m));k=null}}Ext.each(g,function(n){d=n[c];if(e!==d){l();e=d}if(!f[d]){return !i.length}if(k){k.push(n)}else{k=[n]}return true});l();return i}}}());Ext.define("Ext.ux.ajax.JsonSimlet",{extend:"Ext.ux.ajax.DataSimlet",alias:"simlet.json",doGet:function(c){var f=this,h=f.getData(c),g=f.getPage(c,h),a=c.xhr.options.proxy&&c.xhr.options.proxy.getReader(),b=a&&a.getRootProperty(),e=f.callParent(arguments),d={};if(b&&Ext.isArray(g)){d[b]=g;d[a.getTotalProperty()]=h.length}else{d=g}if(c.groupSpec){d.summaryData=f.getSummary(c,h,g)}e.responseText=Ext.encode(d);return e},doPost:function(a){return this.doGet(a)}});Ext.define("Ext.ux.ajax.PivotSimlet",{extend:"Ext.ux.ajax.JsonSimlet",alias:"simlet.pivot",lastPost:null,lastResponse:null,keysSeparator:"",grandTotalKey:"",doPost:function(a){var c=this,b=c.callParent(arguments);c.lastResponse=c.processData(c.getData(a),Ext.decode(a.xhr.body));b.responseText=Ext.encode(c.lastResponse);return b},processData:function(g,c){var n=this,m=g.length,d={success:true,leftAxis:[],topAxis:[],results:[]},a=new Ext.util.MixedCollection(),q=new Ext.util.MixedCollection(),h=new Ext.util.MixedCollection(),f,e,b,p,l,r,o;n.lastPost=c;n.keysSeparator=c.keysSeparator;n.grandTotalKey=c.grandTotalKey;for(f=0;f<m;f++){p=n.extractValues(g[f],c.leftAxis,a);l=n.extractValues(g[f],c.topAxis,q);n.addResult(g[f],n.grandTotalKey,n.grandTotalKey,h);for(e=0;e<p.length;e++){n.addResult(g[f],p[e],n.grandTotalKey,h);for(b=0;b<l.length;b++){n.addResult(g[f],p[e],l[b],h)}}for(e=0;e<l.length;e++){n.addResult(g[f],n.grandTotalKey,l[e],h)}}d.leftAxis=a.getRange();d.topAxis=q.getRange();m=h.getCount();for(f=0;f<m;f++){r=h.getAt(f);r.values={};for(e=0;e<c.aggregate.length;e++){o=c.aggregate[e];r.values[o.id]=n[o.aggregator](r.records,o.dataIndex,r.leftKey,r.topKey)}delete (r.records);d.results.push(r)}a.clear();q.clear();h.clear();return d},getKey:function(b){var a=this;a.keysMap=a.keysMap||{};if(!Ext.isDefined(a.keysMap[b])){a.keysMap[b]=Ext.id()}return a.keysMap[b]},extractValues:function(f,a,b){var g=a.length,l=[],d,c,h,k,e;h="";for(c=0;c<g;c++){e=a[c];h+=(c>0?this.keysSeparator:"")+this.getKey(f[e.dataIndex]);k=b.getByKey(h);if(!k){k=b.add(h,{key:h,value:f[e.dataIndex],dimensionId:e.id})}l.push(h)}return l},addResult:function(a,e,d,b){var c=b.getByKey(e+"/"+d);if(!c){c=b.add(e+"/"+d,{leftKey:e,topKey:d,records:[]})}c.records.push(a)},sum:function(a,c,f,g){var e=a.length,d=0,b;for(b=0;b<e;b++){d+=Ext.Number.from(a[b][c],0)}return d},avg:function(a,c,f,g){var e=a.length,d=0,b;for(b=0;b<e;b++){d+=Ext.Number.from(a[b][c],0)}return e>0?(d/e):0},min:function(b,d,f,h){var g=[],e=b.length,c,a;for(c=0;c<e;c++){g.push(b[c][d])}a=Ext.Array.min(g);return a},max:function(a,c,e,g){var f=[],d=a.length,b;for(b=0;b<d;b++){f.push(a[b][c])}v=Ext.Array.max(f);return v},count:function(a,b,c,d){return a.length},variance:function(d,a,j,b){var g=Ext.pivot.Aggregators,c=d.length,f=g.avg.apply(g,arguments),h=0,e;if(f>0){for(e=0;e<c;e++){h+=Math.pow(Ext.Number.from(d[e][a],0)-f,2)}}return(h>0&&c>1)?(h/(c-1)):0},varianceP:function(d,a,j,b){var g=Ext.pivot.Aggregators,c=d.length,f=g.avg.apply(g,arguments),h=0,e;if(f>0){for(e=0;e<c;e++){h+=Math.pow(Ext.Number.from(d[e][a],0)-f,2)}}return(h>0&&c>0)?(h/c):0},stdDev:function(b,c,e,f){var d=Ext.pivot.Aggregators,a=d.variance.apply(d,arguments);return a>0?Math.sqrt(a):0},stdDevP:function(b,c,e,f){var d=Ext.pivot.Aggregators,a=d.varianceP.apply(d,arguments);return a>0?Math.sqrt(a):0}});Ext.define("Ext.ux.ajax.SimXhr",{readyState:0,mgr:null,simlet:null,constructor:function(a){var b=this;Ext.apply(b,a);b.requestHeaders={}},abort:function(){var a=this;if(a.timer){clearTimeout(a.timer);a.timer=null}a.aborted=true},getAllResponseHeaders:function(){var a=[];if(Ext.isObject(this.responseHeaders)){Ext.Object.each(this.responseHeaders,function(b,c){a.push(b+": "+c)})}return a.join("\r\n")},getResponseHeader:function(b){var a=this.responseHeaders;return(a&&a[b])||null},open:function(f,c,d,a,b){var e=this;e.method=f;e.url=c;e.async=d!==false;e.user=a;e.password=b;e.setReadyState(1)},overrideMimeType:function(a){this.mimeType=a},schedule:function(){var b=this,a=b.simlet.delay||b.mgr.delay;if(a){b.timer=setTimeout(function(){b.onTick()},a)}else{b.onTick()}},send:function(a){var b=this;b.body=a;if(b.async){b.schedule()}else{b.onComplete()}},setReadyState:function(b){var a=this;if(a.readyState!=b){a.readyState=b;a.onreadystatechange()}},setRequestHeader:function(b,a){this.requestHeaders[b]=a},onreadystatechange:Ext.emptyFn,onComplete:function(){var me=this,callback;me.readyState=4;Ext.apply(me,me.simlet.exec(me));callback=me.jsonpCallback;if(callback){var text=callback+"("+me.responseText+")";eval(text)}},onTick:function(){var a=this;a.timer=null;a.onComplete();a.onreadystatechange&&a.onreadystatechange()}});Ext.define("Ext.ux.ajax.SimManager",{singleton:true,requires:["Ext.data.Connection","Ext.ux.ajax.SimXhr","Ext.ux.ajax.Simlet","Ext.ux.ajax.JsonSimlet"],defaultType:"basic",delay:150,ready:false,constructor:function(){this.simlets=[]},getSimlet:function(a){var g=this,e=a.indexOf("?"),b=g.simlets,f=b.length,c,j,h,d;if(e<0){e=a.indexOf("#")}if(e>0){a=a.substring(0,e)}for(c=0;c<f;++c){j=b[c];h=j.url;if(h instanceof RegExp){d=h.test(a)}else{d=h===a}if(d){return j}}return g.defaultSimlet},getXhr:function(e,b,a,c){var d=this.getSimlet(b);if(d){return d.openRequest(e,b,a,c)}return null},init:function(a){var b=this;Ext.apply(b,a);if(!b.ready){b.ready=true;if(!("defaultSimlet" in b)){b.defaultSimlet=new Ext.ux.ajax.Simlet({status:404,statusText:"Not Found"})}b._openRequest=Ext.data.Connection.prototype.openRequest;Ext.data.request.Ajax.override({openRequest:function(d,c,e){var f=!d.nosim&&b.getXhr(c.method,c.url,d,e);if(!f){f=this.callParent(arguments)}return f}});if(Ext.data.JsonP){Ext.data.JsonP.self.override({createScript:function(f,g,e){var c=Ext.urlAppend(f,Ext.Object.toQueryString(g)),d=!e.nosim&&b.getXhr("GET",c,e,true);if(!d){d=this.callParent(arguments)}return d},loadScript:function(d){var c=d.script;if(c.simlet){c.jsonpCallback=d.params[d.callbackKey];c.send(null);d.script=document.createElement("script")}else{this.callParent(arguments)}}})}}return b},openRequest:function(d,a,c){var b={method:d,url:a};return this._openRequest.call(Ext.data.Connection.prototype,{},b,c)},register:function(c){var b=this;b.init();function a(d){var e=d;if(!e.isSimlet){e=Ext.create("simlet."+(e.type||e.stype||b.defaultType),d)}b.simlets.push(e);e.manager=b}if(Ext.isArray(c)){Ext.each(c,a)}else{if(c.isSimlet||c.url){a(c)}else{Ext.Object.each(c,function(d,e){e.url=d;a(e)})}}return b}});Ext.define("Ext.ux.ajax.XmlSimlet",{extend:"Ext.ux.ajax.DataSimlet",alias:"simlet.xml",xmlTpl:["<{root}>\n",'<tpl for="data">',"    <{parent.record}>\n",'<tpl for="parent.fields">',"        <{name}>{[parent[values.name]]}</{name}>\n","</tpl>","    </{parent.record}>\n","</tpl>","</{root}>"],doGet:function(l){var j=this,b=j.getData(l),h=j.getPage(l,b),i=l.xhr.options.operation.getProxy(),e=i&&i.getReader(),d=e&&e.getModel(),g=j.callParent(arguments),a={data:h,reader:e,fields:d&&d.fields,root:e&&e.getRootProperty(),record:e&&e.record},f,c,k;if(l.groupSpec){a.summaryData=j.getSummary(l,b,h)}if(j.xmlTpl){f=Ext.XTemplate.getTpl(j,"xmlTpl");c=f.apply(a)}else{c=b}if(typeof DOMParser!="undefined"){k=(new DOMParser()).parseFromString(c,"text/xml")}else{k=new ActiveXObject("Microsoft.XMLDOM");k.async=false;k.loadXML(c)}g.responseText=c;g.responseXML=k;return g},fixTree:function(){this.callParent(arguments);var a=[];this.buildTreeXml(this.data,a);this.data=a.join("")},buildTreeXml:function(c,b){var a=this.rootProperty,d=this.recordProperty;b.push("<",a,">");Ext.Array.forEach(c,function(f){b.push("<",d,">");for(var e in f){if(e=="children"){this.buildTreeXml(f.children,b)}else{b.push("<",e,">",f[e],"</",e,">")}}b.push("</",d,">")});b.push("</",a,">")}});Ext.define("Ext.ux.event.Driver",{extend:"Ext.util.Observable",active:null,specialKeysByName:{PGUP:33,PGDN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40},specialKeysByCode:{},getTextSelection:function(d){var e=d.ownerDocument,c,a,f,b;if(typeof d.selectionStart==="number"){f=d.selectionStart;b=d.selectionEnd}else{if(e.selection){c=e.selection.createRange();a=d.createTextRange();a.setEndPoint("EndToStart",c);f=a.text.length;b=f+c.text.length}}return[f,b]},getTime:function(){return new Date().getTime()},getTimestamp:function(){var a=this.getTime();return a-this.startTime},onStart:function(){},onStop:function(){},start:function(){var a=this;if(!a.active){a.active=new Date();a.startTime=a.getTime();a.onStart();a.fireEvent("start",a)}},stop:function(){var a=this;if(a.active){a.active=null;a.onStop();a.fireEvent("stop",a)}}},function(){var a=this.prototype;Ext.Object.each(a.specialKeysByName,function(b,c){a.specialKeysByCode[c]=b})});Ext.define("Ext.ux.event.Maker",{eventQueue:[],startAfter:500,timerIncrement:500,currentTiming:0,constructor:function(a){var b=this;b.currentTiming=b.startAfter;if(!Ext.isArray(a)){a=[a]}Ext.Array.each(a,function(c){c.el=c.el||"el";Ext.Array.each(Ext.ComponentQuery.query(c.cmpQuery),function(g){var f={},d,h,e;if(!c.domQuery){e=g[c.el]}else{e=g.el.down(c.domQuery)}f.target="#"+e.dom.id;f.type=c.type;f.button=a.button||0;d=e.getX()+(e.getWidth()/2);h=e.getY()+(e.getHeight()/2);f.xy=[d,h];f.ts=b.currentTiming;b.currentTiming+=b.timerIncrement;b.eventQueue.push(f)});if(c.screenshot){b.eventQueue[b.eventQueue.length-1].screenshot=true}});return b.eventQueue}});Ext.define("Ext.ux.event.Player",function(d){var h={},c={},a={},g,b={},f={resize:1,reset:1,submit:1,change:1,select:1,error:1,abort:1};Ext.each(["click","dblclick","mouseover","mouseout","mousedown","mouseup","mousemove"],function(i){f[i]=h[i]=c[i]={bubbles:true,cancelable:(i!="mousemove"),detail:1,screenX:0,screenY:0,clientX:0,clientY:0,ctrlKey:false,altKey:false,shiftKey:false,metaKey:false,button:0}});Ext.each(["keydown","keyup","keypress"],function(i){f[i]=h[i]=a[i]={bubbles:true,cancelable:true,ctrlKey:false,altKey:false,shiftKey:false,metaKey:false,keyCode:0,charCode:0}});Ext.each(["blur","change","focus","resize","scroll","select"],function(i){h[i]=b[i]={bubbles:(i in f),cancelable:false,detail:1}});var e={8:function(j,k,i){if(k<i){j.value=j.value.substring(0,k)+j.value.substring(i)}else{if(k>0){j.value=j.value.substring(0,--k)+j.value.substring(i)}}this.setTextSelection(j,k,k)},46:function(j,k,i){if(k<i){j.value=j.value.substring(0,k)+j.value.substring(i)}else{if(k<j.value.length-1){j.value=j.value.substring(0,k)+j.value.substring(k+1)}}this.setTextSelection(j,k,k)}};return{extend:"Ext.ux.event.Driver",keyFrameEvents:{click:true},pauseForAnimations:true,speed:1,stallTime:0,_inputSpecialKeys:{INPUT:e,TEXTAREA:Ext.apply({},e)},tagPathRegEx:/(\w+)(?:\[(\d+)\])?/,constructor:function(i){var j=this;j.callParent(arguments);j.timerFn=function(){j.onTick()};j.attachTo=j.attachTo||window;g=j.attachTo.document},getElementFromXPath:function(s){var t=this,p=s.split("/"),u=t.tagPathRegEx,q,l,o,r,w,k,j=t.attachTo.document;j=(p[0]=="~")?j.body:j.getElementById(p[0].substring(1));for(q=1,l=p.length;j&&q<l;++q){o=u.exec(p[q]);r=o[2]?parseInt(o[2],10):1;w=o[1].toUpperCase();for(k=j.firstChild;k;k=k.nextSibling){if(k.tagName==w){if(r==1){break}--r}}j=k}return j},offsetToRangeCharacterMove:function(i,j){return j-(i.value.slice(0,j).split("\r\n").length-1)},setTextSelection:function(m,i,l){if(i<0){i+=m.value.length}if(l==null){l=i}if(l<0){l+=m.value.length}if(typeof m.selectionStart==="number"){m.selectionStart=i;m.selectionEnd=l}else{var k=m.createTextRange();var j=this.offsetToRangeCharacterMove(m,i);k.collapse(true);if(i==l){k.move("character",j)}else{k.moveEnd("character",this.offsetToRangeCharacterMove(m,l));k.moveStart("character",j)}k.select()}},getTimeIndex:function(){var i=this.getTimestamp()-this.stallTime;return i*this.speed},makeToken:function(i,l){var j=this,k;i[l]=true;i.defer=function(){i[l]=false;k=j.getTime()};i.finish=function(){i[l]=true;j.stallTime+=j.getTime()-k;j.schedule()}},nextEvent:function(j){var k=this,i=++k.queueIndex;if(k.keyFrameEvents[j.type]){Ext.Array.insert(k.eventQueue,i,[{keyframe:true,ts:j.ts}])}},peekEvent:function(){return this.eventQueue[this.queueIndex]||null},replaceEvent:function(j,m){for(var l,k=0,o=m.length;k<o;++k){if(k){l=m[k-1];delete l.afterplay;delete l.screenshot;delete m[k].beforeplay}}Ext.Array.replace(this.eventQueue,(j==null)?this.queueIndex:j,1,m)},processEvents:function(){var j=this,k=j.pauseForAnimations&&j.attachTo.Ext.fx.Manager.items,i;while((i=j.peekEvent())!==null){if(k&&k.getCount()){return true}if(i.keyframe){if(!j.processKeyFrame(i)){return false}j.nextEvent(i)}else{if(i.ts<=j.getTimeIndex()&&j.fireEvent("beforeplay",j,i)!==false&&j.playEvent(i)){j.nextEvent(i)}else{return true}}}j.stop();return false},processKeyFrame:function(i){var j=this;if(!i.defer){j.makeToken(i,"done");j.fireEvent("keyframe",j,i)}return i.done},injectEvent:function(n,m){var l=this,k=m.type,i=Ext.apply({},m,h[k]),j;if(k==="type"){j=l._inputSpecialKeys[n.tagName];if(j){return l.injectTypeInputEvent(n,m,j)}return l.injectTypeEvent(n,m)}if(k==="focus"&&n.focus){n.focus();return true}if(k==="blur"&&n.blur){n.blur();return true}if(k==="scroll"){n.scrollLeft=m.pos[0];n.scrollTop=m.pos[1];return true}if(k==="mduclick"){return l.injectEvent(n,Ext.applyIf({type:"mousedown"},m))&&l.injectEvent(n,Ext.applyIf({type:"mouseup"},m))&&l.injectEvent(n,Ext.applyIf({type:"click"},m))}if(c[k]){return d.injectMouseEvent(n,i,l.attachTo)}if(a[k]){return d.injectKeyEvent(n,i,l.attachTo)}if(b[k]){return d.injectUIEvent(n,k,i.bubbles,i.cancelable,i.view||l.attachTo,i.detail)}return false},injectTypeEvent:function(r,k){var t=this,w=k.text,p=[],j,o,q,m,l,u,s;if(w){delete k.text;u=w.toUpperCase();for(q=0,m=w.length;q<m;++q){j=w.charCodeAt(q);o=u.charCodeAt(q);p.push(Ext.applyIf({type:"keydown",charCode:o,keyCode:o},k),Ext.applyIf({type:"keypress",charCode:j,keyCode:j},k),Ext.applyIf({type:"keyup",charCode:o,keyCode:o},k))}}else{p.push(Ext.applyIf({type:"keydown",charCode:k.keyCode},k),Ext.applyIf({type:"keyup",charCode:k.keyCode},k))}for(q=0,m=p.length;q<m;++q){t.injectEvent(r,p[q])}return true},injectTypeInputEvent:function(m,k,i){var j=this,o=k.text,l,p;if(i){l=j.getTextSelection(m);if(o){p=l[0];m.value=m.value.substring(0,p)+o+m.value.substring(l[1]);p+=o.length;j.setTextSelection(m,p,p)}else{if(!(i=i[k.keyCode])){if("caret" in k){j.setTextSelection(m,k.caret,k.caret)}else{if(k.selection){j.setTextSelection(m,k.selection[0],k.selection[1])}}return j.injectTypeEvent(m,k)}i.call(this,m,l[0],l[1]);return true}}return true},playEvent:function(i){var k=this,l=k.getElementFromXPath(i.target),j;if(!l){return false}if(!k.playEventHook(i,"beforeplay")){return false}if(!i.injected){i.injected=true;j=k.translateEvent(i,l);k.injectEvent(l,j)}return k.playEventHook(i,"afterplay")},playEventHook:function(k,j){var l=this,i=j+".done",n=j+".fired",m=k[j];if(m&&!k[i]){if(!k[n]){k[n]=true;l.makeToken(k,i);if(l.eventScope&&Ext.isString(m)){m=l.eventScope[m]}if(m){m.call(l.eventScope||l,k)}}return false}return true},schedule:function(){var i=this;if(!i.timer){i.timer=setTimeout(i.timerFn,10)}},_translateAcross:["type","button","charCode","keyCode","caret","pos","text","selection"],translateEvent:function(q,m){var o=this,j={},p=q.modKeys||"",n=o._translateAcross,l=n.length,k,r;while(l--){k=n[l];if(k in q){j[k]=q[k]}}j.altKey=p.indexOf("A")>0;j.ctrlKey=p.indexOf("C")>0;j.metaKey=p.indexOf("M")>0;j.shiftKey=p.indexOf("S")>0;if(m&&"x" in q){r=Ext.fly(m).getXY();r[0]+=q.x;r[1]+=q.y}else{if("x" in q){r=[q.x,q.y]}else{if("px" in q){r=[q.px,q.py]}}}if(r){j.clientX=j.screenX=r[0];j.clientY=j.screenY=r[1]}if(q.key){j.keyCode=o.specialKeysByName[q.key]}if(q.type==="wheel"){if("onwheel" in o.attachTo.document){j.wheelX=q.dx;j.wheelY=q.dy}else{j.type="mousewheel";j.wheelDeltaX=-40*q.dx;j.wheelDeltaY=j.wheelDelta=-40*q.dy}}return j},onStart:function(){var i=this;i.queueIndex=0;i.schedule()},onStop:function(){var i=this;if(i.timer){clearTimeout(i.timer);i.timer=null}},onTick:function(){var i=this;i.timer=null;if(i.processEvents()){i.schedule()}},statics:{ieButtonCodeMap:{0:1,1:4,2:2},injectKeyEvent:function(n,j,i){var m=j.type,l=null;if(m==="textevent"){m="keypress"}i=i||window;if(g.createEvent){try{l=g.createEvent("KeyEvents");l.initKeyEvent(m,j.bubbles,j.cancelable,i,j.ctrlKey,j.altKey,j.shiftKey,j.metaKey,j.keyCode,j.charCode)}catch(k){try{l=g.createEvent("Events")}catch(o){l=g.createEvent("UIEvents")}finally{l.initEvent(m,j.bubbles,j.cancelable);l.view=i;l.altKey=j.altKey;l.ctrlKey=j.ctrlKey;l.shiftKey=j.shiftKey;l.metaKey=j.metaKey;l.keyCode=j.keyCode;l.charCode=j.charCode}}n.dispatchEvent(l)}else{if(g.createEventObject){l=g.createEventObject();l.bubbles=j.bubbles;l.cancelable=j.cancelable;l.view=i;l.ctrlKey=j.ctrlKey;l.altKey=j.altKey;l.shiftKey=j.shiftKey;l.metaKey=j.metaKey;l.keyCode=(j.charCode>0)?j.charCode:j.keyCode;n.fireEvent("on"+m,l)}else{return false}}return true},injectMouseEvent:function(m,j,i){var l=j.type,k=null;i=i||window;if(g.createEvent){k=g.createEvent("MouseEvents");if(k.initMouseEvent){k.initMouseEvent(l,j.bubbles,j.cancelable,i,j.detail,j.screenX,j.screenY,j.clientX,j.clientY,j.ctrlKey,j.altKey,j.shiftKey,j.metaKey,j.button,j.relatedTarget)}else{k=g.createEvent("UIEvents");k.initEvent(l,j.bubbles,j.cancelable);k.view=i;k.detail=j.detail;k.screenX=j.screenX;k.screenY=j.screenY;k.clientX=j.clientX;k.clientY=j.clientY;k.ctrlKey=j.ctrlKey;k.altKey=j.altKey;k.metaKey=j.metaKey;k.shiftKey=j.shiftKey;k.button=j.button;k.relatedTarget=j.relatedTarget}if(j.relatedTarget&&!k.relatedTarget){if(l=="mouseout"){k.toElement=j.relatedTarget}else{if(l=="mouseover"){k.fromElement=j.relatedTarget}}}m.dispatchEvent(k)}else{if(g.createEventObject){k=g.createEventObject();k.bubbles=j.bubbles;k.cancelable=j.cancelable;k.view=i;k.detail=j.detail;k.screenX=j.screenX;k.screenY=j.screenY;k.clientX=j.clientX;k.clientY=j.clientY;k.ctrlKey=j.ctrlKey;k.altKey=j.altKey;k.metaKey=j.metaKey;k.shiftKey=j.shiftKey;k.button=d.ieButtonCodeMap[j.button]||0;k.relatedTarget=j.relatedTarget;m.fireEvent("on"+l,k)}else{return false}}return true},injectUIEvent:function(l,j,i){var k=null;i=i||window;if(g.createEvent){k=g.createEvent("UIEvents");k.initUIEvent(j.type,j.bubbles,j.cancelable,i,j.detail);l.dispatchEvent(k)}else{if(g.createEventObject){k=g.createEventObject();k.bubbles=j.bubbles;k.cancelable=j.cancelable;k.view=i;k.detail=j.detail;l.fireEvent("on"+j.type,k)}else{return false}}return true}}}});Ext.define("Ext.ux.event.Recorder",function(c){function a(){var f=arguments,j=f.length,h={kind:"other"},g;for(g=0;g<j;++g){Ext.apply(h,arguments[g])}if(h.alt&&!h.event){h.event=h.alt}return h}function d(f){return a({kind:"keyboard",modKeys:true,key:true},f)}function b(f){return a({kind:"mouse",button:true,modKeys:true,xy:true},f)}var e={keydown:d(),keypress:d(),keyup:d(),dragmove:b({alt:"mousemove",pageCoords:true,whileDrag:true}),mousemove:b({pageCoords:true}),mouseover:b(),mouseout:b(),click:b(),wheel:b({wheel:true}),mousedown:b({press:true}),mouseup:b({release:true}),scroll:a({listen:false}),focus:a(),blur:a()};for(var d in e){if(!e[d].event){e[d].event=d}}e.wheel.event=null;return{extend:"Ext.ux.event.Driver",eventsToRecord:e,ignoreIdRegEx:/ext-gen(?:\d+)/,inputRe:/^(input|textarea)$/i,constructor:function(f){var h=this,g=f&&f.eventsToRecord;if(g){h.eventsToRecord=Ext.apply(Ext.apply({},h.eventsToRecord),g);delete f.eventsToRecord}h.callParent(arguments);h.clear();h.modKeys=[];h.attachTo=h.attachTo||window},clear:function(){this.eventsRecorded=[]},listenToEvent:function(j){var i=this,h=i.attachTo.document.body,g=function(){return i.onEvent.apply(i,arguments)},f={};if(h.attachEvent&&h.ownerDocument.documentMode<10){j="on"+j;h.attachEvent(j,g);f.destroy=function(){if(g){h.detachEvent(j,g);g=null}}}else{h.addEventListener(j,g,true);f.destroy=function(){if(g){h.removeEventListener(j,g,true);g=null}}}return f},coalesce:function(m,k){var j=this,g=j.eventsRecorded,i=g.length,f=i&&g[i-1],l=(i>1)&&g[i-2],h=(i>2)&&g[i-3];if(!f){return false}if(m.type==="mousemove"){if(f.type==="mousemove"&&m.ts-f.ts<200){m.ts=f.ts;g[i-1]=m;return true}}else{if(m.type==="click"){if(l&&f.type==="mouseup"&&l.type==="mousedown"){if(m.button==f.button&&m.button==l.button&&m.target==f.target&&m.target==l.target&&j.samePt(m,f)&&j.samePt(m,l)){g.pop();l.type="mduclick";return true}}}else{if(m.type==="keyup"){if(l&&f.type==="keypress"&&l.type==="keydown"){if(m.target===f.target&&m.target===l.target){g.pop();l.type="type";l.text=String.fromCharCode(f.charCode);delete l.charCode;delete l.keyCode;if(h&&h.type==="type"){if(h.text&&h.target===l.target){h.text+=l.text;g.pop()}}return true}}else{if(j.completeKeyStroke(f,m)){f.type="type";j.completeSpecialKeyStroke(k.target,f,m);return true}else{if(f.type==="scroll"&&j.completeKeyStroke(l,m)){l.type="type";j.completeSpecialKeyStroke(k.target,l,m);g.pop();g.pop();g.push(f,l);return true}}}}}}return false},completeKeyStroke:function(g,f){if(g&&g.type==="keydown"&&g.keyCode===f.keyCode){delete g.charCode;return true}return false},completeSpecialKeyStroke:function(h,i,f){var g=this.specialKeysByCode[f.keyCode];if(g&&this.inputRe.test(h.tagName)){delete i.keyCode;i.key=g;i.selection=this.getTextSelection(h);if(i.selection[0]===i.selection[1]){i.caret=i.selection[0];delete i.selection}return true}return false},getElementXPath:function(j){var m=this,l=false,g=[],k,i,h,f;for(h=j;h;h=h.parentNode){if(h==m.attachTo.document.body){g.unshift("~");l=true;break}if(h.id&&!m.ignoreIdRegEx.test(h.id)){g.unshift("#"+h.id);l=true;break}for(k=1,i=h;!!(i=i.previousSibling);){if(i.tagName==h.tagName){++k}}f=h.tagName.toLowerCase();if(k<2){g.unshift(f)}else{g.unshift(f+"["+k+"]")}}return l?g.join("/"):null},getRecordedEvents:function(){return this.eventsRecorded},onEvent:function(k){var j=this,h=new Ext.event.Event(k),f=j.eventsToRecord[h.type],l,m,i,g={type:h.type,ts:j.getTimestamp(),target:j.getElementXPath(h.target)},n;if(!f||!g.target){return}l=h.target.ownerDocument;l=l.defaultView||l.parentWindow;if(l!==j.attachTo){return}if(j.eventsToRecord.scroll){j.syncScroll(h.target)}if(f.xy){n=h.getXY();if(f.pageCoords||!g.target){g.px=n[0];g.py=n[1]}else{i=Ext.fly(h.getTarget()).getXY();n[0]-=i[0];n[1]-=i[1];g.x=n[0];g.y=n[1]}}if(f.button){if("buttons" in k){g.button=k.buttons}else{g.button=k.button}if(!g.button&&f.whileDrag){return}}if(f.wheel){g.type="wheel";if(f.event==="wheel"){g.dx=k.deltaX;g.dy=k.deltaY}else{if(typeof k.wheelDeltaX==="number"){g.dx=-1/40*k.wheelDeltaX;g.dy=-1/40*k.wheelDeltaY}else{if(k.wheelDelta){g.dy=-1/40*k.wheelDelta}else{if(k.detail){g.dy=k.detail}}}}}if(f.modKeys){j.modKeys[0]=h.altKey?"A":"";j.modKeys[1]=h.ctrlKey?"C":"";j.modKeys[2]=h.metaKey?"M":"";j.modKeys[3]=h.shiftKey?"S":"";m=j.modKeys.join("");if(m){g.modKeys=m}}if(f.key){g.charCode=h.getCharCode();g.keyCode=h.getKey()}if(j.coalesce(g,h)){j.fireEvent("coalesce",j,g)}else{j.eventsRecorded.push(g);j.fireEvent("add",j,g)}},onStart:function(){var h=this,i=h.attachTo.Ext.dd.DragDropManager,f=h.attachTo.Ext.EventObjectImpl.prototype,g=[];c.prototype.eventsToRecord.wheel.event=("onwheel" in h.attachTo.document)?"wheel":"mousewheel";h.listeners=[];Ext.Object.each(h.eventsToRecord,function(j,k){if(k&&k.listen!==false){if(!k.event){k.event=j}if(k.alt&&k.alt!==j){if(!h.eventsToRecord[k.alt]){g.push(k)}}else{h.listeners.push(h.listenToEvent(k.event))}}});Ext.each(g,function(j){h.eventsToRecord[j.alt]=j;h.listeners.push(h.listenToEvent(j.alt))});h.ddmStopEvent=i.stopEvent;i.stopEvent=Ext.Function.createSequence(i.stopEvent,function(j){h.onEvent(j)});h.evStopEvent=f.stopEvent;f.stopEvent=Ext.Function.createSequence(f.stopEvent,function(){h.onEvent(this)})},onStop:function(){var f=this;Ext.destroy(f.listeners);f.listeners=null;f.attachTo.Ext.dd.DragDropManager.stopEvent=f.ddmStopEvent;f.attachTo.Ext.EventObjectImpl.prototype.stopEvent=f.evStopEvent},samePt:function(g,f){return g.x==f.x&&g.y==f.y},syncScroll:function(h){var k=this,j=k.getTimestamp(),o,n,m,l,g,i;for(var f=h;f;f=f.parentNode){o=f.$lastScrollLeft;n=f.$lastScrollTop;m=f.scrollLeft;l=f.scrollTop;g=false;if(o!==m){if(m){g=true}f.$lastScrollLeft=m}if(n!==l){if(l){g=true}f.$lastScrollTop=l}if(g){k.eventsRecorded.push(i={type:"scroll",target:k.getElementXPath(f),ts:j,pos:[m,l]});k.fireEvent("add",k,i)}if(f.tagName==="BODY"){break}}}}});Ext.define("Ext.ux.BoxReorderer",{extend:"Ext.plugin.Abstract",alias:"plugin.boxreorderer",requires:["Ext.dd.DD"],mixins:{observable:"Ext.util.Observable"},itemSelector:".x-box-item",animate:100,constructor:function(){this.callParent(arguments);this.mixins.observable.constructor.call(this)},init:function(a){var b=this;b.container=a;b.animatePolicy={};b.animatePolicy[a.getLayout().names.x]=true;b.container.on({scope:b,boxready:b.onBoxReady,beforedestroy:b.onContainerDestroy})},onContainerDestroy:function(){var a=this.dd;if(a){a.unreg();this.dd=null}},onBoxReady:function(){var c=this,b=c.container.getLayout(),d=b.names,a;a=c.dd=new Ext.dd.DD(b.innerCt,c.container.id+"-reorderer");Ext.apply(a,{animate:c.animate,reorderer:c,container:c.container,getDragCmp:c.getDragCmp,clickValidator:Ext.Function.createInterceptor(a.clickValidator,c.clickValidator,c,false),onMouseDown:c.onMouseDown,startDrag:c.startDrag,onDrag:c.onDrag,endDrag:c.endDrag,getNewIndex:c.getNewIndex,doSwap:c.doSwap,findReorderable:c.findReorderable});a.dim=d.width;a.startAttr=d.beforeX;a.endAttr=d.afterX},getDragCmp:function(a){return this.container.getChildByElement(a.getTarget(this.itemSelector,10))},clickValidator:function(b){var a=this.getDragCmp(b);return !!(a&&a.reorderable!==false)},onMouseDown:function(g){var f=this,a=f.container,d,b,c;f.dragCmp=f.getDragCmp(g);if(f.dragCmp){b=f.dragCmp.getEl();f.startIndex=f.curIndex=a.items.indexOf(f.dragCmp);c=b.getBox();f.lastPos=c[f.startAttr];d=a.el.getBox();if(f.dim==="width"){f.minX=d.left;f.maxX=d.right-c.width;f.minY=f.maxY=c.top;f.deltaX=g.getX()-c.left}else{f.minY=d.top;f.maxY=d.bottom-c.height;f.minX=f.maxX=c.left;f.deltaY=g.getY()-c.top}f.constrainY=f.constrainX=true}},startDrag:function(){var b=this,a=b.dragCmp;if(a){a.setPosition=Ext.emptyFn;a.animate=false;if(b.animate){b.container.getLayout().animatePolicy=b.reorderer.animatePolicy}b.dragElId=a.getEl().id;b.reorderer.fireEvent("StartDrag",b,b.container,a,b.curIndex);a.suspendEvents();a.disabled=true;a.el.setStyle("zIndex",100)}else{b.dragElId=null}},findReorderable:function(c){var d=this,a=d.container.items,b;if(a.getAt(c).reorderable===false){b=a.getAt(c);if(c>d.startIndex){while(b&&b.reorderable===false){c++;b=a.getAt(c)}}else{while(b&&b.reorderable===false){c--;b=a.getAt(c)}}}c=Math.min(Math.max(c,0),a.getCount()-1);if(a.getAt(c).reorderable===false){return -1}return c},doSwap:function(d){var f=this,b=f.container.items,a=f.container,e=f.container._isLayoutRoot,h,c,g;d=f.findReorderable(d);if(d===-1){return}f.reorderer.fireEvent("ChangeIndex",f,a,f.dragCmp,f.startIndex,d);h=b.getAt(f.curIndex);c=b.getAt(d);b.remove(h);g=Math.min(Math.max(d,0),b.getCount()-1);b.insert(g,h);b.remove(c);b.insert(f.curIndex,c);a._isLayoutRoot=true;a.updateLayout();a._isLayoutRoot=e;f.curIndex=d},onDrag:function(c){var b=this,a;a=b.getNewIndex(c.getPoint());if((a!==undefined)){b.reorderer.fireEvent("Drag",b,b.container,b.dragCmp,b.startIndex,b.curIndex);b.doSwap(a)}},endDrag:function(d){if(d){d.stopEvent()}var c=this,b=c.container.getLayout(),a;if(c.dragCmp){delete c.dragElId;delete c.dragCmp.setPosition;c.dragCmp.animate=true;c.dragCmp.lastBox[b.names.x]=c.dragCmp.getPosition(true)[b.names.widthIndex];c.container._isLayoutRoot=true;c.container.updateLayout();c.container._isLayoutRoot=undefined;a=Ext.fx.Manager.getFxQueue(c.dragCmp.el.id)[0];if(a){a.on({afteranimate:c.reorderer.afterBoxReflow,scope:c})}else{Ext.Function.defer(c.reorderer.afterBoxReflow,1,c)}if(c.animate){delete b.animatePolicy}c.reorderer.fireEvent("drop",c,c.container,c.dragCmp,c.startIndex,c.curIndex)}},afterBoxReflow:function(){var a=this;a.dragCmp.el.setStyle("zIndex","");a.dragCmp.disabled=false;a.dragCmp.resumeEvents()},getNewIndex:function(h){var g=this,a=g.getDragEl(),b=Ext.fly(a).getBox(),l,f,k,d=0,c=g.container.items.items,e=c.length,j=g.lastPos;g.lastPos=b[g.startAttr];for(;d<e;d++){l=c[d].getEl();if(l.is(g.reorderer.itemSelector)){f=l.getBox();k=f[g.startAttr]+(f[g.dim]>>1);if(d<g.curIndex){if((b[g.startAttr]<j)&&(b[g.startAttr]<(k-5))){return d}}else{if(d>g.curIndex){if((b[g.startAttr]>j)&&(b[g.endAttr]>(k+5))){return d}}}}}}});Ext.define("Ext.ux.CellDragDrop",{extend:"Ext.plugin.Abstract",alias:"plugin.celldragdrop",uses:["Ext.view.DragZone"],enforceType:false,applyEmptyText:false,emptyText:"",dropBackgroundColor:"green",noDropBackgroundColor:"red",dragText:"{0} selected row{1}",ddGroup:"GridDD",enableDrop:true,enableDrag:true,containerScroll:false,init:function(a){var b=this;a.on("render",b.onViewRender,b,{single:true})},destroy:function(){var a=this;a.dragZone=a.dropZone=Ext.destroy(a.dragZone,a.dropZone);a.callParent()},enable:function(){var a=this;if(a.dragZone){a.dragZone.unlock()}if(a.dropZone){a.dropZone.unlock()}a.callParent()},disable:function(){var a=this;if(a.dragZone){a.dragZone.lock()}if(a.dropZone){a.dropZone.lock()}a.callParent()},onViewRender:function(a){var b=this,c;if(b.enableDrag){if(b.containerScroll){c=a.getEl()}b.dragZone=new Ext.view.DragZone({view:a,ddGroup:b.dragGroup||b.ddGroup,dragText:b.dragText,containerScroll:b.containerScroll,scrollEl:c,getDragData:function(j){var h=this.view,i=j.getTarget(h.getItemSelector()),g=h.getRecord(i),f=j.getTarget(h.getCellSelector()),d,k;if(i){d=document.createElement("div");d.className="x-form-text";d.appendChild(document.createTextNode(f.textContent||f.innerText));k=h.getHeaderByCell(f);return{event:new Ext.EventObjectImpl(j),ddel:d,item:j.target,columnName:k.dataIndex,record:g}}},onInitDrag:function(e,k){var h=this,j=h.dragData,g=h.view,d=g.getSelectionModel(),f=j.record,i=j.ddel;if(!d.isSelected(f)){d.select(f,true)}Ext.fly(h.ddel).update(i.textContent||i.innerText);h.proxy.update(h.ddel);h.onStartDrag(e,k);return true}})}if(b.enableDrop){b.dropZone=new Ext.dd.DropZone(a.el,{view:a,ddGroup:b.dropGroup||b.ddGroup,containerScroll:true,getTargetFromEvent:function(h){var g=this,f=g.view,d=h.getTarget(f.cellSelector),i,j;if(d){i=f.findItemByChild(d);j=f.getHeaderByCell(d);if(i&&j){return{node:d,record:f.getRecord(i),columnName:j.dataIndex}}}},onNodeEnter:function(k,d,j,i){var f=this,h=k.record.getField(k.columnName).type.toUpperCase(),g=i.record.getField(i.columnName).type.toUpperCase();delete f.dropOK;if(!k||k.node===i.item.parentNode){return}if(b.enforceType&&h!==g){f.dropOK=false;if(b.noDropCls){Ext.fly(k.node).addCls(b.noDropCls)}else{Ext.fly(k.node).applyStyles({backgroundColor:b.noDropBackgroundColor})}return false}f.dropOK=true;if(b.dropCls){Ext.fly(k.node).addCls(b.dropCls)}else{Ext.fly(k.node).applyStyles({backgroundColor:b.dropBackgroundColor})}},onNodeOver:function(h,d,g,f){return this.dropOK?this.dropAllowed:this.dropNotAllowed},onNodeOut:function(i,d,h,g){var f=this.dropOK?b.dropCls:b.noDropCls;if(f){Ext.fly(i.node).removeCls(f)}else{Ext.fly(i.node).applyStyles({backgroundColor:""})}},onNodeDrop:function(h,d,g,f){if(this.dropOK){h.record.set(h.columnName,f.record.get(f.columnName));if(b.applyEmptyText){f.record.set(f.columnName,b.emptyText)}return true}},onCellDrop:Ext.emptyFn})}}});Ext.define("Ext.ux.DataTip",function(e){function c(){var f=this.isXType("panel")?this.body:this.el;if(this.dataTip.renderToTarget){this.dataTip.render(f)}this.dataTip.setTarget(f)}function a(g,f){if(g.rendered){if(g.host.fireEvent("beforeshowtip",g.eventHost,g,f)===false){return false}g.update(f)}else{if(Ext.isString(f)){g.html=f}else{g.data=f}}}function d(g){var h=this.view.getRecord(g.triggerElement),f;if(h){f=g.initialConfig.data?Ext.apply(g.initialConfig.data,h.data):h.data;return a(g,f)}else{return false}}function b(f){var g=Ext.getCmp(f.triggerElement.id);if(g&&(g.tooltip||f.tpl)){return a(f,g.tooltip||g)}else{return false}}return{extend:"Ext.tip.ToolTip",mixins:{plugin:"Ext.plugin.Abstract"},alias:"plugin.datatip",lockableScope:"both",constructor:function(f){var g=this;g.callParent([f]);g.mixins.plugin.constructor.call(g,f)},init:function(g){var f=this;f.mixins.plugin.init.call(f,g);g.dataTip=f;f.host=g;if(g.isXType("tablepanel")){f.view=g.getView();if(g.ownerLockable){f.host=g.ownerLockable}f.delegate=f.delegate||f.view.rowSelector;f.on("beforeshow",d)}else{if(g.isXType("dataview")){f.view=f.host;f.delegate=f.delegate||g.itemSelector;f.on("beforeshow",d)}else{if(g.isXType("form")){f.delegate="."+Ext.form.Labelable.prototype.formItemCls;f.on("beforeshow",b)}else{if(g.isXType("combobox")){f.view=g.getPicker();f.delegate=f.delegate||f.view.getItemSelector();f.on("beforeshow",d)}}}}if(g.rendered){c.call(g)}else{g.onRender=Ext.Function.createSequence(g.onRender,c)}}}});Ext.define("Ext.ux.DataView.Animated",{alias:"plugin.ux-animated-dataview",defaults:{duration:750,idProperty:"id"},constructor:function(a){Ext.apply(this,a||{},this.defaults)},init:function(a){var e=this,d=a.store,c=a.all,b={interval:20},g=e.duration;e.dataview=a;a.blockRefresh=true;a.updateIndexes=Ext.Function.createSequence(a.updateIndexes,function(){this.getTargetEl().select(this.itemSelector).each(function(i,j,h){i.dom.id=Ext.util.Format.format("{0}-{1}",a.id,d.getAt(h).internalId)},this)},a);e.dataviewID=a.id;e.cachedStoreData={};e.cacheStoreData(d.data||d.snapshot);a.on("resize",function(){var h=a.store;if(h.getCount()>0){}},this);a.store.on({datachanged:f,scope:this,buffer:50});function f(){var k=a.getTargetEl(),p=k.getY(),q=k.getPadding("t"),m=e.getAdded(d),x=e.getRemoved(d),r=e.getRemaining(d),z,w,t,o=new Ext.dom.Fly(),l=e.dataview.getInherited().rtl,u,h,n=l?"right":"left",s={};if(!k){return}Ext.iterate(x,function(B,i){t=e.dataviewID+"-"+B;Ext.fx.Manager.stopAnimation(t);i.dom=Ext.getDom(t);if(!i.dom){delete x[B]}});e.cacheStoreData(d);var j={},A={};Ext.iterate(r,function(B,i){if(o.attach(Ext.getDom(e.dataviewID+"-"+B))){u=j[B]={top:o.getY()-p-o.getMargin("t")-q};u[n]=e.getItemX(o)}else{delete r[B]}});a.refresh();Ext.iterate(x,function(B,i){k.dom.appendChild(i.dom);o.attach(i.dom).animate({duration:g,opacity:0,callback:function(D){var C=Ext.get(D.target.id);if(C){C.destroy()}}});delete i.dom});if(!d.getCount()){return}z=c.slice();for(w=z.length-1;w>=0;w--){t=d.getAt(w).internalId;o.attach(z[w]);A[t]={dom:o.dom,top:o.getY()-p-o.getMargin("t")-q};A[t][n]=e.getItemX(o);h=j[t]||A[t];s.position="absolute";s.top=h.top+"px";s[n]=h.left+"px";o.applyStyles(s)}var y=function(){var I=new Date()-b.taskStartTime,K=I/g;if(K>=1){s.position=s.top=s[n]="";for(t in A){o.attach(A[t].dom).applyStyles(s)}Ext.TaskManager.stop(b)}else{for(t in r){var C=j[t],F=A[t],D=C.top,G=F.top,B=C[n],H=F[n],E=K*Math.abs(D-G),J=K*Math.abs(B-H),L=D>G?D-E:D+E,i=B>H?B-J:B+J;s.top=L+"px";s[n]=i+"px";o.attach(F.dom).applyStyles(s)}}};Ext.iterate(m,function(B,i){if(o.attach(Ext.getDom(e.dataviewID+"-"+B))){o.setOpacity(0);o.animate({duration:g,opacity:1})}});Ext.TaskManager.stop(b);b.run=y;Ext.TaskManager.start(b);e.cacheStoreData(d)}},getItemX:function(b){var c=this.dataview.getInherited().rtl,a=b.up("");if(c){return a.getViewRegion().right-b.getRegion().right+b.getMargin("r")}else{return b.getX()-a.getX()-b.getMargin("l")-a.getPadding("l")}},cacheStoreData:function(a){var b=this.cachedStoreData={};a.each(function(c){b[c.internalId]=c})},getExisting:function(){return this.cachedStoreData},getExistingCount:function(){var c=0,b=this.getExisting();for(var a in b){c++}return c},getAdded:function(a){var c=this.cachedStoreData,b={};a.each(function(d){if(c[d.internalId]==null){b[d.internalId]=d}});return b},getRemoved:function(a){var b=this.cachedStoreData,c={},d;for(d in b){if(a.findBy(function(e){return e.internalId==d})==-1){c[d]=b[d]}}return c},getRemaining:function(a){var c=this.cachedStoreData,b={};a.each(function(d){if(c[d.internalId]!=null){b[d.internalId]=d}});return b}});Ext.define("Ext.ux.DataView.DragSelector",{requires:["Ext.dd.DragTracker","Ext.util.Region"],init:function(b){var a=b.getScrollable();if(a&&(a.getX()||a.getY())&&(Ext.supports.PointerEvents||Ext.supports.MSPointerEvents)){return}this.dataview=b;b.mon(b,{beforecontainerclick:this.cancelClick,scope:this,render:{fn:this.onRender,scope:this,single:true}})},onRender:function(){this.tracker=Ext.create("Ext.dd.DragTracker",{dataview:this.dataview,el:this.dataview.el,onBeforeStart:this.onBeforeStart,onStart:this.onStart.bind(this),onDrag:this.onDrag.bind(this),onEnd:Ext.Function.createDelayed(this.onEnd,100,this)});this.dragRegion=Ext.create("Ext.util.Region")},onBeforeStart:function(a){return a.target===this.dataview.getEl().dom},onStart:function(b){var a=this.dataview;this.dragging=true;this.fillRegions();this.getProxy().show();a.getSelectionModel().deselectAll()},cancelClick:function(){return !this.dragging},onDrag:function(k){var j=this.dataview.getSelectionModel(),p=this.dragRegion,o=this.bodyRegion,m=this.getProxy(),f=this.regions,b=f.length,l=this.tracker.startXY,r=this.tracker.getXY(),d=Math.min(l[0],r[0]),c=Math.min(l[1],r[1]),a=Math.abs(l[0]-r[0]),q=Math.abs(l[1]-r[1]),n,g,h;Ext.apply(p,{top:c,left:d,right:d+a,bottom:c+q});p.constrainTo(o);m.setBox(p);for(h=0;h<b;h++){n=f[h];g=p.intersect(n);if(g){j.select(h,true)}else{j.deselect(h)}}},onEnd:function(c){var a=this.dataview,b=a.getSelectionModel();this.dragging=false;this.getProxy().hide()},getProxy:function(){if(!this.proxy){this.proxy=this.dataview.getEl().createChild({tag:"div",cls:"x-view-selector"})}return this.proxy},fillRegions:function(){var a=this.dataview,b=this.regions=[];a.all.each(function(c){b.push(c.getRegion())});this.bodyRegion=a.getEl().getRegion()}});Ext.define("Ext.ux.DataView.Draggable",{requires:"Ext.dd.DragZone",ghostCls:"x-dataview-draggable-ghost",ghostTpl:['<tpl for=".">',"{title}","</tpl>"],init:function(a,b){this.dataview=a;a.on("render",this.onRender,this);Ext.apply(this,{itemSelector:a.itemSelector,ghostConfig:{}},b||{});Ext.applyIf(this.ghostConfig,{itemSelector:"img",cls:this.ghostCls,tpl:this.ghostTpl})},onRender:function(){var b=this,a=Ext.apply({},b.ddConfig||{},{dvDraggable:b,dataview:b.dataview,getDragData:b.getDragData,getTreeNode:b.getTreeNode,afterRepair:b.afterRepair,getRepairXY:b.getRepairXY});b.dragZone=Ext.create("Ext.dd.DragZone",b.dataview.getEl(),a);b.dataview.setItemsDraggable(true)},getDragData:function(h){var a=this.dvDraggable,b=this.dataview,c=b.getSelectionModel(),g=h.getTarget(a.itemSelector),d,f;if(g){h.preventDefault();if(!b.isSelected(g)){c.select(b.getRecord(g))}d=b.getSelectedNodes();f={copy:true,nodes:d,records:c.getSelection(),item:true};if(d.length===1){f.single=true;f.ddel=g}else{f.multi=true;f.ddel=a.prepareGhost(c.getSelection())}return f}return false},getTreeNode:function(){},afterRepair:function(){this.dragging=false;var a=this.dragData.nodes,c=a.length,b;for(b=0;b<c;b++){Ext.get(a[b]).frame("#8db2e3",1)}},getRepairXY:function(c){if(this.dragData.multi){return false}else{var a=Ext.get(this.dragData.ddel),b=a.getXY();b[0]+=a.getPadding("t")+a.getMargin("t");b[1]+=a.getPadding("l")+a.getMargin("l");return b}},prepareGhost:function(a){return this.createGhost(a).getEl().dom},createGhost:function(b){var c=this,a;if(c.ghost){(a=c.ghost.store).loadRecords(b)}else{a=Ext.create("Ext.data.Store",{model:b[0].self});a.loadRecords(b);c.ghost=Ext.create("Ext.view.View",Ext.apply({renderTo:document.createElement("div"),store:a},c.ghostConfig));c.ghost.container.skipGarbageCollection=c.ghost.el.skipGarbageCollection=true}a.clearData();return c.ghost},destroy:function(){var a=this.ghost;if(a){a.container.destroy();a.destroy()}this.callParent()}});Ext.define("Ext.ux.DataView.LabelEditor",{extend:"Ext.Editor",alignment:"tl-tl",completeOnEnter:true,cancelOnEsc:true,shim:false,autoSize:{width:"boundEl",height:"field"},labelSelector:"x-editable",requires:["Ext.form.field.Text"],constructor:function(a){a.field=a.field||Ext.create("Ext.form.field.Text",{allowOnlyWhitespace:false,selectOnFocus:true});this.callParent([a])},init:function(a){this.view=a;this.mon(a,"afterrender",this.bindEvents,this);this.on("complete",this.onSave,this)},bindEvents:function(){this.mon(this.view.getEl(),{click:{fn:this.onClick,scope:this}})},onClick:function(f,d){var c=this,b,a;if(Ext.fly(d).hasCls(c.labelSelector)&&!c.editing&&!f.ctrlKey&&!f.shiftKey){f.stopEvent();b=c.view.findItemByChild(d);a=c.view.store.getAt(c.view.indexOf(b));c.startEdit(d,a.data[c.dataIndex]);c.activeRecord=a}else{if(c.editing){c.field.blur();f.preventDefault()}}},onSave:function(a,b){this.activeRecord.set(this.dataIndex,b)}});Ext.ux.DataViewTransition=Ext.extend(Object,{defaults:{duration:750,idProperty:"id"},constructor:function(a){Ext.apply(this,a||{},this.defaults)},init:function(a){this.dataview=a;var b=this.idProperty;a.blockRefresh=true;a.updateIndexes=Ext.Function.createSequence(a.updateIndexes,function(){this.getTargetEl().select(this.itemSelector).each(function(d,e,c){d.id=d.dom.id=Ext.util.Format.format("{0}-{1}",a.id,a.store.getAt(c).get(b))},this)},a);this.dataviewID=a.id;this.cachedStoreData={};this.cacheStoreData(a.store.snapshot);a.store.on("datachanged",function(l){var j=a.getTargetEl(),f=l.getAt(0),n=this.getAdded(l),w=this.getRemoved(l),g=this.getRemaining(l),s=Ext.apply({},g,n);Ext.each(w,function(B){Ext.fly(this.dataviewID+"-"+B.get(this.idProperty)).animate({remove:false,duration:c,opacity:0,useDisplay:true})},this);if(f==undefined){this.cacheStoreData(l);return}var e=Ext.get(this.dataviewID+"-"+f.get(this.idProperty));var y=l.getCount(),i=e.getMargin("lr")+e.getWidth(),t=e.getMargin("bt")+e.getHeight(),p=j.getWidth(),d=Math.floor(p/i),o=Math.ceil(y/d),z=Math.ceil(this.getExistingCount()/d);j.applyStyles({display:"block",position:"relative"});var h={},A={},q={};Ext.iterate(g,function(D,C){var D=C.get(this.idProperty),B=q[D]=Ext.get(this.dataviewID+"-"+D);h[D]={top:B.getY()-j.getY()-B.getMargin("t")-j.getPadding("t"),left:B.getX()-j.getX()-B.getMargin("l")-j.getPadding("l")}},this);Ext.iterate(g,function(E,D){var B=h[E],C=q[E];if(C.getStyle("position")!="absolute"){q[E].applyStyles({position:"absolute",left:B.left+"px",top:B.top+"px",width:C.getWidth(!Ext.isIE||Ext.isStrict),height:C.getHeight(!Ext.isIE||Ext.isStrict)})}});var m=0;Ext.iterate(l.data.items,function(D){var H=D.get(b),C=q[H];var B=m%d,G=Math.floor(m/d),F=G*t,E=B*i;A[H]={top:F,left:E};m++},this);var r=new Date(),c=this.duration,k=this.dataviewID;var x=function(){var K=new Date()-r,M=K/c;if(M>=1){for(var B in A){Ext.fly(k+"-"+B).applyStyles({top:A[B].top+"px",left:A[B].left+"px"})}Ext.TaskManager.stop(u)}else{for(var B in A){if(!g[B]){continue}var E=h[B],H=A[B],F=E.top,I=H.top,D=E.left,J=H.left,G=M*Math.abs(F-I),L=M*Math.abs(D-J),N=F>I?F-G:F+G,C=D>J?D-L:D+L;Ext.fly(k+"-"+B).applyStyles({top:N+"px",left:C+"px"})}}};var u={run:x,interval:20,scope:this};Ext.TaskManager.start(u);Ext.iterate(n,function(C,B){Ext.fly(this.dataviewID+"-"+B.get(this.idProperty)).applyStyles({top:A[B.get(this.idProperty)].top+"px",left:A[B.get(this.idProperty)].left+"px"});Ext.fly(this.dataviewID+"-"+B.get(this.idProperty)).animate({remove:false,duration:c,opacity:1})},this);this.cacheStoreData(l)},this)},cacheStoreData:function(a){this.cachedStoreData={};a.each(function(b){this.cachedStoreData[b.get(this.idProperty)]=b},this)},getExisting:function(){return this.cachedStoreData},getExistingCount:function(){var c=0,b=this.getExisting();for(var a in b){c++}return c},getAdded:function(a){var b={};a.each(function(c){if(this.cachedStoreData[c.get(this.idProperty)]==undefined){b[c.get(this.idProperty)]=c}},this);return b},getRemoved:function(a){var b=[];for(var c in this.cachedStoreData){if(a.findExact(this.idProperty,Number(c))==-1){b.push(this.cachedStoreData[c])}}return b},getRemaining:function(a){var b={};a.each(function(c){if(this.cachedStoreData[c.get(this.idProperty)]!=undefined){b[c.get(this.idProperty)]=c}},this);return b}});Ext.define("Ext.ux.Explorer",{extend:"Ext.panel.Panel",xtype:"explorer",requires:["Ext.layout.container.Border","Ext.toolbar.Breadcrumb","Ext.tree.Panel"],config:{breadcrumb:{dock:"top",xtype:"breadcrumb",reference:"breadcrumb"},contentView:{xtype:"dataview",reference:"contentView",region:"center",cls:Ext.baseCSSPrefix+"explorer-view",itemSelector:"."+Ext.baseCSSPrefix+"explorer-item",tpl:'<tpl for="."><div class="'+Ext.baseCSSPrefix+'explorer-item"><div class="{iconCls}"><div class="'+Ext.baseCSSPrefix+'explorer-node-icon{[values.leaf ? " '+Ext.baseCSSPrefix+'explorer-leaf-icon" : ""]}"></div><div class="'+Ext.baseCSSPrefix+'explorer-item-text">{text}</div></div></div></tpl>'},store:null,tree:{xtype:"treepanel",reference:"tree",region:"west",width:200}},renderConfig:{selection:null},layout:"border",referenceHolder:true,defaultListenerScope:true,cls:Ext.baseCSSPrefix+"explorer",initComponent:function(){var b=this,a=b.getStore();b.dockedItems=[b.getBreadcrumb()];b.items=[b.getTree(),b.getContentView()];b.callParent()},applyBreadcrumb:function(b){var a=this.getStore();b=Ext.create(Ext.apply({store:a,selection:a.getRoot()},b));b.on("selectionchange","_onBreadcrumbSelectionChange",this);return b},applyContentView:function(a){var b=this.contentStore=new Ext.data.Store({model:this.getStore().model});a=Ext.create(Ext.apply({store:b},a));return a},applyTree:function(a){a=Ext.create(Ext.apply({store:this.getStore()},a));a.on("selectionchange","_onTreeSelectionChange",this);return a},updateSelection:function(b){var f=this,h=f.getReferences(),e=h.breadcrumb,i=h.tree,d=i.getSelectionModel(),g=f.contentStore,c,a;if(e.getSelection()!==b){e.setSelection(b)}if(d.getSelection()[0]!==b){d.select([b]);c=b.parentNode;if(c){c.expand()}a=i.getView();a.scrollRowIntoView(a.getRow(b))}g.removeAll();g.add(b.hasChildNodes()?b.childNodes:[b])},updateStore:function(a){this.getBreadcrumb().setStore(a)},privates:{_onTreeSelectionChange:function(a,b){this.setSelection(b[0])},_onBreadcrumbSelectionChange:function(b,a){this.setSelection(a)}}});Ext.define("Ext.ux.FieldReplicator",{alias:"plugin.fieldreplicator",init:function(a){if(!a.replicatorId){a.replicatorId=Ext.id()}a.on("blur",this.onBlur,this)},onBlur:function(e){var b=e.ownerCt,d=e.replicatorId,g=Ext.isEmpty(e.getRawValue()),f=b.query("[replicatorId="+d+"]"),c=f[f.length-1]===e,h,a;if(g&&!c){Ext.Function.defer(e.destroy,10,e)}else{if(!g&&c){if(e.onReplicate){e.onReplicate()}h=e.cloneConfig({replicatorId:d});a=b.items.indexOf(e);b.add(a+1,h)}}}});Ext.define("Ext.ux.GMapPanel",{extend:"Ext.panel.Panel",alias:"widget.gmappanel",requires:["Ext.window.MessageBox"],initComponent:function(){Ext.applyIf(this,{plain:true,gmapType:"map",border:false});this.callParent()},onBoxReady:function(){var a=this.center;this.callParent(arguments);if(a){if(a.geoCodeAddr){this.lookupCode(a.geoCodeAddr,a.marker)}else{this.createMap(a)}}else{Ext.raise("center is required")}},createMap:function(a,b){var c=Ext.apply({},this.mapOptions);c=Ext.applyIf(c,{zoom:14,center:a,mapTypeId:google.maps.MapTypeId.HYBRID});this.gmap=new google.maps.Map(this.body.dom,c);if(b){this.addMarker(Ext.applyIf(b,{position:a}))}Ext.each(this.markers,this.addMarker,this);this.fireEvent("mapready",this,this.gmap)},addMarker:function(a){a=Ext.apply({map:this.gmap},a);if(!a.position){a.position=new google.maps.LatLng(a.lat,a.lng)}var b=new google.maps.Marker(a);Ext.Object.each(a.listeners,function(c,d){google.maps.event.addListener(b,c,d)});return b},lookupCode:function(b,a){this.geocoder=new google.maps.Geocoder();this.geocoder.geocode({address:b},Ext.Function.bind(this.onLookupComplete,this,[a],true))},onLookupComplete:function(c,b,a){if(b!="OK"){Ext.MessageBox.alert("Error",'An error occured: "'+b+'"');return}this.createMap(c[0].geometry.location,a)},afterComponentLayout:function(a,b){this.callParent(arguments);this.redraw()},redraw:function(){var a=this.gmap;if(a){google.maps.event.trigger(a,"resize")}}});Ext.define("Ext.ux.GroupTabRenderer",{extend:"Ext.plugin.Abstract",alias:"plugin.grouptabrenderer",tableTpl:new Ext.XTemplate('<div id="{view.id}-body" class="'+Ext.baseCSSPrefix+"{view.id}-table "+Ext.baseCSSPrefix+'grid-table-resizer" style="{tableStyle}">',"{%","values.view.renderRows(values.rows, values.viewStartIndex, out);","%}","</div>",{priority:5}),rowTpl:new Ext.XTemplate("{%",'Ext.Array.remove(values.itemClasses, "',Ext.baseCSSPrefix+'grid-row");','var dataRowCls = values.recordIndex === -1 ? "" : " '+Ext.baseCSSPrefix+'grid-data-row";',"%}",'<div {[values.rowId ? ("id=\\"" + values.rowId + "\\"") : ""]} ','data-boundView="{view.id}" ','data-recordId="{record.internalId}" ','data-recordIndex="{recordIndex}" ','class="'+Ext.baseCSSPrefix+'grouptab-row {[values.itemClasses.join(" ")]} {[values.rowClasses.join(" ")]}{[dataRowCls]}" ',"{rowAttr:attributes}>",'<tpl for="columns">{%',"parent.view.renderCell(values, parent.record, parent.recordIndex, parent.rowIndex, xindex - 1, out, parent)","%}","</tpl>","</div>",{priority:5}),cellTpl:new Ext.XTemplate('{%values.tdCls = values.tdCls.replace(" '+Ext.baseCSSPrefix+'grid-cell "," ");%}','<div class="'+Ext.baseCSSPrefix+'grouptab-cell {tdCls}" {tdAttr}>','<div {unselectableAttr} class="'+Ext.baseCSSPrefix+'grid-cell-inner" style="text-align: {align}; {style};">{value}</div>','<div class="x-grouptabs-corner x-grouptabs-corner-top-left"></div>','<div class="x-grouptabs-corner x-grouptabs-corner-bottom-left"></div>',"</div>",{priority:5}),selectors:{bodySelector:"div."+Ext.baseCSSPrefix+"grid-table-resizer",nodeContainerSelector:"div."+Ext.baseCSSPrefix+"grid-table-resizer",itemSelector:"div."+Ext.baseCSSPrefix+"grouptab-row",rowSelector:"div."+Ext.baseCSSPrefix+"grouptab-row",cellSelector:"div."+Ext.baseCSSPrefix+"grouptab-cell",getCellSelector:function(a){return a?a.getCellSelector():this.cellSelector}},init:function(b){var a=b.getView(),c=this;a.addTpl(c.tableTpl);a.addRowTpl(c.rowTpl);a.addCellTpl(c.cellTpl);Ext.apply(a,c.selectors)}});Ext.define("Ext.ux.GroupTabPanel",{extend:"Ext.Container",alias:"widget.grouptabpanel",requires:["Ext.tree.Panel","Ext.ux.GroupTabRenderer"],baseCls:Ext.baseCSSPrefix+"grouptabpanel",initComponent:function(a){var b=this;Ext.apply(b,a);b.store=b.createTreeStore();b.layout={type:"hbox",align:"stretch"};b.defaults={border:false};b.items=[{xtype:"treepanel",cls:"x-tree-panel x-grouptabbar",width:150,rootVisible:false,store:b.store,hideHeaders:true,animate:false,processEvent:Ext.emptyFn,border:false,plugins:[{ptype:"grouptabrenderer"}],viewConfig:{overItemCls:"",getRowClass:b.getRowClass},columns:[{xtype:"treecolumn",sortable:false,dataIndex:"text",flex:1,renderer:function(j,d,i,h,g,f,c){var e="";if(i.parentNode&&i.parentNode.parentNode===null){e+=" x-grouptab-first";if(i.previousSibling){e+=" x-grouptab-prev"}if(!i.get("expanded")||i.firstChild==null){e+=" x-grouptab-last"}}else{if(i.nextSibling===null){e+=" x-grouptab-last"}else{e+=" x-grouptab-center"}}if(i.data.activeTab){e+=" x-active-tab"}d.tdCls="x-grouptab"+e;return j}}]},{xtype:"container",flex:1,layout:"card",activeItem:b.mainItem,baseCls:Ext.baseCSSPrefix+"grouptabcontainer",items:b.cards}];b.callParent(arguments);b.setActiveTab(b.activeTab);b.setActiveGroup(b.activeGroup);b.mon(b.down("treepanel").getSelectionModel(),"select",b.onNodeSelect,b)},getRowClass:function(d,e,c,b){var a="";if(d.data.activeGroup){a+=" x-active-group"}return a},onNodeSelect:function(a,e){var d=this,b=d.store.getRootNode(),c;if(e.parentNode&&e.parentNode.parentNode===null){c=e}else{c=e.parentNode}if(d.setActiveGroup(c.get("id"))===false||d.setActiveTab(e.get("id"))===false){return false}while(b){b.set("activeTab",false);b.set("activeGroup",false);b=b.firstChild||b.nextSibling||b.parentNode.nextSibling}c.set("activeGroup",true);c.eachChild(function(f){f.set("activeGroup",true)});e.set("activeTab",true);a.view.refresh()},setActiveTab:function(b){var a=this,d=b,c;if(Ext.isString(b)){d=Ext.getCmp(d)}if(d===a.activeTab){return false}c=a.activeTab;if(a.fireEvent("beforetabchange",a,d,c)!==false){a.activeTab=d;if(a.rendered){a.down("container[baseCls="+Ext.baseCSSPrefix+"grouptabcontainer]").getLayout().setActiveItem(d)}a.fireEvent("tabchange",a,d,c)}return true},setActiveGroup:function(c){var b=this,d=c,a;if(Ext.isString(c)){d=Ext.getCmp(d)}if(d===b.activeGroup){return true}a=b.activeGroup;if(b.fireEvent("beforegroupchange",b,d,a)!==false){b.activeGroup=d;b.fireEvent("groupchange",b,d,a)}else{return false}return true},createTreeStore:function(){var b=this,a=b.prepareItems(b.items),c={text:".",children:[]},d=b.cards=[];b.activeGroup=b.activeGroup||0;Ext.each(a,function(g,e){var h=g.items.items,f=(h[g.mainItem]||h[0]),i={children:[]};i.id=f.id;i.text=f.title;i.iconCls=f.iconCls;i.expanded=true;i.activeGroup=(b.activeGroup===e);i.activeTab=i.activeGroup?true:false;if(i.activeTab){b.activeTab=i.id}if(i.activeGroup){b.mainItem=g.mainItem||0;b.activeGroup=i.id}Ext.each(h,function(j){if(j.id!==i.id){var k={id:j.id,leaf:true,text:j.title,iconCls:j.iconCls,activeGroup:i.activeGroup,activeTab:false};i.children.push(k)}delete j.title;delete j.iconCls;d.push(j)});c.children.push(i)});return Ext.create("Ext.data.TreeStore",{fields:["id","text","activeGroup","activeTab"],root:{expanded:true},proxy:{type:"memory",data:c}})},getActiveTab:function(){return this.activeTab},getActiveGroup:function(){return this.activeGroup}});Ext.define("Ext.ux.IFrame",{extend:"Ext.Component",alias:"widget.uxiframe",loadMask:"Loading...",src:"about:blank",renderTpl:['<iframe src="{src}" id="{id}-iframeEl" data-ref="iframeEl" name="{frameName}" width="100%" height="100%" frameborder="0"></iframe>'],childEls:["iframeEl"],initComponent:function(){this.callParent();this.frameName=this.frameName||this.id+"-frame"},initEvents:function(){var a=this;a.callParent();a.iframeEl.on("load",a.onLoad,a)},initRenderData:function(){return Ext.apply(this.callParent(),{src:this.src,frameName:this.frameName})},getBody:function(){var a=this.getDoc();return a.body||a.documentElement},getDoc:function(){try{return this.getWin().document}catch(a){return null}},getWin:function(){var b=this,a=b.frameName,c=Ext.isIE?b.iframeEl.dom.contentWindow:window.frames[a];return c},getFrame:function(){var a=this;return a.iframeEl.dom},onLoad:function(){var a=this,b=a.getDoc();if(b){this.el.unmask();this.fireEvent("load",this)}else{if(a.src){this.el.unmask();this.fireEvent("error",this)}}},load:function(d){var a=this,c=a.loadMask,b=a.getFrame();if(a.fireEvent("beforeload",a,d)!==false){if(c&&a.el){a.el.mask(c)}b.src=a.src=(d||a.src)}}});Ext.define("Ext.ux.statusbar.StatusBar",{extend:"Ext.toolbar.Toolbar",alternateClassName:"Ext.ux.StatusBar",alias:"widget.statusbar",requires:["Ext.toolbar.TextItem"],cls:"x-statusbar",busyIconCls:"x-status-busy",busyText:"Loading...",autoClear:5000,emptyText:"&#160;",activeThreadId:0,initComponent:function(){var a=this.statusAlign==="right";this.callParent(arguments);this.currIconCls=this.iconCls||this.defaultIconCls;this.statusEl=Ext.create("Ext.toolbar.TextItem",{cls:"x-status-text "+(this.currIconCls||""),text:this.text||this.defaultText||""});if(a){this.cls+=" x-status-right";this.add("->");this.add(this.statusEl)}else{this.insert(0,this.statusEl);this.insert(1,"->")}},setStatus:function(e){var a=this;e=e||{};Ext.suspendLayouts();if(Ext.isString(e)){e={text:e}}if(e.text!==undefined){a.setText(e.text)}if(e.iconCls!==undefined){a.setIcon(e.iconCls)}if(e.clear){var f=e.clear,d=a.autoClear,b={useDefaults:true,anim:true};if(Ext.isObject(f)){f=Ext.applyIf(f,b);if(f.wait){d=f.wait}}else{if(Ext.isNumber(f)){d=f;f=b}else{if(Ext.isBoolean(f)){f=b}}}f.threadId=this.activeThreadId;Ext.defer(a.clearStatus,d,a,[f])}Ext.resumeLayouts(true);return a},clearStatus:function(e){e=e||{};var c=this,b=c.statusEl;if(c.destroyed||e.threadId&&e.threadId!==c.activeThreadId){return c}var d=e.useDefaults?c.defaultText:c.emptyText,a=e.useDefaults?(c.defaultIconCls?c.defaultIconCls:""):"";if(e.anim){b.el.puff({remove:false,useDisplay:true,callback:function(){b.el.show();c.setStatus({text:d,iconCls:a})}})}else{c.setStatus({text:d,iconCls:a})}return c},setText:function(b){var a=this;a.activeThreadId++;a.text=b||"";if(a.rendered){a.statusEl.setText(a.text)}return a},getText:function(){return this.text},setIcon:function(a){var b=this;b.activeThreadId++;a=a||"";if(b.rendered){if(b.currIconCls){b.statusEl.removeCls(b.currIconCls);b.currIconCls=null}if(a.length>0){b.statusEl.addCls(a);b.currIconCls=a}}else{b.currIconCls=a}return b},showBusy:function(a){if(Ext.isString(a)){a={text:a}}a=Ext.applyIf(a||{},{text:this.busyText,iconCls:this.busyIconCls});return this.setStatus(a)}});Ext.define("Ext.ux.LiveSearchGridPanel",{extend:"Ext.grid.Panel",requires:["Ext.toolbar.TextItem","Ext.form.field.Checkbox","Ext.form.field.Text","Ext.ux.statusbar.StatusBar"],searchValue:null,matches:[],currentIndex:null,searchRegExp:null,caseSensitive:false,regExpMode:false,matchCls:"x-livesearch-match",defaultStatusText:"Nothing Found",initComponent:function(){var a=this;a.tbar=["Search",{xtype:"textfield",name:"searchField",hideLabel:true,width:200,listeners:{change:{fn:a.onTextFieldChange,scope:this,buffer:500}}},{xtype:"button",text:"&lt;",tooltip:"Find Previous Row",handler:a.onPreviousClick,scope:a},{xtype:"button",text:"&gt;",tooltip:"Find Next Row",handler:a.onNextClick,scope:a},"-",{xtype:"checkbox",hideLabel:true,margin:"0 0 0 4px",handler:a.regExpToggle,scope:a},"Regular expression",{xtype:"checkbox",hideLabel:true,margin:"0 0 0 4px",handler:a.caseSensitiveToggle,scope:a},"Case sensitive"];a.bbar=new Ext.ux.StatusBar({defaultText:a.defaultStatusText,name:"searchStatusBar"});a.callParent(arguments)},afterRender:function(){var a=this;a.callParent(arguments);a.textField=a.down("textfield[name=searchField]");a.statusBar=a.down("statusbar[name=searchStatusBar]");a.view.on("cellkeydown",a.focusTextField,a)},focusTextField:function(b,i,c,a,f,h,g,d){if(g.getKey()===g.S){g.preventDefault();this.textField.focus()}},tagsRe:/<[^>]*>/gm,tagsProtect:"\x0f",getSearchValue:function(){var b=this,c=b.textField.getValue();if(c===""){return null}if(!b.regExpMode){c=Ext.String.escapeRegex(c)}else{try{new RegExp(c)}catch(a){b.statusBar.setStatus({text:a.message,iconCls:"x-status-error"});return null}if(c==="^"||c==="$"){return null}}return c},onTextFieldChange:function(){var f=this,e=0,b=f.view,a=b.cellSelector,c=b.innerSelector,d=f.visibleColumnManager.getColumns();b.refresh();f.statusBar.setStatus({text:f.defaultStatusText,iconCls:""});f.searchValue=f.getSearchValue();f.matches=[];f.currentIndex=null;if(f.searchValue!==null){f.searchRegExp=new RegExp(f.getSearchValue(),"g"+(f.caseSensitive?"":"i"));f.store.each(function(h,g){var i=b.getNode(h);if(i){Ext.Array.forEach(d,function(l){var j=Ext.fly(i).down(l.getCellInnerSelector(),true),n,m,k;if(j){n=j.innerHTML.match(f.tagsRe);m=j.innerHTML.replace(f.tagsRe,f.tagsProtect);m=m.replace(f.searchRegExp,function(o){++e;if(!k){f.matches.push({record:h,column:l});k=true}return'<span class="'+f.matchCls+'">'+o+"</span>"},f);Ext.each(n,function(o){m=m.replace(f.tagsProtect,o)});j.innerHTML=m}})}},f);if(e){f.currentIndex=0;f.gotoCurrent();f.statusBar.setStatus({text:Ext.String.format("{0} match{1} found.",e,e===1?"es":""),iconCls:"x-status-valid"})}}if(f.currentIndex===null){f.getSelectionModel().deselectAll();f.textField.focus()}},onPreviousClick:function(){var c=this,d=c.matches,b=d.length,a=c.currentIndex;if(b){c.currentIndex=a===0?b-1:a-1;c.gotoCurrent()}},onNextClick:function(){var c=this,d=c.matches,b=d.length,a=c.currentIndex;if(b){c.currentIndex=a===b-1?0:a+1;c.gotoCurrent()}},caseSensitiveToggle:function(b,a){this.caseSensitive=a;this.onTextFieldChange()},regExpToggle:function(b,a){this.regExpMode=a;this.onTextFieldChange()},privates:{gotoCurrent:function(){var a=this.matches[this.currentIndex];this.getNavigationModel().setPosition(a.record,a.column);this.getSelectionModel().select(a.record)}}});Ext.define("Ext.ux.PreviewPlugin",{extend:"Ext.plugin.Abstract",alias:"plugin.preview",requires:["Ext.grid.feature.RowBody"],hideBodyCls:"x-grid-row-body-hidden",bodyField:"",previewExpanded:true,setCmp:function(f){this.callParent(arguments);var d=this,c=d.cmp=f.isXType("gridview")?f.grid:f,a=d.bodyField,g=d.hideBodyCls,b=Ext.create("Ext.grid.feature.RowBody",{grid:c,getAdditionalData:function(l,i,j,m){var h=Ext.grid.feature.RowBody.prototype.getAdditionalData,k={rowBody:l[a],rowBodyCls:c.getView().previewExpanded?"":g};if(Ext.isFunction(h)){Ext.apply(k,h.apply(this,arguments))}return k}}),e=function(i,h){h.previewExpanded=d.previewExpanded;h.featuresMC.add(b);b.init(i)};if(c.view){e(c,c.view)}else{c.on({viewcreated:e,single:true})}},toggleExpanded:function(b){var c=this.getCmp(),a=c&&c.getView(),e=a.bufferedRenderer,d=a.scrollManager;if(c&&a&&b!==a.previewExpanded){this.previewExpanded=a.previewExpanded=!!b;a.refreshView();if(d){if(e){e.stretchView(a,e.getScrollHeight(true))}else{d.refresh(true)}}}}});Ext.define("Ext.ux.ProgressBarPager",{alias:"plugin.ux-progressbarpager",requires:["Ext.ProgressBar"],width:225,defaultText:"Loading...",defaultAnimCfg:{duration:1000,easing:"bounceOut"},constructor:function(a){if(a){Ext.apply(this,a)}},init:function(b){var a;if(b.displayInfo){this.parent=b;a=b.child("#displayItem");if(a){b.remove(a,true)}this.progressBar=Ext.create("Ext.ProgressBar",{text:this.defaultText,width:this.width,animate:this.defaultAnimCfg,style:{cursor:"pointer"},listeners:{el:{scope:this,click:this.handleProgressBarClick}}});b.displayItem=this.progressBar;b.add(b.displayItem);Ext.apply(b,this.parentOverrides)}},handleProgressBarClick:function(f){var i=this.parent,g=i.displayItem,c=this.progressBar.getBox(),k=f.getXY(),d=k[0]-c.x,j=i.store,h=i.pageSize||j.pageSize,b=Math.ceil(j.getTotalCount()/h),a=Math.max(Math.ceil(d/(g.width/b)),1);j.loadPage(a)},parentOverrides:{updateInfo:function(){if(this.displayItem){var d=this.store.getCount(),b=this.getPageData(),c=d===0?this.emptyMsg:Ext.String.format(this.displayMsg,b.fromRecord,b.toRecord,this.store.getTotalCount()),a=b.pageCount>0?(b.currentPage/b.pageCount):0;this.displayItem.updateProgress(a,c,this.animate||this.defaultAnimConfig)}}}});Ext.define("Ext.ux.RowExpander",{extend:"Ext.grid.plugin.RowExpander"});Ext.define("Ext.ux.SlidingPager",{alias:"plugin.ux-slidingpager",requires:["Ext.slider.Single","Ext.slider.Tip"],constructor:function(a){if(a){Ext.apply(this,a)}},init:function(b){var a=b.items.indexOf(b.child("#inputItem")),c;Ext.each(b.items.getRange(a-2,a+2),function(d){d.hide()});c=Ext.create("Ext.slider.Single",{width:114,minValue:1,maxValue:1,hideLabel:true,tipText:function(d){return Ext.String.format("Page <b>{0}</b> of <b>{1}</b>",d.value,d.slider.maxValue)},listeners:{changecomplete:function(e,d){b.store.loadPage(d)}}});b.insert(a+1,c);b.on({change:function(d,e){c.setMaxValue(e.pageCount);c.setValue(e.currentPage)}})}});Ext.define("Ext.ux.Spotlight",{baseCls:"x-spotlight",animate:true,duration:250,easing:null,active:false,constructor:function(a){Ext.apply(this,a)},createElements:function(){var c=this,b=c.baseCls,a=Ext.getBody();c.right=a.createChild({cls:b});c.left=a.createChild({cls:b});c.top=a.createChild({cls:b});c.bottom=a.createChild({cls:b});c.all=Ext.create("Ext.CompositeElement",[c.right,c.left,c.top,c.bottom])},show:function(b,d,a){var c=this;c.el=Ext.get(b);if(!c.right){c.createElements()}if(!c.active){c.all.setDisplayed("");c.active=true;Ext.on("resize",c.syncSize,c);c.applyBounds(c.animate,false)}else{c.applyBounds(false,false)}},hide:function(c,a){var b=this;Ext.un("resize",b.syncSize,b);b.applyBounds(b.animate,true)},syncSize:function(){this.applyBounds(false,false)},applyBounds:function(a,g){var j=this,e=j.el.getBox(),f=Ext.Element.getViewportWidth(),c=Ext.Element.getViewportHeight(),d=0,b=false,l,k,h;l={right:{x:e.right,y:c,width:(f-e.right),height:0},left:{x:0,y:0,width:e.x,height:0},top:{x:f,y:0,width:0,height:e.y},bottom:{x:0,y:(e.y+e.height),width:0,height:(c-(e.y+e.height))+"px"}};k={right:{x:e.right,y:e.y,width:(f-e.right)+"px",height:(c-e.y)+"px"},left:{x:0,y:0,width:e.x+"px",height:(e.y+e.height)+"px"},top:{x:e.x,y:0,width:(f-e.x)+"px",height:e.y+"px"},bottom:{x:0,y:(e.y+e.height),width:(e.x+e.width)+"px",height:(c-(e.y+e.height))+"px"}};if(g){h=Ext.clone(l);l=k;k=h}if(a){Ext.Array.forEach(["right","left","top","bottom"],function(i){j[i].setBox(l[i]);j[i].animate({duration:j.duration,easing:j.easing,to:k[i]})},this)}else{Ext.Array.forEach(["right","left","top","bottom"],function(i){j[i].setBox(Ext.apply(l[i],k[i]));j[i].repaint()},this)}},destroy:function(){var a=this;Ext.destroy(a.right,a.left,a.top,a.bottom);delete a.el;delete a.all;a.callParent()}});Ext.define("Ext.ux.TabCloseMenu",{extend:"Ext.plugin.Abstract",alias:"plugin.tabclosemenu",mixins:{observable:"Ext.util.Observable"},closeTabText:"Close Tab",showCloseOthers:true,closeOthersTabsText:"Close Other Tabs",showCloseAll:true,closeAllTabsText:"Close All Tabs",extraItemsHead:null,extraItemsTail:null,constructor:function(a){this.callParent([a]);this.mixins.observable.constructor.call(this,a)},init:function(a){this.tabPanel=a;this.tabBar=a.down("tabbar");this.mon(this.tabPanel,{scope:this,afterlayout:this.onAfterLayout,single:true})},onAfterLayout:function(){this.mon(this.tabBar.el,{scope:this,contextmenu:this.onContextMenu,delegate:".x-tab"})},destroy:function(){Ext.destroy(this.menu);this.callParent()},onContextMenu:function(d,f){var c=this,g=c.createMenu(),e=true,h=true,b=c.tabBar.getChildByElement(f),a=c.tabBar.items.indexOf(b);c.item=c.tabPanel.getComponent(a);g.child("#close").setDisabled(!c.item.closable);if(c.showCloseAll||c.showCloseOthers){c.tabPanel.items.each(function(i){if(i.closable){e=false;if(i!==c.item){h=false;return false}}return true});if(c.showCloseAll){g.child("#closeAll").setDisabled(e)}if(c.showCloseOthers){g.child("#closeOthers").setDisabled(h)}}d.preventDefault();c.fireEvent("beforemenu",g,c.item,c);g.showAt(d.getXY())},createMenu:function(){var b=this;if(!b.menu){var a=[{itemId:"close",text:b.closeTabText,scope:b,handler:b.onClose}];if(b.showCloseAll||b.showCloseOthers){a.push("-")}if(b.showCloseOthers){a.push({itemId:"closeOthers",text:b.closeOthersTabsText,scope:b,handler:b.onCloseOthers})}if(b.showCloseAll){a.push({itemId:"closeAll",text:b.closeAllTabsText,scope:b,handler:b.onCloseAll})}if(b.extraItemsHead){a=b.extraItemsHead.concat(a)}if(b.extraItemsTail){a=a.concat(b.extraItemsTail)}b.menu=Ext.create("Ext.menu.Menu",{items:a,listeners:{hide:b.onHideMenu,scope:b}})}return b.menu},onHideMenu:function(){var a=this;a.fireEvent("aftermenu",a.menu,a)},onClose:function(){this.tabPanel.remove(this.item)},onCloseOthers:function(){this.doClose(true)},onCloseAll:function(){this.doClose(false)},doClose:function(b){var a=[];this.tabPanel.items.each(function(c){if(c.closable){if(!b||c!==this.item){a.push(c)}}},this);Ext.suspendLayouts();Ext.Array.forEach(a,function(c){this.tabPanel.remove(c)},this);Ext.resumeLayouts(true)}});Ext.define("Ext.ux.TabReorderer",{extend:"Ext.ux.BoxReorderer",alias:"plugin.tabreorderer",itemSelector:"."+Ext.baseCSSPrefix+"tab",init:function(b){var a=this;a.callParent([b.getTabBar()]);b.onAdd=Ext.Function.createSequence(b.onAdd,a.onAdd)},onBoxReady:function(){var c,a,b=0,d;this.callParent(arguments);for(c=this.container.items.items,a=c.length;b<a;b++){d=c[b];if(d.card){d.reorderable=d.card.reorderable}}},onAdd:function(b,a){b.tab.reorderable=b.reorderable},afterBoxReflow:function(){var a=this;Ext.ux.BoxReorderer.prototype.afterBoxReflow.apply(a,arguments);if(a.dragCmp){a.container.tabPanel.setActiveTab(a.dragCmp.card);a.container.tabPanel.move(a.dragCmp.card,a.curIndex)}}});Ext.ns("Ext.ux");Ext.define("Ext.ux.TabScrollerMenu",{alias:"plugin.tabscrollermenu",requires:["Ext.menu.Menu"],pageSize:10,maxText:15,menuPrefixText:"Items",constructor:function(a){Ext.apply(this,a)},init:function(b){var a=this;a.tabPanel=b;b.on({render:function(){a.tabBar=b.tabBar;a.layout=a.tabBar.layout;a.layout.overflowHandler.handleOverflow=Ext.Function.bind(a.showButton,a);a.layout.overflowHandler.clearOverflow=Ext.Function.createSequence(a.layout.overflowHandler.clearOverflow,a.hideButton,a)},destroy:a.destroy,scope:a,single:true})},showButton:function(){var c=this,a=Ext.getClass(c.layout.overflowHandler).prototype.handleOverflow.apply(c.layout.overflowHandler,arguments),b=c.menuButton;if(c.tabPanel.items.getCount()>1){if(!b){b=c.menuButton=c.tabBar.body.createChild({cls:Ext.baseCSSPrefix+"tab-tabmenu-right"},c.tabBar.body.child("."+Ext.baseCSSPrefix+"box-scroller-right"));b.addClsOnOver(Ext.baseCSSPrefix+"tab-tabmenu-over");b.on("click",c.showTabsMenu,c)}b.setVisibilityMode(Ext.dom.Element.DISPLAY);b.show();a.reservedSpace+=b.getWidth()}else{c.hideButton()}return a},hideButton:function(){var a=this;if(a.menuButton){a.menuButton.hide()}},getPageSize:function(){return this.pageSize},setPageSize:function(a){this.pageSize=a},getMaxText:function(){return this.maxText},setMaxText:function(a){this.maxText=a},getMenuPrefixText:function(){return this.menuPrefixText},setMenuPrefixText:function(a){this.menuPrefixText=a},showTabsMenu:function(d){var a=this;if(a.tabsMenu){a.tabsMenu.removeAll()}else{a.tabsMenu=new Ext.menu.Menu()}a.generateTabMenuItems();var c=Ext.get(d.getTarget()),b=c.getXY();b[1]+=24;a.tabsMenu.showAt(b)},generateTabMenuItems:function(){var l=this,h=l.tabPanel,a=h.getActiveTab(),j=h.items.getRange(),m=l.getPageSize(),e=l.tabsMenu,q,d,o,f,k,b,n,p,c,g;e.suspendLayouts();j=Ext.Array.filter(j,function(i){if(i.id==a.id){return false}return i.hidden?!!i.hiddenByLayout:true});q=j.length;d=Math.floor(q/m);o=q%m;if(q>m){for(f=0;f<d;f++){k=(f+1)*m;b=[];for(n=0;n<m;n++){g=n+k-m;p=j[g];b.push(l.autoGenMenuItem(p))}e.add({text:l.getMenuPrefixText()+" "+(k-m+1)+" - "+k,menu:b})}if(o>0){c=d*m;b=[];for(f=c;f<q;f++){p=j[f];b.push(l.autoGenMenuItem(p))}l.tabsMenu.add({text:l.menuPrefixText+" "+(c+1)+" - "+(c+b.length),menu:b})}}else{for(f=0;f<q;++f){e.add(l.autoGenMenuItem(j[f]))}}e.resumeLayouts(true)},autoGenMenuItem:function(b){var a=this.getMaxText(),c=Ext.util.Format.ellipsis(b.title,a);return{text:c,handler:this.showTabFromMenu,scope:this,disabled:b.disabled,tabToShow:b,iconCls:b.iconCls}},showTabFromMenu:function(a){this.tabPanel.setActiveTab(a.tabToShow)},destroy:function(){Ext.destroy(this.tabsMenu,this.menuButton);this.callParent()}});Ext.define("Ext.ux.ToolbarDroppable",{constructor:function(a){Ext.apply(this,a)},init:function(a){this.toolbar=a;this.toolbar.on({scope:this,render:this.createDropTarget})},createDropTarget:function(){this.dropTarget=Ext.create("Ext.dd.DropTarget",this.toolbar.getEl(),{notifyOver:Ext.Function.bind(this.notifyOver,this),notifyDrop:Ext.Function.bind(this.notifyDrop,this)})},addDDGroup:function(a){this.dropTarget.addToGroup(a)},calculateEntryIndex:function(h){var j=0,k=this.toolbar,i=k.items.items,f=i.length,b=h.getXY()[0],g=0,c,d,a,l;for(;g<f;g++){c=i[g].getEl();d=c.getXY()[0];a=c.getWidth();l=d+a/2;if(b<l){j=g;break}else{j=g+1}}return j},canDrop:function(a){return true},notifyOver:function(a,b,c){return this.canDrop.apply(this,arguments)?this.dropTarget.dropAllowed:this.dropTarget.dropNotAllowed},notifyDrop:function(a,d,e){var c=this.canDrop(a,d,e),f=this.toolbar;if(c){var b=this.calculateEntryIndex(d);f.insert(b,this.createItem(e));this.afterLayout()}return c},createItem:function(a){},afterLayout:Ext.emptyFn});Ext.define("Ext.ux.TreePicker",{extend:"Ext.form.field.Picker",xtype:"treepicker",uses:["Ext.tree.Panel"],triggerCls:Ext.baseCSSPrefix+"form-arrow-trigger",config:{store:null,displayField:null,columns:null,selectOnTab:true,maxPickerHeight:300,minPickerHeight:100},editable:false,initComponent:function(){var a=this;a.callParent(arguments);a.mon(a.store,{scope:a,load:a.onLoad,update:a.onUpdate})},createPicker:function(){var c=this,b=new Ext.tree.Panel({baseCls:Ext.baseCSSPrefix+"boundlist",shrinkWrapDock:2,store:c.store,floating:true,displayField:c.displayField,columns:c.columns,minHeight:c.minPickerHeight,maxHeight:c.maxPickerHeight,manageHeight:false,shadow:false,listeners:{scope:c,itemclick:c.onItemClick,itemkeydown:c.onPickerKeyDown}}),a=b.getView();if(Ext.isIE9&&Ext.isStrict){a.on({scope:c,highlightitem:c.repaintPickerView,unhighlightitem:c.repaintPickerView,afteritemexpand:c.repaintPickerView,afteritemcollapse:c.repaintPickerView})}return b},repaintPickerView:function(){var a=this.picker.getView().getEl().dom.style;a.display=a.display},onItemClick:function(b,a,c,f,d){this.selectItem(a)},onPickerKeyDown:function(b,a,f,c,g){var d=g.getKey();if(d===g.ENTER||(d===g.TAB&&this.selectOnTab)){this.selectItem(a)}},selectItem:function(a){var b=this;b.setValue(a.getId());b.fireEvent("select",b,a);b.collapse()},onExpand:function(){var b=this.picker,a=b.store,d=this.value,c;if(d){c=a.getNodeById(d)}if(!c){c=a.getRoot()}b.ensureVisible(c,{select:true,focus:true})},setValue:function(c){var b=this,a;b.value=c;if(b.store.loading){return b}a=c?b.store.getNodeById(c):b.store.getRoot();if(c===undefined){a=b.store.getRoot();b.value=a.getId()}else{a=b.store.getNodeById(c)}b.setRawValue(a?a.get(b.displayField):"");return b},getSubmitValue:function(){return this.value},getValue:function(){return this.value},onLoad:function(){var a=this.value;if(a){this.setValue(a)}},onUpdate:function(a,e,b,c){var d=this.displayField;if(b==="edit"&&c&&Ext.Array.contains(c,d)&&this.value===e.getId()){this.setRawValue(e.get(d))}}});Ext.define("Ext.ux.colorpick.Selection",{mixinId:"colorselection",config:{format:"hex6",value:"FF0000",color:null,previousColor:null},applyColor:function(a){var b=a;if(Ext.isString(b)){b=Ext.ux.colorpick.ColorUtils.parseColor(a)}return b},applyValue:function(a){var b=Ext.ux.colorpick.ColorUtils.parseColor(a||"#000000");return this.formatColor(b)},formatColor:function(a){return Ext.ux.colorpick.ColorUtils.formats[this.getFormat()](a)},updateColor:function(a){var b=this;if(!b.syncing){b.syncing=true;b.setValue(b.formatColor(a));b.syncing=false}},updateValue:function(c,a){var b=this;if(!b.syncing){b.syncing=true;b.setColor(c);b.syncing=false}this.fireEvent("change",b,c,a)}});Ext.define("Ext.ux.colorpick.ColorUtils",function(a){var b=Ext.isIE&&Ext.ieVersion<10;return{singleton:true,constructor:function(){a=this},backgroundTpl:b?"filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#{alpha}{hex}', endColorstr='#{alpha}{hex}');":"background: {rgba};",setBackground:b?function(e,c){if(e){var d=Ext.XTemplate.getTpl(a,"backgroundTpl"),f={hex:a.rgb2hex(c.r,c.g,c.b),alpha:Math.floor(c.a*255).toString(16)},g=d.apply(f);e.applyStyles(g)}}:function(e,c){if(e){var d=Ext.XTemplate.getTpl(a,"backgroundTpl"),f={rgba:a.getRGBAString(c)},g=d.apply(f);e.applyStyles(g)}},formats:{HEX6:function(c){return a.rgb2hex(c.r,c.g,c.b)},HEX8:function(c){var e=a.rgb2hex(c.r,c.g,c.b),d=Math.round(c.a*255).toString(16);if(d.length<2){e+="0"}e+=d.toUpperCase();return e}},hexRe:/#?([0-9a-f]{3,8})/i,rgbaAltRe:/rgba\(\s*([\w#\d]+)\s*,\s*([\d\.]+)\s*\)/,rgbaRe:/rgba\(\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)/,rgbRe:/rgb\(\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)/,parseColor:function(c){if(!c){return null}var h=this,g=h.colorMap[c],f,e,d;if(g){e={r:g[0],g:g[1],b:g[2],a:1}}else{if(c==="transparent"){e={r:0,g:0,b:0,a:0}}else{f=h.hexRe.exec(c);if(f){f=f[1];switch(f.length){default:return null;case 3:e={r:parseInt(f[0]+f[0],16),g:parseInt(f[1]+f[1],16),b:parseInt(f[2]+f[2],16),a:1};break;case 6:case 8:e={r:parseInt(f.substr(0,2),16),g:parseInt(f.substr(2,2),16),b:parseInt(f.substr(4,2),16),a:parseInt(f.substr(6,2)||"ff",16)/255};break}}else{f=h.rgbaRe.exec(c);if(f){e={r:parseFloat(f[1]),g:parseFloat(f[2]),b:parseFloat(f[3]),a:parseFloat(f[4])}}else{f=h.rgbaAltRe.exec(c);if(f){e=h.parseColor(f[1]);e.a=parseFloat(f[2]);return e}f=h.rgbRe.exec(c);if(f){e={r:parseFloat(f[1]),g:parseFloat(f[2]),b:parseFloat(f[3]),a:1}}else{return null}}}}}d=this.rgb2hsv(e.r,e.g,e.b);return Ext.apply(e,d)},getRGBAString:function(c){return"rgba("+c.r+","+c.g+","+c.b+","+c.a+")"},getRGBString:function(c){return"rgb("+c.r+","+c.g+","+c.b+")"},hsv2rgb:function(k,j,g){k=k*360;if(k===360){k=0}var l=g*j;var f=k/60;var e=l*(1-Math.abs(f%2-1));var i=[0,0,0];switch(Math.floor(f)){case 0:i=[l,e,0];break;case 1:i=[e,l,0];break;case 2:i=[0,l,e];break;case 3:i=[0,e,l];break;case 4:i=[e,0,l];break;case 5:i=[l,0,e];break;default:break}var d=g-l;i[0]+=d;i[1]+=d;i[2]+=d;i[0]=Math.round(i[0]*255);i[1]=Math.round(i[1]*255);i[2]=Math.round(i[2]*255);return{r:i[0],g:i[1],b:i[2]}},rgb2hsv:function(d,i,l){d=d/255;i=i/255;l=l/255;var j=Math.max(d,i,l);var e=Math.min(d,i,l);var k=j-e;var o=0;if(k!==0){if(j===d){o=((i-l)/k)%6}else{if(j===i){o=((l-d)/k)+2}else{if(j===l){o=((d-i)/k)+4}}}}var f=o*60;if(f===360){f=0}var n=j;var p=0;if(k!==0){p=k/n}f=f/360;if(f<0){f=f+1}return{h:f,s:p,v:n}},rgb2hex:function(e,d,c){e=e.toString(16);d=d.toString(16);c=c.toString(16);if(e.length<2){e="0"+e}if(d.length<2){d="0"+d}if(c.length<2){c="0"+c}return(e+d+c).toUpperCase()},colorMap:{aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]}}},function(c){var a=c.formats,b={};a["#HEX6"]=function(d){return"#"+a.HEX6(d)};a["#HEX8"]=function(d){return"#"+a.HEX8(d)};Ext.Object.each(a,function(d,e){b[d.toLowerCase()]=function(f){var g=e(f);return g.toLowerCase()}});Ext.apply(a,b)});Ext.define("Ext.ux.colorpick.ColorMapController",{extend:"Ext.app.ViewController",alias:"controller.colorpickercolormapcontroller",requires:["Ext.ux.colorpick.ColorUtils"],onFirstBoxReady:function(){var d=this,c=d.getView(),b=c.down("#dragHandle"),a=b.dd;a.constrain=true;a.constrainTo=c.getEl();a.initialConstrainTo=a.constrainTo;a.on("drag",Ext.bind(d.onHandleDrag,d));d.mon(c.getEl(),{mousedown:d.onMouseDown,dragstart:d.onDragStart,scope:d})},onHandleDrag:function(c,g){var i=this,a=i.getView(),h=a.down("#dragHandle"),l=h.getX()-a.getX(),j=h.getY()-a.getY(),f=a.getEl(),k=f.getWidth(),m=f.getHeight(),b=l/k,d=j/m;if(b>0.99){b=1}if(d>0.99){d=1}a.fireEvent("handledrag",b,d)},onMouseDown:function(d){var c=this,b=c.getView(),a=b.down("#dragHandle");a.setY(d.getY());a.setX(d.getX());c.onHandleDrag();a.dd.onMouseDown(d,a.dd.el)},onDragStart:function(d){var c=this,b=c.getView(),a=b.down("#dragHandle");a.dd.onDragStart(d,a.dd.el)},onMapClick:function(h){var d=this,c=d.getView(),a=c.down("#dragHandle"),i=c.getXY(),b=h.getXY(),g,f;g=b[0]-i[0];f=b[1]-i[1];a.getEl().setStyle({left:g+"px",top:f+"px"});d.onHandleDrag()},onColorBindingChanged:function(a){var k=this,d=k.getViewModel(),f=d.get("selectedColor"),g,b=k.getView(),j=b.down("#dragHandle"),i=b.getEl(),m=i.getWidth(),n=i.getHeight(),c,h,e,l;g=Ext.ux.colorpick.ColorUtils.rgb2hsv(f.r,f.g,f.b);c=g.s;e=m*c;h=1-g.v;l=n*h;j.getEl().setStyle({left:e+"px",top:l+"px"})},onHueBindingChanged:function(b){var e=this,c=e.getViewModel(),a,d;a=Ext.ux.colorpick.ColorUtils.hsv2rgb(b,1,1);d=Ext.ux.colorpick.ColorUtils.rgb2hex(a.r,a.g,a.b);e.getView().getEl().applyStyles({"background-color":"#"+d})}});Ext.define("Ext.ux.colorpick.ColorMap",{extend:"Ext.container.Container",alias:"widget.colorpickercolormap",controller:"colorpickercolormapcontroller",requires:["Ext.ux.colorpick.ColorMapController"],cls:Ext.baseCSSPrefix+"colorpicker-colormap",items:[{xtype:"component",cls:Ext.baseCSSPrefix+"colorpicker-colormap-draghandle-container",itemId:"dragHandle",width:1,height:1,draggable:true,html:'<div class="'+Ext.baseCSSPrefix+'colorpicker-colormap-draghandle"></div>'}],listeners:{boxready:{single:true,fn:"onFirstBoxReady",scope:"controller"},colorbindingchanged:{fn:"onColorBindingChanged",scope:"controller"},huebindingchanged:{fn:"onHueBindingChanged",scope:"controller"}},afterRender:function(){var b=this,c=b.mapGradientUrl,a=b.el;b.callParent();if(!c){c=a.getStyle("background-image");c=c.substring(4,c.length-1);if(c.indexOf('"')===0){c=c.substring(1,c.length-1)}Ext.ux.colorpick.ColorMap.prototype.mapGradientUrl=c}a.setStyle("background-image","none");a=b.layout.getElementTarget();a.createChild({tag:"img",cls:Ext.baseCSSPrefix+"colorpicker-colormap-blender",src:c})},setPosition:function(c){var b=this,a=b.down("#dragHandle");if(!a.dd||!a.dd.constrain){return}if(typeof a.dd.dragEnded!=="undefined"&&!a.dd.dragEnded){return}b.fireEvent("colorbindingchanged",c)},setHue:function(a){var b=this;if(!b.getEl()){return}b.fireEvent("huebindingchanged",a)}});Ext.define("Ext.ux.colorpick.SelectorModel",{extend:"Ext.app.ViewModel",alias:"viewmodel.colorpick-selectormodel",requires:["Ext.ux.colorpick.ColorUtils"],data:{selectedColor:{r:255,g:255,b:255,h:0,s:1,v:1,a:1},previousColor:{r:0,g:0,b:0,h:0,s:1,v:1,a:1}},formulas:{hex:{get:function(d){var f=d("selectedColor.r").toString(16),e=d("selectedColor.g").toString(16),c=d("selectedColor.b").toString(16),a;a=Ext.ux.colorpick.ColorUtils.rgb2hex(f,e,c);return"#"+a},set:function(b){var a=Ext.ux.colorpick.ColorUtils.hex2rgb(b);this.changeRGB(a)}},red:{get:function(a){return a("selectedColor.r")},set:function(a){this.changeRGB({r:a})}},green:{get:function(a){return a("selectedColor.g")},set:function(a){this.changeRGB({g:a})}},blue:{get:function(a){return a("selectedColor.b")},set:function(a){this.changeRGB({b:a})}},hue:{get:function(a){return a("selectedColor.h")*360},set:function(a){this.changeHSV({h:a/360})}},saturation:{get:function(a){return a("selectedColor.s")*100},set:function(a){this.changeHSV({s:a/100})}},value:{get:function(b){var a=b("selectedColor.v");return a*100},set:function(a){this.changeHSV({v:a/100})}},alpha:{get:function(c){var b=c("selectedColor.a");return b*100},set:function(a){this.set("selectedColor",Ext.applyIf({a:a/100},this.data.selectedColor))}}},changeHSV:function(b){Ext.applyIf(b,this.data.selectedColor);var a=Ext.ux.colorpick.ColorUtils.hsv2rgb(b.h,b.s,b.v);b.r=a.r;b.g=a.g;b.b=a.b;this.set("selectedColor",b)},changeRGB:function(b){Ext.applyIf(b,this.data.selectedColor);var a=Ext.ux.colorpick.ColorUtils.rgb2hsv(b.r,b.g,b.b);b.h=a.h;b.s=a.s;b.v=a.v;this.set("selectedColor",b)}});Ext.define("Ext.ux.colorpick.SelectorController",{extend:"Ext.app.ViewController",alias:"controller.colorpick-selectorcontroller",requires:["Ext.ux.colorpick.ColorUtils"],destroy:function(){var c=this,b=c.getView(),a=b.childViewModel;if(a){a.destroy();b.childViewModel=null}c.callParent()},changeHSV:function(d){var a=this.getView(),b=a.getColor(),c;Ext.applyIf(d,b);c=Ext.ux.colorpick.ColorUtils.hsv2rgb(d.h,d.s,d.v);Ext.apply(d,c);a.setColor(d)},onColorMapHandleDrag:function(b,a){this.changeHSV({s:b,v:1-a})},onValueSliderHandleDrag:function(a){this.changeHSV({v:1-a})},onSaturationSliderHandleDrag:function(a){this.changeHSV({s:1-a})},onHueSliderHandleDrag:function(a){this.changeHSV({h:1-a})},onAlphaSliderHandleDrag:function(c){var a=this.getView(),b=a.getColor(),d=Ext.applyIf({a:1-c},b);a.setColor(d);a.el.repaint()},onPreviousColorSelected:function(c,b){var a=this.getView();a.setColor(b)},onOK:function(){var b=this,a=b.getView();a.fireEvent("ok",a,a.getValue())},onCancel:function(){this.fireViewEvent("cancel",this.getView())},onResize:function(){var j=this,c=j.getView(),i=c.childViewModel,e=j.getReferences(),g,f,d,b;if(!j.hasResizedOnce){j.hasResizedOnce=true;return}g=i.get("hue");f=i.get("saturation");d=i.get("value");b=i.get("alpha");e.colorMap.setPosition(i.getData());e.hueSlider.setHue(g);e.satSlider.setSaturation(f);e.valueSlider.setValue(d);e.alphaSlider.setAlpha(b)}});Ext.define("Ext.ux.colorpick.ColorPreview",{extend:"Ext.Component",alias:"widget.colorpickercolorpreview",requires:["Ext.util.Format"],style:"position: relative",html:'<div class="'+Ext.baseCSSPrefix+'colorpreview-filter" style="height:100%; width:100%; position: absolute;"></div><a class="btn" style="height:100%; width:100%; position: absolute;"></a>',cls:Ext.baseCSSPrefix+"colorpreview",height:256,onRender:function(){var a=this;a.callParent(arguments);a.mon(a.el.down(".btn"),"click",a.onClick,a)},onClick:function(){this.fireEvent("click",this,this.color)},setColor:function(a){var c=this,b=c.getEl();if(!b){return}c.color=a;c.applyBgStyle(a)},bgStyleTpl:Ext.create("Ext.XTemplate",Ext.isIE&&Ext.ieVersion<10?"filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#{hexAlpha}{hex}', endColorstr='#{hexAlpha}{hex}');":"background: {rgba};"),applyBgStyle:function(g){var i=this,a=Ext.ux.colorpick.ColorUtils,b="."+Ext.baseCSSPrefix+"colorpreview-filter",c=i.getEl().down(b),d,e,f,h;d=a.rgb2hex(g.r,g.g,g.b);e=Ext.util.Format.hex(Math.floor(g.a*255),2);f=a.getRGBAString(g);h=this.bgStyleTpl.apply({hex:d,hexAlpha:e,rgba:f});c.applyStyles(h)}});Ext.define("Ext.ux.colorpick.SliderController",{extend:"Ext.app.ViewController",alias:"controller.colorpick-slidercontroller",boxReady:function(d){var e=this,c=e.getDragContainer(),b=e.getDragHandle(),a=b.dd;a.constrain=true;a.constrainTo=c.getEl();a.initialConstrainTo=a.constrainTo;a.on("drag",e.onHandleDrag,e)},getDragHandle:function(){return this.view.lookupReference("dragHandle")},getDragContainer:function(){return this.view.lookupReference("dragHandleContainer")},onHandleDrag:function(d){var g=this,i=g.getView(),a=g.getDragContainer(),f=g.getDragHandle(),h=f.getY()-a.getY(),c=a.getEl(),j=c.getHeight(),b=h/j;if(b>0.99){b=1}i.fireEvent("handledrag",b)},onMouseDown:function(c){var b=this,a=b.getDragHandle(),d=c.getY();a.setY(d);b.onHandleDrag();a.el.repaint();a.dd.onMouseDown(c,a.dd.el)},onDragStart:function(c){var b=this,a=b.getDragHandle();a.dd.onDragStart(c,a.dd.el)},onMouseUp:function(){var a=this.getDragHandle();a.dd.dragEnded=true}});Ext.define("Ext.ux.colorpick.Slider",{extend:"Ext.container.Container",xtype:"colorpickerslider",controller:"colorpick-slidercontroller",afterRender:function(){this.callParent(arguments);var a=this.width,c=this.lookupReference("dragHandleContainer"),b=c.getWidth();c.el.setStyle("left",((a-b)/2)+"px")},baseCls:Ext.baseCSSPrefix+"colorpicker-slider",requires:["Ext.ux.colorpick.SliderController"],referenceHolder:true,listeners:{element:"el",mousedown:"onMouseDown",mouseup:"onMouseUp",dragstart:"onDragStart"},items:{xtype:"container",cls:Ext.baseCSSPrefix+"colorpicker-draghandle-container",reference:"dragHandleContainer",height:"100%",items:{xtype:"component",cls:Ext.baseCSSPrefix+"colorpicker-draghandle-outer",reference:"dragHandle",width:"100%",height:1,draggable:true,html:'<div class="'+Ext.baseCSSPrefix+'colorpicker-draghandle"></div>'}},getDragHandle:function(){return this.lookupReference("dragHandle")},getDragContainer:function(){return this.lookupReference("dragHandleContainer")}});Ext.define("Ext.ux.colorpick.SliderAlpha",{extend:"Ext.ux.colorpick.Slider",alias:"widget.colorpickerslideralpha",cls:Ext.baseCSSPrefix+"colorpicker-alpha",requires:["Ext.XTemplate"],gradientStyleTpl:Ext.create("Ext.XTemplate",Ext.isIE&&Ext.ieVersion<10?"filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#FF{hex}', endColorstr='#00{hex}');":"background: -moz-linear-gradient(top, rgba({r}, {g}, {b}, 1) 0%, rgba({r}, {g}, {b}, 0) 100%);background: -webkit-linear-gradient(top,rgba({r}, {g}, {b}, 1) 0%, rgba({r}, {g}, {b}, 0) 100%);background: -o-linear-gradient(top, rgba({r}, {g}, {b}, 1) 0%, rgba({r}, {g}, {b}, 0) 100%);background: -ms-linear-gradient(top, rgba({r}, {g}, {b}, 1) 0%, rgba({r}, {g}, {b}, 0) 100%);background: linear-gradient(to bottom, rgba({r}, {g}, {b}, 1) 0%, rgba({r}, {g}, {b}, 0) 100%);"),setAlpha:function(g){var d=this,b=d.getDragContainer(),a=d.getDragHandle(),f=b.getEl(),e=f.getHeight(),c,h;if(!a.dd||!a.dd.constrain){return}if(typeof a.dd.dragEnded!=="undefined"&&!a.dd.dragEnded){return}h=e*(1-(g/100));c=a.getEl();c.setStyle({top:h+"px"})},setColor:function(b){var e=this,a=e.getDragContainer(),d,c;if(!e.getEl()){return}d=Ext.ux.colorpick.ColorUtils.rgb2hex(b.r,b.g,b.b);c=a.getEl().first();c.applyStyles(e.gradientStyleTpl.apply({hex:d,r:b.r,g:b.g,b:b.b}))}});Ext.define("Ext.ux.colorpick.SliderSaturation",{extend:"Ext.ux.colorpick.Slider",alias:"widget.colorpickerslidersaturation",cls:Ext.baseCSSPrefix+"colorpicker-saturation",gradientStyleTpl:Ext.create("Ext.XTemplate",Ext.isIE&&Ext.ieVersion<10?"filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#{hex}', endColorstr='#ffffff');":"background: -mox-linear-gradient(top, #{hex} 0%, #ffffff 100%);background: -webkit-linear-gradient(top, #{hex} 0%,#ffffff 100%);background: -o-linear-gradient(top, #{hex} 0%,#ffffff 100%);background: -ms-linear-gradient(top, #{hex} 0%,#ffffff 100%);background: linear-gradient(to bottom, #{hex} 0%,#ffffff 100%);"),setSaturation:function(e){var d=this,b=d.getDragContainer(),a=d.getDragHandle(),g=b.getEl(),f=g.getHeight(),c,h;if(!a.dd||!a.dd.constrain){return}if(typeof a.dd.dragEnded!=="undefined"&&!a.dd.dragEnded){return}c=1-(e/100);h=f*c;a.getEl().setStyle({top:h+"px"})},setHue:function(b){var e=this,a=e.getDragContainer(),c,d;if(!e.getEl()){return}c=Ext.ux.colorpick.ColorUtils.hsv2rgb(b,1,1);d=Ext.ux.colorpick.ColorUtils.rgb2hex(c.r,c.g,c.b);a.getEl().applyStyles(e.gradientStyleTpl.apply({hex:d}))}});Ext.define("Ext.ux.colorpick.SliderValue",{extend:"Ext.ux.colorpick.Slider",alias:"widget.colorpickerslidervalue",cls:Ext.baseCSSPrefix+"colorpicker-value",requires:["Ext.XTemplate"],gradientStyleTpl:Ext.create("Ext.XTemplate",Ext.isIE&&Ext.ieVersion<10?"filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#{hex}', endColorstr='#000000');":"background: -mox-linear-gradient(top, #{hex} 0%, #000000 100%);background: -webkit-linear-gradient(top, #{hex} 0%,#000000 100%);background: -o-linear-gradient(top, #{hex} 0%,#000000 100%);background: -ms-linear-gradient(top, #{hex} 0%,#000000 100%);background: linear-gradient(to bottom, #{hex} 0%,#000000 100%);"),setValue:function(g){var d=this,b=d.getDragContainer(),a=d.getDragHandle(),f=b.getEl(),e=f.getHeight(),c,h;if(!a.dd||!a.dd.constrain){return}if(typeof a.dd.dragEnded!=="undefined"&&!a.dd.dragEnded){return}c=1-(g/100);h=e*c;a.getEl().setStyle({top:h+"px"})},setHue:function(b){var e=this,a=e.getDragContainer(),c,d;if(!e.getEl()){return}c=Ext.ux.colorpick.ColorUtils.hsv2rgb(b,1,1);d=Ext.ux.colorpick.ColorUtils.rgb2hex(c.r,c.g,c.b);a.getEl().applyStyles(e.gradientStyleTpl.apply({hex:d}))}});Ext.define("Ext.ux.colorpick.SliderHue",{extend:"Ext.ux.colorpick.Slider",alias:"widget.colorpickersliderhue",cls:Ext.baseCSSPrefix+"colorpicker-hue",afterRender:function(){var b=this,c=b.gradientUrl,a=b.el;b.callParent();if(!c){c=a.getStyle("background-image");c=c.substring(4,c.length-1);if(c.indexOf('"')===0){c=c.substring(1,c.length-1)}Ext.ux.colorpick.SliderHue.prototype.gradientUrl=c}a.setStyle("background-image","none");a=b.getDragContainer().layout.getElementTarget();a.createChild({tag:"img",cls:Ext.baseCSSPrefix+"colorpicker-hue-gradient",src:c})},setHue:function(c){var e=this,b=e.getDragContainer(),a=e.getDragHandle(),g=b.getEl(),f=g.getHeight(),d,h;if(!a.dd||!a.dd.constrain){return}if(typeof a.dd.dragEnded!=="undefined"&&!a.dd.dragEnded){return}h=f*(1-c);d=a.getEl();d.setStyle({top:h+"px"})}});Ext.define("Ext.ux.colorpick.Selector",{extend:"Ext.container.Container",xtype:"colorselector",mixins:["Ext.ux.colorpick.Selection"],controller:"colorpick-selectorcontroller",requires:["Ext.layout.container.HBox","Ext.form.field.Text","Ext.form.field.Number","Ext.ux.colorpick.ColorMap","Ext.ux.colorpick.SelectorModel","Ext.ux.colorpick.SelectorController","Ext.ux.colorpick.ColorPreview","Ext.ux.colorpick.Slider","Ext.ux.colorpick.SliderAlpha","Ext.ux.colorpick.SliderSaturation","Ext.ux.colorpick.SliderValue","Ext.ux.colorpick.SliderHue"],width:580,height:337,cls:Ext.baseCSSPrefix+"colorpicker",padding:10,layout:{type:"hbox",align:"stretch"},defaultBindProperty:"value",twoWayBindable:["value"],fieldWidth:50,fieldPad:5,showPreviousColor:false,showOkCancelButtons:false,listeners:{resize:"onResize"},constructor:function(b){var c=this,a=Ext.Factory.viewModel("colorpick-selectormodel");c.childViewModel=a;c.items=[c.getMapAndHexRGBFields(a),c.getSliderAndHField(a),c.getSliderAndSField(a),c.getSliderAndVField(a),c.getSliderAndAField(a),c.getPreviewAndButtons(a,b)];c.childViewModel.bind("{selectedColor}",function(d){c.setColor(d)});c.callParent(arguments)},updateColor:function(a){var b=this;b.mixins.colorselection.updateColor.call(b,a);b.childViewModel.set("selectedColor",a)},updatePreviousColor:function(a){this.childViewModel.set("previousColor",a)},getMapAndHexRGBFields:function(a){var c=this,d={top:0,right:c.fieldPad,bottom:0,left:0},b=c.fieldWidth;return{xtype:"container",viewModel:a,cls:Ext.baseCSSPrefix+"colorpicker-escape-overflow",flex:1,layout:{type:"vbox",align:"stretch"},margin:"0 10 0 0",items:[{xtype:"colorpickercolormap",reference:"colorMap",flex:1,bind:{position:{bindTo:"{selectedColor}",deep:true},hue:"{selectedColor.h}"},listeners:{handledrag:"onColorMapHandleDrag"}},{xtype:"container",layout:"hbox",defaults:{labelAlign:"top",labelSeparator:"",allowBlank:false,onChange:function(){if(this.isValid()){Ext.form.field.Base.prototype.onChange.apply(this,arguments)}}},items:[{xtype:"textfield",fieldLabel:"HEX",flex:1,bind:"{hex}",margin:d,readOnly:true},{xtype:"numberfield",fieldLabel:"R",bind:"{red}",width:b,hideTrigger:true,maxValue:255,minValue:0,margin:d},{xtype:"numberfield",fieldLabel:"G",bind:"{green}",width:b,hideTrigger:true,maxValue:255,minValue:0,margin:d},{xtype:"numberfield",fieldLabel:"B",bind:"{blue}",width:b,hideTrigger:true,maxValue:255,minValue:0,margin:0}]}]}},getSliderAndHField:function(a){var c=this,b=c.fieldWidth;return{xtype:"container",viewModel:a,cls:Ext.baseCSSPrefix+"colorpicker-escape-overflow",width:b,layout:{type:"vbox",align:"stretch"},items:[{xtype:"colorpickersliderhue",reference:"hueSlider",flex:1,bind:{hue:"{selectedColor.h}"},width:b,listeners:{handledrag:"onHueSliderHandleDrag"}},{xtype:"numberfield",fieldLabel:"H",labelAlign:"top",labelSeparator:"",bind:"{hue}",hideTrigger:true,maxValue:360,minValue:0,allowBlank:false,margin:0}]}},getSliderAndSField:function(a){var c=this,b=c.fieldWidth;return{xtype:"container",viewModel:a,cls:Ext.baseCSSPrefix+"colorpicker-escape-overflow",width:b,layout:{type:"vbox",align:"stretch"},margin:{right:c.fieldPad,left:c.fieldPad},items:[{xtype:"colorpickerslidersaturation",reference:"satSlider",flex:1,bind:{saturation:"{saturation}",hue:"{selectedColor.h}"},width:b,listeners:{handledrag:"onSaturationSliderHandleDrag"}},{xtype:"numberfield",fieldLabel:"S",labelAlign:"top",labelSeparator:"",bind:"{saturation}",hideTrigger:true,maxValue:100,minValue:0,allowBlank:false,margin:0}]}},getSliderAndVField:function(a){var c=this,b=c.fieldWidth;return{xtype:"container",viewModel:a,cls:Ext.baseCSSPrefix+"colorpicker-escape-overflow",width:b,layout:{type:"vbox",align:"stretch"},items:[{xtype:"colorpickerslidervalue",reference:"valueSlider",flex:1,bind:{value:"{value}",hue:"{selectedColor.h}"},width:b,listeners:{handledrag:"onValueSliderHandleDrag"}},{xtype:"numberfield",fieldLabel:"V",labelAlign:"top",labelSeparator:"",bind:"{value}",hideTrigger:true,maxValue:100,minValue:0,allowBlank:false,margin:0}]}},getSliderAndAField:function(a){var c=this,b=c.fieldWidth;return{xtype:"container",viewModel:a,cls:Ext.baseCSSPrefix+"colorpicker-escape-overflow",width:b,layout:{type:"vbox",align:"stretch"},margin:{left:c.fieldPad},items:[{xtype:"colorpickerslideralpha",reference:"alphaSlider",flex:1,bind:{alpha:"{alpha}",color:{bindTo:"{selectedColor}",deep:true}},width:b,listeners:{handledrag:"onAlphaSliderHandleDrag"}},{xtype:"numberfield",fieldLabel:"A",labelAlign:"top",labelSeparator:"",bind:"{alpha}",hideTrigger:true,maxValue:100,minValue:0,allowBlank:false,margin:0}]}},getPreviewAndButtons:function(a,c){var b=[{xtype:"colorpickercolorpreview",flex:1,bind:{color:{bindTo:"{selectedColor}",deep:true}}}];if(c.showPreviousColor){b.push({xtype:"colorpickercolorpreview",flex:1,bind:{color:{bindTo:"{previousColor}",deep:true}},listeners:{click:"onPreviousColorSelected"}})}if(c.showOkCancelButtons){b.push({xtype:"button",text:"OK",margin:"10 0 0 0",handler:"onOK"},{xtype:"button",text:"Cancel",margin:"10 0 0 0",handler:"onCancel"})}return{xtype:"container",viewModel:a,width:70,margin:"0 0 0 10",items:b,layout:{type:"vbox",align:"stretch"}}}});Ext.define("Ext.ux.colorpick.ButtonController",{extend:"Ext.app.ViewController",alias:"controller.colorpick-buttoncontroller",requires:["Ext.window.Window","Ext.layout.container.Fit","Ext.ux.colorpick.Selector","Ext.ux.colorpick.ColorUtils"],afterRender:function(a){a.updateColor(a.getColor())},destroy:function(){var a=this.getView(),b=a.colorPickerWindow;if(b){b.destroy();a.colorPickerWindow=a.colorPicker=null}this.callParent()},getPopup:function(){var c=this.getView(),b=c.colorPickerWindow,a;if(!b){b=Ext.create(c.getPopup());c.colorPickerWindow=b;b.colorPicker=c.colorPicker=a=b.lookupReference("selector");a.setFormat(c.getFormat());a.on({ok:"onColorPickerOK",cancel:"onColorPickerCancel",scope:this});b.on({close:"onColorPickerCancel",scope:this})}return b},onClick:function(){var e=this,c=e.getView(),d=c.getColor(),b=e.getPopup(),a=b.colorPicker;a.setColor(d);a.setPreviousColor(d);b.showBy(c,"tl-br?")},onColorPickerOK:function(c){var a=this.getView(),b=c.getColor(),d=a.colorPickerWindow;d.hide();a.setColor(b)},onColorPickerCancel:function(){var a=this.getView(),b=a.colorPickerWindow;b.hide()},syncColor:function(b){var a=this.getView();Ext.ux.colorpick.ColorUtils.setBackground(a.filterEl,b)}});Ext.define("Ext.ux.colorpick.Button",{extend:"Ext.Component",xtype:"colorbutton",controller:"colorpick-buttoncontroller",mixins:["Ext.ux.colorpick.Selection"],requires:["Ext.ux.colorpick.ButtonController"],baseCls:Ext.baseCSSPrefix+"colorpicker-button",width:20,height:20,childEls:["btnEl","filterEl"],config:{popup:{lazy:true,$value:{xtype:"window",closeAction:"hide",referenceHolder:true,minWidth:540,minHeight:200,layout:"fit",header:false,resizable:true,items:{xtype:"colorselector",reference:"selector",showPreviousColor:true,showOkCancelButtons:true}}}},defaultBindProperty:"value",twoWayBindable:"value",renderTpl:'<div id="{id}-filterEl" data-ref="filterEl" style="height:100%; width:100%; position: absolute;"></div><a id="{id}-btnEl" data-ref="btnEl" style="height:100%; width:100%; position: absolute;"></a>',listeners:{click:"onClick",element:"btnEl"},updateColor:function(a){var b=this,c=b.colorPicker;b.mixins.colorselection.updateColor.call(b,a);Ext.ux.colorpick.ColorUtils.setBackground(b.filterEl,a);if(c){c.setColor(a)}},updateFormat:function(b){var a=this.colorPicker;if(a){a.setFormat(b)}}});Ext.define("Ext.ux.colorpick.Field",{extend:"Ext.form.field.Picker",xtype:"colorfield",mixins:["Ext.ux.colorpick.Selection"],requires:["Ext.window.Window","Ext.ux.colorpick.Selector","Ext.ux.colorpick.ColorUtils","Ext.layout.container.Fit"],editable:false,matchFieldWidth:false,beforeBodyEl:['<div class="'+Ext.baseCSSPrefix+'colorpicker-field-swatch"><div id="{id}-swatchEl" data-ref="swatchEl" class="'+Ext.baseCSSPrefix+'colorpicker-field-swatch-inner"></div></div>'],cls:Ext.baseCSSPrefix+"colorpicker-field",childEls:["swatchEl"],config:{popup:{lazy:true,$value:{xtype:"window",closeAction:"hide",referenceHolder:true,minWidth:540,minHeight:200,layout:"fit",header:false,resizable:true,items:{xtype:"colorselector",reference:"selector",showPreviousColor:true,showOkCancelButtons:true}}}},afterRender:function(){this.callParent();this.updateValue(this.value)},createPicker:function(){var c=this,a=c.getPopup(),b;c.colorPickerWindow=a=Ext.create(a);c.colorPicker=b=a.lookupReference("selector");b.setFormat(c.getFormat());b.setColor(c.getColor());b.on({ok:"onColorPickerOK",cancel:"onColorPickerCancel",scope:c});a.on({close:"onColorPickerCancel",scope:c});return c.colorPickerWindow},onColorPickerOK:function(a){this.setColor(a.getColor());this.collapse()},onColorPickerCancel:function(){this.collapse()},onExpand:function(){var a=this.getColor();this.colorPicker.setPreviousColor(a)},setValue:function(a){var b=this,d=b.applyValue(a);b.callParent([d]);b.updateValue(d)},updateFormat:function(b){var a=this.colorPicker;if(a){a.setFormat(b)}},updateValue:function(a){var b=this,d;if(!b.syncing){b.syncing=true;b.setColor(a);b.syncing=false}d=b.getColor();Ext.ux.colorpick.ColorUtils.setBackground(b.swatchEl,d);if(b.colorPicker){b.colorPicker.setColor(d)}}});Ext.define("Ext.ux.data.PagingMemoryProxy",{extend:"Ext.data.proxy.Memory",alias:"proxy.pagingmemory",alternateClassName:"Ext.data.PagingMemoryProxy",constructor:function(){Ext.log.warn("Ext.ux.data.PagingMemoryProxy functionality has been merged into Ext.data.proxy.Memory by using the enablePaging flag.");this.callParent(arguments)},read:function(c,g,h){var d=this.getReader(),i=d.read(this.data),e,a,f,b;h=h||this;a=c.filters;if(a.length>0){b=[];Ext.each(i.records,function(j){var o=true,p=a.length,k;for(k=0;k<p;k++){var n=a[k],m=n.filterFn,l=n.scope;o=o&&m.call(l,j)}if(o){b.push(j)}},this);i.records=b;i.totalRecords=i.total=b.length}e=c.sorters;if(e.length>0){f=function(l,k){var j=e[0].sort(l,k),n=e.length,m;for(m=1;m<n;m++){j=j||e[m].sort.call(this,l,k)}return j};i.records.sort(f)}if(c.start!==undefined&&c.limit!==undefined){i.records=i.records.slice(c.start,c.start+c.limit);i.count=i.records.length}Ext.apply(c,{resultSet:i});c.setCompleted();c.setSuccessful();Ext.Function.defer(function(){Ext.callback(g,h,[c])},10)}});Ext.define("Ext.ux.dd.CellFieldDropZone",{extend:"Ext.dd.DropZone",alias:"plugin.ux-cellfielddropzone",containerScroll:true,onCellDrop:Ext.emptyFn,constructor:function(b){if(b){var d=this,a=b.ddGroup,c=b.onCellDrop;if(c){if(typeof c==="string"){d.onCellDropFn=c;d.onCellDrop=d.callCellDrop}else{d.onCellDrop=c}}if(a){d.ddGroup=a}}},init:function(a){var b=this;if(a.rendered){b.grid=a;a.getView().on({render:function(c){b.view=c;Ext.ux.dd.CellFieldDropZone.superclass.constructor.call(b,b.view.el)},single:true})}else{a.on("render",b.init,b,{single:true})}},getTargetFromEvent:function(f){var d=this,b=d.view;var a=f.getTarget(b.getCellSelector());if(a){var g=b.findItemByChild(a),c=a.cellIndex;if(g&&Ext.isDefined(c)){return{node:a,record:b.getRecord(g),fieldName:d.grid.getVisibleColumnManager().getColumns()[c].dataIndex}}}},onNodeEnter:function(h,a,g,d){delete this.dropOK;if(!h){return}var b=d.field;if(!b){return}var c=h.record.fieldsMap[h.fieldName];if(c.isNumeric){if(!b.isXType("numberfield")){return}}else{if(c.isDateField){if(!b.isXType("datefield")){return}}else{if(c.isBooleanField){if(!b.isXType("checkbox")){return}}}}this.dropOK=true;Ext.fly(h.node).addCls("x-drop-target-active")},onNodeOver:function(d,a,c,b){return this.dropOK?this.dropAllowed:this.dropNotAllowed},onNodeOut:function(d,a,c,b){Ext.fly(d.node).removeCls("x-drop-target-active")},onNodeDrop:function(f,a,d,c){if(this.dropOK){var b=c.field.getValue();f.record.set(f.fieldName,b);this.onCellDrop(f.fieldName,b);return true}},callCellDrop:function(b,a){Ext.callback(this.onCellDropFn,null,[b,a],0,this.grid)}});Ext.define("Ext.ux.dd.PanelFieldDragZone",{extend:"Ext.dd.DragZone",alias:"plugin.ux-panelfielddragzone",scroll:false,constructor:function(a){if(a){if(a.ddGroup){this.ddGroup=a.ddGroup}}},init:function(a){var b;if(a.nodeType){Ext.ux.dd.PanelFieldDragZone.superclass.init.apply(this,arguments)}else{if(a.rendered){b=a.getEl();b.unselectable();Ext.ux.dd.PanelFieldDragZone.superclass.constructor.call(this,b)}else{a.on("afterrender",this.init,this,{single:true})}}},getDragData:function(f){var c=f.getTarget("label",null,true),g,b,d,a;if(c){d=Ext.getCmp(c.up("."+Ext.form.Labelable.prototype.formItemCls).id);b=d.preventMark;d.preventMark=true;if(d.isValid()){d.preventMark=b;a=document.createElement("div");a.className=Ext.baseCSSPrefix+"form-text";g=d.getRawValue();a.innerHTML=Ext.isEmpty(g)?"&#160;":g;Ext.fly(a).setWidth(d.getEl().getWidth());return{field:d,ddel:a}}f.stopEvent();d.preventMark=b}},getRepairXY:function(){return this.dragData.field.getEl().getXY()}});
/*!
 * Ext JS Library
 * Copyright(c) 2006-2014 Sencha Inc.
 * <EMAIL>
 * http://www.sencha.com/license
 */
Ext.define("Ext.ux.desktop.Desktop",{extend:"Ext.panel.Panel",alias:"widget.desktop",uses:["Ext.util.MixedCollection","Ext.menu.Menu","Ext.view.View","Ext.window.Window","Ext.ux.desktop.TaskBar","Ext.ux.desktop.Wallpaper"],activeWindowCls:"ux-desktop-active-win",inactiveWindowCls:"ux-desktop-inactive-win",lastActiveWindow:null,border:false,html:"&#160;",layout:"fit",xTickSize:1,yTickSize:1,app:null,shortcuts:null,shortcutItemSelector:"div.ux-desktop-shortcut",shortcutTpl:['<tpl for=".">','<div class="ux-desktop-shortcut" id="{name}-shortcut">','<div class="ux-desktop-shortcut-icon {iconCls}">','<img src="',Ext.BLANK_IMAGE_URL,'" title="{name}">',"</div>",'<span class="ux-desktop-shortcut-text">{name}</span>',"</div>","</tpl>",'<div class="x-clear"></div>'],taskbarConfig:null,windowMenu:null,initComponent:function(){var b=this;b.windowMenu=new Ext.menu.Menu(b.createWindowMenu());b.bbar=b.taskbar=new Ext.ux.desktop.TaskBar(b.taskbarConfig);b.taskbar.windowMenu=b.windowMenu;b.windows=new Ext.util.MixedCollection();b.contextMenu=new Ext.menu.Menu(b.createDesktopMenu());b.items=[{xtype:"wallpaper",id:b.id+"_wallpaper"},b.createDataView()];b.callParent();b.shortcutsView=b.items.getAt(1);b.shortcutsView.on("itemclick",b.onShortcutItemClick,b);var a=b.wallpaper;b.wallpaper=b.items.getAt(0);if(a){b.setWallpaper(a,b.wallpaperStretch)}},afterRender:function(){var a=this;a.callParent();a.el.on("contextmenu",a.onDesktopMenu,a)},createDataView:function(){var a=this;return{xtype:"dataview",overItemCls:"x-view-over",trackOver:true,itemSelector:a.shortcutItemSelector,store:a.shortcuts,style:{position:"absolute"},x:0,y:0,tpl:new Ext.XTemplate(a.shortcutTpl)}},createDesktopMenu:function(){var b=this,a={items:b.contextMenuItems||[]};if(a.items.length){a.items.push("-")}a.items.push({text:"Tile",handler:b.tileWindows,scope:b,minWindows:1},{text:"Cascade",handler:b.cascadeWindows,scope:b,minWindows:1});return a},createWindowMenu:function(){var a=this;return{defaultAlign:"br-tr",items:[{text:"Restore",handler:a.onWindowMenuRestore,scope:a},{text:"Minimize",handler:a.onWindowMenuMinimize,scope:a},{text:"Maximize",handler:a.onWindowMenuMaximize,scope:a},"-",{text:"Close",handler:a.onWindowMenuClose,scope:a}],listeners:{beforeshow:a.onWindowMenuBeforeShow,hide:a.onWindowMenuHide,scope:a}}},onDesktopMenu:function(b){var a=this,c=a.contextMenu;b.stopEvent();if(!c.rendered){c.on("beforeshow",a.onDesktopMenuBeforeShow,a)}c.showAt(b.getXY());c.doConstrain()},onDesktopMenuBeforeShow:function(c){var b=this,a=b.windows.getCount();c.items.each(function(e){var d=e.minWindows||0;e.setDisabled(a<d)})},onShortcutItemClick:function(e,a){var c=this,b=c.app.getModule(a.data.module),d=b&&b.createWindow();if(d){c.restoreWindow(d)}},onWindowClose:function(b){var a=this;a.windows.remove(b);a.taskbar.removeTaskButton(b.taskButton);a.updateActiveWindow()},onWindowMenuBeforeShow:function(c){var a=c.items.items,b=c.theWin;a[0].setDisabled(b.maximized!==true&&b.hidden!==true);a[1].setDisabled(b.minimized===true);a[2].setDisabled(b.maximized===true||b.hidden===true)},onWindowMenuClose:function(){var a=this,b=a.windowMenu.theWin;b.close()},onWindowMenuHide:function(a){Ext.defer(function(){a.theWin=null},1)},onWindowMenuMaximize:function(){var a=this,b=a.windowMenu.theWin;b.maximize();b.toFront()},onWindowMenuMinimize:function(){var a=this,b=a.windowMenu.theWin;b.minimize()},onWindowMenuRestore:function(){var a=this,b=a.windowMenu.theWin;a.restoreWindow(b)},getWallpaper:function(){return this.wallpaper.wallpaper},setTickSize:function(b,c){var e=this,a=e.xTickSize=b,d=e.yTickSize=(arguments.length>1)?c:a;e.windows.each(function(g){var f=g.dd,h=g.resizer;f.xTickSize=a;f.yTickSize=d;h.widthIncrement=a;h.heightIncrement=d})},setWallpaper:function(b,a){this.wallpaper.setWallpaper(b,a);return this},cascadeWindows:function(){var a=0,c=0,b=this.getDesktopZIndexManager();b.eachBottomUp(function(d){if(d.isWindow&&d.isVisible()&&!d.maximized){d.setPosition(a,c);a+=20;c+=20}})},createWindow:function(c,b){var d=this,e,a=Ext.applyIf(c||{},{stateful:false,isWindow:true,constrainHeader:true,minimizable:true,maximizable:true});b=b||Ext.window.Window;e=d.add(new b(a));d.windows.add(e);e.taskButton=d.taskbar.addTaskButton(e);e.animateTarget=e.taskButton.el;e.on({activate:d.updateActiveWindow,beforeshow:d.updateActiveWindow,deactivate:d.updateActiveWindow,minimize:d.minimizeWindow,destroy:d.onWindowClose,scope:d});e.on({boxready:function(){e.dd.xTickSize=d.xTickSize;e.dd.yTickSize=d.yTickSize;if(e.resizer){e.resizer.widthIncrement=d.xTickSize;e.resizer.heightIncrement=d.yTickSize}},single:true});e.doClose=function(){e.doClose=Ext.emptyFn;e.el.disableShadow();e.el.fadeOut({listeners:{afteranimate:function(){e.destroy()}}})};return e},getActiveWindow:function(){var b=null,a=this.getDesktopZIndexManager();if(a){a.eachTopDown(function(c){if(c.isWindow&&!c.hidden){b=c;return false}return true})}return b},getDesktopZIndexManager:function(){var a=this.windows;return(a.getCount()&&a.getAt(0).zIndexManager)||null},getWindow:function(a){return this.windows.get(a)},minimizeWindow:function(a){a.minimized=true;a.hide()},restoreWindow:function(a){if(a.isVisible()){a.restore();a.toFront()}else{a.show()}return a},tileWindows:function(){var b=this,e=b.body.getWidth(true);var a=b.xTickSize,d=b.yTickSize,c=d;b.windows.each(function(g){if(g.isVisible()&&!g.maximized){var f=g.el.getWidth();if(a>b.xTickSize&&a+f>e){a=b.xTickSize;d=c}g.setPosition(a,d);a+=f+b.xTickSize;c=Math.max(c,d+g.el.getHeight()+b.yTickSize)}})},updateActiveWindow:function(){var b=this,c=b.getActiveWindow(),a=b.lastActiveWindow;if(a&&a.destroyed){b.lastActiveWindow=null;return}if(c===a){return}if(a){if(a.el.dom){a.addCls(b.inactiveWindowCls);a.removeCls(b.activeWindowCls)}a.active=false}b.lastActiveWindow=c;if(c){c.addCls(b.activeWindowCls);c.removeCls(b.inactiveWindowCls);c.minimized=false;c.active=true}b.taskbar.setActiveButton(c&&c.taskButton)}});Ext.define("Ext.ux.desktop.App",{mixins:{observable:"Ext.util.Observable"},requires:["Ext.container.Viewport","Ext.ux.desktop.Desktop"],isReady:false,modules:null,useQuickTips:true,constructor:function(a){var b=this;b.mixins.observable.constructor.call(this,a);if(Ext.isReady){Ext.Function.defer(b.init,10,b)}else{Ext.onReady(b.init,b)}},init:function(){var b=this,a;if(b.useQuickTips){Ext.QuickTips.init()}b.modules=b.getModules();if(b.modules){b.initModules(b.modules)}a=b.getDesktopConfig();b.desktop=new Ext.ux.desktop.Desktop(a);b.viewport=new Ext.container.Viewport({layout:"fit",items:[b.desktop]});Ext.getWin().on("beforeunload",b.onUnload,b);b.isReady=true;b.fireEvent("ready",b)},getDesktopConfig:function(){var b=this,a={app:b,taskbarConfig:b.getTaskbarConfig()};Ext.apply(a,b.desktopConfig);return a},getModules:Ext.emptyFn,getStartConfig:function(){var b=this,a={app:b,menu:[]},c;Ext.apply(a,b.startConfig);Ext.each(b.modules,function(d){c=d.launcher;if(c){c.handler=c.handler||Ext.bind(b.createWindow,b,[d]);a.menu.push(d.launcher)}});return a},createWindow:function(a){var b=a.createWindow();b.show()},getTaskbarConfig:function(){var b=this,a={app:b,startConfig:b.getStartConfig()};Ext.apply(a,b.taskbarConfig);return a},initModules:function(a){var b=this;Ext.each(a,function(c){c.app=b})},getModule:function(d){var c=this.modules;for(var e=0,b=c.length;e<b;e++){var a=c[e];if(a.id==d||a.appType==d){return a}}return null},onReady:function(b,a){if(this.isReady){b.call(a,this)}else{this.on({ready:b,scope:a,single:true})}},getDesktop:function(){return this.desktop},onUnload:function(a){if(this.fireEvent("beforeunload",this)===false){a.stopEvent()}}});
/*!
 * Ext JS Library
 * Copyright(c) 2006-2014 Sencha Inc.
 * <EMAIL>
 * http://www.sencha.com/license
 */
Ext.define("Ext.ux.desktop.Module",{mixins:{observable:"Ext.util.Observable"},constructor:function(a){this.mixins.observable.constructor.call(this,a);this.init()},init:Ext.emptyFn});
/*!
 * Ext JS Library
 * Copyright(c) 2006-2014 Sencha Inc.
 * <EMAIL>
 * http://www.sencha.com/license
 */
Ext.define("Ext.ux.desktop.ShortcutModel",{extend:"Ext.data.Model",fields:[{name:"name",convert:Ext.String.createVarName},{name:"iconCls"},{name:"module"}]});Ext.define("Ext.ux.desktop.StartMenu",{extend:"Ext.menu.Menu",baseCls:Ext.baseCSSPrefix+"panel",cls:"x-menu ux-start-menu",bodyCls:"ux-start-menu-body",defaultAlign:"bl-tl",iconCls:"user",bodyBorder:true,width:300,initComponent:function(){var a=this;a.layout.align="stretch";a.items=a.menu;a.callParent();a.toolbar=new Ext.toolbar.Toolbar(Ext.apply({dock:"right",cls:"ux-start-menu-toolbar",vertical:true,width:100,layout:{align:"stretch"}},a.toolConfig));a.addDocked(a.toolbar);delete a.toolItems},addMenuItem:function(){var a=this.menu;a.add.apply(a,arguments)},addToolItem:function(){var a=this.toolbar;a.add.apply(a,arguments)}});
/*!
 * Ext JS Library
 * Copyright(c) 2006-2014 Sencha Inc.
 * <EMAIL>
 * http://www.sencha.com/license
 */
Ext.define("Ext.ux.desktop.TaskBar",{extend:"Ext.toolbar.Toolbar",requires:["Ext.button.Button","Ext.resizer.Splitter","Ext.menu.Menu","Ext.ux.desktop.StartMenu"],alias:"widget.taskbar",cls:"ux-taskbar",startBtnText:"Start",initComponent:function(){var a=this;a.startMenu=new Ext.ux.desktop.StartMenu(a.startConfig);a.quickStart=new Ext.toolbar.Toolbar(a.getQuickStart());a.windowBar=new Ext.toolbar.Toolbar(a.getWindowBarConfig());a.tray=new Ext.toolbar.Toolbar(a.getTrayConfig());a.items=[{xtype:"button",cls:"ux-start-button",iconCls:"ux-start-button-icon",menu:a.startMenu,menuAlign:"bl-tl",text:a.startBtnText},a.quickStart,{xtype:"splitter",html:"&#160;",height:14,width:2,cls:"x-toolbar-separator x-toolbar-separator-horizontal"},a.windowBar,"-",a.tray];a.callParent()},afterLayout:function(){var a=this;a.callParent();a.windowBar.el.on("contextmenu",a.onButtonContextMenu,a)},getQuickStart:function(){var b=this,a={minWidth:20,width:Ext.themeName==="neptune"?70:60,items:[],enableOverflow:true};Ext.each(this.quickStart,function(c){a.items.push({tooltip:{text:c.name,align:"bl-tl"},overflowText:c.name,iconCls:c.iconCls,module:c.module,handler:b.onQuickStartClick,scope:b})});return a},getTrayConfig:function(){var a={items:this.trayItems};delete this.trayItems;return a},getWindowBarConfig:function(){return{flex:1,cls:"ux-desktop-windowbar",items:["&#160;"],layout:{overflowHandler:"Scroller"}}},getWindowBtnFromEl:function(a){var b=this.windowBar.getChildByElement(a);return b||null},onQuickStartClick:function(b){var a=this.app.getModule(b.module),c;if(a){c=a.createWindow();c.show()}},onButtonContextMenu:function(d){var c=this,b=d.getTarget(),a=c.getWindowBtnFromEl(b);if(a){d.stopEvent();c.windowMenu.theWin=a.win;c.windowMenu.showBy(b)}},onWindowBtnClick:function(a){var b=a.win;if(b.minimized||b.hidden){a.disable();b.show(null,function(){a.enable()})}else{if(b.active){a.disable();b.on("hide",function(){a.enable()},null,{single:true});b.minimize()}else{b.toFront()}}},addTaskButton:function(c){var a={iconCls:c.iconCls,enableToggle:true,toggleGroup:"all",width:140,margin:"0 2 0 3",text:Ext.util.Format.ellipsis(c.title,20),listeners:{click:this.onWindowBtnClick,scope:this},win:c};var b=this.windowBar.add(a);b.toggle(true);return b},removeTaskButton:function(a){var c,b=this;b.windowBar.items.each(function(d){if(d===a){c=d}return !c});if(c){b.windowBar.remove(c)}return c},setActiveButton:function(a){if(a){a.toggle(true)}else{this.windowBar.items.each(function(b){if(b.isButton){b.toggle(false)}})}}});Ext.define("Ext.ux.desktop.TrayClock",{extend:"Ext.toolbar.TextItem",alias:"widget.trayclock",cls:"ux-desktop-trayclock",html:"&#160;",timeFormat:"g:i A",tpl:"{time}",initComponent:function(){var a=this;a.callParent();if(typeof(a.tpl)=="string"){a.tpl=new Ext.XTemplate(a.tpl)}},afterRender:function(){var a=this;Ext.Function.defer(a.updateTime,100,a);a.callParent()},doDestroy:function(){var a=this;if(a.timer){window.clearTimeout(a.timer);a.timer=null}a.callParent()},updateTime:function(){var a=this,b=Ext.Date.format(new Date(),a.timeFormat),c=a.tpl.apply({time:b});if(a.lastText!=c){a.setText(c);a.lastText=c}a.timer=Ext.Function.defer(a.updateTime,10000,a)}});
/*!
* Ext JS Library
* Copyright(c) 2006-2015 Sencha Inc.
* <EMAIL>
* http://www.sencha.com/license
*/
Ext.define("Ext.ux.desktop.Video",{extend:"Ext.panel.Panel",alias:"widget.video",layout:"fit",autoplay:false,controls:true,bodyStyle:"background-color:#000;color:#fff",html:"",tpl:['<video id="{id}-video" autoPlay="{autoplay}" controls="{controls}" poster="{poster}" start="{start}" loopstart="{loopstart}" loopend="{loopend}" autobuffer="{autobuffer}" loop="{loop}" style="width:100%;height:100%">','<tpl for="src">','<source src="{src}" type="{type}"/>',"</tpl>","{html}","</video>"],initComponent:function(){var e=this,f,c,b,d;if(e.fallbackHTML){f=e.fallbackHTML}else{f="Your browser does not support HTML5 Video. ";if(Ext.isChrome){f+="Upgrade Chrome."}else{if(Ext.isGecko){f+="Upgrade to Firefox 3.5 or newer."}else{var a='<a href="http://www.google.com/chrome">Chrome</a>';f+='Please try <a href="http://www.mozilla.com">Firefox</a>';if(Ext.isIE){f+=", "+a+' or <a href="http://www.apple.com/safari/">Safari</a>.'}else{f+=" or "+a+"."}}}}e.fallbackHTML=f;b=e.data=Ext.copyTo({tag:"video",html:f},e,"id,poster,start,loopstart,loopend,playcount,autobuffer,loop");if(e.autoplay){b.autoplay=1}if(e.controls){b.controls=1}if(Ext.isArray(e.src)){b.src=e.src}else{b.src=[{src:e.src}]}e.callParent()},afterRender:function(){var a=this;a.callParent();a.video=a.body.getById(a.id+"-video");el=a.video.dom;a.supported=(el&&el.tagName.toLowerCase()=="video");if(a.supported){a.video.on("error",a.onVideoError,a)}},getFallback:function(){return'<h1 style="background-color:#ff4f4f;padding: 10px;">'+this.fallbackHTML+"</h1>"},onVideoError:function(){var a=this;a.video.remove();a.supported=false;a.body.createChild(a.getFallback())},doDestroy:function(){var c=this;var b=c.video;if(c.supported&&b){var a=b.dom;if(a&&a.pause){a.pause()}b.remove();c.video=null}c.callParent()}});
/*!
 * Ext JS Library
 * Copyright(c) 2006-2014 Sencha Inc.
 * <EMAIL>
 * http://www.sencha.com/license
 */
Ext.define("Ext.ux.desktop.Wallpaper",{extend:"Ext.Component",alias:"widget.wallpaper",cls:"ux-wallpaper",html:'<img src="'+Ext.BLANK_IMAGE_URL+'">',stretch:false,wallpaper:null,stateful:true,stateId:"desk-wallpaper",afterRender:function(){var a=this;a.callParent();a.setWallpaper(a.wallpaper,a.stretch)},applyState:function(){var b=this,a=b.wallpaper;b.callParent(arguments);if(a!=b.wallpaper){b.setWallpaper(b.wallpaper)}},getState:function(){return this.wallpaper&&{wallpaper:this.wallpaper}},setWallpaper:function(b,a){var c=this,e,d;c.stretch=(a!==false);c.wallpaper=b;if(c.rendered){e=c.el.dom.firstChild;if(!b||b==Ext.BLANK_IMAGE_URL){Ext.fly(e).hide()}else{if(c.stretch){e.src=b;c.el.removeCls("ux-wallpaper-tiled");Ext.fly(e).setStyle({width:"100%",height:"100%"}).show()}else{Ext.fly(e).hide();d="url("+b+")";c.el.addCls("ux-wallpaper-tiled")}}c.el.setStyle({backgroundImage:d||""});if(c.stateful){c.saveState()}}return c}});Ext.define("Ext.ux.event.RecorderManager",{extend:"Ext.panel.Panel",alias:"widget.eventrecordermanager",uses:["Ext.ux.event.Recorder","Ext.ux.event.Player"],layout:"fit",buttonAlign:"left",eventsToIgnore:{mousemove:1,mouseover:1,mouseout:1},bodyBorder:false,playSpeed:1,initComponent:function(){var b=this;b.recorder=new Ext.ux.event.Recorder({attachTo:b.attachTo,listeners:{add:b.updateEvents,coalesce:b.updateEvents,buffer:200,scope:b}});b.recorder.eventsToRecord=Ext.apply({},b.recorder.eventsToRecord);function c(e,d){return{text:e,speed:d,group:"speed",checked:d==b.playSpeed,handler:b.onPlaySpeed,scope:b}}b.tbar=[{text:"Record",xtype:"splitbutton",whenIdle:true,handler:b.onRecord,scope:b,menu:b.makeRecordButtonMenu()},{text:"Play",xtype:"splitbutton",whenIdle:true,handler:b.onPlay,scope:b,menu:[c("Qarter Speed (0.25x)",0.25),c("Half Speed (0.5x)",0.5),c("3/4 Speed (0.75x)",0.75),"-",c("Recorded Speed (1x)",1),c("Double Speed (2x)",2),c("Quad Speed (4x)",4),"-",c("Full Speed",1000)]},{text:"Clear",whenIdle:true,handler:b.onClear,scope:b},"->",{text:"Stop",whenActive:true,disabled:true,handler:b.onStop,scope:b}];var a=b.attachTo&&b.attachTo.testEvents;b.items=[{xtype:"textarea",itemId:"eventView",fieldStyle:"font-family: monospace",selectOnFocus:true,emptyText:"Events go here!",value:a?b.stringifyEvents(a):"",scrollToBottom:function(){var d=this.inputEl.dom;d.scrollTop=d.scrollHeight}}];b.fbar=[{xtype:"tbtext",text:"Attached To: "+(b.attachTo&&b.attachTo.location.href)}];b.callParent()},makeRecordButtonMenu:function(){var b=[],c={},e=this.recorder.eventsToRecord,d=this.eventsToIgnore;Ext.Object.each(e,function(f,h){var g=c[h.kind];if(!g){c[h.kind]=g=[];b.push({text:h.kind,menu:g})}g.push({text:f,checked:true,handler:function(i){if(i.checked){e[f]=h}else{delete e[f]}}});if(d[f]){g[g.length-1].checked=false;Ext.Function.defer(function(){delete e[f]},1)}});function a(f,g){return(f.text<g.text)?-1:((g.text<f.text)?1:0)}b.sort(a);Ext.Array.each(b,function(f){f.menu.sort(a)});return b},getEventView:function(){return this.down("#eventView")},onClear:function(){var a=this.getEventView();a.setValue("")},onPlay:function(){var c=this,a=c.getEventView(),b=a.getValue();if(b){b=Ext.decode(b);if(b.length){c.player=Ext.create("Ext.ux.event.Player",{attachTo:window.opener,eventQueue:b,speed:c.playSpeed,listeners:{stop:c.onPlayStop,scope:c}});c.player.start();c.syncBtnUI()}}},onPlayStop:function(){this.player=null;this.syncBtnUI()},onPlaySpeed:function(a){this.playSpeed=a.speed},onRecord:function(){this.recorder.start();this.syncBtnUI()},onStop:function(){var a=this;if(a.player){a.player.stop();a.player=null}else{a.recorder.stop()}a.syncBtnUI();a.updateEvents()},syncBtnUI:function(){var c=this,b=!c.player&&!c.recorder.active;Ext.each(c.query("[whenIdle]"),function(d){d.setDisabled(!b)});Ext.each(c.query("[whenActive]"),function(d){d.setDisabled(b)});var a=c.getEventView();a.setReadOnly(!b)},stringifyEvents:function(c){var b,a=[];Ext.each(c,function(d){b=[];Ext.Object.each(d,function(e,f){if(b.length){b.push(", ")}else{b.push("  { ")}b.push(e,": ");b.push(Ext.encode(f))});b.push(" }");a.push(b.join(""))});return"[\n"+a.join(",\n")+"\n]"},updateEvents:function(){var b=this,c=b.stringifyEvents(b.recorder.getRecordedEvents()),a=b.getEventView();a.setValue(c);a.scrollToBottom()}});Ext.define("Ext.ux.form.MultiSelect",{extend:"Ext.form.FieldContainer",mixins:["Ext.util.StoreHolder","Ext.form.field.Field"],alternateClassName:"Ext.ux.Multiselect",alias:["widget.multiselectfield","widget.multiselect"],requires:["Ext.panel.Panel","Ext.view.BoundList","Ext.layout.container.Fit"],uses:["Ext.view.DragZone","Ext.view.DropZone"],layout:"anchor",ddReorder:false,appendOnly:false,displayField:"text",allowBlank:true,minSelections:0,maxSelections:Number.MAX_VALUE,blankText:"This field is required",minSelectionsText:"Minimum {0} item(s) required",maxSelectionsText:"Maximum {0} item(s) required",delimiter:",",dragText:"{0} Item{1}",ignoreSelectChange:0,pageSize:10,initComponent:function(){var a=this;a.items=a.setupItems();a.bindStore(a.store,true);a.callParent();a.initField()},setupItems:function(){var a=this;a.boundList=new Ext.view.BoundList(Ext.apply({anchor:"none 100%",border:1,multiSelect:true,store:a.store,displayField:a.displayField,disabled:a.disabled,tabIndex:0,navigationModel:{type:"default"}},a.listConfig));a.boundList.getNavigationModel().addKeyBindings({pageUp:a.onKeyPageUp,pageDown:a.onKeyPageDown,scope:a});a.boundList.getSelectionModel().on("selectionchange",a.onSelectChange,a);a.boundList.pickerField=a;if(!a.title){return a.boundList}a.boundList.border=false;return{xtype:"panel",isAriaRegion:false,border:true,anchor:"none 100%",layout:"anchor",title:a.title,tbar:a.tbar,items:a.boundList}},onSelectChange:function(a,b){if(!this.ignoreSelectChange){this.setValue(b)}},getSelected:function(){return this.boundList.getSelectionModel().getSelection()},isEqual:function(e,d){var b=Ext.Array.from,c=0,a;e=b(e);d=b(d);a=e.length;if(a!==d.length){return false}for(;c<a;c++){if(d[c]!==e[c]){return false}}return true},afterRender:function(){var d=this,c=d.boundList,b,a;d.callParent();if(d.selectOnRender){b=d.getRecordsForValue(d.value);if(b.length){++d.ignoreSelectChange;d.boundList.getSelectionModel().select(b);--d.ignoreSelectChange}delete d.toSelect}if(d.ddReorder&&!d.dragGroup&&!d.dropGroup){d.dragGroup=d.dropGroup="MultiselectDD-"+Ext.id()}if(d.draggable||d.dragGroup){d.dragZone=Ext.create("Ext.view.DragZone",{view:d.boundList,ddGroup:d.dragGroup,dragText:d.dragText})}if(d.droppable||d.dropGroup){d.dropZone=Ext.create("Ext.view.DropZone",{view:d.boundList,ddGroup:d.dropGroup,handleNodeDrop:function(k,j,e){var f=this.view,h=f.getStore(),g=k.records,i;k.view.store.remove(g);i=h.indexOf(j);if(e==="after"){i++}h.insert(i,g);f.getSelectionModel().select(g);d.fireEvent("drop",d,g)}})}a=d.down("panel");if(a&&c){c.ariaEl.dom.setAttribute("aria-labelledby",a.header.id+"-title-textEl")}},onKeyPageUp:function(h){var g=this,b=g.pageSize,c=g.boundList,a=c.getNavigationModel(),d,f;d=a.recordIndex;f=d>b?d-b:0;a.setPosition(f,h)},onKeyPageDown:function(i){var h=this,b=h.pageSize,c=h.boundList,a=c.getNavigationModel(),g,d,f;g=c.getStore().getCount();d=a.recordIndex;f=d<(g-b)?d+b:g-1;a.setPosition(f,i)},isValid:function(){var b=this,a=b.disabled,c=b.forceValidation||!a;return c?b.validateValue(b.value):a},validateValue:function(b){var a=this,d=a.getErrors(b),c=Ext.isEmpty(d);if(!a.preventMark){if(c){a.clearInvalid()}else{a.markInvalid(d)}}return c},markInvalid:function(c){var b=this,a=b.getActiveError();b.setActiveErrors(Ext.Array.from(c));if(a!==b.getActiveError()){b.updateLayout()}},clearInvalid:function(){var b=this,a=b.hasActiveError();b.unsetActiveError();if(a){b.updateLayout()}},getSubmitData:function(){var a=this,b=null,c;if(!a.disabled&&a.submitValue&&!a.isFileUpload()){c=a.getSubmitValue();if(c!==null){b={};b[a.getName()]=c}}return b},getSubmitValue:function(){var b=this,a=b.delimiter,c=b.getValue();return Ext.isString(a)?c.join(a):c},getValue:function(){return this.value||[]},getRecordsForValue:function(g){var f=this,a=[],h=f.store.getRange(),l=f.valueField,d=0,k=h.length,b,c,e;for(e=g.length;d<e;++d){for(c=0;c<k;++c){b=h[c];if(b.get(l)==g[d]){a.push(b)}}}return a},setupValue:function(g){var b=this.delimiter,d=this.valueField,e=0,c,a,f;if(Ext.isDefined(g)){if(b&&Ext.isString(g)){g=g.split(b)}else{if(!Ext.isArray(g)){g=[g]}}for(a=g.length;e<a;++e){f=g[e];if(f&&f.isModel){g[e]=f.get(d)}}c=Ext.Array.unique(g)}else{c=[]}return c},setValue:function(d){var c=this,b=c.boundList.getSelectionModel(),a=c.store;if(!a.getCount()){a.on({load:Ext.Function.bind(c.setValue,c,[d]),single:true});return}d=c.setupValue(d);c.mixins.field.setValue.call(c,d);if(c.rendered){++c.ignoreSelectChange;b.deselectAll();if(d.length){b.select(c.getRecordsForValue(d))}--c.ignoreSelectChange}else{c.selectOnRender=true}},clearValue:function(){this.setValue([])},onEnable:function(){var a=this.boundList;this.callParent();if(a){a.enable()}},onDisable:function(){var a=this.boundList;this.callParent();if(a){a.disable()}},getErrors:function(b){var a=this,c=Ext.String.format,e=[],d;b=Ext.Array.from(b||a.getValue());d=b.length;if(!a.allowBlank&&d<1){e.push(a.blankText)}if(d<a.minSelections){e.push(c(a.minSelectionsText,a.minSelections))}if(d>a.maxSelections){e.push(c(a.maxSelectionsText,a.maxSelections))}return e},doDestroy:function(){var a=this;a.bindStore(null);Ext.destroy(a.dragZone,a.dropZone,a.keyNav);a.callParent()},onBindStore:function(a){var c=this,b=this.boundList;if(a.autoCreated){c.resolveDisplayField()}if(!Ext.isDefined(c.valueField)){c.valueField=c.displayField}if(b){b.bindStore(a)}},resolveDisplayField:function(){var c=this,b=c.boundList,a=c.getStore();c.valueField=c.displayField="field1";if(!a.expanded){c.displayField="field2"}if(b){b.setDisplayField(c.displayField)}}});Ext.define("Ext.ux.form.ItemSelector",{extend:"Ext.ux.form.MultiSelect",alias:["widget.itemselectorfield","widget.itemselector"],alternateClassName:["Ext.ux.ItemSelector"],requires:["Ext.button.Button","Ext.ux.form.MultiSelect"],hideNavIcons:false,buttons:["top","up","add","remove","down","bottom"],buttonsText:{top:"Move to Top",up:"Move Up",add:"Add to Selected",remove:"Remove from Selected",down:"Move Down",bottom:"Move to Bottom"},layout:{type:"hbox",align:"stretch"},ariaRole:"group",initComponent:function(){var a=this;a.ddGroup=a.id+"-dd";a.ariaRenderAttributes=a.ariaRenderAttributes||{};a.ariaRenderAttributes["aria-labelledby"]=a.id+"-labelEl";a.callParent();a.bindStore(a.store)},createList:function(b){var a=this;return Ext.create("Ext.ux.form.MultiSelect",{submitValue:false,getSubmitData:function(){return null},getModelData:function(){return null},flex:1,dragGroup:a.ddGroup,dropGroup:a.ddGroup,title:b,store:{model:a.store.model,data:[]},displayField:a.displayField,valueField:a.valueField,disabled:a.disabled,listeners:{boundList:{scope:a,itemdblclick:a.onItemDblClick,drop:a.syncValue}}})},setupItems:function(){var a=this;a.fromField=a.createList(a.fromTitle);a.toField=a.createList(a.toTitle);return[a.fromField,{xtype:"toolbar",margin:"0 4",padding:0,layout:{type:"vbox",pack:"center"},items:a.createButtons()},a.toField]},createButtons:function(){var b=this,a=[];if(!b.hideNavIcons){Ext.Array.forEach(b.buttons,function(c){a.push({xtype:"button",ui:"default",tooltip:b.buttonsText[c],ariaLabel:b.buttonsText[c],handler:b["on"+Ext.String.capitalize(c)+"BtnClick"],cls:Ext.baseCSSPrefix+"form-itemselector-btn",iconCls:Ext.baseCSSPrefix+"form-itemselector-"+c,navBtn:true,scope:b,margin:"4 0 0 0"})})}return a},getSelections:function(b){var a=b.getStore();return Ext.Array.sort(b.getSelectionModel().getSelection(),function(d,c){d=a.indexOf(d);c=a.indexOf(c);if(d<c){return -1}else{if(d>c){return 1}}return 0})},onTopBtnClick:function(){var c=this.toField.boundList,a=c.getStore(),b=this.getSelections(c);a.suspendEvents();a.remove(b,true);a.insert(0,b);a.resumeEvents();c.refresh();this.syncValue();c.getSelectionModel().select(b)},onBottomBtnClick:function(){var c=this.toField.boundList,a=c.getStore(),b=this.getSelections(c);a.suspendEvents();a.remove(b,true);a.add(b);a.resumeEvents();c.refresh();this.syncValue();c.getSelectionModel().select(b)},onUpBtnClick:function(){var f=this.toField.boundList,b=f.getStore(),e=this.getSelections(f),g,d=0,a=e.length,c=0;b.suspendEvents();for(;d<a;++d,c++){g=e[d];c=Math.max(c,b.indexOf(g)-1);b.remove(g,true);b.insert(c,g)}b.resumeEvents();f.refresh();this.syncValue();f.getSelectionModel().select(e)},onDownBtnClick:function(){var e=this.toField.boundList,a=e.getStore(),d=this.getSelections(e),f,c=d.length-1,b=a.getCount()-1;a.suspendEvents();for(;c>-1;--c,b--){f=d[c];b=Math.min(b,a.indexOf(f)+1);a.remove(f,true);a.insert(b,f)}a.resumeEvents();e.refresh();this.syncValue();e.getSelectionModel().select(d)},onAddBtnClick:function(){var b=this,a=b.getSelections(b.fromField.boundList);b.moveRec(true,a);b.toField.boundList.getSelectionModel().select(a)},onRemoveBtnClick:function(){var b=this,a=b.getSelections(b.toField.boundList);b.moveRec(false,a);b.fromField.boundList.getSelectionModel().select(a)},moveRec:function(f,e){var c=this,g=c.fromField,a=c.toField,b=f?g.store:a.store,d=f?a.store:g.store;b.suspendEvents();d.suspendEvents();b.remove(e);d.add(e);b.resumeEvents();d.resumeEvents();if(g.boundList.containsFocus){g.boundList.focus()}g.boundList.refresh();a.boundList.refresh();c.syncValue()},syncValue:function(){var a=this;a.mixins.field.setValue.call(a,a.setupValue(a.toField.store.getRange()))},onItemDblClick:function(a,b){this.moveRec(a===this.fromField.boundList,b)},setValue:function(f){var d=this,g=d.fromField,a=d.toField,b=g.store,e=a.store,c;if(!d.fromStorePopulated){d.fromField.store.on({load:Ext.Function.bind(d.setValue,d,[f]),single:true});return}f=d.setupValue(f);d.mixins.field.setValue.call(d,f);c=d.getRecordsForValue(f);b.suspendEvents();e.suspendEvents();b.removeAll();e.removeAll();d.populateFromStore(d.store);Ext.Array.forEach(c,function(h){if(b.indexOf(h)>-1){b.remove(h)}e.add(h)});b.resumeEvents();e.resumeEvents();Ext.suspendLayouts();g.boundList.refresh();a.boundList.refresh();Ext.resumeLayouts(true)},onBindStore:function(b,c){var d=this,e=d.fromField,a=d.toField;if(e){e.store.removeAll();a.store.removeAll();if(b.autoCreated){e.resolveDisplayField();a.resolveDisplayField();d.resolveDisplayField()}if(!Ext.isDefined(d.valueField)){d.valueField=d.displayField}if(b.getCount()){d.populateFromStore(b)}else{d.store.on("load",d.populateFromStore,d)}}},populateFromStore:function(a){var b=this.fromField.store;this.fromStorePopulated=true;b.add(a.getRange());b.fireEvent("load",b)},onEnable:function(){var a=this;a.callParent();a.fromField.enable();a.toField.enable();Ext.Array.forEach(a.query("[navBtn]"),function(b){b.enable()})},onDisable:function(){var a=this;a.callParent();a.fromField.disable();a.toField.disable();Ext.Array.forEach(a.query("[navBtn]"),function(b){b.disable()})},doDestroy:function(){this.bindStore(null);this.callParent()}});Ext.define("Ext.ux.form.SearchField",{extend:"Ext.form.field.Text",alias:"widget.searchfield",triggers:{clear:{weight:0,cls:Ext.baseCSSPrefix+"form-clear-trigger",hidden:true,handler:"onClearClick",scope:"this"},search:{weight:1,cls:Ext.baseCSSPrefix+"form-search-trigger",handler:"onSearchClick",scope:"this"}},hasSearch:false,paramName:"query",initComponent:function(){var c=this,a=c.store,b;c.callParent(arguments);c.on("specialkey",function(d,g){if(g.getKey()==g.ENTER){c.onSearchClick()}});if(!a||!a.isStore){a=c.store=Ext.data.StoreManager.lookup(a)}a.setRemoteFilter(true);b=c.store.getProxy();b.setFilterParam(c.paramName);b.encodeFilters=function(d){return d[0].getValue()}},onClearClick:function(){var b=this,a=b.activeFilter;if(a){b.setValue("");b.store.getFilters().remove(a);b.activeFilter=null;b.getTrigger("clear").hide();b.updateLayout()}},onSearchClick:function(){var a=this,b=a.getValue();if(b.length>0){a.activeFilter=new Ext.util.Filter({property:a.paramName,value:b});a.store.getFilters().add(a.activeFilter);a.getTrigger("clear").show();a.updateLayout()}}});Ext.define("Ext.ux.grid.SubTable",{extend:"Ext.grid.plugin.RowExpander",alias:"plugin.subtable",rowBodyTpl:['<table class="'+Ext.baseCSSPrefix+'grid-subtable">',"{%","this.owner.renderTable(out, values);","%}","</table>"],init:function(d){var e=this,c=e.columns,a,b,f;e.callParent(arguments);e.columns=[];if(c){for(b=0,a=c.length;b<a;++b){f=Ext.apply({preventRegister:true},c[b]);f.xtype=f.xtype||"gridcolumn";e.columns.push(Ext.widget(f))}}},destroy:function(){var c=this.columns,a,b;if(c){for(b=0,a=c.length;b<a;++b){c[b].destroy()}}this.columns=null;this.callParent()},getRowBodyFeatureData:function(b,a,c){this.callParent(arguments);c.rowBodyCls+=" "+Ext.baseCSSPrefix+"grid-subtable-row"},renderTable:function(g,m){var k=this,d=k.columns,a=d.length,c=k.getAssociatedRecords(m.record),n=c.length,e,b,h,f,l;g.push("<thead>");for(f=0;f<a;f++){g.push('<th class="'+Ext.baseCSSPrefix+'grid-subtable-header">',d[f].text,"</th>")}g.push("</thead><tbody>");for(h=0;h<n;h++){e=c[h];g.push("<tr>");for(f=0;f<a;f++){b=d[f];l=e.get(b.dataIndex);if(b.renderer&&b.renderer.call){l=b.renderer.call(b.scope||k,l,{},e)}g.push('<td class="'+Ext.baseCSSPrefix+'grid-subtable-cell"');if(b.width!=null){g.push(' style="width:'+b.width+'px"')}g.push('><div class="'+Ext.baseCSSPrefix+'grid-cell-inner">',l,"</div></td>")}g.push("</tr>")}g.push("</tbody>")},getRowBodyContentsFn:function(a){var b=this;return function(c){a.owner=b;return a.applyTemplate(c)}},getAssociatedRecords:function(a){return a[this.association]().getRange()}});Ext.define("Ext.ux.grid.TransformGrid",{extend:"Ext.grid.Panel",constructor:function(q,f){f=Ext.apply({},f);q=this.table=Ext.get(q);var j=f.fields||[],c=f.columns||[],k=[],m=[],e=q.query("thead th"),h=0,l=e.length,g=q.dom,b,p,n,d,o,a;for(;h<l;++h){d=e[h];o=d.innerHTML;a="tcol-"+h;k.push(Ext.applyIf(j[h]||{},{name:a,mapping:"td:nth("+(h+1)+")/@innerHTML"}));m.push(Ext.applyIf(c[h]||{},{text:o,dataIndex:a,width:d.offsetWidth,tooltip:d.title,sortable:true}))}if(f.width){b=f.width}else{b=q.getWidth()+1}if(f.height){p=f.height}Ext.applyIf(f,{store:{data:g,fields:k,proxy:{type:"memory",reader:{record:"tbody tr",type:"xml"}}},columns:m,width:b,height:p});this.callParent([f]);if(f.remove!==false){g.parentNode.removeChild(g)}},doDestroy:function(){this.table.remove();this.tabl=null;this.callParent()}});Ext.define("Ext.ux.grid.plugin.AutoSelector",{extend:"Ext.plugin.Abstract",alias:"plugin.gridautoselector",config:{store:null},init:function(a){var b=this;b.grid=a;b.watchGrid();a.on({reconfigure:b.watchGrid,scope:b})},destroy:function(){this.setStore(null);this.grid=null;this.callParent()},ensureSelection:function(){var b=this.grid,a=b.getStore(),c;if(a.getCount()){c=b.getSelection();if(!c||!c.length){b.getSelectionModel().select(0)}}},watchGrid:function(){this.setStore(this.grid.getStore());this.ensureSelection()},updateStore:function(a){var b=this;Ext.destroy(b.storeListeners);b.storeListeners=a&&a.on({add:b.ensureSelection,remove:b.ensureSelection,destroyable:true,scope:b})}});Ext.define("Ext.ux.layout.ResponsiveColumn",{extend:"Ext.layout.container.Auto",alias:"layout.responsivecolumn",states:{small:1000,large:0},_responsiveCls:Ext.baseCSSPrefix+"responsivecolumn",initLayout:function(){this.innerCtCls+=" "+this._responsiveCls;this.callParent()},beginLayout:function(d){var i=this,h=Ext.Element.getViewportWidth(),j=i.states,g=Infinity,b=i.innerCt,c=i._currentState,a,f,e;for(a in j){f=j[a]||Infinity;if(h<=f&&f<=g){g=f;e=a}}if(e!==c){b.replaceCls(c,e,i._responsiveCls);i._currentState=e}i.callParent(arguments)},onAdd:function(a){this.callParent([a]);var b=a.responsiveCls;if(b){a.addCls(b)}}},function(a){if(Ext.isIE8){a.override({responsiveSizePolicy:{readsWidth:0,readsHeight:0,setsWidth:1,setsHeight:0},setsItemSize:true,calculateItems:function(e,b){var o=this,f=e.targetContext,n=e.childItems,l=n.length,g=b.gotWidth,c=b.width,j,p,h,d,k,m;if(g===false){f.domBlock(o,"width");return false}if(!g){return true}for(h=0;h<l;++h){d=n[h];m=parseInt(d.el.getStyle("background-position-x"),10);k=parseInt(d.el.getStyle("background-position-y"),10);d.setWidth((m/100*(c-k))-k)}e.setContentWidth(c+e.paddingContext.getPaddingInfo().width);return true},getItemSizePolicy:function(){return this.responsiveSizePolicy}})}});Ext.define("Ext.ux.rating.Picker",{extend:"Ext.Widget",xtype:"rating",focusable:true,cachedConfig:{family:"monospace",glyphs:"☆★",minimum:1,limit:5,overStyle:null,rounding:1,scale:"125%",selectedStyle:null,tooltip:null,trackOver:true,value:null,tooltipText:null,trackingValue:null},config:{animate:null},element:{cls:"u"+Ext.baseCSSPrefix+"rating-picker",reference:"element",children:[{reference:"innerEl",cls:"u"+Ext.baseCSSPrefix+"rating-picker-inner",listeners:{click:"onClick",mousemove:"onMouseMove",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave"},children:[{reference:"valueEl",cls:"u"+Ext.baseCSSPrefix+"rating-picker-value"},{reference:"trackerEl",cls:"u"+Ext.baseCSSPrefix+"rating-picker-tracker"}]}]},defaultBindProperty:"value",twoWayBindable:"value",overCls:"u"+Ext.baseCSSPrefix+"rating-picker-over",trackOverCls:"u"+Ext.baseCSSPrefix+"rating-picker-track-over",applyGlyphs:function(a){if(typeof a==="string"){a=[a.charAt(0),a.charAt(1)]}else{if(typeof a[0]==="number"){a=[String.fromCharCode(a[0]),String.fromCharCode(a[1])]}}return a},applyOverStyle:function(a){this.trackerEl.applyStyles(a)},applySelectedStyle:function(a){this.valueEl.applyStyles(a)},applyTooltip:function(a){if(a&&typeof a!=="function"){if(!a.isTemplate){a=new Ext.XTemplate(a)}a=a.apply.bind(a)}return a},applyTrackingValue:function(a){return this.applyValue(a)},applyValue:function(c){if(c!==null){var b=this.getRounding(),a=this.getLimit(),d=this.getMinimum();c=Math.round(Math.round(c/b)*b*1000)/1000;c=(c<d)?d:(c>a?a:c)}return c},onClick:function(a){var b=this.valueFromEvent(a);this.setValue(b)},onMouseEnter:function(){this.element.addCls(this.overCls)},onMouseLeave:function(){this.element.removeCls(this.overCls)},onMouseMove:function(a){var b=this.valueFromEvent(a);this.setTrackingValue(b)},updateFamily:function(a){this.element.setStyle("fontFamily","'"+a+"'")},updateGlyphs:function(){this.refreshGlyphs()},updateLimit:function(){this.refreshGlyphs()},updateScale:function(a){this.element.setStyle("fontSize",a)},updateTooltip:function(){this.refreshTooltip()},updateTooltipText:function(e){var d=this.innerEl,a=Ext.tip&&Ext.tip.QuickTipManager,b=a&&a.tip,c;if(a){d.dom.setAttribute("data-qtip",e);this.trackerEl.dom.setAttribute("data-qtip",e);c=b&&b.activeTarget;c=c&&c.el;if(c&&d.contains(c)){b.update(e)}}},updateTrackingValue:function(d){var c=this,a=c.trackerEl,b=c.valueToPercent(d);a.setStyle("width",b);c.refreshTooltip()},updateTrackOver:function(a){this.element[a?"addCls":"removeCls"](this.trackOverCls)},updateValue:function(h,c){var f=this,b=f.getAnimate(),g=f.valueEl,e=f.valueToPercent(h),d,a;if(f.isConfiguring||!b){g.setStyle("width",e)}else{g.stopAnimation();g.animate(Ext.merge({from:{width:f.valueToPercent(c)},to:{width:e}},b))}f.refreshTooltip();if(!f.isConfiguring){if(f.hasListeners.change){f.fireEvent("change",f,h,c)}d=f.getWidgetColumn&&f.getWidgetColumn();a=d&&f.getWidgetRecord&&f.getWidgetRecord();if(a&&d.dataIndex){a.set(d.dataIndex,h)}}},afterCachedConfig:function(){this.refresh();return this.callParent(arguments)},initConfig:function(a){this.isConfiguring=true;this.callParent([a]);this.refresh()},setConfig:function(){var a=this;a.isReconfiguring=true;a.callParent(arguments);a.isReconfiguring=false;a.refresh();return a},destroy:function(){this.tip=Ext.destroy(this.tip);this.callParent()},privates:{getGlyphTextNode:function(b){var a=b.lastChild;if(!a||a.nodeType!==3){a=b.ownerDocument.createTextNode("");b.appendChild(a)}return a},getTooltipData:function(){var a=this;return{component:a,tracking:a.getTrackingValue(),trackOver:a.getTrackOver(),value:a.getValue()}},refresh:function(){var a=this;if(a.invalidGlyphs){a.refreshGlyphs(true)}if(a.invalidTooltip){a.refreshTooltip(true)}},refreshGlyphs:function(a){var i=this,g=!a&&(i.isConfiguring||i.isReconfiguring),c,j,f,h,e,b,d;if(!g){c=i.getGlyphTextNode(i.innerEl.dom);d=i.getGlyphTextNode(i.valueEl.dom);b=i.getGlyphTextNode(i.trackerEl.dom);j=i.getGlyphs();f=i.getLimit();for(h=e="";f--;){e+=j[0];h+=j[1]}c.nodeValue=e;d.nodeValue=h;b.nodeValue=h}i.invalidGlyphs=g},refreshTooltip:function(b){var c=this,a=!b&&(c.isConfiguring||c.isReconfiguring),e=c.getTooltip(),d,f;if(!a){e=c.getTooltip();if(e){d=c.getTooltipData();f=e(d);c.setTooltipText(f)}}c.invalidTooltip=a},valueFromEvent:function(a){var f=this,b=f.innerEl,e=a.getX(),j=f.getRounding(),d=b.getX(),g=e-d,h=b.getWidth(),c=f.getLimit(),i;if(f.getInherited().rtl){g=h-g}i=g/h*c;i=Math.ceil(i/j)*j;return i},valueToPercent:function(a){a=(a/this.getLimit())*100;return a+"%"}}});Ext.define("Ext.ux.statusbar.ValidationStatus",{extend:"Ext.Component",alias:"plugin.validationstatus",requires:["Ext.util.MixedCollection"],errorIconCls:"x-status-error",errorListCls:"x-status-error-list",validIconCls:"x-status-valid",showText:"The form has errors (click for details...)",hideText:"Click again to hide the error list",submitText:"Saving...",init:function(b){var a=this;a.statusBar=b;b.on({single:true,scope:a,render:a.onStatusbarRender});b.on({click:{element:"el",fn:a.onStatusClick,scope:a,buffer:200}})},onStatusbarRender:function(c){var b=this,a=function(){b.monitor=true};b.monitor=true;b.errors=Ext.create("Ext.util.MixedCollection");b.listAlign=(c.statusAlign==="right"?"br-tr?":"bl-tl?");if(b.form){b.formPanel=Ext.getCmp(b.form)||b.statusBar.lookupController().lookupReference(b.form);b.basicForm=b.formPanel.getForm();b.startMonitoring();b.basicForm.on({beforeaction:function(e,d){if(d.type==="submit"){b.monitor=false}}});b.formPanel.on({beforedestroy:b.destroy,scope:b});b.basicForm.on("actioncomplete",a);b.basicForm.on("actionfailed",a)}},startMonitoring:function(){this.basicForm.getFields().each(function(a){a.on("validitychange",this.onFieldValidation,this)},this)},stopMonitoring:function(){var a=this.basicForm;if(!a.destroyed){a.getFields().each(function(b){b.un("validitychange",this.onFieldValidation,this)},this)}},doDestroy:function(){Ext.destroy(this.msgEl);this.stopMonitoring();this.statusBar.statusEl.un("click",this.onStatusClick,this);this.callParent()},onFieldValidation:function(b,c){var a=this,d;if(!a.monitor){return false}d=b.getErrors()[0];if(d){a.errors.add(b.id,{field:b,msg:d})}else{a.errors.removeAtKey(b.id)}this.updateErrorList();if(a.errors.getCount()>0){if(a.statusBar.getText()!==a.showText){a.statusBar.setStatus({text:a.showText,iconCls:a.errorIconCls})}}else{a.statusBar.clearStatus().setIcon(a.validIconCls)}},updateErrorList:function(){var b=this,c,a=b.getMsgEl();if(b.errors.getCount()>0){c=["<ul>"];this.errors.each(function(d){c.push('<li id="x-err-',d.field.id,'"><a href="#">',d.msg,"</a></li>")});c.push("</ul>");a.update(c.join(""))}else{a.update("")}a.setSize("auto","auto")},getMsgEl:function(){var c=this,a=c.msgEl,b;if(!a){a=c.msgEl=Ext.DomHelper.append(Ext.getBody(),{cls:c.errorListCls},true);a.hide();a.on("click",function(d){b=d.getTarget("li",10,true);if(b){Ext.getCmp(b.id.split("x-err-")[1]).focus();c.hideErrors()}},null,{stopEvent:true})}return a},showErrors:function(){var a=this;a.updateErrorList();a.getMsgEl().alignTo(a.statusBar.getEl(),a.listAlign).slideIn("b",{duration:300,easing:"easeOut"});a.statusBar.setText(a.hideText);a.formPanel.body.on("click",a.hideErrors,a,{single:true})},hideErrors:function(){var a=this.getMsgEl();if(a.isVisible()){a.slideOut("b",{duration:300,easing:"easeIn"});this.statusBar.setText(this.showText)}this.formPanel.body.un("click",this.hideErrors,this)},onStatusClick:function(){if(this.getMsgEl().isVisible()){this.hideErrors()}else{if(this.errors.getCount()>0){this.showErrors()}}}});