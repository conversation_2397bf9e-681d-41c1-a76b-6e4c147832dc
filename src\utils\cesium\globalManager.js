/**
 * 全局Cesium管理器
 * 替代原来的window.earth, window.layersManager等全局变量
 */
import { createViewer, MeasurementTools, LayerManager } from './cesiumInit.js'
import * as Cesium from 'cesium'

class GlobalCesiumManager {
  constructor() {
    this.viewer = null
    this.layerManager = null
    this.measurementTools = null
    this.initialized = false
  }

  /**
   * 初始化Cesium
   */
  async init(containerId, options = {}) {
    if (this.initialized) {
      console.warn('Cesium已经初始化')
      return this.viewer
    }

    try {
      // 创建viewer
      this.viewer = createViewer(containerId, options)
      
      // 创建图层管理器
      this.layerManager = new LayerManager()
      
      // 创建测量工具
      this.measurementTools = new MeasurementTools(this.viewer)
      
      // 设置全局变量以兼容现有代码
      window.viewer = this.viewer
      window.layersManager = this.layerManager
      window.measurementTools = this.measurementTools
      
      // 创建earth对象以兼容现有代码
      window.earth = {
        viewer: this.viewer,
        layerManager: this.layerManager
      }

      this.initialized = true
      
      console.log('Cesium初始化完成')
      return this.viewer
    } catch (error) {
      console.error('Cesium初始化失败:', error)
      throw error
    }
  }

  /**
   * 添加3D Tiles
   */
  async add3DTileset(url, options = {}) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    const tileset = await Cesium.Cesium3DTileset.fromUrl(url, options)
    this.viewer.scene.primitives.add(tileset)
    
    return tileset
  }

  /**
   * 添加图像图层
   */
  addImageryLayer(provider, options = {}) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    const layer = this.viewer.imageryLayers.addImageryProvider(provider, options)
    return layer
  }

  /**
   * 飞行到位置
   */
  flyTo(target, options = {}) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    return this.viewer.flyTo(target, options)
  }

  /**
   * 缩放到目标
   */
  zoomTo(target, options = {}) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    return this.viewer.zoomTo(target, options)
  }

  /**
   * 获取当前相机位置
   */
  getCameraPosition() {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    const camera = this.viewer.camera
    const position = camera.position
    const cartographic = Cesium.Cartographic.fromCartesian(position)
    
    return {
      longitude: Cesium.Math.toDegrees(cartographic.longitude),
      latitude: Cesium.Math.toDegrees(cartographic.latitude),
      height: cartographic.height,
      heading: Cesium.Math.toDegrees(camera.heading),
      pitch: Cesium.Math.toDegrees(camera.pitch),
      roll: Cesium.Math.toDegrees(camera.roll)
    }
  }

  /**
   * 设置相机位置
   */
  setCameraPosition(longitude, latitude, height, heading = 0, pitch = -90, roll = 0) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    this.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
      orientation: {
        heading: Cesium.Math.toRadians(heading),
        pitch: Cesium.Math.toRadians(pitch),
        roll: Cesium.Math.toRadians(roll)
      }
    })
  }

  /**
   * 屏幕坐标转世界坐标
   */
  screenToWorld(screenPosition) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    return this.viewer.camera.pickEllipsoid(screenPosition, this.viewer.scene.globe.ellipsoid)
  }

  /**
   * 世界坐标转屏幕坐标
   */
  worldToScreen(worldPosition) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    return Cesium.SceneTransforms.wgs84ToWindowCoordinates(this.viewer.scene, worldPosition)
  }

  /**
   * 拾取对象
   */
  pick(screenPosition) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    return this.viewer.scene.pick(screenPosition)
  }

  /**
   * 射线拾取
   */
  pickFromRay(ray) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    return this.viewer.scene.pickFromRay(ray)
  }

  /**
   * 详细射线拾取（替代drillPickFromRayMostDetailed）
   */
  async drillPickFromRay(ray, limit = 10) {
    if (!this.viewer) {
      throw new Error('Viewer未初始化')
    }

    try {
      // 使用新的API
      const results = this.viewer.scene.drillPickFromRay(ray, limit)
      return results || []
    } catch (error) {
      console.warn('drillPickFromRay失败，尝试备用方法:', error)
      
      // 备用方法：使用pickFromRay
      const result = this.viewer.scene.pickFromRay(ray)
      return result ? [result] : []
    }
  }

  /**
   * 销毁
   */
  destroy() {
    if (this.measurementTools) {
      this.measurementTools.cleanup()
    }
    
    if (this.viewer) {
      this.viewer.destroy()
    }
    
    // 清理全局变量
    delete window.viewer
    delete window.layersManager
    delete window.measurementTools
    delete window.earth
    
    this.viewer = null
    this.layerManager = null
    this.measurementTools = null
    this.initialized = false
  }
}

// 创建全局实例
const globalCesiumManager = new GlobalCesiumManager()

// 导出单例
export default globalCesiumManager

// 兼容性函数，用于替代geowin3d的功能
export const compatibilityFunctions = {
  // 替代原来的测量功能
  startDistanceMeasurement: () => {
    if (globalCesiumManager.measurementTools) {
      globalCesiumManager.measurementTools.startDistanceMeasurement()
    }
  },
  
  startAreaMeasurement: () => {
    if (globalCesiumManager.measurementTools) {
      globalCesiumManager.measurementTools.startAreaMeasurement()
    }
  },
  
  clearMeasurements: () => {
    if (globalCesiumManager.measurementTools) {
      globalCesiumManager.measurementTools.clearAll()
    }
  },
  
  // 替代原来的图层管理功能
  addLayer: (label, layer) => {
    if (globalCesiumManager.layerManager) {
      return globalCesiumManager.layerManager.addLayer(label, layer)
    }
  },
  
  getLayerByLabel: (label) => {
    if (globalCesiumManager.layerManager) {
      const id = globalCesiumManager.layerManager.getIdByLabel(label)
      return id ? globalCesiumManager.layerManager.getLayer(id) : null
    }
  },
  
  setLayerVisible: (id, visible) => {
    if (globalCesiumManager.layerManager) {
      globalCesiumManager.layerManager.setLayerVisible(id, visible)
    }
  }
}

// 在window上挂载兼容性函数
window.cesiumManager = globalCesiumManager
window.cesiumCompat = compatibilityFunctions
