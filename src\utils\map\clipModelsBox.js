
class ClipModelsBox {

    constructor(models, path,offsetLength) {
        this.tilesets = models
        this.path = path
        this.offsetLength = offsetLength
        
    }

    clipStart() {
        for (var tiles of this.tilesets) {
            var rightPlane = this.getRightPlane(tiles)
            var leftPlane = this.getLeftPlane(tiles)
            var frontPlane = this.getFrontPlane(tiles)
            var afterPlane = this.getAfterPlane(tiles)
            
            
            const PlaneCollection = new Cesium.ClippingPlaneCollection({
                planes: [leftPlane,rightPlane,frontPlane,afterPlane],
                unionClippingRegions:true

            })

            tiles.clippingPlanes = PlaneCollection
        }
        
                viewer.scene.forceRender();
        
    }

    destroy() {
        
        for (var tiles of this.tilesets) {
            tiles.clippingPlanes = undefined
        }
        

    }

    getLeftPlane(tiles) {
        var inverseTransform = this.getInverseTransform(tiles)


        var offsetPath = this.getOffsetVector(this.path, 'left')

        var PList = [this.cart3ToWGS(offsetPath[1]), this.cart3ToWGS(offsetPath[0])]
        var clipPlane = this.createPlane(PList[0], PList[1], inverseTransform)



        return clipPlane
    }

    getRightPlane(tiles) {
        var inverseTransform = this.getInverseTransform(tiles)

        var offsetPath = this.getOffsetVector(this.path, 'right')

        var PList = [this.cart3ToWGS(offsetPath[0]), this.cart3ToWGS(offsetPath[1])]
        var clipPlane = this.createPlane(PList[0], PList[1], inverseTransform)



        return clipPlane
    }

    getFrontPlane(tiles){
        var inverseTransform = this.getInverseTransform(tiles)
        var offsetPath = this.getOffsetVector2(this.path, 'front')
        

        var PList = [this.cart3ToWGS(offsetPath[0]), this.cart3ToWGS(offsetPath[1])]
        var clipPlane = this.createPlane(PList[0], PList[1], inverseTransform)



        return clipPlane
    }

    getAfterPlane(tiles){
        var inverseTransform = this.getInverseTransform(tiles)
        var offsetPath = this.getOffsetVector2(this.path, 'after')
        

        var PList = [this.cart3ToWGS(offsetPath[1]), this.cart3ToWGS(offsetPath[0])]
        var clipPlane = this.createPlane(PList[0], PList[1], inverseTransform)



        return clipPlane
    }

    //获取左右偏移向量
    getOffsetVector(pathList, lr) {
        var offsetLength = this.offsetLength
        if (lr == 'left') {
            offsetLength = -offsetLength
        }
        var startP = pathList[0]
        var endP = pathList[1]
        //路径的前进向量
        var L1 = Cesium.Cartesian3.subtract(endP, startP, new Cesium.Cartesian3())
        var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3())
        //用于做叉乘的向量
        var L2SP = this._getMoveUpCoordinate(startP, -1)
        var L2 = Cesium.Cartesian3.subtract(L2SP, startP, new Cesium.Cartesian3())
        var L2N = Cesium.Cartesian3.normalize(L2, new Cesium.Cartesian3())
        //叉乘，获取前进向量的右侧向量
        var L3 = Cesium.Cartesian3.cross(L1N, L2N, new Cesium.Cartesian3())
        var L3N = Cesium.Cartesian3.normalize(L3, new Cesium.Cartesian3())
        var offsetVector = Cesium.Cartesian3.multiplyByScalar(L3N, offsetLength, new Cesium.Cartesian3())

        var startP2 = Cesium.Cartesian3.add(startP, offsetVector, new Cesium.Cartesian3())
        var endP2 = Cesium.Cartesian3.add(endP, offsetVector, new Cesium.Cartesian3())

        return [startP2, endP2]
    }

    //获取前后偏移向量
    getOffsetVector2(pathList, lr) {
        var offsetLength = this.offsetLength
        if (lr == 'front') {
            offsetLength = -offsetLength
        }
        var startP = pathList[0]
        var endP = pathList[1]
        //路径的前进向量
        var L1 = Cesium.Cartesian3.subtract(endP, startP, new Cesium.Cartesian3())
        var L1N = Cesium.Cartesian3.normalize(L1, new Cesium.Cartesian3())
        //用于做叉乘的向量
        var L2SP = this._getMoveUpCoordinate(startP, -1)
        var L2 = Cesium.Cartesian3.subtract(L2SP, startP, new Cesium.Cartesian3())
        var L2N = Cesium.Cartesian3.normalize(L2, new Cesium.Cartesian3())
        //叉乘，获取前进向量的右侧向量
        var L3 = Cesium.Cartesian3.cross(L1N, L2N, new Cesium.Cartesian3())
        var L3N = Cesium.Cartesian3.normalize(L3, new Cesium.Cartesian3())
        var offsetVector = Cesium.Cartesian3.multiplyByScalar(L3N, 10, new Cesium.Cartesian3())

        var startP2 = Cesium.Cartesian3.add(startP, offsetVector, new Cesium.Cartesian3())
        // var endP2 = Cesium.Cartesian3.add(endP, offsetVector, new Cesium.Cartesian3())

        var offsetVector2 = Cesium.Cartesian3.multiplyByScalar(L1N, offsetLength, new Cesium.Cartesian3())
        var startP3=Cesium.Cartesian3.add(startP, offsetVector2, new Cesium.Cartesian3())
        var endP3=Cesium.Cartesian3.add(startP2, offsetVector2, new Cesium.Cartesian3())
        return [startP3, endP3]
    }

    //获得某点向上平移的坐标
    _getMoveUpCoordinate(cartesian, height) {
        let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
        let lon = Geowin3D.Math.toDegrees(rad.longitude);
        let lat = Geowin3D.Math.toDegrees(rad.latitude);
        return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height)
    }

    getInverseTransform(tileSet) {
        let transform;
        const tmp = tileSet.root.transform;
        if ((tmp && tmp.equals(Cesium.Matrix4.IDENTITY)) || !tmp) {
            transform = Cesium.Transforms.eastNorthUpToFixedFrame(
                tileSet.boundingSphere.center
            );
        } else {
            transform = Cesium.Matrix4.fromArray(tileSet.root.transform);
        }
        return Cesium.Matrix4.inverseTransformation(
            transform,
            new Cesium.Matrix4()
        );
    }

    cart3ToWGS(cart3) {
        return {
            lat: Cesium.Math.toDegrees(Cesium.Cartographic.fromCartesian(cart3).latitude),
            lng: Cesium.Math.toDegrees(Cesium.Cartographic.fromCartesian(cart3).longitude)
        }
    }

    createPlane(p1, p2, inverseTransform) {
        // 将仅包含经纬度信息的p1,p2，转换为相应坐标系的cartesian3对象
        const p1C3 = this.getOriginCoordinateSystemPoint(p1, inverseTransform)
        const p2C3 = this.getOriginCoordinateSystemPoint(p2, inverseTransform)

        // 定义一个垂直向上的向量up
        const up = new Cesium.Cartesian3(0, 0, 10)
        //  right 实际上就是由p1指向p2的向量
        const right = Cesium.Cartesian3.subtract(p2C3, p1C3, new Cesium.Cartesian3())

        // 计算normal， right叉乘up，得到平面法向量，这个法向量指向right的右侧
        let normal = Cesium.Cartesian3.cross(right, up, new Cesium.Cartesian3())
        normal = Cesium.Cartesian3.normalize(normal, normal)

        // 由于已经获得了法向量和过平面的一点，因此可以直接构造Plane,并进一步构造ClippingPlane
        const planeTmp = Cesium.Plane.fromPointNormal(p1C3, normal)
        return Cesium.ClippingPlane.fromPlane(planeTmp)
    }



    getOriginCoordinateSystemPoint(point, inverseTransform) {
        const val = Cesium.Cartesian3.fromDegrees(point.lng, point.lat)
        return Cesium.Matrix4.multiplyByPoint(
            inverseTransform, val, new Cesium.Cartesian3(0, 0, 0))

    }



}

export default ClipModelsBox;