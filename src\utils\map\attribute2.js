/*
 * @Description: 属性查询
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2022-11-11 09:16:38
 * @LastEditors: wbw
 * @LastEditTime: 2022-12-01 17:16:01
 */

import createEdgeStage from './CesiumEdgeStage/createEdgeStage'
class Attribute {
    /**
     * @description: 初始化Attribute
     * @param {Cesium.viewer} _viewer Cesium的视图对象
     * @author: wbw
     */
    constructor(_viewer) {
        this.viewer = _viewer
        this.selectedEntity = new Cesium.Entity();
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        this.resolutionScale = this.viewer.resolutionScale
        this.postProcessStages = this.viewer.postProcessStages.fxaa.enabled
        this.depthTestAgainstTerrain = this.viewer.scene.globe.depthTestAgainstTerrain
        this.edgeStage = createEdgeStage()
        this.edgeStage.visibleEdgeColor = Cesium.Color.fromCssColorString('#f75e3f')
        this.edgeStage.hiddenEdgeColor = Cesium.Color.fromCssColorString('#f75e3f')
        
        this.cesiumStage = Cesium.PostProcessStageLibrary.createSilhouetteStage()
        this.edgeStage.enabled = false
        // this.viewer.postProcessStages.add(this.edgeStage);

         
        this.cesiumStage.enabled = false;
        // this.viewer.postProcessStages.add(this.cesiumStage);
 

        this.selectedModel = {
            feature: undefined,
        };
    }

    /**
     * @description: 开启属性查询
     * @author: wbw
     */
    start() {
        // this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        // this.handler.setInputAction((movement) => {
        //     var pickedObject = viewer.scene.pick(movement.position);
        //     if (Cesium.defined(pickedObject)) {
        //         //选中要素
        //         this.stage.selected = [pickedObject];
        //         this.selectedModel.feature = pickedObject;
        //     } else {
        //         this.stage.selected = [];
        //         this.selectedModel.feature = undefined;
        //     }
        //     this._setAttribute()
        //     this.viewer.scene.forceRender();
        // }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        this.viewer.resolutionScale = devicePixelRatio;
        this.viewer.postProcessStages.fxaa.enabled = true
        this.viewer.scene.globe.depthTestAgainstTerrain = true

         
        this.edgeStage.selected = []
        this.selectedModel.feature = undefined;
        

        //鼠标点击，拾取对象并高亮显示
        this.handler.setInputAction((e) => {
            var mousePosition = e.position;
            var picked = viewer.scene.pick(mousePosition)

            this.edgeStage.selected = []
            this.edgeStage.enabled = false

            this.selectedModel.feature = undefined;

            if (picked && picked.primitive) {

                let primitive = picked.primitive
                let pickIds = primitive._pickIds;
                let pickId = picked.pickId;

                if (!pickId && !pickIds && picked.content) {
                    pickIds = picked.content._model._pickIds;
                }

                if (!pickId) {
                    if (picked.id) {
                        pickId = pickIds.find(pickId => {
                            return pickId.object == picked;
                        })
                    } else if (pickIds) {
                        pickId = pickIds[0]
                    }
                }

                if (pickId) {
                    let pickObject = {
                        pickId: pickId
                    }
                    this.edgeStage.selected = [pickObject]
                    this.cesiumStage.selected = [pickObject]
                    this.selectedModel.feature = pickObject.pickId.object;
                    console.log(pickObject.pickId.object)
                    this.edgeStage.enabled = !this.cesiumStage.enabled
                } else {
                    $message.alert('未找到pickId')
                }

            }
            this._setAttribute()
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

        // 增加拖拽
        var moveEl = document.getElementsByClassName('cesium-infoBox')[0];
        const mouseDown = (e) => {
            let X = e.clientX - moveEl.offsetLeft;
            let Y = e.clientY - moveEl.offsetTop;
            const move = (e) => {
                moveEl.style.left = e.clientX - X + "px";
                moveEl.style.top = e.clientY - Y + "px";
            };
            document.addEventListener("mousemove", move);
            document.addEventListener("mouseup", () => {
                document.removeEventListener("mousemove", move);
            });
        };
        moveEl.addEventListener("mousedown", mouseDown);
    }

    /**
     * @description: 将点击获取的要素的属性设置给展示框
     * @author: wbw
     */
    _setAttribute() {
        if (this.selectedModel.feature != undefined) {
            var pickModel = this.selectedModel.feature
            var items = {};
            items["batchId"] = pickModel.getProperty("batchId");
            items["extdata"] = pickModel.getProperty("extdata");
            items["layerType"] = pickModel.getProperty("layerType");
            items["uuid"] = pickModel.getProperty("uuid");
            items["webid"] = pickModel.getProperty("webid");
            var contentHtml =
                '<table class="infoBoxp cesium-infoBox-defaultTable"><tbody>';
            for (let pro in items) {
                contentHtml +=
                    "<tr><th>" +
                    `${pro}` +
                    "</th>" +
                    "<td>" +
                    `${items[pro]}` +
                    "</td>" +
                    "</tr>";
            }
            contentHtml += "</tbody></table>";
        }

        var featureName = pickModel.getProperty("name");
        this.selectedEntity.name = featureName;
        this.selectedEntity.description = contentHtml;
        this.viewer.selectedEntity = this.selectedEntity;
    }

    /**
     * @description: 关闭属性查询
     * @author: wbw
     */
    cancel() {
        // this.stage.selected = [];
        this.selectedEntity.description = null
        // this.viewer.selectedEntity = null;
        this.selectedEntity.name = null
        this.viewer.resolutionScale = this.resolutionScale;
        this.viewer.postProcessStages.fxaa.enabled = this.postProcessStages
        this.viewer.scene.globe.depthTestAgainstTerrain = this.depthTestAgainstTerrain
        this.edgeStage.selected = [];
        this.viewer.scene.forceRender();
        this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
   
        this.selectedModel.feature = undefined
        this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

}

export default Attribute