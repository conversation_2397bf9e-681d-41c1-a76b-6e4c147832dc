// import Vue from 'vue';
import axios from 'axios';
import router from '@/router/index';
import {
  ElMessage
} from 'element-plus';
// Vue.prototype.axios = axios;
// 创建axios实例
console.log("url",process.env.NODE_ENV)
let baseURL = process.env.NODE_ENV === "development"?'':'http://127.0.0.1:3000/'
// let baseURL = process.env.NODE_ENV === "development"?'':'http://172.19.7.226:48080/'
const $axios = axios.create({
  baseURL:baseURL
});
$axios.defaults.timeout = 1000 * 50; // 请求超时时间

// request拦截器
$axios.interceptors.request.use(config => {
  if (sessionStorage.getItem('token')) {
    config.headers.Authorization = sessionStorage.getItem('token')
  }
  if (navigator.userAgent.indexOf('Trident') != -1) {
    if (config.method == 'post') {
      config.data = {
        ...config.data,
        t: Date.now()
      };
    } else if (config.method == 'get') {
      config.params = {
        ...config.params,
        t: Date.now()
      };
    }
  }
  return config;
}, error => {
  return Promise.reject(error);
});

// respone 拦截器
$axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    if (error && error.response) {
      let message = "";
      switch (error.response.status) {
          case 400:
              message = "请求错误";
              break;
          case 401: {
              message = "未授权，请登录";
              router.replace({
                  name: "Login",
              });
              break;
          }
          case 403:
              message = "没有权限，拒绝访问";
              break;
          case 404:
              message = `请求地址出错`;
              break;
          case 408:
              message = "请求超时";
              break;
          case 500:
              message = "服务器内部错误";
              break;
          case 501:
              message = "服务未实现";
              break;
          case 502:
              message = "网关错误";
              break;
          case 503:
              message = "服务不可用";
              break;
          case 504:
              message = "网关超时";
              break;
          case 505:
              message = "HTTP版本不受支持";
              break;
          default:
              break;
      }
      ElMessage({
        type: 'error',
        message: `${message}`
      });
  }
  ElMessage({
    type: 'error',
    message: `接口请求错误！`
  });
    return Promise.reject(error);
  }
);

export default $axios;
