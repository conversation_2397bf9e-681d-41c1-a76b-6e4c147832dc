<template>
  <div class="measurement-tools">
    <div class="tool-buttons">
      <button 
        class="tool-btn" 
        :class="{ active: currentTool === 'distance' }"
        @click="startDistanceMeasurement"
        title="距离测量"
      >
        <i class="icon-distance">📏</i>
        距离测量
      </button>
      
      <button 
        class="tool-btn" 
        :class="{ active: currentTool === 'area' }"
        @click="startAreaMeasurement"
        title="面积测量"
      >
        <i class="icon-area">📐</i>
        面积测量
      </button>
      
      <button 
        class="tool-btn" 
        :class="{ active: currentTool === 'height' }"
        @click="startHeightMeasurement"
        title="高度测量"
      >
        <i class="icon-height">📊</i>
        高度测量
      </button>
      
      <button 
        class="tool-btn clear-btn" 
        @click="clearAll"
        title="清除所有测量"
      >
        <i class="icon-clear">🗑️</i>
        清除
      </button>
    </div>
    
    <div v-if="measurements.length > 0" class="measurement-results">
      <div class="results-header">测量结果</div>
      <div 
        v-for="(measurement, index) in measurements" 
        :key="index"
        class="measurement-item"
      >
        <div class="measurement-type">{{ measurement.type }}</div>
        <div class="measurement-value">{{ measurement.value }}</div>
        <button 
          class="delete-btn" 
          @click="deleteMeasurement(index)"
          title="删除此测量"
        >
          ✕
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as Cesium from 'cesium'

const currentTool = ref(null)
const measurements = ref([])
const viewer = ref(null)
const handler = ref(null)
const activePoints = ref([])
const activeEntity = ref(null)

onMounted(() => {
  // 获取全局viewer
  viewer.value = window.viewer || window.cesiumManager?.viewer
  if (!viewer.value) {
    console.error('Cesium viewer未找到')
  }
})

onUnmounted(() => {
  cleanup()
})

/**
 * 开始距离测量
 */
const startDistanceMeasurement = () => {
  cleanup()
  currentTool.value = 'distance'
  activePoints.value = []
  
  if (!viewer.value) return
  
  handler.value = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
  
  handler.value.setInputAction((event) => {
    const position = viewer.value.camera.pickEllipsoid(event.position, viewer.value.scene.globe.ellipsoid)
    if (!position) return
    
    activePoints.value.push(position)
    
    if (activePoints.value.length === 1) {
      // 第一个点
      viewer.value.entities.add({
        position: position,
        point: {
          pixelSize: 8,
          color: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      })
    } else if (activePoints.value.length === 2) {
      // 第二个点，完成测量
      viewer.value.entities.add({
        position: position,
        point: {
          pixelSize: 8,
          color: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      })
      
      // 添加线段和标签
      const distance = Cesium.Cartesian3.distance(activePoints.value[0], activePoints.value[1])
      const midpoint = Cesium.Cartesian3.midpoint(activePoints.value[0], activePoints.value[1], new Cesium.Cartesian3())
      
      activeEntity.value = viewer.value.entities.add({
        polyline: {
          positions: activePoints.value,
          width: 3,
          color: Cesium.Color.YELLOW,
          clampToGround: true
        },
        position: midpoint,
        label: {
          text: `${distance.toFixed(2)} m`,
          font: '14pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -40),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      })
      
      // 添加到测量结果
      measurements.value.push({
        type: '距离',
        value: `${distance.toFixed(2)} m`,
        entity: activeEntity.value
      })
      
      cleanup()
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

/**
 * 开始面积测量
 */
const startAreaMeasurement = () => {
  cleanup()
  currentTool.value = 'area'
  activePoints.value = []
  
  if (!viewer.value) return
  
  handler.value = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
  
  handler.value.setInputAction((event) => {
    const position = viewer.value.camera.pickEllipsoid(event.position, viewer.value.scene.globe.ellipsoid)
    if (!position) return
    
    activePoints.value.push(position)
    
    // 添加点
    viewer.value.entities.add({
      position: position,
      point: {
        pixelSize: 8,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    
    if (activePoints.value.length >= 3) {
      // 更新多边形
      if (activeEntity.value) {
        viewer.value.entities.remove(activeEntity.value)
      }
      
      const area = calculatePolygonArea(activePoints.value)
      const center = calculatePolygonCenter(activePoints.value)
      
      activeEntity.value = viewer.value.entities.add({
        polygon: {
          hierarchy: activePoints.value,
          material: Cesium.Color.YELLOW.withAlpha(0.3),
          outline: true,
          outlineColor: Cesium.Color.YELLOW,
          height: 0
        },
        position: center,
        label: {
          text: `${area.toFixed(2)} m²`,
          font: '14pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -40),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      })
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
  
  // 右键完成测量
  handler.value.setInputAction(() => {
    if (activePoints.value.length >= 3 && activeEntity.value) {
      const area = calculatePolygonArea(activePoints.value)
      measurements.value.push({
        type: '面积',
        value: `${area.toFixed(2)} m²`,
        entity: activeEntity.value
      })
    }
    cleanup()
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
}

/**
 * 开始高度测量
 */
const startHeightMeasurement = () => {
  cleanup()
  currentTool.value = 'height'
  activePoints.value = []
  
  if (!viewer.value) return
  
  handler.value = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
  
  handler.value.setInputAction((event) => {
    const position = viewer.value.camera.pickEllipsoid(event.position, viewer.value.scene.globe.ellipsoid)
    if (!position) return
    
    const cartographic = Cesium.Cartographic.fromCartesian(position)
    const height = cartographic.height
    
    activeEntity.value = viewer.value.entities.add({
      position: position,
      point: {
        pixelSize: 10,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: `高度: ${height.toFixed(2)} m`,
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -40),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    
    measurements.value.push({
      type: '高度',
      value: `${height.toFixed(2)} m`,
      entity: activeEntity.value
    })
    
    cleanup()
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

/**
 * 计算多边形面积
 */
const calculatePolygonArea = (positions) => {
  if (positions.length < 3) return 0
  
  const cartographics = positions.map(pos => Cesium.Cartographic.fromCartesian(pos))
  let area = 0
  
  for (let i = 0; i < cartographics.length; i++) {
    const j = (i + 1) % cartographics.length
    area += cartographics[i].longitude * cartographics[j].latitude
    area -= cartographics[j].longitude * cartographics[i].latitude
  }
  
  area = Math.abs(area) / 2.0
  area = area * 6378137 * 6378137 // 转换为平方米
  
  return area
}

/**
 * 计算多边形中心点
 */
const calculatePolygonCenter = (positions) => {
  let x = 0, y = 0, z = 0
  
  positions.forEach(pos => {
    x += pos.x
    y += pos.y
    z += pos.z
  })
  
  return new Cesium.Cartesian3(
    x / positions.length,
    y / positions.length,
    z / positions.length
  )
}

/**
 * 删除单个测量
 */
const deleteMeasurement = (index) => {
  const measurement = measurements.value[index]
  if (measurement.entity && viewer.value) {
    viewer.value.entities.remove(measurement.entity)
  }
  measurements.value.splice(index, 1)
}

/**
 * 清除所有测量
 */
const clearAll = () => {
  cleanup()
  measurements.value.forEach(measurement => {
    if (measurement.entity && viewer.value) {
      viewer.value.entities.remove(measurement.entity)
    }
  })
  measurements.value = []
  
  // 清除所有实体（可选）
  if (viewer.value) {
    viewer.value.entities.removeAll()
  }
}

/**
 * 清理当前工具
 */
const cleanup = () => {
  if (handler.value) {
    handler.value.destroy()
    handler.value = null
  }
  currentTool.value = null
  activePoints.value = []
  activeEntity.value = null
}
</script>

<style lang="less" scoped>
.measurement-tools {
  background: rgba(11, 24, 36, 0.9);
  border: 1px solid rgba(51, 214, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  min-width: 200px;
}

.tool-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(51, 214, 255, 0.1);
  border: 1px solid rgba(51, 214, 255, 0.3);
  border-radius: 4px;
  color: #33d6ff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  
  &:hover {
    background: rgba(51, 214, 255, 0.2);
    border-color: #33d6ff;
  }
  
  &.active {
    background: rgba(51, 214, 255, 0.3);
    border-color: #33d6ff;
    color: #ffffff;
  }
  
  &.clear-btn {
    background: rgba(255, 107, 107, 0.1);
    border-color: rgba(255, 107, 107, 0.3);
    color: #ff6b6b;
    
    &:hover {
      background: rgba(255, 107, 107, 0.2);
      border-color: #ff6b6b;
    }
  }
}

.measurement-results {
  border-top: 1px solid rgba(51, 214, 255, 0.3);
  padding-top: 15px;
}

.results-header {
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.measurement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: rgba(51, 214, 255, 0.1);
  border-radius: 4px;
  margin-bottom: 8px;
}

.measurement-type {
  color: #33d6ff;
  font-size: 12px;
}

.measurement-value {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
}

.delete-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 2px;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: rgba(255, 107, 107, 0.2);
  }
}
</style>
