<template>
    <div class="zuantan" >
      <div class="right_box1">
        <div class="box1_title">
          <img :src="titleImg" class="box_img" />
          <span>分析窗口</span>
        </div>
        <div class="right_content">
          <div class="right_content_box">
            <div class="buffer_line_text">
              <span class="buffer_line_title">插值：</span>
              <el-input-number :max="100" v-model="slopeInterpolations"></el-input-number>
            </div>
            <div class="buffer_line_text">
              <span class="buffer_line_title">单元格采样：</span>
              <el-input-number :max="100" v-model="slopeSamples"></el-input-number>
            </div>
            <div class="buffer_line_text">
              <span class="buffer_line_title">箭头宽度：</span>
              <el-input-number :max="100" v-model="slopeArrowWidth"></el-input-number>
            </div>
            <div class="buffer_line">
              <span class="buffer_line_title">箭头颜色：</span>
              <el-color-picker v-model="slopeArrowColor" show-alpha />
            </div>
            <div class="buffer_line_text">
              <span class="buffer_line_title">填充半径：</span>
              <el-input-number :max="4000" v-model="slopeMapRadius"></el-input-number>
            </div>
          </div>
          <div class="buffer_btn_box">
            <div class="buffer_btn" @click="slopeReset()">重置</div>
            <div class="buffer_btn buffer_cancel" @click="slopeOK()">
              开始
            </div>
          </div>
        </div>
      </div>
    </div>
</template>
      
<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef } from "vue";
import titleImg from "@/assets/img/home/<USER>";
import * as echarts from "echarts";
import GwSlope2 from "@/utils/map/GwSlope2";
const setDrag = () => {
  // 增加拖拽
  var moveEl = document.getElementsByClassName('buffer_btn_box')[0];
  var moveEl1 = document.getElementsByClassName('box1_title')[0];
  var moveEl2 = document.getElementsByClassName('zuantan')[0];
  const mouseDown = (e) => {
    let X = e.clientX - moveEl2.offsetLeft;
    let Y = e.clientY - moveEl2.offsetTop;
    const move = (e) => {
      moveEl2.style.left = e.clientX - X + "px";
      moveEl2.style.top = e.clientY - Y + "px";
    };
    document.addEventListener("mousemove", move);
    document.addEventListener("mouseup", () => {
      document.removeEventListener("mousemove", move);
    });
  };
  moveEl.addEventListener("mousedown", mouseDown);
  moveEl1.addEventListener("mousedown", mouseDown);
}
//生命周期
onMounted(() => {
  let toolBox = document.getElementsByClassName('home_legend')[0]
  toolBox.style.right = '280px'
  toolBox.style.left = ''
  setDrag()
});
onUnmounted(() => {
  if(slope){
    slope.destroy();
  }
  removeTimeout()
});

let timer1
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  } 
}


let slope;
timer1=setTimeout(() => {
  window.slope = slope = new GwSlope2(viewer);
}, 1000);


//工具栏——坡度
let isSlope = false;
let slopeInterpolations = ref(5);
let slopeSamples = ref(5);
let slopeArrowWidth = ref(8);
let slopeMapRadius = ref(120);
let slopeArrowColor = ref("rgba(255, 255, 0, 0.8)");
//重置
const slopeReset = () => {
  slopeInterpolations.value = 5;
  slopeSamples.value = 5;
  slopeArrowWidth.value = 8;
  slopeMapRadius.value = 120;
  slopeArrowColor.value = "rgba(255, 255, 0, 0.8)";
};
//确定
const slopeOK = () => {
  slopeMethods();
};
const slopeMethods = () => {
  slope.resetOptions();
  let [red, green, blue, alpha] = slopeArrowColor.value
    .match(/\d+(\.\d+)?/g)
    .map(Number);
  var aColor = new Geowin3D.Color(red / 255, green / 255, blue / 255, alpha);
  let option = {
    id: "slefSlopeTooltip",
    interpolations: slopeInterpolations.value, // 多边形包围矩形短边插值数
    samples: slopeSamples.value, // 单元格采样点数
    arrowWidth: slopeArrowWidth.value, // 坡向箭头宽度
    arrowColor: aColor, // 坡向箭头颜色
    mapRadius: slopeMapRadius.value, // 坡面填充色半径
    mapGradient: {
      // 坡面填充渐变色
      0.25: "rgb(0, 255, 255)",
      0.55: "rgb(0, 255, 0)",
      0.85: "rgb(255, 255, 0)",
      1: "rgb(255, 0, 0)",
    },
  };
  slope.set(option);
  slope.start();
};



</script>
<style lang="less" scoped>
.zuantan {
  top: 90px;
  right: 10px;
  width: 260px;
  height: 380px;
  display: flex;
  justify-content: space-between;
  position: fixed;
  flex-direction: column;
  // background: rgba(11, 24, 36,.7);
  background-image: url("../../assets/img/home/<USER>");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 18px;
  .right_box1 {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0px 0px 0 0px;
    display: flex;
    flex-direction: column;
    .right_content {
      font-size: 14px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #00f0e8;
      flex: 1;
      // margin-top: 20px;
      display: flex;
      align-items: flex-end;
      .right_content_box{
        position: absolute;
        top: 80px;
        left: 20px;
        width: 220px;
      }
      .buffer_line_text {
        font-size: 18px;
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 15px;
      }

      .buffer_line {
        display: flex;
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }

      .buffer_line_title {
        width: 120px;
        font-size: 15px;
      }

      .buffer_btn_box {
        width: 100%;
        display: flex;
        justify-content: space-around;
        padding-bottom: 20px;
        cursor: pointer;
        .buffer_btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 32px;
          cursor: pointer;
          border: 1px solid;
          border-radius: 4px;
          font-size: 16px;

          &:hover {
            border: 1px solid #73d897;
            transform: scale(1.05);
          }
        }

        .buffer_cancel {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  // 图框 标题
  .box1_title{
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>
      