import { ref } from 'vue'
export default function cameraInfo(){
    let lng = ref(0);
    let lat = ref(0);
    let height = ref(0);    
    let heading = ref(0);    
    let pitch = ref(0);    
    let roll = ref(0);    
    setInterval(()=>{
        const CameraInfo = ref(window.earth.getCameraInfo())
        lng.value=CameraInfo.value['lontitude']
        lat.value=CameraInfo.value['latitude']
        height.value=CameraInfo.value['height']
        heading.value =CameraInfo.value['heading']
      pitch.value=CameraInfo.value['pitch']
      roll.value =CameraInfo.value['roll']
    },100)
    return{
        lng:lng,
        lat:lat,
        height:height,
        heading:heading,
        pitch:pitch,
        roll:roll
    }
}
