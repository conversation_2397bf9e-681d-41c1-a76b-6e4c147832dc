import { ref } from 'vue'

export default function cameraInfo(){
    let lng = ref(0);
    let lat = ref(0);
    let height = ref(0);
    let heading = ref(0);
    let pitch = ref(0);
    let roll = ref(0);

    setInterval(()=>{
        try {
            // 检查是否有可用的相机信息获取方法
            let cameraInfoData = null;

            if (window.earth && typeof window.earth.getCameraInfo === 'function') {
                // 使用原有的earth对象
                cameraInfoData = window.earth.getCameraInfo();
            } else if (window.viewer && window.viewer.camera) {
                // 使用Cesium viewer直接获取相机信息
                const camera = window.viewer.camera;
                const position = camera.position;
                const cartographic = window.Cesium.Cartographic.fromCartesian(position);

                cameraInfoData = {
                    lontitude: window.Cesium.Math.toDegrees(cartographic.longitude),
                    latitude: window.Cesium.Math.toDegrees(cartographic.latitude),
                    height: cartographic.height,
                    heading: window.Cesium.Math.toDegrees(camera.heading),
                    pitch: window.Cesium.Math.toDegrees(camera.pitch),
                    roll: window.Cesium.Math.toDegrees(camera.roll)
                };
            } else if (window.cesiumManager && window.cesiumManager.viewer) {
                // 使用新的Cesium管理器
                const cameraPos = window.cesiumManager.getCameraPosition();
                cameraInfoData = {
                    lontitude: cameraPos.longitude,
                    latitude: cameraPos.latitude,
                    height: cameraPos.height,
                    heading: cameraPos.heading,
                    pitch: cameraPos.pitch,
                    roll: cameraPos.roll
                };
            }

            if (cameraInfoData) {
                lng.value = cameraInfoData.lontitude || cameraInfoData.longitude || 0;
                lat.value = cameraInfoData.latitude || 0;
                height.value = cameraInfoData.height || 0;
                heading.value = cameraInfoData.heading || 0;
                pitch.value = cameraInfoData.pitch || 0;
                roll.value = cameraInfoData.roll || 0;
            }
        } catch (error) {
            console.warn('获取相机信息失败:', error);
            // 保持之前的值，不更新
        }
    }, 100);

    return{
        lng: lng,
        lat: lat,
        height: height,
        heading: heading,
        pitch: pitch,
        roll: roll
    }
}
