<!doctype html>

<title>CodeMirror: Multiple Buffer & Split View Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/javascript/javascript.js"></script>
<script src="../mode/css/css.js"></script>
<style type="text/css" id=style>
      .CodeMirror {border: 1px solid black; height: 250px;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Multiple Buffer & Split View</a>
  </ul>
</div>

<article>
<h2>Multiple Buffer & Split View Demo</h2>


    <div id=code_top></div>
    <div>
      Select buffer: <select id=buffers_top></select>
      &nbsp; &nbsp; <button onclick="newBuf('top')">New buffer</button>
    </div>
    <div id=code_bot></div>
    <div>
      Select buffer: <select id=buffers_bot></select>
      &nbsp; &nbsp; <button onclick="newBuf('bot')">New buffer</button>
    </div>

    <script id=script>
var sel_top = document.getElementById("buffers_top");
CodeMirror.on(sel_top, "change", function() {
  selectBuffer(ed_top, sel_top.options[sel_top.selectedIndex].value);
});

var sel_bot = document.getElementById("buffers_bot");
CodeMirror.on(sel_bot, "change", function() {
  selectBuffer(ed_bot, sel_bot.options[sel_bot.selectedIndex].value);
});

var buffers = {};

function openBuffer(name, text, mode) {
  buffers[name] = CodeMirror.Doc(text, mode);
  var opt = document.createElement("option");
  opt.appendChild(document.createTextNode(name));
  sel_top.appendChild(opt);
  sel_bot.appendChild(opt.cloneNode(true));
}

function newBuf(where) {
  var name = prompt("Name for the buffer", "*scratch*");
  if (name == null) return;
  if (buffers.hasOwnProperty(name)) {
    alert("There's already a buffer by that name.");
    return;
  }
  openBuffer(name, "", "javascript");
  selectBuffer(where == "top" ? ed_top : ed_bot, name);
  var sel = where == "top" ? sel_top : sel_bot;
  sel.value = name;
}

function selectBuffer(editor, name) {
  var buf = buffers[name];
  if (buf.getEditor()) buf = buf.linkedDoc({sharedHist: true});
  var old = editor.swapDoc(buf);
  var linked = old.iterLinkedDocs(function(doc) {linked = doc;});
  if (linked) {
    // Make sure the document in buffers is the one the other view is looking at
    for (var name in buffers) if (buffers[name] == old) buffers[name] = linked;
    old.unlinkDoc(linked);
  }
  editor.focus();
}

function nodeContent(id) {
  var node = document.getElementById(id), val = node.textContent || node.innerText;
  val = val.slice(val.match(/^\s*/)[0].length, val.length - val.match(/\s*$/)[0].length) + "\n";
  return val;
}
openBuffer("js", nodeContent("script"), "javascript");
openBuffer("css", nodeContent("style"), "css");

var ed_top = CodeMirror(document.getElementById("code_top"), {lineNumbers: true});
selectBuffer(ed_top, "js");
var ed_bot = CodeMirror(document.getElementById("code_bot"), {lineNumbers: true});
selectBuffer(ed_bot, "js");
</script>

    <p>Demonstration of
    using <a href="../doc/manual.html#linkedDoc">linked documents</a>
    to provide a split view on a document, and
    using <a href="../doc/manual.html#swapDoc"><code>swapDoc</code></a>
    to use a single editor to display multiple documents.</p>

  </article>
