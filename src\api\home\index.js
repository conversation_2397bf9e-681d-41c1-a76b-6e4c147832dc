/*
 * @Description: 
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2023-02-20 09:12:30
 * @LastEditors: wbw
 * @LastEditTime: 2023-03-01 15:25:29
 */
import $http from '@/utils/request';
import $ from "jquery";

export function getDataBackMine(arg) {
	// return $http.get(`/app-api/geo/datacoalbaseinfo-server/getDataBackMine`);
	// return $http.get(`/public/endData/getDataBackMine`);
	return new Promise(function (resolve, reject) {
		$.ajax({
			url: "/endData/getDataBackMine.json",
			type: "GET",
			dataType: "json",
			success: (data) => {
				resolve(data);
			}
		})
	});
}

export function getDataCoalBaseInfo(params) {
	return new Promise(function (resolve, reject) {
		$.ajax({
			url: "/endData/getDataCoalBaseInfo.json",
			type: "GET",
			dataType: "json",
			success: (data) => {
				resolve(data);
			}
		})

	});
}

export function getDataExcavation(params) {
	// return $http.get(`/public/endData/getDataExcavation`);
	return new Promise(function (resolve, reject) {
		$.ajax({
			url: "/endData/getDataExcavation.json",
			type: "GET",
			dataType: "json",
			success: (data) => {
				resolve(data);
			}
		})
	});
}

export function getDatageology(params) {
	// return $http.get(`/public/endData/getDatageology`);
	return new Promise(function (resolve, reject) {
		$.ajax({
			url: "/endData/getDatageology.json",
			type: "GET",
			dataType: "json",
			success: (data) => {
				resolve(data);
			}
		})
	});
}


export function getLayerdirectory(params) {
	// return $http.get(`/public/endData/getLayerdirectory`);
	return new Promise(function (resolve, reject) {
		$.ajax({
			url: "/endData/getLayerdirectory.json",
			type: "GET",
			dataType: "json",
			success: (data) => {
				resolve(data);
			}
		})
	});
}
//未使用
var flag = 20005
// var flag = 48080
export function getJuejinModel() {
	return "/endData/Model/采煤机.glb"
	// if (flag == 20005) {
	// 	return "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1596741818243813376"
	// } else if (flag == 48080) {
	// 	return "http://172.19.7.226:48080/app-api/geo/function-server-gltf/get-code/1610659036567171072"
	// }
}

export function getJYeyaModel() {
	return "/endData/Model/液压.glb"
	// if (flag == 20005) {
	// 	return "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1596741845225771008"
	// } else if (flag == 48080) {
	// 	return "http://172.19.7.226:48080/app-api/geo/function-server-gltf/get-code/1610658997031661568"
	// }

}

export function getCaimeiModel() {
	return "/endData/Model/掘进车.glb"
	// if (flag == 20005) {
	// 	return "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1612797327182860288"
	// } else if (flag == 48080) {
	// 	return "http://172.19.7.226:48080/app-api/geo/function-server-gltf/get-code/1610659017244012544"
	// }

}

export function getXYZMap() {
	return '/endData/237/{z}/{x}/{y}.png'
}

export function getYCQJson(params) {
	if (flag == 20005) {
		return "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1596741818243813376"
	} else if (flag == 48080) {
		return "http://172.19.7.226:48080/app-api/geo/function-server-gltf/get-code/1610659036567171072"
	}
}