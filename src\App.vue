<template>
  <!-- <Resize> -->
    <div class="main">
      <router-view />
    </div>
  <!-- </Resize> -->
</template>

<script>
import { useRouter } from 'vue-router'
import {localGet} from '@/utils/index'
// import Resize from '@/utils/Resize.vue'
// const router = useRouter()

// router.beforeEach((to, from, next) => {
//   if (to.path == '/login') {
//     // 如果路径是 /login 则正常执行
//     next()
//   } else {
//     // 如果不是 /login，判断是否有 token
//     // if (!localGet('token')) {
//     //   // 如果没有，则跳至登录页面
//     //   next({ path: '/login' })
//     // } else {
//     //   // 否则继续执行
//     //   next()
//     // }
//     // next({ path: '/login' })
//     console.log(to)
//     next()
//   }
//   // next()
//   // state.showMenu = !noMenu.includes(to.path)
//   // state.currentPath = to.path
//   document.title = "地质保障系统"
// })
</script>
<style>
@import './assets/css/reset.css';

</style>