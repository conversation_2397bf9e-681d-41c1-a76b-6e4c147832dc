/* 全局滑动条样式优化 */

/* 默认滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(51, 214, 255, 0.6);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 214, 255, 0.8);
}

::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.1);
}

/* 针对特定容器的滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 5px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(51, 214, 255, 0.7);
  border-radius: 5px;
  cursor: pointer;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 214, 255, 0.9);
}

/* 细滚动条样式 */
.thin-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 3px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(51, 214, 255, 0.5);
  border-radius: 3px;
  cursor: pointer;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 214, 255, 0.7);
}

/* 隐藏滚动条但保持滚动功能 */
.hidden-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hidden-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 表格滚动条样式 */
.table-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-scrollbar::-webkit-scrollbar-track {
  background: rgba(4, 48, 98, 0.3);
  border-radius: 4px;
}

.table-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(51, 214, 255, 0.6);
  border-radius: 4px;
  cursor: pointer;
}

.table-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 214, 255, 0.8);
}

/* 图表容器滚动条样式 */
.chart-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.chart-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.chart-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(51, 214, 255, 0.6);
  border-radius: 4px;
  cursor: pointer;
}

.chart-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 214, 255, 0.8);
}

/* 响应式滚动条 */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
}

/* 滚动条动画效果 */
@keyframes scrollbar-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animated-scrollbar::-webkit-scrollbar-thumb {
  animation: scrollbar-fade-in 0.3s ease-in-out;
}
