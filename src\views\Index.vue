<template>
    <div class="home_box">
      <Header />
      <Map />
    </div>
  </template>
  
  <script setup>
  import { useRouter } from 'vue-router'
  import {localGet} from '@/utils/index'
  import Header from "@/components/layout/Header.vue";
  import Map from "@/components/Map.vue";

  </script>
  <style lang="less" scoped>
  .home_box{
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  </style>