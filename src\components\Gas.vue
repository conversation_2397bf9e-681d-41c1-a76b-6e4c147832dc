<template>
  <div class="monitoring">
    <transition
      appear
      name="animate__animated animate__pulse"
      enter-active-class="animate__fadeInLeft"
      leave-active-class="animate__fadeOutLeft"
    >
      <div class="left" v-show="show">
        <div class="left_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            基础信息统计
          </div>
          <div class="left_box1_content">
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg1" />
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>22.40</span>MPa</div>
                <span class="left_box1_text">最大支架效率</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg2" />
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>14</span>左立柱</div>
                <span class="left_box1_text">最大阻力位置</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg3" />
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>175</span>个</div>
                <span class="left_box1_text">在线测点数</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg4" />
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>5</span>个</div>
                <span class="left_box1_text">报警组点数</span>
              </div>
            </div>
          </div>
        </div>
        <div class="left_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            支架阻力位移监测
          </div>
          <div class="right_content">
            <div class="right_box1_table_head">
              <div class="right_box1_name right_box1_head_text">监测地点</div>
              <div class="right_box1_time right_box1_head_text">时间</div>
              <div class="right_box1_num right_box1_head_text">阻力值MPa</div>
              <div class="right_box1_num right_box1_head_text">位移值mm</div>
            </div>
            <vue3-seamless-scroll
              :list="list"
              class="right_three_scroll"
              :step="0.5"
              v-model="scroll"
              :hover="hover"
              :limitScrollNum="limitScrollNum"
              wheel="true"
            >
              <div class="" v-for="(item, index) in list" :key="index">
                <div
                  class="right_box1_table_head right_box1_table_line"
                  :class="
                    index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                  "
                >
                  <div class="right_box1_name">立柱1</div>
                  <div class="right_box1_time">
                    09-06 22：34
                  </div>
                  <div class="right_box1_num ">
                    1.24
                  </div>
                  <div class="right_box1_num ">
                    10
                  </div>
                </div>
              </div>
            </vue3-seamless-scroll>
          </div>
        </div>
      </div>
    </transition>
    <transition
      appear
      name="animate__animated animate__bounce"
      enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight"
    >
      <div class="right" v-show="show">
        <div class="right_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            支架位移曲线
          </div>
          <div id="receptionTrend" class="myChart"></div>
        </div>
        <div class="right_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            支架历史云图
          </div>
          <div id="hisChart" class="myChart"></div>
        </div>
        <div class="right_box3">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            最大工作阻力曲线
          </div>
          <div id="resistance" class="resChart"></div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import titleImg from "@/assets/img/home/<USER>";
import lineImg from "@/assets/img/home/<USER>";
import resImg from "@/assets/img/home/<USER>";
import upImg from "@/assets/img/home/<USER>";
import downImg from "@/assets/img/home/<USER>";
import leftImg1 from "@/assets/img/stoping/1-1.png";
import leftImg2 from "@/assets/img/stoping/1-2.png";
import leftImg3 from "@/assets/img/stoping/1-2.png";
import leftImg4 from "@/assets/img/stoping/1-2.png";
import * as echarts from "echarts";
import 'echarts-gl'
import '@/utils/echarts3D.js';
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef } from "vue";
let list = ref([
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
]);
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 15,
});
let { scroll, hover, limitScrollNum } = toRefs(state);
const handleOpen = () => {
  // 查看
};
const echart = echarts;
let myChart = null;
let hisChart = null
let resChart = null
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
//生命周期
onMounted(() => {
  // 初始化 左右两侧 确保展开  工具栏 默认到1  图层关闭
  initStore(6);
  initEcharts();
  initHisEcharts()
  initResEcharts()
});

const initEcharts=() =>{
  document.getElementById("receptionTrend").removeAttribute('_echarts_instance_')
  myChart = echart.init(document.getElementById("receptionTrend"));
  myChart.setOption({
    title: {
      text: "单位（mm）",
      textStyle: {
        color: "#FFFFFF",
        
        fontSize: 14,
      },
    },
    textStyle: {
      color: "#FFFFFF",
      
      fontSize: 14,
    },
    tooltip: {
      trigger: "axis",
    },
    boundaryGap: [0, 0.2],
    color: ["#0DD6E3"],
    legend: {
      data: ["立柱位移"],
      textStyle: {
        color: "#FFFFFF",
        
        fontSize: 14,
      },
    },
    grid: {
      left: "2%",
      right: "3%",
      bottom: "4%",
      // top:'1%',
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: true,
      data: ["1", "2", "3", "4", "5"],
      axisLine: {
        lineStyle: {
          color: "#0DD6E3",
        },
      },
      axisLabel: {
        // interval:0,
        // rotate:"5",
        // align:'',
        overflow: "breakAll",
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          color: "#0DD6E3",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: "立柱位移",
        type: "line",
        stack: "Total",
        data: [120, 132, 101, 134, 90],
      }
    ],
  });
}
const initHisEcharts=() =>{
  hisChart = echart.init(document.getElementById("hisChart"));
  var noise = new SimplexNoise(Math.random);
  function generateData(theta, min, max) {
    var data = [];
    for (var i = 0; i <= 50; i++) {
      for (var j = 0; j <= 50; j++) {
        var value = noise.noise2D(i / 20, j / 20);
        valMax = Math.max(valMax, value);
        valMin = Math.min(valMin, value);
        data.push([i, j, value * 2 + 4]);
      }
    }
    return data;
  }
  var valMin = Infinity;
  var valMax = -Infinity;
  var data = generateData(2, -5, 5);
  hisChart.setOption({
        visualMap: {
            show: false,
            min: 2,
            max: 6,
            inRange: {
              color: [
                '#313695',
                '#4575b4',
                '#74add1',
                '#abd9e9',
                '#e0f3f8',
                '#ffffbf',
                '#fee090',
                '#fdae61',
                '#f46d43',
                '#d73027',
                '#a50026'
              ]
            }
        },
        tooltip: {},
        xAxis3D: {
          type: 'value',
          name:"距离"
        },
        yAxis3D: {
          type: 'value',
          name:"阻力值"
        },
        zAxis3D: {
          type: 'value',
          max: 10,
          min: 0,
          name:"时间"
        },
        grid3D: {
          axisLine: {
            lineStyle: { color: '#fff' }
          },
          axisPointer: {
            lineStyle: { color: '#fff' }
          },
          viewControl: {
            // autoRotate: true,
            // distance:200
          },
          light: {
            main: {
              shadow: true,
              quality: 'ultra',
              intensity: 1.5
            }
          },
          left: "center",
          right: "center",
          top:'-20',
          // boxDepth: 150,//三维笛卡尔坐标系组件在三维场景中的深度
          // boxWidth: 160,//三维场景高度
          // boxHeight:100,//三维场景高度
          // top:'1%',
          // containLabel: true,
        },
        series: [
          {
            type: 'bar3D',
            data: data,
            shading: 'lambert',
            label: {
              formatter: function (param) {
                return param.value[2].toFixed(1);
              }
            },
          }
        ]
        }
    );
 
}
const initResEcharts=() =>{
  resChart = echart.init(document.getElementById("resistance"));
  resChart.setOption({
    title: {
      text: "单位（MPa）",
      textStyle: {
        color: "#FFFFFF",
        
        fontSize: 14,
      },
    },
    textStyle: {
      color: "#FFFFFF",
      
      fontSize: 14,
    },
    tooltip: {
      trigger: "axis",
    },
    boundaryGap: [0, 0.2],
    color: ['#FFD200','#FFD200'],
    legend: {
      data: ["立柱位移"],
      textStyle: {
        color: "#FFFFFF",
        
        fontSize: 14,
      },
    },
    grid: {
      left: "2%",
      right: "3%",
      bottom: "4%",
      // top:'1%',
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: true,
      data: ["8.1", "8.12", "8.23", "8.34", "8.5"],
      axisLine: {
        lineStyle: {
          color: "#0DD6E3",
        },
      },
      axisLabel: {
        // interval:0,
        // rotate:"5",
        // align:'',
        overflow: "breakAll",
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          color: "#0DD6E3",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: "立柱位移",
        type: "line",
        stack: "Total",
        data: [120, 132, 101, 134, 90],
      }
    ],
  });
}
window.onresize = function () {
  myChart.resize();
  hisChart.resize();
  resChart.resize()
};
</script>
<style lang="less" scoped>
.monitoring {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  // overflow: hidden;
  .left {
    z-index: 1;
    width: 394px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
    // overflow-y: scroll;
    // overflow-x: hidden;
    .left_box1 {
      width: 100%;
      height: 256px;
      // flex: 1;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      // background-size:cover;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 16px 0px 24px 24px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);
      .left_box1_content {
        display: flex;
        width: 100%;
        flex: 1;
        justify-content: flex-start;
        box-sizing: border-box;
        padding: 30px 20px 0 0px;
        flex-wrap: wrap;
        .left_box1_li {
          display: flex;
          align-items: center;
          width: 50%;
          margin-bottom: 30px;
          font-size: 16px;
          .left_box_li_right {
            display: flex;
            flex-direction: column;
          }
          .left_box1_num {
            font-size: 14px;
            font-family: Source Han Sans SC;
            font-weight: bold;
            color: #fff;
            margin-right: 20px;
            span {
              font-size: 24px;
              color: #fdff4d;
              margin-right: 4px;
            }
          }
          .left_box1_text {
            font-size: 14px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #ffffff;
            margin: 10px 0 0px 0;
          }
          .left_box_li_img {
            width: 46px;
            height: 36px;
            margin-right: 10px;
          }
        }
      }
    }
    .left_box2 {
      margin-top: 17px;
      width: 100%;
      height: 652px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 23px 0 0px 18px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);
      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 20px 22px;
        overflow: hidden;
        .right_three_scroll {
          height: 560px;
          overflow: hidden;
          
        }
        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 40px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          // justify-content: center;
          align-items: center;
          background: linear-gradient(0deg, rgba(0, 222, 255, 0.24));
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);
          .right_box1_head_text {
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: #26c1d1;
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_num {
            width: 120px;
            text-align: center;
          }
          .right_box1_name {
            width: 100px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            justify-content: flex-start;
            text-align: center;
          }
          .right_box1_time {
            width: 144px;
          }
        }

        .right_box1_table_line {
          height: 36px;
        }
        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }
        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }
  }
  .right {
    z-index: 1;
    width: 394px;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .right_box1 {
      width: 100%;
      height: 336px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 14px 22px 0 22px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);
      .myChart {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 15px 0px 0 0;
      }
    }
    .right_box2 {
      margin-top: 11px;
      width: 100%;
      height: 336px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 15px 0 10px 20px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);
      .myChart {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 15px 0px 0 0;
      }
    }
    .right_box3 {
      margin-top: 11px;
      width: 100%;
      height: 336px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 14px 22px 0 22px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);
      .resChart {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 15px 0px 0 0;
      }
    }
  }
  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }
 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>
