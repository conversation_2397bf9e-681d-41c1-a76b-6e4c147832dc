/**
 * 缓冲区类
 * <AUTHOR>
 * @time 2022年10月16日
 */
class Buffer {
    constructor(_viewer, _options) {
        this.viewer = _viewer;
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.buffer = new Geowin3D.GwTools.GwBufferAnalysis(this.viewer, {
            showBuffer: true,
            showProgress: true
        });
    }

    /**
     * 执行缓冲区回调事件
     * 
     * @param {*} callback 
     */
    on(callback) {
        this._buffer.on(function(et, ev) {
            callback(et, ev);
        });
    }

    /**
     * 设置缓冲距离
     */
    setRadius(_value) {
        this.buffer.radius = parseInt(_value);
    }

    /**
     * 点查询
     */
    pointBuffer() {
        this.buffer.reset();
        this.buffer.type = 'Point';
        this.buffer.start();
    }

    /**
     * 线查询
     */
    lineBuffer() {
        this.buffer.reset();
        this.buffer.type = 'Polyline';
        this.buffer.start();
    }

    /**
     * 面查询
     */
    polygonBuffer() {
        this.buffer.reset();
        this.buffer.type = 'Polygon';
        this.buffer.start();
    }

    /**
     * 缓冲区显示
     */
    show() {
        this.buffer.show();
    }

    /**
     * 缓冲区隐藏
     */
    hide() {
        this.buffer.hide();
    }

    /**
     * 高亮构件
     */
    highlightComponent() {
        this.buffer.highlightComponent();
    }

    /**
     * 取消高亮构件
     */
    unhighlightComponent() {
        this.buffer.unhighlightComponent();
    }

    /**
     * 清空
     */
    destroy() {
        this.buffer.destroy();
    }

}

export default Buffer