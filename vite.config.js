/*
 * @Description: 
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2023-02-20 09:01:25
 * @LastEditors: wbw
 * @LastEditTime: 2023-03-01 15:25:14
 */
import {
  defineConfig
} from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import _ from 'lodash'
// import requireTransform from 'vite-plugin-require-transform';

// https://vitejs.dev/config/
export default defineConfig(({
  command,
  mode
}) => {
  //console.log(command,"command",mode)
  const isBuild = command == "build"
  return {
    plugins: [
      vue(),
      AutoImport({
        // 自动导入
        imports: ['vue', 'vue-router']
      }),
      // requireTransform({
      //   fileRegex: /.js$|.vue$/
      // })
    ],
    base: isBuild ? './' : '/',
    server: {
      hmr: true,
      open: false,
      server: {
        host: '0.0.0.0'
      },
      proxy: {
        '/app-api': {
          target: 'http://************:20005/',
          // target: 'http://************:48080/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/app-api/, 'app-api') //url: '/api/user/login'
        }
      }
    },
    build: {
      outDir: './mineWeb2'
    },
    resolve: {
      alias: {
        '@': path.join(__dirname, 'src')
      }
    },
    define: {
      // 定义Cesium全局变量
      CESIUM_BASE_URL: JSON.stringify('./cesium/')
    },
    optimizeDeps: {
      // 预构建Cesium，但排除一些大文件
      include: ['cesium'],
      exclude: ['cesium/Source/Workers']
    },
    assetsInclude: ['**/*.gltf', '**/*.glb']
  }
})