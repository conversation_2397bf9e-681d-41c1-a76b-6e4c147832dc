<template>
  <div class="deduction">
    <div class="top_box">
      <div class="top_left" :class="btnDiZhiActive==0?'top_left_active':'top_left_d'" @click="handelDiZhiAnalyse(0)">掘进推演</div>
      <div class="top_right" :class="btnDiZhiActive==1?'top_right_active':'top_right_d'" @click="handelDiZhiAnalyse(1)">回采推演</div>
    </div>
    <transition appear name="animate__animated animate__bounce" enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight">
      <div class="right" v-show="show">
        <div class="right_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>预测预报</span>
          </div>
          <div class="right_content">
            <div class="right_table_box">
              <div class="right_box1_table_head right_box1_table_sticky">
                <div class="right_box1_index right_box1_head_text">序号</div>
                <div class="right_box1_type right_box1_head_text">
                  类型
                </div>
                <div class="right_box1_name right_box1_head_text">
                  预测预报内容
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  水平距离
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  垂直距离
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  空间距离
                </div>
                <!-- <div class="right_box1_oper right_box1_head_text">操作</div> -->
              </div>
              <!-- <vue3-seamless-scroll :list="forecastList" class="right_three_scroll" :step="0.5" v-model="scroll" wheel="true"
                :hover="hover" :limitScrollNum="limitScrollNum"> -->
                <div class="right_box2_content">
                <div class="" v-for="(item, index) in forecastList" :key="index">
                  <div class="right_box1_table_head right_box1_table_line" :class="
                    index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                  " @click="handleOpen3(item)" :style="{color: ( item==selectItem ?  'yellow':'white')}" >
                    <div class="right_box1_index right_box1_line_text">
                      {{ index + 1 }}
                    </div>
                    <div class="right_box1_type right_box1_line_text">
                      {{ item.type }}
                    </div>
                    <div class="right_box1_name right_box1_line_text" style="text-align: center">
                      {{ item.title }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance3 }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance2 }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance }}
                    </div>
                    <!-- <div class="right_box1_oper right_box1_line_text">
                      <span class="right_box1_see" @click="handleOpen(item, true)" v-show="!item.show&&item.type=='异常区'" style="color: greenyellow">显示</span>
                      <span class="right_box1_see" @click="handleOpen(item,false)" v-show="item.show&&item.type=='异常区'"
                        style="color: yellow">隐藏</span>
                        <span class="right_box1_see" @click="handleOpen2(item)" v-show="item.type == '断层'">查看</span>
                    </div> -->
                  </div>
                </div> 
                </div>
              <!-- </vue3-seamless-scroll> -->
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
  <!-- <ul class="deduction_tip">
    <li v-for="(item, index) in menuLlist" :key="index" :class="(index == menuActive ? 'li_active' : '')"
      @click="changeMenu(index)">{{ item }}</li>
  </ul> -->
  <div>
    <router-view> </router-view>
  </div>
</template>

<script setup>

import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import router from "../router";
import startImg from "@/assets/img/home/<USER>";
import pausedImg from "@/assets/img/home/<USER>";
import leftImg1 from "@/assets/img/tunnel/1-1.png";
import leftImg2 from "@/assets/img/tunnel/1-2.png";
import leftImg3 from "@/assets/img/tunnel/1-2.png";
import leftImg4 from "@/assets/img/tunnel/1-2.png";
import titleImg from "@/assets/img/home/<USER>";
import btnImgA from "@/assets/img/tunnel/bntA.png";
import btnImg from "@/assets/img/tunnel/btn.png";
import { useToolsStore } from "@/store/tools";
import ForecastSort from "@/utils/map/forecastSort";
import { getDataBackMine, getDataExcavation } from "@/api/home/<USER>";
import StopingBox from "@/utils/map/stopingBox";
import { ref, toRef, toRaw } from "vue";
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
const tunnelData = ref();
const toolsStore = useToolsStore();


var amStore;
var amRate
//进入页面
onMounted(() => {
  // 初始化 左右两侧 确保展开  工具栏 默认到1  图层关闭
  initStore(5);
  // amStore=toRaw(tunnelData.value).amStore
  // amRate=toRaw(tunnelData.value).amRate
  findData()
  setTimeout(() => {
    loadGeoJson()
    addSelectInputAction()
  }, 2000);
});
// 左上角
let excavationInfo = ref({})
const findData = () => {
  getDataExcavation().then((res) => {
    if (res) {
      excavationInfo.value = res
    }
  })
}
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 5,
});
let { scroll, hover, limitScrollNum } = toRefs(state);
// 左中间 超前探
var stopingBox = new StopingBox();
let resultInfoIist = stopingBox.stopingBoxInfoIist;
let resultList = stopingBox.stopingBoxList;
let resultIndex = stopingBox.stopingBoxIndex;
const changeResult = (index) => {
  resultIndex.value = index;
};
const confirmEvent = (item) => {
  stopingBox.downLoadDoc(item)
}
const cancelEvent = (item) => {
  stopingBox.downLoadDWG(item)
}
//回采超前探 图层控制 点击事件
const changeClick = (item) => {
  if (!item.imgUrl) {
    return;
  }
  if (item.check) {
    if (item.imageIdx == -1) {
      stopingBox.loadImage(item);

    } else {
      stopingBox.showImage(item.imageIdx, true);

    }
  } else {
    stopingBox.showImage(item.imageIdx, false);

  }
  viewer.scene.forceRender();
};

// 左下角 实时监测
let btnActive = ref(0);
let btnArr = [
  "矿压监测",
  "水文监测",
  "瓦斯监测",
  "微震监测",
  "电法监测",
  "地音监测",
  "音频电透视",
  "槽波",
];
// 按钮点击
const handelAnalyse = (index, item) => {
  btnActive.value = index;
  var monitoringPoint = window.monitoringPoint;
  monitoringPoint.hideAll();
  monitoringPoint.show(item);
  viewer.scene.forceRender();
};
// 左下角 地质分析
let btnDiZhiActive = ref(0);
let btnDiZhiArr = [
  "掘进推演",
  "回采推演",
  
];
// 按钮点击
const handelDiZhiAnalyse = (index, item) => {
  btnDiZhiActive.value = index;
  router.push(routerArr[index]);
};


//右下角——异常区和断层
let forecastList = ref([]);
var ycq;
var dc
//右下角——加载异常区json文件
const loadGeoJson = () => {
  ycq = window.ycq
  ycq.getDataByList(forecastList)
  dc=window.dc
  dc.getDataByList(forecastList)
};
var forecastSort=new ForecastSort()
var forecastTimer=setInterval(() => {
  forecastSort.bubbleSort(forecastList)
}, 5000);

//右下角——异常区按钮显隐
var dataTree;
const handleOpen = (item, show) => {
  toolsStore.$patch((state) => {
    dataTree = state.dataTree
    var id = window.layersManager.getIdByLabel(item.title)
    if (id != -1) {
      dataTree.setChecked(id, show);
    } else {
      window.ycq.show(item.index, show)
    }
  });
  viewer.scene.forceRender();
};
const handleOpen2=(item)=>{
  var dcPosition=item.position
  var camreaPosition = _getMoveUpCoordinate(dcPosition, 1400)
  viewer.camera.flyTo({
    duration: 1,
    destination: camreaPosition,
    offset: {
      heading: Geowin3D.Math.toRadians(0), // 水平偏角，默认正北 0
      pitch: Geowin3D.Math.toRadians(-90), // 俯视角，默认-90，垂直向下
      // range: 200, //镜头（屏幕）到定位目标点（实体）的距离
    },
  }, 3000)
  function _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }
}
let selectItem=ref("")
const handleOpen3 = (item) => {
  selectItem.value=item
  var camreaPosition = ""
  switch (item.type) {
    case "异常区":
      toolsStore.$patch((state) => {
        dataTree = state.dataTree
        var id = window.layersManager.getIdByLabel(item.title)
        if (id != -1) {
          dataTree.setChecked(id, true);
        } else {
          ycq.show(item.index, true)
        }
      });
      viewer.scene.forceRender();
      if(item.centre){
        camreaPosition = _getMoveUpCoordinate(item.centre, 300)
      }  
      break
    case "断层":
      camreaPosition = _getMoveUpCoordinate(item.position, 1400)
      dc.selectFeatureByTitle(item.title)
      break
    default:
      break
  }
  if (camreaPosition != "") {
    viewer.camera.flyTo({
      duration: 1,
      destination: camreaPosition,
      offset: {
        heading: Geowin3D.Math.toRadians(0), // 水平偏角，默认正北 0
        pitch: Geowin3D.Math.toRadians(-90), // 俯视角，默认-90，垂直向下
        // range: 200, //镜头（屏幕）到定位目标点（实体）的距离
      },
    }, 3000)
  }
  function _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }
}
var selectHandler;
const addSelectInputAction = () => {
  selectHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  selectHandler.setInputAction((movement) => {
    selectItem.value=''
    window.dc.removeSelect()
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
const removeSelectInputAction = () => {
  selectItem.value=''
  window.dc.removeSelect()
  selectHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

// 中间菜单
const GoTo = (name) => {
  const rouerLink = ["/index/Deduction/DTunnel", "/index/Deduction/DStoping"];
  router.push(rouerLink[name]);
};
let menuActive = ref(0)
let menuLlist = ref(['掘进推演', '回采推演'])
let routerArr = ["/index/Deduction/DTunnel", "/index/Deduction/DStoping"];
const changeMenu = (index) => {
  menuActive.value = index
  router.push(routerArr[index]);
}



// 右上角 推演设计
let endPoint = ref('')
let startPoint = ref('')
let btnList = ref(["其他地质构造", "水文属性设置", "地质断层设置", '瓦斯属性设置']);
let btnIndex = ref(-1);
const changeBtn = (index) => {

};

// 时间轴 数据
let timeList = ref([
  "2022-12-10",
  "2022-12-11",
  "2022-12-12",
  "2022-12-13",
  "2022-12-14",
  "2022-12-15",
  "2022-12-16",
  "2022-12-17",
  "2022-12-18",
]);
let timeIndex = ref(0);
let timeFlag = ref(true);
let nowTime = ref(0);
// 手动选择时间
const onChangeTime = (index) => {
  timeIndex.value = index;
};
// 开始 暂停
const timeChange = (value) => {
  if (value) {
    initTime();
  } else {
    timePaused();
  }
  timeFlag.value = !value;
};
let timerLine = null;

//初始化时间
const initTime = () => {
  let currTime = new Date().getTime();
  //1000 毫秒运行一次
  if (currTime - nowTime.value > 1000) {
    nowTime.value = new Date().getTime();
    timeStart();
  }
  timerLine = requestAnimationFrame(initTime);
};
//   开始
const timeStart = () => {

  timeIndex.value += 1;
  if (timeIndex.value == timeList.value.length) {
    timeIndex.value = 0;
  }
};
//   暂停
const timePaused = () => {
  cancelAnimationFrame(timerLine);
  timerLine = null;
};
//离开页面 
onBeforeUnmount(() => {
  timePaused();
  if (renderTimer) {
    clearInterval(renderTimer);
  }
  //移除排序
  if (forecastTimer) {
    clearInterval(forecastTimer);
  }
setTimeout(() => {
  removeSelectInputAction()
}, 2100);

  
});
//监听timeIndex，改变时执行
watch(timeIndex, (newVal, oldVal) => {
  if (newVal >= oldVal) {
    // var startR = oldVal / timeList.value.length
    // var endR = newVal / timeList.value.length
    // var jiange=(endR-startR)/50
    var startR = amRate + newVal / 300
    var endR = amRate + oldVal / 300
    // console.log(amRate,amRate)
    var jiange = (endR - startR) / 100
    var timer = setInterval(() => {
      amStore.setRate(startR);
      startR += jiange
      if (startR >= endR) {
        clearInterval(timer)
      }
    }, 10);
  } else {
    amStore.setRate(amRate + newVal / 300);
    setTimeout(() => {
      window.amc.flyToModel()
    }, 200);
  }
}, 200);

//不断渲染页面
var renderTimer;
setTimeout(() => {
  renderTimer = setInterval(() => {
    // viewer.scene.forceRender();
  }, 20);
}, 1000);


</script>
<style lang="less" scoped>
.deduction {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  position: relative;
  .top_box{
    position: absolute;
    left: 50%;
    z-index: 3;
    transform: translateX(-50%);
    color: rgb(167, 184, 203);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 1.8000000715255737px;
    text-align: left;
    display: flex;
    cursor: pointer;
    .top_left{
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      /* 组 75871 */
      width: 248.5px;
      height: 44px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: 20px;
      margin-right: 9px;
    }
    .top_left_d{
      background-image: url("../assets/img/tunnel/left1.png");
    }
    .top_left_active{
      background-image: url("../assets/img/tunnel/left.png");
    }
    .top_right{
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 248.5px;
      height: 44px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 20px;
    }
    .top_right_active{
      background-image: url("../assets/img/tunnel/right1.png");
    }
    .top_right_d{
      background-image: url("../assets/img/tunnel/right.png");
    }
  }
  .left {
    z-index: 1;
    width: 344px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
   
    .left_box1 {
      width: 100%;
      height: 215px;
      box-sizing: border-box;
      padding: 5px 0px 0px 14px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .left_box1_content {
        display: flex;
        width: 100%;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 8px 12px 0 0px;
        flex-wrap: wrap;

        .left_box1_li {
          display: flex;
          width: 46%;
          margin-bottom: 12px;
          justify-content: space-between;

          .left_box_li_right {
            display: flex;
            flex-direction: column;
            align-items: center;
          }

          .left_box1_num {
            font-size: 16px;
            font-family: Source Han Sans SC;
            font-weight: bold;
            color: #fdff4d;
          }

          .left_box1_text {
            font-size: 12px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #ffffff;
            margin: 5px 0 0px 0;
            text-align: center;
          }

          .left_box_li_img {
            // width: 40px;
            height: 30px;
            margin-right: 10px;
          }
        }
      }
    }

    .left_box2 {
      margin-top: 10px;
      width: 100%;
      height: 300px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 5px 0 0px 18px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .right_box1_ul {
        margin-top: 5px;
        width: 100%;
        height: 24px;
        display: flex;

        .right_box1_li {
          height: 24px;
          border: 2px solid #0d3745;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          cursor: pointer;
          padding: 4px;
        }

        .right_box1_li_active {
          color: #fff;
          border: 1px solid #00f0ff;
          background: rgba(4, 37, 66, 0.7);
        }
      }

      .left_box2_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 10px 20px 0 0;
        overflow: hidden;

        .left_bxo2_scroll {
          height: 170px;
          overflow: hidden;
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          align-items: center;
          background: linear-gradient(0deg, rgba(0, 222, 255, 0.24));
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

          .right_box1_head_text {
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: #26c1d1;
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_index {
            width: 40px;
          }

          .right_box1_title {
            width: 40px;
            font-size: 12px;
            text-align: center;
          }

          .right_box1_name {
            width: 120px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: flex;
            justify-content: center;
          }

          .right_box1_time {
            width: 100px;
            display: flex;
            justify-content: center;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_oper {
            width: 80px;
            display: flex;
            justify-content: center;

            &:hover {
              color: #00f0ff;
            }
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }

    .left_box3 {
      width: 100%;
      height: 211px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 2px 0px 10px 24px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);
      margin-top: 10px;

      .left_box3_content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        box-sizing: border-box;
        padding: 10px 0;

        .left_box3_btn {
          position: relative;
          width: 130px;
          height: 23px;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 20px 14px 0px;
          cursor: pointer;

          &:hover {
            transform: scale(1.1);
          }

          .left_box3_btn_img {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .left_box4 {
      width: 100%;
      height: 230px;
      box-sizing: border-box;
      padding: 0px 0px 10px 0px;
      display: flex;
      flex-direction: column;
      z-index: 9999;
      background: rgba(11, 24, 36,.7);
      .left_box4_content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        box-sizing: border-box;
        padding: 20px 0;
        .left_box4_btn {
          position: relative;
          width: 193px;
          height:64px;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 20px 30px 0px;
          z-index: 9999;
          cursor: pointer;
          &:hover {
            transform: scale(1.1);
          }
          .left_box4_btn_img {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
          }
          span{
            z-index: 10;
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 1.600000023841858px;
            color:#fff;
            margin-left: 20px;
          }
        }
      }
    }
  }

  .right {
    z-index: 12;
    width: 344px;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-left: 18px;
    .right_box2 {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 0px 0 10px 0px;
      display: flex;
      flex-direction: column;
      background: rgba(11, 24, 36,.7);
      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 20px 10px 0 10px;
        overflow-y: hidden;
        overflow-x: scroll;
        .right_three_scroll {
          overflow: hidden;
        }

        .right_table_box {
          width: 500px;
        }
        .right_box1_table_sticky {
          position: sticky;
          overflow-y: hidden;
          /*纵向不滚动*/
          overflow-x: auto;
          /*横向滚动*/
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          // justify-content: center;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);
          .right_box1_head_text {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(143, 199, 255);
            text-align: center;
            display: flex;
            justify-content: center;
          }
          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }
          .right_box1_index {
            width: 40px;
          }
          .right_box1_name {
            width: 140px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            justify-content: flex-start;
          }

          .right_box1_type {
            width: 80px;
          }

          .right_box1_distance {
            width: 90px;
          }

          .right_box1_time {
            width: 120px;
          }

          .right_box1_upTime {
            width: 120px;
          }

          .right_box1_oper {
            width: 100px;
            display: flex;
            justify-content: center;

            span {
              margin-left: 10px;
              width: 49px;
              height: 20px;
              font-size: 12px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;

              &:hover {
                filter: brightness(1.1);
              }
            }

            .right_box1_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }

          }
        }
        .right_box1_table_line {
          height: 30px;
        }
        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }
        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
      .right_box2_content{
        overflow-x: scroll;
        height: 90%;
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }

}

.deduction_tip {
  position: absolute;
  top: 20px;
  left: 0px;
  width: 100%;
  z-index: 99;
  display: flex;
  justify-content: center;
  color: #fff;

  li {
    margin-right: 20px;
    font-size: 16px;
    line-height: 16px;
    cursor: pointer;
    padding-bottom: 10px;
  }

  .li_active {
    border-bottom: 1px solid #00bae3;
    color: #00bae3;
  }
}
</style>