import { createRouter, createWebHistory,createWebHashHistory } from "vue-router";
import { useUserStore } from '@/store/user'


const routes = [{
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
  },
  {
    path: '/',
    component: () =>
      import('@/views/index.vue'),
    name: 'index',
    meta: {
      pagetitle: '管理平台'
    },
    redirect: '/index/Home',
    children: [
      {
        path: '/index/Home',
        name: 'home',
        component: () =>
          import('@/components/Home.vue')
      },
      {
        path: '/index/Tunnel',
        name: 'tunnel',
        component: () =>
          import('@/components/Tunnel.vue')
      },
      {
        path: '/index/Stoping',
        name: 'stoping',
        component: () =>
          import('@/components/Stoping.vue')
      },
      {
        path: '/index/Monitoring',
        name: 'monitoring',
        component: () =>
          import('@/components/Monitoring.vue')
      },
      {
        path: '/index/Deduction',
        name: 'deduction',
        redirect: '/index/Deduction/DTunnel',
        component: () =>
          import('@/components/Deduction.vue'),
          children:[{
            path: '/index/Deduction/DTunnel',
            name: 'DTunnel',
            component: () =>
              import('@/components/deduction/DTunnel.vue')
          },{
            path: '/index/Deduction/DStoping',
            name: 'DStoping',
            component: () =>
              import('@/components/deduction/DStoping.vue')
          }]
      },
      {
        path: '/index/Analyse',
        name: 'analyse',
        redirect: '/index/Analyse/dizhi',
        component: () =>
          import('@/components/Analyse.vue'),
        children: [{
            path: '/index/Analyse/dizhi',
            name: 'dizhi',
            component: () =>
              import('@/components/analyse/dizhi.vue')
          },
          {
            path: '/index/Analyse/chuliang',
            name: 'chuliang',
            component: () =>
              import('@/components/analyse/chuliang.vue')
          },
          {
            path: '/index/Analyse/fenxian',
            name: 'fenxian',
            component: () =>
              import('@/components/analyse/fenxian.vue')
          },
          {
            path: '/index/Analyse/luwang',
            name: 'luwang',
            component: () =>
              import('@/components/analyse/luwang.vue')
          },
          {
            path: '/index/Analyse/poumian',
            name: 'poumian',
            component: () =>
              import('@/components/analyse/poumian.vue')
          },
          {
            path: '/index/Analyse/lujing',
            name: 'lujing',
            component: () =>
              import('@/components/analyse/lujing.vue')
          },
        ]
      },
      {
        path: '/index/Petrol',
        name: 'petrol',
        component: () =>
          import('@/components/Petrol.vue')
      },
      {
        path: '/index/Gas',
        name: 'gas',
        component: () =>
          import('@/components/Gas.vue')
      },
    ],
  },

  // { 
  //   path:'/cart',
  //   name:'Cart',
  //   component: () =>
  //     import("../views/Cart.vue"),
  //   beforeEnter: (to, from, next) => {
  //     if( useUserStore().token ){
  //       next()
  //     }else {
  //       next('/login')
  //     }

  //   }
  // },
];
// const base = (process.env.NODE_ENV === "development" ? "/" : "/mineWeb2/"); 
const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由守卫 - 记录路由切换信息
router.beforeEach((to, from, next) => {
  console.log(`路由切换: ${from.path} -> ${to.path}`);

  // 标记从3D页面离开，但不阻塞路由
  const has3DContent = [
    '/index/Home',
    '/index/Tunnel',
    '/index/Stoping',
    '/index/Monitoring',
    '/index/Deduction/DTunnel',
    '/index/Deduction/DStoping'
  ];

  if (has3DContent.includes(from.path)) {
    console.log('从3D页面离开，标记资源清理...');
    // 设置一个全局标记，让组件知道正在切换路由
    window.isRoutingAway = true;
  }

  // 立即继续路由切换
  next();
});

// 路由切换完成后的处理
router.afterEach((to, from) => {
  console.log(`路由切换完成: ${from.path} -> ${to.path}`);

  // 清除路由切换标记
  window.isRoutingAway = false;

  // 延迟强制渲染，确保新页面正确显示
  setTimeout(() => {
    try {
      if (window.viewer && window.viewer.scene) {
        window.viewer.scene.forceRender();
      }

      // 延迟垃圾回收
      if (window.gc) {
        window.gc();
      }
    } catch (error) {
      console.warn('路由切换后处理失败:', error);
    }
  }, 100);
});

export default router;