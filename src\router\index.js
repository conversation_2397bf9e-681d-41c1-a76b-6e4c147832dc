import { createRouter, createWebHistory,createWebHashHistory } from "vue-router";
import { useUserStore } from '@/store/user'


const routes = [{
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
  },
  {
    path: '/',
    component: () =>
      import('@/views/index.vue'),
    name: 'index',
    meta: {
      pagetitle: '管理平台'
    },
    redirect: '/index/Home',
    children: [
      {
        path: '/index/Home',
        name: 'home',
        component: () =>
          import('@/components/Home.vue')
      },
      {
        path: '/index/Tunnel',
        name: 'tunnel',
        component: () =>
          import('@/components/Tunnel.vue')
      },
      {
        path: '/index/Stoping',
        name: 'stoping',
        component: () =>
          import('@/components/Stoping.vue')
      },
      {
        path: '/index/Monitoring',
        name: 'monitoring',
        component: () =>
          import('@/components/Monitoring.vue')
      },
      {
        path: '/index/Deduction',
        name: 'deduction',
        redirect: '/index/Deduction/DTunnel',
        component: () =>
          import('@/components/Deduction.vue'),
          children:[{
            path: '/index/Deduction/DTunnel',
            name: 'DTunnel',
            component: () =>
              import('@/components/deduction/DTunnel.vue')
          },{
            path: '/index/Deduction/DStoping',
            name: 'DStoping',
            component: () =>
              import('@/components/deduction/DStoping.vue')
          }]
      },
      {
        path: '/index/Analyse',
        name: 'analyse',
        redirect: '/index/Analyse/dizhi',
        component: () =>
          import('@/components/Analyse.vue'),
        children: [{
            path: '/index/Analyse/dizhi',
            name: 'dizhi',
            component: () =>
              import('@/components/analyse/dizhi.vue')
          },
          {
            path: '/index/Analyse/chuliang',
            name: 'chuliang',
            component: () =>
              import('@/components/analyse/chuliang.vue')
          },
          {
            path: '/index/Analyse/fenxian',
            name: 'fenxian',
            component: () =>
              import('@/components/analyse/fenxian.vue')
          },
          {
            path: '/index/Analyse/luwang',
            name: 'luwang',
            component: () =>
              import('@/components/analyse/luwang.vue')
          },
          {
            path: '/index/Analyse/poumian',
            name: 'poumian',
            component: () =>
              import('@/components/analyse/poumian.vue')
          },
          {
            path: '/index/Analyse/lujing',
            name: 'lujing',
            component: () =>
              import('@/components/analyse/lujing.vue')
          },
        ]
      },
      {
        path: '/index/Petrol',
        name: 'petrol',
        component: () =>
          import('@/components/Petrol.vue')
      },
      {
        path: '/index/Gas',
        name: 'gas',
        component: () =>
          import('@/components/Gas.vue')
      },
    ],
  },

  // { 
  //   path:'/cart',
  //   name:'Cart',
  //   component: () =>
  //     import("../views/Cart.vue"),
  //   beforeEnter: (to, from, next) => {
  //     if( useUserStore().token ){
  //       next()
  //     }else {
  //       next('/login')
  //     }

  //   }
  // },
];
// const base = (process.env.NODE_ENV === "development" ? "/" : "/mineWeb2/"); 
const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由守卫 - 确保页面切换时资源正确清理
router.beforeEach(async (to, from, next) => {
  console.log(`路由切换: ${from.path} -> ${to.path}`);

  try {
    // 如果是从有3D内容的页面离开，给一些时间让组件清理资源
    const has3DContent = [
      '/index/Home',
      '/index/Tunnel',
      '/index/Stoping',
      '/index/Monitoring',
      '/index/Deduction/DTunnel',
      '/index/Deduction/DStoping'
    ];

    if (has3DContent.includes(from.path)) {
      console.log('从3D页面离开，等待资源清理...');

      // 给组件一些时间来清理资源
      await new Promise(resolve => setTimeout(resolve, 100));

      // 强制垃圾回收（如果可用）
      if (window.gc) {
        window.gc();
      }
    }

    next();
  } catch (error) {
    console.error('路由守卫执行出错:', error);
    next();
  }
});

// 路由切换完成后的处理
router.afterEach((to, from) => {
  console.log(`路由切换完成: ${from.path} -> ${to.path}`);

  // 强制进行一次渲染，确保新页面正确显示
  if (window.viewer && window.viewer.scene) {
    setTimeout(() => {
      try {
        window.viewer.scene.forceRender();
      } catch (error) {
        console.warn('强制渲染失败:', error);
      }
    }, 200);
  }
});

export default router;