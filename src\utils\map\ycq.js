/**
 * 获取异常区数据
 */
import {
  ref,
  toRaw
} from "vue";
import $ from "jquery";
import {
  storeToRefs
} from "pinia";
import {
  useLoadStore
} from "@/store/load";
const loadStore = useLoadStore();
const {
  loadData
} = storeToRefs(loadStore);
class YCQ {
  constructor() {
    this.load()
  }

  //加载异常区数据
  load() {
    $.ajax({
      // url: "/TMJJ/GEOJSON/newYCQ.geojson",
      url: "/TMJJ/GEOJSON/ycq.geojson",
      // url: "http://82.157.9.218:20005/app-api/geo/function-source-file/5/get/1673353554620_1612787818016215040.geojson",
      type: "GET",
      dataType: "json",
      success: (data) => {
        // var features = JSON.parse(data).features
        console.log(features)
        var features = data.features;
        for (var feature of features) {
          var layer = feature.properties.Layer;
          var index = this.getTitleIdx(this.anomalousArea.value, layer);
          if (index == -1) {
            this.anomalousArea.value.push({
              title: layer,
              type:"异常区",
              features: [],
              show: false,
              index: -1,
              centre: 0,
              distance: 0,//空间距离
              distance2: 0,//垂直距离
              distance3: 0,//水平距离
              collection: new (window.Cesium || Cesium).PrimitiveCollection()
            });
            index = this.anomalousArea.value.length - 1;
            this.anomalousArea.value[index].index = index;
          }

          var positions = [];
          var coordinates = feature.geometry.coordinates[0];
          var v1 = 116.98334282318025 - 116.98321248018293
          var v2 = 34.86474115656025 - 34.864841508374354
          for (var coordinate of coordinates) {
            var _coordinate = [
              coordinate[0] + v1 * 0.85,
              coordinate[1] + v2 * 0.85,
              coordinate[2] + 1900 + 12,
            ];
            positions.push(Cesium.Cartesian3.fromDegrees(..._coordinate));
          }

          var color = Cesium.Color.RED;
          switch (feature.properties.Color) {
            case 1:
              color = Cesium.Color.RED;
              break;
            case 2:
              color = Cesium.Color.YELLOW;
              break;
            case 3:
              color = Cesium.Color.BLUE;
              break;
            case 4:
              color = Cesium.Color.WHITE;
              break;
            case 5:
              color = Cesium.Color.PURPLE;
              break;
            case 6:
              color = Cesium.Color.PINK;
              break;
          }
          var primitive = new Cesium.Primitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.PolylineGeometry({
                positions: positions,
                width: 3.0,
                vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
              }),
              attributes: {
                color: Cesium.ColorGeometryInstanceAttribute.fromColor(color),
              },
            }),
            appearance: new Cesium.PolylineColorAppearance({
              translucent: false, //是否透明
            }),
          });

          this.anomalousArea.value[index].features.push(primitive);
        }
        for (var item of this.anomalousArea.value) {
          for (var feature of item.features) {
            item.collection.add(feature)
          }
          item.collection.show = false
          viewer.scene.primitives.add(toRaw(item.collection))
        }
        this.centrePoint()
        //加载完成,提示loadStore
        this.isLoadEnd = true
        loadData.value['ycq'] = true
        loadStore.setIsLoadEnd()
      },
      error: () => {
        
          loadData.value['ycq'] = true
          console.log("异常区 加载失败！")
          loadStore.setIsLoadEnd()
        
      }
    });
    // setTimeout(() => {
    //   if (loadData.value['ycq'] == false) {
    //     loadData.value['ycq'] = true
    //     console.log("异常区 加载失败！")
    //     loadStore.setIsLoadEnd()
    //   }
    // }, 5000);


  }

  //计算中心点
  centrePoint() {
    const timer = () => {
      for (var i = 0; i < this.anomalousArea.value.length; i++) {
        var pointList = []
        for (var feature of toRaw(this.anomalousArea.value[i].collection._primitives)) {
          if (feature._boundingSpheres.length == 0) {
            setTimeout(() => {
              timer()
            }, 200)
            return
          } else {
            pointList.push(feature._boundingSpheres[0].center)
          }

        }
        var [x, y, z] = [0, 0, 0]
        for (var point of pointList) {
          x += point.x
          y += point.y
          z += point.z
        }
        [x, y, z] = [x / pointList.length, y / pointList.length, z / pointList.length]
        this.anomalousArea.value[i].centre = new Cesium.Cartesian3(x, y, z)
      }
    }
    timer()
  }

  //不断更新异常区和小车之间的距离
  updataDistanceByAm(am) {
    // if(this.timer!=undefined){
    //   clearInterval(this.timer)
    // }

    this.timer = setInterval(() => {

      var amPosition = am.getCurrentPosition()
      for (var i = 0; i < this.anomalousArea.value.length; i++) {
        var ycqPosition = this.anomalousArea.value[i].centre
        if (ycqPosition != 0) {
          var distance = Cesium.Cartesian3.distance(toRaw(ycqPosition), amPosition)
          this.anomalousArea.value[i].distance = distance.toFixed(2)

          var distance2 = Geowin3D.Cartographic.fromCartesian(amPosition).height - Geowin3D.Cartographic.fromCartesian(toRaw(ycqPosition)).height

          this.anomalousArea.value[i].distance2 = distance2.toFixed(2)

          var distance3 = Math.sqrt(distance ** 2 - distance2 ** 2)
          this.anomalousArea.value[i].distance3 = distance3.toFixed(2)
        }
      }
    }, 200);
  }


  //异常区显隐
  show(index, show) {
    // if(this.anomalousArea.value[index].hasOwnProperty('show')){
    this.anomalousArea.value[index].show = show;
    toRaw(this.anomalousArea.value[index].collection).show = show;
    // }else{
    //   console.log("异常区数据无效")
    // }

  }

  getIndexByLabel(label) {
    for (var i = 0; i < this.anomalousArea.value.length; i++) {
      if (label == this.anomalousArea.value[i].title) {
        return i
      }
    }
    return -1
  }

  //移除异常区距离监控内容
  removeUpdataDistance() {
    clearInterval(this.timer)
    for (var i = 0; i < this.anomalousArea.value.length; i++) {
      this.anomalousArea.value[i].distance = 0
    }
    

  }

  getTitleIdx = (list, property) => {
    for (var i = 0; i < list.length; i++) {
      if (list[i].title == property) {
        return i;
      }
    }
    return -1;
  };

  getData() {
    // console.log(this.anomalousArea)
    return this.anomalousArea;
  }

  getDataByList(anomalousArealist) {
    var timer = () => {
      console.log('this.isLoadEnd', this.isLoadEnd)
      if (this.isLoadEnd) {
        for (var ycq of this.anomalousArea.value) {

          anomalousArealist.value.push(ycq)
        }
        console.log('anomalousArealist', anomalousArealist)
      } else {
        setTimeout(() => {
          timer()
        }, 200);

      }
    }
    timer()
  }

  getPrimitives(){
    var primitivesList=[]
    for (var ycq of this.anomalousArea.value) {
      primitivesList.push(...toRaw(ycq.collection)._primitives)
      
    }
    return primitivesList
  }


  isLoadEnd = false
  anomalousArea = ref([])
}

export default YCQ;