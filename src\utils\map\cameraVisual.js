/*
 * @Description: 
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2022-11-25 17:36:36
 * @LastEditors: wbw
 * @LastEditTime: 2022-12-01 17:38:28
 */
class CameraVisual {

    /**
     * @description: 摄像机飞到该页面的默认视角
     * @return {*}
     * @author: wbw
     */
    flytoDefault() {
        var location = window.location.hash
        if (!this.cameraInfo.hasOwnProperty(location)) {
            location = 'default'
            this.cameraInfo[location]()
        } else if (location == '#/index/Monitoring') {
            this.cameraInfo[location]()
        } else {
            this.cameraInfo[location].flyToModel()
        }
    }

    /**
     * @description: 通过am设置本页面的模型视角
     * @param {*} location 当前页面的window.location.hash路径
     * @param {*} am am对象
     * @return {*}
     * @author: wbw
     */
    setVisualByAm(location, am) {
        this.cameraInfo[location] = am
    }

    cameraInfo = {
        'default': () => {
            viewer.camera.flyTo({
                duration: 1,
                destination: new Cesium.Cartesian3.fromDegrees(116.88, 34.94, 5500),
                orientation: {
                    heading: Cesium.Math.toRadians(153),
                    pitch: Cesium.Math.toRadians(-24),
                    roll: 0,
                },
            }, 3000)
        },
        '#/index/Monitoring': () => {
            viewer.camera.flyTo({
                duration: 1,
                destination: new Cesium.Cartesian3.fromDegrees(116.98657, 34.867629, -571.61 + 1900),
                orientation: {
                    heading: Cesium.Math.toRadians(232),
                    pitch: Cesium.Math.toRadians(-29.52),
                    roll: 0,
                },
            }, 3000)
        }
    }
}
export default CameraVisual;