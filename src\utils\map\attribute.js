/*
 * @Description: 属性查询
 * @Version: 2.0
 * @Autor: wbw
 * @Date: 2022-11-11 09:16:38
 * @LastEditors: wbw
 * @LastEditTime: 2022-12-01 17:16:01
 */
import $ from "jquery";

class Attribute {
    /**
     * @description: 初始化Attribute
     * @param {Cesium.viewer} _viewer Cesium的视图对象
     * @author: wbw
     */
    constructor(_viewer) {
        this.viewer = _viewer || window.viewer;

        // 安全地创建handler
        const currentViewer = this.viewer;
        if (!currentViewer || !currentViewer.scene) {
            console.warn('Viewer未初始化，无法创建Attribute');
            return;
        }

        this.handler = new window.Cesium.ScreenSpaceEventHandler(currentViewer.scene.canvas);
        this.selectedEntity = new window.Cesium.Entity();
        var fragmentShaderSource =
            "uniform sampler2D colorTexture;\n" +
            "varying vec2 v_textureCoordinates;\n" +
            "uniform vec4 highlight;\n" +
            "void main() {\n" +
            "    vec4 color = texture2D(colorTexture, v_textureCoordinates);\n" +
            "    if (czm_selected()) {\n" +
            "        vec3 highlighted = highlight.a * highlight.rgb + (1.0 - highlight.a) * color.rgb;\n" +
            "        gl_FragColor = vec4(highlighted, 1.0);\n" +
            "    } else { \n" +
            "        gl_FragColor = color;\n" +
            "    }\n" +
            "}\n";
        // 安全地获取viewer
        const currentViewer = this.viewer || window.viewer;
        if (!currentViewer || !currentViewer.scene || !currentViewer.scene.postProcessStages) {
            console.warn('Viewer或PostProcessStages未初始化，无法添加属性高亮');
            return;
        }

        this.stage = currentViewer.scene.postProcessStages.add(
            new window.Cesium.PostProcessStage({
                fragmentShader: fragmentShaderSource,
                uniforms: {
                    highlight: function () {
                        return new window.Cesium.Color(1.0, 1.0, 0.0, 1.0);
                    },
                },
            })
        );
        this.stage.selected = [];
        this.selectedModel = {
            feature: undefined,
        };
        this.pickposition=undefined
    }

    /**
     * @description: 开启属性查询
     * @author: wbw
     */
    start() {
        if (!this.handler) {
            console.warn('Handler未初始化，无法启动属性查询');
            return;
        }

        const currentViewer = this.viewer || window.viewer;
        if (!currentViewer) {
            console.warn('Viewer未初始化，无法启动属性查询');
            return;
        }

        this.handler.removeInputAction(window.Cesium.ScreenSpaceEventType.LEFT_CLICK);
        this.handler.setInputAction((movement) => {
            var pickedObject = currentViewer.scene.pick(movement.position);
            if (window.Cesium.defined(pickedObject)) {
                //选中要素
                this.stage.selected = [pickedObject];
                this.selectFeature = pickedObject;
                this.selectedModel.feature = pickedObject;
                console.log(pickedObject)
            } else {
                this.stage.selected = [];
                this.selectedModel.feature = undefined;
            }
            this._setAttribute()
            this.viewer.scene.forceRender();

            //获取位置信息
            let cartesian = currentViewer.scene.pickPosition(movement.position);
            if (cartesian) {
                let cartographic = window.Cesium.Cartographic.fromCartesian(cartesian);
                let lng = window.Cesium.Math.toDegrees(cartographic.longitude); // 经度
                let lat = window.Cesium.Math.toDegrees(cartographic.latitude); // 纬度
                let alt = cartographic.height; // 高度
                var result = lng + ',' + lat + ',' + alt
                console.log(result)
                this.pickposition = result
            }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);
        // 增加拖拽
        var moveEl = document.getElementsByClassName('cesium-infoBox')[0];
        const mouseDown = (e) => {
            let X = e.clientX - moveEl.offsetLeft;
            let Y = e.clientY - moveEl.offsetTop;
            const move = (e) => {
                moveEl.style.left = e.clientX - X + "px";
                moveEl.style.top = e.clientY - Y + "px";
            };
            document.addEventListener("mousemove", move);
            document.addEventListener("mouseup", () => {
                document.removeEventListener("mousemove", move);
            });
        };
        moveEl.addEventListener("mousedown", mouseDown);
    }

    /**
     * @description: 将点击获取的要素的属性设置给展示框
     * @author: wbw
     */
    _setAttribute() {
        if (this.selectFeature != undefined) {
            this.pickModel = this.selectFeature
            console.log(this.selectFeature)
            this.items = {};
            this.items["编号"] = this.pickModel.getProperty("batchId");
            this.items["名称"] = this.pickModel.getProperty("extdata");
            this.items["所在图层"] = this.pickModel.tileset.dataLabel;
            console.log(this.pickModel.tileset.dataLabe)
            switch (this.pickModel.tileset.dataLabel) {
                case '断层模型':
                    $.ajax({
                        url: "/attribute/duanceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            var dataid = this.pickModel.getProperty("batchId")
                            if (dataid < data.length) {
                                this.items["名称"] = data[dataid]['断层编号']
                                this.items["断层性质"] = data[dataid]['性质']
                                this.items["倾向"] = data[dataid]['倾向']
                                this.items["倾角（°）"] = data[dataid]['倾角（°）']
                            }
                            console.log(this.items["名称"]+','+this.pickposition)
                            this.setContent()
                        }
                    })
                    break
                case '7号煤层':
                    $.ajax({
                        url: "/attribute/meiceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层位"] = data[0]['层位']
                            this.items["平均煤厚"] = data[0]['平均煤厚']
                            this.setContent()
                        }
                    })
                    break
                case '8号煤层':
                    $.ajax({
                        url: "/attribute/meiceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层位"] = data[1]['层位']
                            this.items["平均煤厚"] = data[1]['平均煤厚']
                            this.setContent()
                        }
                    })
                    break
                case '第四系':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[0]['层厚信息']
                            this.items["主要岩性"] = data[0]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                case '上侏罗～下白垩统':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[1]['层厚信息']
                            this.items["主要岩性"] = data[1]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                case '二叠系地层':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[2]['层厚信息']
                            this.items["主要岩性"] = data[2]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                case '石炭系地层':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[3]['层厚信息']
                            this.items["主要岩性"] = data[3]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                case '回采工作面':
                    this.items["名称"] = '7431工作面';
                    this.setContent()
                    break
                default:
                    this.setContent()
                    break

            }


        }


    }
    setContent() {
        this.contentHtml =
            '<table class="infoBoxp cesium-infoBox-defaultTable"><tbody>';
        for (let pro in this.items) {
            this.contentHtml +=
                "<tr><th>" +
                `${pro}` +
                "</th>" +
                "<td>" +
                `${this.items[pro]}` +
                "</td>" +
                "</tr>";
        }
        this.contentHtml += "</tbody></table>";
        var featureName = this.pickModel.tileset.dataLabel;
        this.selectedEntity.name = featureName;
        this.selectedEntity.description = this.contentHtml;
        this.viewer.selectedEntity = this.selectedEntity;

        // 增加拖拽
        var moveEl = document.getElementsByClassName('cesium-infoBox')[0];
        const mouseDown = (e) => {
            let X = e.clientX - moveEl.offsetLeft;
            let Y = e.clientY - moveEl.offsetTop;
            const move = (e) => {
                moveEl.style.left = e.clientX - X + "px";
                moveEl.style.top = e.clientY - Y + "px";
            };
            document.addEventListener("mousemove", move);
            document.addEventListener("mouseup", () => {
                document.removeEventListener("mousemove", move);
            });
        };
        moveEl.addEventListener("mousedown", mouseDown);

    }
    _setAttribute2() {
        if (this.selectedModel.feature != undefined) {
            var pickModel = this.selectedModel.feature
            var items = {};
            items["batchId"] = pickModel.getProperty("batchId");
            items["extdata"] = pickModel.getProperty("extdata");
            items["layerType"] = pickModel.getProperty("layerType");
            items["uuid"] = pickModel.getProperty("uuid");
            items["webid"] = pickModel.getProperty("webid");
            var contentHtml =
                '<table class="infoBoxp cesium-infoBox-defaultTable"><tbody>';
            for (let pro in items) {
                contentHtml +=
                    "<tr><th>" +
                    `${pro}` +
                    "</th>" +
                    "<td>" +
                    `${items[pro]}` +
                    "</td>" +
                    "</tr>";
            }
            contentHtml += "</tbody></table>";
        }

        var featureName = pickModel.getProperty("name");
        this.selectedEntity.name = featureName;
        this.selectedEntity.description = contentHtml;
        this.viewer.selectedEntity = this.selectedEntity;
    }

    /**
     * @description: 关闭属性查询
     * @author: wbw
     */
    cancel() {
        this.selectFeature = undefined
        this.stage.selected = [];
        this.selectedEntity.description = null
        this.viewer.selectedEntity = null;
        this.selectedEntity.name = null
        this.selectedModel.feature = undefined
        if (this.handler) {
            this.handler.removeInputAction(window.Cesium.ScreenSpaceEventType.LEFT_CLICK);
        }
    }

}

export default Attribute