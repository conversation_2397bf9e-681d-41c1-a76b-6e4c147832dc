 import yc from '@/assets/img/geology/yc.png'
 import yc2 from '@/assets/img/geology/yc2.png'
 import {
     getXYZMap
 } from "@/api/home/<USER>";
 class Earth2D {
     layerManager = {}
     entities = []

     constructor(_container) {
         this.initEarth(_container);
     }

     /**
      * 初始化加载地球
      * <AUTHOR>
      */
     initEarth(_container) {
         window.geoapp2d = this.geoapp2d = new Geowin3DAPP(
             _container, {
                 sceneMode: Cesium.SceneMode.SCENE2D,
                 shouldAnimate: true,
                 infoBox: true,
                 orderIndependentTranslucency: false,
                 contextOptions: {
                     webgl: {
                         alpha: true,
                     },
                 },
             }, {}
         );

         window.viewer2d = this.viewer2d = this.geoapp2d.getViewer();
         this.viewer2d.terrainProvider = new Geowin3D.GeowinTerrainProvider({
             tilename: "dem"
         });
         this.viewer2d.showLogo = false;
         this.camera = this.viewer2d.scene.camera;

         this.initEarthConfig();
     }

     /**
      * 初始化模型管理
      * @returns 
      */
     initModelManagerTools() {
         this.modelManager = new Geowin3D.GwModel.GwModelManager(this.viewer2d);
         return this.modelManager;
     }

     getViewer() {
         return this.viewer2d;
     }

     getGeoAPP() {
         return this.geoapp2d;
     }
     getEntities() {
         return this.entities;
     }
     /**
      * 初始化球配置
      * <AUTHOR>
      */
     initEarthConfig() {
         // this.viewer2d.clock.shouldAnimate = true;
         this.viewer2d.scene.globe.depthTestAgainstTerrain = true;
         this.viewer2d.scene.backgroundColor = new Cesium.Color(0.0, 0.0, 0.0, 0.0);
         this.viewer2d.scene.skyBox.show = false;

         this.viewer2d.scene.globe.enableLighting = false; //关闭光照
         this.viewer2d.shadows = false; //关闭阴影
         this.viewer2d.scene.light = new Cesium.DirectionalLight({
             direction: this.viewer2d.scene.camera.directionWC,
         });

         var xyzMapUrl = getXYZMap()
         let l = new Cesium.UrlTemplateImageryProvider({
             url: xyzMapUrl
             // url:'http://172.19.7.226:48080/app-api/geo/function-server-wmts/get-code/1609961252339838976/{z}/{x}/{y}.png'
         });
         this.viewer2d.imageryLayers.addImageryProvider(l);
         //   var position=new Cesium.Cartesian3(116.944505909004,
         //             34.8417361534315,
         //             0)
         //   this.viewer2d.scene.camera.lookAt(
         //     position,
         //     new Cesium.Cartesian3(0.0, 0.0, 1000)
         // )

         this.viewer2d.imageryLayers.get(0).show = false; //不显示底图
         this.viewer2d.scene.globe.baseColor = Cesium.Color.WHITE; //设置地球颜色
         this.viewer2d.camera.setView({
             destination: Cesium.Cartesian3.fromDegrees(116.944505909004,
                 34.8417361534315,
                 1000), // 设置位置
             orientation: {
                 heading: Cesium.Math.toRadians(20.0), // 方向
                 pitch: Cesium.Math.toRadians(-90.0), // 倾斜角度
                 roll: 0
             }
         })
         // this.addSphereGeometry(this.viewer2d)
         // console.log(this.viewer2d)
     }

     // addSphereGeometry(viewer2d) {

     //     let sphere = new Cesium.SphereGeometry({
     //       radius : 30.0,
     //       vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
     //     });
     //     let geometry = Cesium.SphereGeometry.createGeometry(sphere);


     //     let polygonInstance1 = new Cesium.GeometryInstance({
     //       geometry: geometry,
     //       modelMatrix : Cesium.Matrix4.multiplyByTranslation(Cesium.Transforms.eastNorthUpToFixedFrame(
     //         Cesium.Cartesian3.fromDegrees(116.944505909004,
     //             34.8417361534315)), new Cesium.Cartesian3(0.0, 0.0, 100.0), new Cesium.Matrix4()),
     //       attributes: {
     //         color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED)
     //       },
     //       id: 'polygonInstance1'
     //     });


     //     let primitive = new Cesium.Primitive({
     //       geometryInstances: [polygonInstance1],
     //       asynchronous: false,//是否采用多线程
     //       appearance: new Cesium.PerInstanceColorAppearance({
     //         translucent: false,//半透明
     //         flat: true,//当 true 时，片段着色器中将使用平面阴影，这意味着不考虑光照
     //       })
     //     });

     //     viewer2d.scene.primitives.add(primitive);
     //   }


     /**
      * 添加图层，支持3dtiles,wmts url,gltf等，后续所有图层类型均通过该方法进行扩展
      * tileset:3dtileset配置别名
      * tileset_url:3dtileset的服务地址
      * geowintms:二维瓦片配置别名
      * geowintms_url:二维瓦片的url服务地址
      * heatmap:热力图
      * @params {*}
      * {
      *  id:layerid,
      *  type:geowindem/tileset/geowintms,heatmap,
      *  position:
      *  options:
      *  bounds:
      *  tileName:
      * }
      *
      * <AUTHOR>
      */
     loadLayer(treeNode) {
         var id = treeNode.id;
         var layer = null;
         switch (treeNode.type) {
             //暂且未启用
             case "tileset":
                 if (this.viewer2d.camera._mode == 2) return;
                 layer = this.geoapp2d.load3DTileset({
                     tileName: treeNode.layername,
                     position: treeNode.position,
                     orientation: treeNode.orientation,
                 });
                 this.geoapp2d.show(layer);
                 this.entities.push(layer);
                 break;
             case "tileset_url":
                 if (this.viewer2d.camera._mode == 2) return;
                 layer = this.geoapp2d.load3DTileset({
                     url: treeNode.url,
                     position: treeNode.position,
                     orientation: treeNode.orientation,
                 });
                 this.geoapp2d.show(layer);
                 this.entities.push(layer);
                 break;
             case "geowintms": //暂且未启用
                 layer = this.geoapp2d.addImageLayer({
                     url: treeNode.url,
                     bounds: treeNode["bounds"],
                 });

                 if (treeNode.options != null) {
                     for (var x in treeNode.options) {
                         layer[x] = treeNode.options[x];
                     }
                 }
                 break;

             case "geowindem": //暂且未启用
                 layer = this.geoapp2d.addDefaultTerrain(treeNode.layername);
                 break;
             case "dem":
                 this.viewer2d.terrainProvider = new Geowin3D.Geowin3DTerrainProvider({
                     url: treeNode.url,
                 });
                 break;

             case "heatmap": //暂且未启用
                 layer = this.geoapp2d.addHeatMapLayer(
                     treeNode.layername,
                     treeNode.options,
                     treeNode
                 );
                 break;
             default:
                 if (this.viewer2d.camera._mode == 2) return;
                 layer = this.geoapp2d.loadEntity(
                     treeNode.layername,
                     treeNode.type,
                     treeNode.options,
                     treeNode
                 );
                 break;
         }

         this.layerManager[id] = {
             treeNode: treeNode,
             layer: layer,
             features: []
         };

         if (treeNode.type == "tileset_url" || treeNode.type == 'tileset') {
             layer.tileLoad.addEventListener((tile) => {
                 let content = tile.content;
                 let featuresLength = content.featuresLength;
                 for (var i = 0; i < featuresLength; i++) {
                     this.layerManager[id].features.push(content.getFeature(i))
                 }
             });
         }

         // if (treeNode.setStyle != null)
         //     window[treeNode.setStyle].call(this, layer, treeNode);

         if (treeNode.position != null)
             this.geoapp2d.flyToByPosition(treeNode.position);
     }

     /**
      * 移除图层
      * @param {*} treeNode
      * @returns
      */
     removeLayer(treeNode) {
         var id = treeNode.id;
         if (this.layerManager[id] == null) return;
         if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
             this.geoapp2d.hide(this.layerManager[id].layer);
             return;
         }
         if (treeNode.type == "geowindem") {
             this.geoapp2d.removeTerrain();
             return;
         }
         if (this.layerManager[id].layer)
             this.geoapp2d.removeEntity(this.layerManager[id].layer);
         delete this.layerManager[id];
     }





     /**
      * 移除图层
      * @param {*} treeNode 
      * @returns 
      */
     removeLayer(treeNode) {
         var id = treeNode.id;
         if (this.layerManager[id] == null) return;
         if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
             this.geoapp2d.hide(this.layerManager[id].layer);
             return;
         }
         if (treeNode.type == "geowindem") {
             this.geoapp2d.removeTerrain();
             return;
         }
         if (this.layerManager[id].layer)
             this.geoapp2d.removeEntity(this.layerManager[id].layer);
         delete this.layerManager[id];
     }


     setCamera(lng, lat, height) {
         // console.log(lng, lat, height)
         this.viewer2d.camera.setView({
             destination: Cesium.Cartesian3.fromDegrees(lng,
                 lat,
                 height), // 设置位置
             orientation: {
                 heading: Cesium.Math.toRadians(20.0), // 方向
                 pitch: Cesium.Math.toRadians(-90.0), // 倾斜角度
                 roll: 0
             }
         })
     }

 }
 export default Earth2D;