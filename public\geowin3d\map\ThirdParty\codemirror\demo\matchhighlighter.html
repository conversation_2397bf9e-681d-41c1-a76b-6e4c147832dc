<!doctype html>

<title>CodeMirror: Match Highlighter Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/scroll/annotatescrollbar.js"></script>
<script src="../addon/search/matchesonscrollbar.js"></script>
<script src="../addon/search/searchcursor.js"></script>
<script src="../addon/search/match-highlighter.js"></script>
<style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      .CodeMirror-focused .cm-matchhighlight {
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAACCAYAAABytg0kAAAAFklEQVQI12NgYGBgkKzc8x9CMDAwAAAmhwSbidEoSQAAAABJRU5ErkJggg==);
        background-position: bottom;
        background-repeat: repeat-x;
      }
      .cm-matchhighlight {background-color: lightgreen}
      .CodeMirror-selection-highlight-scrollbar {background-color: green}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Match Highlighter</a>
  </ul>
</div>

<article>
<h2>Match Highlighter Demo</h2>
<form><textarea id="code" name="code">Select this text: hardtospot
  And everywhere else in your code where hardtospot appears will
automatically illuminate.  Give it a try!  No more hard to spot
variables - stay in context of your code all the time.

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut pharetra
interdum dui eu pulvinar. Mauris maximus ligula venenatis tempus
interdum. Cras hendrerit, ipsum sed ultrices pharetra, ligula diam
porttitor lacus, ac tempor eros est a massa. Nam orci elit, vulputate
in tristique quis, consectetur vitae metus. Pellentesque et enim
elementum, lobortis augue in, lacinia sapien. Morbi eu nunc semper,
sagittis felis a, pellentesque mauris. Lorem ipsum dolor sit amet,
consectetur adipiscing elit. Aenean quis diam turpis.

Fusce lobortis nisl quis aliquet euismod. Aenean vitae nulla non ipsum
efficitur scelerisque. Curabitur auctor, lorem non rhoncus porttitor,
augue ligula lacinia dolor, et vehicula magna lorem imperdiet velit.
Fusce risus sem, hardtospot commodo eleifend hendrerit vitae, mollis
quis risus. Cras tincidunt, justo vitae hendrerit venenatis, urna
dolor placerat tortor, eu lobortis lectus dolor in ligula. Nullam non
erat non nisl vulputate ultrices sit amet vestibulum dolor. Quisque in
tortor porta, pellentesque odio nec, malesuada nibh.

In a dui feugiat, ullamcorper urna in, accumsan magna. Donec egestas
sem nec eros rhoncus, vel gravida purus ornare. Nulla orci mauris,
porta nec pharetra sed, ornare et lorem. Donec luctus turpis nunc,
eget dictum felis mollis et.  Sed sodales hardtospot nunc vitae leo
rhoncus imperdiet. Donec elementum malesuada velit quis placerat.
Proin accumsan lorem id nisi volutpat ullamcorper. Vivamus laoreet
dolor ac sem malesuada, ac scelerisque ex efficitur. Aliquam tempus
libero velit, vel tristique augue vulputate nec.

Mauris ultrices leo felis, sit amet congue augue aliquam condimentum.
Vivamus purus leo, mattis vitae dignissim vel, ultricies ac ex. Mauris
eu dolor eu purus ultricies ultrices. Sed euismod feugiat ex et
mattis. Morbi cursus laoreet pharetra. Donec eu dolor sodales,
ultricies nisi et, malesuada urna.  Praesent sit amet fringilla felis.
Nam rhoncus, est blandit auctor auctor, lorem ipsum laoreet ipsum,
quis sodales libero odio in lorem. Phasellus odio dolor, elementum
sagittis nibh non, fermentum semper libero. Mauris hendrerit
hardtospot lectus sit amet commodo eleifend. Morbi pulvinar eget nisl
at eleifend. Fusce eget porta erat, vitae lobortis libero.

Phasellus sit amet massa in massa pharetra malesuada. Vestibulum at
quam vel libero aliquam volutpat at ut dui. Praesent scelerisque vel
mauris sit amet vehicula. Phasellus at mi nec ligula cursus interdum
sit amet non quam. Aliquam tempus sollicitudin euismod. Nulla euismod
mollis enim tincidunt placerat.  Proin ac scelerisque enim, quis
sollicitudin metus. Pellentesque congue nec sapien ut rhoncus. Sed
eget ornare diam, ut consectetur ante. Aenean eleifend mauris quis
ornare accumsan. In hac habitasse hardtospot platea dictumst.

</textarea></form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  lineNumbers: true,
  // To highlight on scrollbars as well, pass annotateScrollbar in options
  // as below.
  highlightSelectionMatches: {showToken: /\w/, annotateScrollbar: true}
});
</script>

    <p>Search and highlight occurences of the selected text.</p>

  </article>
