// user模块，类似于 vuex 中的moduel
import { defineStore } from 'pinia'
export const useViwerStore = defineStore({
  // 模块ID
  id: 'viewerStore',
  state: () => {
    return {
      viewer:null,
      v2:null
    }
  },
  actions: {
    setViewer(viewer) {
      this.viewer = viewer;
      this.v2 = viewer;
      //console.log("this.viewer",this.viewer)
    },
    exeFunc(func,para=[]){
        var s=0
        var timer = setInterval(() => {     
            if(this.viewer!=null){
                try{
                    func(...para)
                    clearInterval(timer);
                }catch(e){

                }    
            } 
            s++;
            if (s > 29 ) {
                clearInterval(timer);
            }
            //console.log(s)
        }, 200); //每0.2秒轮询一次
    },
  },
}
)