import threeImg from "@/assets/img/common/three.png";
import {
    useToolsStore
} from "@/store/tools";
import YCQ from "@/utils/map/ycq";
import DC from "@/utils/map/dc";
import {
    getLayerdirectory
} from "@/api/home/<USER>";
import MonitoringPoint from "@/utils/map/monitoringPoint";
import {
    storeToRefs
} from "pinia";
import {
    useLoadStore
} from "@/store/load";
import {
    defineComponent
} from "vue";


const loadStore = useLoadStore();
const {
    loadData,
    isLoadEnd
} = storeToRefs(loadStore);
class LayerManager {
    constructor() {
        this.init();
    }

    init() {
        getLayerdirectory().then((res)=>res.data).then((res) => {
            this.loadLabels = [];
            this.cloneData(res);
            
            this.loadYCQ(false);
            this.loadModels(false);
            this.loadMonitoringPoint(false);

            this.loadYCQ(true);
            this.loadModels(true);
            this.loadMonitoringPoint(true);

            this.loadDC()

            this.initCheckedKeys();
        });
    }

    cloneData(nodes, index = -1) {
        for (var i = 0; i < nodes.length; i++) {
            var node = nodes[i];
            if (
                node.hasOwnProperty("layerDirectoryRespVOList") &&
                node.layerDirectoryRespVOList.length > 0
            ) {
                this.treeData.value.push({
                    id: node.id,
                    label: node.typeName,
                    icon: threeImg,
                    layer: node.layer,
                    type: node.typeCode,
                    legend: [{
                        icon: threeImg,
                        title: node.description,
                    }, ],
                    children: [],
                });
                this.cloneData(node.layerDirectoryRespVOList, i);
            } else if (
                node.hasOwnProperty("layerDirectoryRespVOList") &&
                node.layerDirectoryRespVOList.length == 0
            ) {
                this.treeData.value.push({
                    id: node.id,
                    label: node.typeName,
                    icon: threeImg,
                    layer: node.layer,
                    type: node.typeCode,
                    legend: [{
                        icon: threeImg,
                        title: node.description,
                    }, ],
                });
            } else {
                this.treeData.value[index].children.push({
                    id: node.id,
                    label: node.typeName,
                    icon: threeImg,
                    layer: node.layer,
                    type: node.typeCode,
                    legend: [{
                        icon: threeImg,
                        title: node.description,
                    }, ],
                });
            }
        }
    }

    loadModels(load) {
        const loadModel = (nodes, load) => {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    loadModel(node.children, load);
                }
                if (node.layer != null && node.layer != '' && node.type == "tileset") {
                    if (load) {
                        earth.loadLayer({
                            id: node.id,
                            url: node.layer,
                            type: "tileset_url",
                            label: node.label,
                        });
                        this.loadLabels.push(node.label);
                    } else {
                        loadData.value[node.id] = false;
                    }
                }
            }
        };
        loadModel(this.treeData.value, load);


    }

    loadYCQ(load) {
        if (load) {
            var ycq;
            ycq = new YCQ();
            window.ycq = ycq;
        } else {
            loadData.value['ycq'] = false;
        }

    }

    loadMonitoringPoint(load) {
        if (load) {
            var monitoringPoint = new MonitoringPoint();
            window.monitoringPoint = monitoringPoint;
        } else {
            loadData.value['monitoringPoint'] = false;
        }

    }

    initCheckedKeys() {
        if (isLoadEnd.value == true) {
            //设置图层管理默认勾选
            const toolsStore = useToolsStore();
            toolsStore.$patch((state) => {
                var dataTree = state.dataTree;
                // var labels = ['断层模型', '7号煤层', '8号煤层', '井巷模型', '勘探钻孔', '掘进工作面', '陷落柱', '积水区', '采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层', '异常区范围', '电法异常区', '坑透异常区']
                var indexList = this.getIdsByLabels(this.loadLabels);
                console.log(this.loadLabels)
                dataTree.setCheckedKeys(indexList);
            });
        } else {

            setTimeout(() => {
                this.initCheckedKeys()
            }, 200)


        }

    }

    loadDC() {
        var dc;
        dc = new DC();
        window.dc = dc;
    }

    nodeClick() {
        return (data, checked, node) => {
            // switch (data.label) {
            //     case "地层模型":
            //         viewer.flyTo(earth.layerManager["3"].layer);
            //         break;
            //     case "断层模型":
            //         viewer.flyTo(earth.layerManager["1"].layer);
            //         break;
            //     case "井巷模型":
            //         viewer.flyTo(earth.layerManager["2"].layer);
            //         break;
            //     case "钻孔":
            //         viewer.flyTo(earth.layerManager["4"].layer);
            //         break;
            // }
        };
    }

    checkChange() {
        return (data, checked, node) => {
            const toolsStore = useToolsStore();
            if (!data.children) {
                //没有子节点    ----用于添加右侧图例
                if (checked) {
                    toolsStore.$patch((state) => {
                        state.layerArr.push(data);
                    });
                } else {
                    toolsStore.$patch((state) => {
                        let acData = state.layerArr;
                        let _acData = acData.findIndex((item) => item.id == data.id);
                        if (_acData > -1) {
                            acData.splice(_acData, 1);
                        }
                        state.layerArr = acData;
                    });
                }
            }

            var _node = this.getNodeByLabel(data.label);
            switch (true) {
                case _node.type == "tileset":
                    var id = this.getIdByLabel(data.label);
                    if (earth.layerManager.hasOwnProperty(id)) {
                        earth.layerManager[id].layer.show = checked;
                    }
                    break;
                case _node.type == "geojson":
                    var idx = window.ycq.getIndexByLabel(data.label);
                    if (idx != -1) {
                        window.ycq.show(idx, checked);
                    }
                    // if (data.label == '异常区范围'||data.label =='瞬变电磁异常区') {
                    //     var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
                    //     window.ycq.show(sid, checked)
                    //     var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
                    //     window.ycq.show(sid, checked)
                    //     var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
                    //     window.ycq.show(sid, checked)
                    // }
                    break;
                case _node.type == "wmts":
                    break;
                case _node.type == "dir":
                    var id = this.getIdByLabel(data.label);
                    if (earth.layerManager.hasOwnProperty(id)) {
                        earth.layerManager[id].layer.show = checked;
                    }
                    break;
            }

            viewer.scene.forceRender();
        };
    }
    //通过extdata属性来确定是否显隐
    showFeatureByExtdata(featureList, data, checked) {
        //图层列表的label与模型的属性可能会不一致，需要映射
        var label = this.labelMap.hasOwnProperty(data.label) ?
            this.labelMap[data.label] :
            data.label;
        for (var i = 0; i < featureList.length; i++) {
            if (featureList[i].getProperty("extdata") == label) {
                featureList[i].show = checked;
            }
        }
    }

    getNodesByLabels(labels) {
        var nodesList = [];

        function getNodes(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getNodes(node.children);
                }
                if (labels.indexOf(node.label) > -1) {
                    nodesList.push(node);
                }
            }
        }
        getNodes(this.treeData.value);
        return nodesList;
    }

    getIdsByLabels(labels) {
        var idsList = [];

        function getIds(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getIds(node.children);
                }
                if (labels.indexOf(node.label) > -1) {
                    idsList.push(node.id);
                }
            }
        }
        getIds(this.treeData.value);
        return idsList;
    }

    getIdByLabel(label) {
        var id = -1;

        function getId(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getId(node.children);
                }
                if (label == node.label) {
                    id = node.id;
                    break;
                }
            }
        }
        getId(this.treeData.value);
        return id;
    }
    getNodeByLabel(label) {
        var _node = -1;

        function getNode(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getNode(node.children);
                }
                if (label == node.label) {
                    _node = node;
                    break;
                }
            }
        }
        getNode(this.treeData.value);
        return _node;
    }

    getLayersByLabels(labels) {
        var layers = [];

        function getLayers(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getLayers(node.children);
                }
                if (labels.indexOf(node.label) > -1) {
                    layers.push(earth.layerManager[node.id].layer);
                }
            }
        }
        getLayers(this.treeData.value);
        return layers;
    }
    //图层树列表
    treeData = ref([]);

    // treeData = ref([{
    //         id: 100,
    //         label: "地表模型",
    //         icon: threeImg,
    //         children: [{
    //                 id: 5,
    //                 label: "卫星影像",
    //                 icon: threeImg,
    //                 legend: [{
    //                     icon: threeImg,
    //                     title: "卫星影像简介"
    //                 }]
    //             },
    //             {
    //                 id: 6,
    //                 label: "工业广场",
    //                 icon: threeImg,
    //                 legend: [{
    //                     icon: threeImg,
    //                     title: "工业广场简介"
    //                 }]
    //             },
    //             {
    //                 id: 7,
    //                 label: "井田边界",
    //                 icon: threeImg,
    //                 legend: [{
    //                     icon: threeImg,
    //                     title: "井田边界简介"
    //                 }]
    //             },
    //             {
    //                 id: 8,
    //                 label: "井下布置图",
    //                 icon: threeImg,
    //                 legend: [{
    //                     icon: threeImg,
    //                     title: "井下布置图简介"
    //                 }]
    //             },
    //         ],
    //     },
    //     {
    //         id: 101,
    //         label: "地层模型",
    //         icon: threeImg,
    //         children: [{
    //                 id: 9,
    //                 label: "第四系",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 10,
    //                 label: "上侏罗～下白垩统",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 11,
    //                 label: "二叠系地层",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 12,
    //                 label: "石炭系地层",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 13,
    //                 label: "7号煤层",
    //                 icon: threeImg,
    //                 legend: [{
    //                     icon: threeImg,
    //                     title: "7号煤层简介"
    //                 }, {
    //                     icon: threeImg,
    //                     title: "该煤层位于山西组中下部，上距下石盒子组底部分界砂岩约60m，下距8号煤层0.30～34.0m之间。全井田共有173个见煤控制点（包括24线以东的F15-22号钻孔），其中受岩浆侵入影响（7号煤层变为天然焦或天然焦煤混合体）的钻孔9个（孔号为D59、D35、704、SD2、444、D61、905、D29、465上盘），原生河流冲刷钻孔4个（孔号为生4、生5、D30、460），受断层影响7号煤层变薄或断缺的钻孔4个（孔号为D04、生23、5、D44），Q1号钻孔由于钻探煤厚资料不全而又未测井，为非正常见煤点，因此全井田7号煤层钻探正常见煤点为155个，见煤两极厚度1.19～7.97m，平均煤厚4.97m。"
    //                 }]
    //             },
    //             {
    //                 id: 14,
    //                 label: "8号煤层",
    //                 icon: threeImg,
    //                 legend: [{
    //                         icon: threeImg,
    //                         title: "8号煤层简介"
    //                     },
    //                     {
    //                         icon: threeImg,
    //                         title: "    8号煤层位于山西组底部，其厚度较大，仅次于7号煤层，全井田共有124个见煤控制点，其中受岩浆岩影响钻孔1个（D61号孔），断层缺失钻孔1个（生23号孔），7线以东钻孔5个（D33、D45、D55、453、井检孔）。因此全井田8号煤层钻探正常见煤点为117个，见煤两极厚度0～6.54m，平均煤厚2.78m。"
    //                     }
    //                 ]
    //             }
    //             // {
    //             //     id: 15,
    //             //     label: "太原组",
    //             //     icon: threeImg,
    //             // },
    //             // {
    //             //     id: 16,
    //             //     label: "本溪组",
    //             //     icon: threeImg,
    //             // },
    //         ],
    //     },

    //     {
    //         id: 102,
    //         label: "断层模型",
    //         icon: threeImg,
    //         // children: [{
    //         //         id: 17,
    //         //         label: "F1",
    //         //         icon: threeImg,
    //         //         legend: [{
    //         //             icon: threeImg,
    //         //             title: "F1简介"
    //         //         }]

    //         //     },
    //         //     {
    //         //         id: 18,
    //         //         label: "F2",
    //         //         icon: threeImg,
    //         //         legend: [{
    //         //             icon: threeImg,
    //         //             title: "F2简介"
    //         //         }]
    //         //     },
    //         //     {
    //         //         id: 19,
    //         //         label: "F5",
    //         //         icon: threeImg,
    //         //     },
    //         //     {
    //         //         id: 20,
    //         //         label: "F14",
    //         //         icon: threeImg,
    //         //     },
    //         //     {
    //         //         id: 21,
    //         //         label: "F77-59",
    //         //         icon: threeImg,
    //         //     },
    //         //     {
    //         //         id: 22,
    //         //         label: "F33-1",
    //         //         icon: threeImg,
    //         //     },
    //         //     {
    //         //         id: 23,
    //         //         label: "F117-1",
    //         //         icon: threeImg,
    //         //     },
    //         //     {
    //         //         id: 24,
    //         //         label: "F117-2",
    //         //         icon: threeImg,
    //         //     },
    //         // ],
    //     },
    //     {
    //         id: 103,
    //         label: "井巷模型",
    //         icon: threeImg,
    //         children: [],
    //         legend: [{
    //             icon: threeImg,
    //             title: "井巷模型简介"
    //         }]

    //     },
    //     {
    //         id: 104,
    //         label: "掘进工作面",
    //         icon: threeImg,
    //         children: [],
    //     },
    //     {
    //         id: 105,
    //         label: "回采工作面",
    //         icon: threeImg,
    //         children: [{
    //                 id: 25,
    //                 label: "61304",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 26,
    //                 label: "61305",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 27,
    //                 label: "61306",
    //                 icon: threeImg,
    //             },
    //         ],
    //     },
    //     {
    //         id: 106,
    //         label: "钻孔",
    //         icon: threeImg,
    //         children: [{
    //                 id: 28,
    //                 label: "勘探钻孔",
    //                 icon: threeImg,
    //                 legend: [{
    //                     icon: threeImg,
    //                     title: "勘探钻孔简介"
    //                 }]
    //             },
    //             {
    //                 id: 29,
    //                 label: "超前钻探",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 30,
    //                 label: "地面定向钻孔",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 31,
    //                 label: "井下定向钻孔",
    //                 icon: threeImg,
    //             },
    //         ],
    //     },
    //     {
    //         id: 107,
    //         label: "异常区",
    //         icon: threeImg,
    //         children: [{
    //                 id: 2000,
    //                 label: "异常区范围",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 2001,
    //                 label: "电法异常区",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 2002,
    //                 label: "坑透异常区",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 2003,
    //                 label: "瞬变电磁顶板",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 2004,
    //                 label: "瞬变电磁顺层",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 2005,
    //                 label: "瞬变电磁底板",
    //                 icon: threeImg,
    //             },
    //         ],
    //     },
    //     {
    //         id: 108,
    //         label: "积水区",
    //         icon: threeImg,
    //         children: [],
    //     },
    //     {
    //         id: 109,
    //         label: "陷落柱",
    //         icon: threeImg,
    //         children: [],
    //     },
    //     {
    //         id: 110,
    //         label: "采空区",
    //         icon: threeImg,
    //         children: [],
    //     },
    //     {
    //         id: 111,
    //         label: "勘探成果图件",
    //         icon: threeImg,
    //         children: [{
    //                 id: 35,
    //                 label: "煤层储量等值线图",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 36,
    //                 label: "煤层底板等值线图",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 37,
    //                 label: "煤层厚度等值线图",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 38,
    //                 label: "灰分等值线图",
    //                 icon: threeImg,
    //             },
    //             {
    //                 id: 39,
    //                 label: "硫分等值线图",
    //                 icon: threeImg,
    //             },
    //         ],
    //     },
    // ]);

    //映射列表
    labelMap = {
        "7号煤层": "7煤",
        "8号煤层": "8煤",
        "井巷模型": "巷道",
    };
    //断层列表
    dcLabelArr = ["F1", "F2", "F5", "F14", "F77-59", "F33-1", "F117-1", "F117-2"];
    //地层列表
    dicLabelArr = ["7号煤层", "8号煤层"];
    //钻孔列表
    zkLabelArr = ["勘探钻孔", "超前钻探", "地面定向钻孔", "井下定向钻孔"];
}

export default LayerManager;