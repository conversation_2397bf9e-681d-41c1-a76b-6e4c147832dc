<template>
  <div class="analyse">
    <transition
      appear
      name="animate__animated animate__pulse"
      enter-active-class="animate__fadeInLeft"
      leave-active-class="animate__fadeOutLeft"
    >
      <div class="left" v-show="show">
        <div class="left_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>分析工具按钮</span>
          </div>
          <div class="left_box1_content">
            <div class="left_box1_li" @click="handelAnalyse(0)">
              <img class="left_box_li_img" :src="btnActive ==0 ?leftImgA1:leftImg1" />
              <div class="left_box_li_btn" :class="btnActive == 0?'left_box_li_active':''">
                测量
              </div>
            </div> 
            <div class="left_box1_li" @click="handelAnalyse(3)">
              <img class="left_box_li_img" :src="btnActive ==3 ?leftImgA2:leftImg2" />
              <div class="left_box_li_btn" :class="btnActive ==3?'left_box_li_active':''">
                剖面分析
              </div>
            </div>
            <div class="left_box1_li" @click="handelAnalyse(2)">
              <img class="left_box_li_img" :src="btnActive ==2 ?leftImgA3:leftImg3" />
              <div class="left_box_li_btn" :class="btnActive ==2?'left_box_li_active':''">
                风险点缓冲分析
              </div>
            </div>
            <div class="left_box1_li" @click="handelAnalyse(1)">
              <img class="left_box_li_img" :src="btnActive ==1 ?leftImgA3:leftImg3" />
              <div class="left_box_li_btn" :class="btnActive ==1?'left_box_li_active':''">
                储量分析
              </div>
            </div> 
            <div class="left_box1_li" @click="handelAnalyse(4)">
              <img class="left_box_li_img" :src="btnActive ==4 ?leftImgA4:leftImg4" />
              <div class="left_box_li_btn" :class="btnActive ==4?'left_box_li_active':''">
                产状分析
              </div>
            </div>
            <div class="left_box1_li" @click="handelAnalyse(5)">
              <img class="left_box_li_img" :src="btnActive == 5 ?leftImgA5:leftImg5" />
              <div class="left_box_li_btn" :class="btnActive ==5?'left_box_li_active':''">
                路径规划
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
    <transition
      appear
      name="animate__animated animate__bounce"
      enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight"
    >
      <div class="right" v-show="show">
        <router-view> </router-view>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import {initStore} from '@/utils/store'
import { storeToRefs } from "pinia";
import titleImg from "@/assets/img/home/<USER>";
import lineImg from "@/assets/img/home/<USER>";
import resImg from "@/assets/img/home/<USER>";
import btnImgA from "@/assets/img/analyse/bntA.png";
import btnImg from "@/assets/img/analyse/btn.png";
import leftImg1 from "@/assets/img/analyse/1.png";
import leftImg2 from "@/assets/img/analyse/2.png";
import leftImg3 from "@/assets/img/analyse/3.png";
import leftImg4 from "@/assets/img/analyse/4.png";
import leftImg5 from "@/assets/img/analyse/5.png";
import leftImgA1 from "@/assets/img/analyse/1-1.png";
import leftImgA2 from "@/assets/img/analyse/2-1.png";
import leftImgA3 from "@/assets/img/analyse/3-1.png";
import leftImgA4 from "@/assets/img/analyse/4-1.png";
import leftImgA5 from "@/assets/img/analyse/5-1.png";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import router from "../router";
import { useRouter } from "vue-router";
import { ref, toRef } from "vue";
import { useLoadStore } from "@/store/load";
import InitModelShow from "@/utils/map/initModelShow";
const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
let btnActive = ref(0)
let routerArr = ['/index/Analyse/dizhi','/index/Analyse/chuliang','/index/Analyse/fenxian','/index/Analyse/poumian','/index/Analyse/luwang','/index/Analyse/lujing']
// 分析按钮点击事件
const handelAnalyse=(index)=>{
  btnActive.value=index
  router.push(routerArr[index]);
}
//生命周期
onMounted(() => {
  const router = useRouter();
  // 页面刷新  定位到当前按钮
  btnActive.value = routerArr.indexOf(router.currentRoute.value.path)
  // 初始化 左右两侧 确保展开  工具栏 默认到1  图层关闭
  initStore(4);
  let layerBox = document.getElementsByClassName('home_layer')[0]
  layerBox.style.left='240px'
  let toolBox = document.getElementsByClassName('home_legend')[0]
    toolBox.style.right='350px'
    toolBox.style.left=''

    if (isLoadEnd.value == true) {
    init();
  }
});

watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);

const init=()=>{
  initModelsShow()
}


//初始化图层列表和模型显隐
const initModelsShow=()=>{
  var initModelShow=new InitModelShow()
  initModelShow.initShow()
}

// 下载
const handleDownload = () => {};

</script>
<style lang="less" scoped>
.analyse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  // overflow: hidden;
  .left {
    z-index: 1;
    width: 241px;
    height: 550px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
    background: rgba(11, 24, 36,.7);
    // background-image: url("../assets/img/home/<USER>");
    // background-position: center center;
    // padding-right: 18px;
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    .left_box1 {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 0px 0px 14px 0px;
      display: flex;
      flex-direction: column;
      .left_box1_content{
        display: flex;
        width: 100%;
        box-sizing: border-box;
        padding: 26px 0px 0 20px ;
        flex-wrap: wrap;
        .left_box1_li{
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          // margin-right: 20px;
          cursor: pointer;
          &:hover{
            .left_box_li_img{
              transform: scale(1.1);
            }
          }
          .left_box_li_img{
            width: 53px;
            height: 53px;
            margin-bottom: 10px;
          }
          .left_box_li_btn{
            width: 129px;
            height: 38px;
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            background: rgba(54, 175, 255, 0.32);
            border-radius: 6px;
          }
          .left_box_li_active{
              background: rgba(255, 242, 118, 0.32);
            }
        }
      }
    }
    .left_box2 {
      margin-top: 17px;
      width: 100%;
      height: 321px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size:100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 23px 0 0px 18px;
      display: flex;
      flex-direction: column;
      background-color:RGBA(5, 31, 89, .6);
    }
  }
  .right {
    z-index: 1;
    width: 344px;
    height: 100%;
    padding-bottom: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }
 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}

</style>