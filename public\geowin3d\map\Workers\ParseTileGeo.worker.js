(function(e){var n={};function i(t){if(n[t]){return n[t].exports}var r=n[t]={i:t,l:false,exports:{}};e[t].call(r.exports,r,r.exports,i);r.l=true;return r.exports}i.m=e;i.c=n;i.d=function(t,r,e){if(!i.o(t,r)){Object.defineProperty(t,r,{enumerable:true,get:e})}};i.r=function(t){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(t,"__esModule",{value:true})};i.t=function(r,t){if(t&1)r=i(r);if(t&8)return r;if(t&4&&typeof r==="object"&&r&&r.__esModule)return r;var e=Object.create(null);i.r(e);Object.defineProperty(e,"default",{enumerable:true,value:r});if(t&2&&typeof r!="string")for(var n in r)i.d(e,n,function(t){return r[t]}.bind(null,n));return e};i.n=function(r){var t=r&&r.__esModule?function t(){return r["default"]}:function t(){return r};i.d(t,"a",t);return t};i.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)};i.p="";return i(i.s=56)})([function(t,r){var e=t.exports=typeof window!="undefined"&&window.Math==Math?window:typeof self!="undefined"&&self.Math==Math?self:Function("return this")();if(typeof __g=="number")__g=e},function(t,r,e){var n=e(39)("wks");var i=e(28);var a=e(0).Symbol;var o=typeof a=="function";var u=t.exports=function(t){return n[t]||(n[t]=o&&a[t]||(o?a:i)("Symbol."+t))};u.store=n},function(t,r){var e=t.exports={version:"2.6.12"};if(typeof __e=="number")__e=e},function(t,r,e){var d=e(0);var y=e(2);var w=e(5);var x=e(9);var g=e(13);var _="prototype";var m=function(t,r,e){var n=t&m.F;var i=t&m.G;var a=t&m.S;var o=t&m.P;var u=t&m.B;var s=t&m.W;var f=i?y:y[r]||(y[r]={});var c=f[_];var v=i?d:a?d[r]:(d[r]||{})[_];var h,l,p;if(i)e=r;for(h in e){l=!n&&v&&v[h]!==undefined;if(l&&g(f,h))continue;p=l?v[h]:e[h];f[h]=i&&typeof v[h]!="function"?e[h]:u&&l?w(p,d):s&&v[h]==p?function(n){var t=function(t,r,e){if(this instanceof n){switch(arguments.length){case 0:return new n;case 1:return new n(t);case 2:return new n(t,r)}return new n(t,r,e)}return n.apply(this,arguments)};t[_]=n[_];return t}(p):o&&typeof p=="function"?w(Function.call,p):p;if(o){(f.virtual||(f.virtual={}))[h]=p;if(t&m.R&&c&&!c[h])x(c,h,p)}}};m.F=1;m.G=2;m.S=4;m.P=8;m.B=16;m.W=32;m.U=64;m.R=128;t.exports=m},function(t,r){t.exports=function(t){return typeof t==="object"?t!==null:typeof t==="function"}},function(t,r,e){var a=e(12);t.exports=function(n,i,t){a(n);if(i===undefined)return n;switch(t){case 1:return function(t){return n.call(i,t)};case 2:return function(t,r){return n.call(i,t,r)};case 3:return function(t,r,e){return n.call(i,t,r,e)}}return function(){return n.apply(i,arguments)}}},function(t,r,e){var i=e(7);var a=e(61);var o=e(62);var u=Object.defineProperty;r.f=e(8)?Object.defineProperty:function t(r,e,n){i(r);e=o(e,true);i(n);if(a)try{return u(r,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");if("value"in n)r[e]=n.value;return r}},function(t,r,e){var n=e(4);t.exports=function(t){if(!n(t))throw TypeError(t+" is not an object!");return t}},function(t,r,e){t.exports=!e(16)(function(){return Object.defineProperty({},"a",{get:function(){return 7}}).a!=7})},function(t,r,e){var n=e(6);var i=e(25);t.exports=e(8)?function(t,r,e){return n.f(t,r,i(1,e))}:function(t,r,e){t[r]=e;return t}},function(t,r){t.exports={}},function(t,r){var e={}.toString;t.exports=function(t){return e.call(t).slice(8,-1)}},function(t,r){t.exports=function(t){if(typeof t!="function")throw TypeError(t+" is not a function!");return t}},function(t,r){var e={}.hasOwnProperty;t.exports=function(t,r){return e.call(t,r)}},function(t,r,e){var h=e(5);var l=e(43);var p=e(44);var d=e(7);var y=e(17);var w=e(30);var x={};var g={};var r=t.exports=function(t,r,e,n,i){var a=i?function(){return t}:w(t);var o=h(e,n,r?2:1);var u=0;var s,f,c,v;if(typeof a!="function")throw TypeError(t+" is not iterable!");if(p(a))for(s=y(t.length);s>u;u++){v=r?o(d(f=t[u])[0],f[1]):o(t[u]);if(v===x||v===g)return v}else for(c=a.call(t);!(f=c.next()).done;){v=l(c,o,f.value,r);if(v===x||v===g)return v}};r.BREAK=x;r.RETURN=g},function(t,r,e){var n=e(36);var i=e(21);t.exports=function(t){return n(i(t))}},function(t,r){t.exports=function(t){try{return!!t()}catch(t){return true}}},function(t,r,e){var n=e(26);var i=Math.min;t.exports=function(t){return t>0?i(n(t),9007199254740991):0}},function(t,r,e){var n=e(6).f;var i=e(13);var a=e(1)("toStringTag");t.exports=function(t,r,e){if(t&&!i(t=e?t:t.prototype,a))n(t,a,{configurable:true,value:r})}},function(t,r,e){"use strict";var n=e(70)(true);e(22)(String,"String",function(t){this._t=String(t);this._i=0},function(){var t=this._t;var r=this._i;var e;if(r>=t.length)return{value:undefined,done:true};e=n(t,r);this._i+=e.length;return{value:e,done:false}})},function(t,r,e){e(59);var n=e(0);var i=e(9);var a=e(10);var o=e(1)("toStringTag");var u=("CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,"+"DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,"+"MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,"+"SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,"+"TextTrackList,TouchList").split(",");for(var s=0;s<u.length;s++){var f=u[s];var c=n[f];var v=c&&c.prototype;if(v&&!v[o])i(v,o,f);a[f]=a.Array}},function(t,r){t.exports=function(t){if(t==undefined)throw TypeError("Can't call method on  "+t);return t}},function(t,r,e){"use strict";var g=e(23);var _=e(3);var m=e(63);var b=e(9);var S=e(10);var M=e(64);var F=e(18);var V=e(69);var P=e(1)("iterator");var k=!([].keys&&"next"in[].keys());var T="@@iterator";var O="keys";var E="values";var j=function(){return this};t.exports=function(t,r,e,n,i,a,o){M(e,r,n);var u=function(r){if(!k&&r in v)return v[r];switch(r){case O:return function t(){return new e(this,r)};case E:return function t(){return new e(this,r)}}return function t(){return new e(this,r)}};var s=r+" Iterator";var f=i==E;var c=false;var v=t.prototype;var h=v[P]||v[T]||i&&v[i];var l=h||u(i);var p=i?!f?l:u("entries"):undefined;var d=r=="Array"?v.entries||h:h;var y,w,x;if(d){x=V(d.call(new t));if(x!==Object.prototype&&x.next){F(x,s,true);if(!g&&typeof x[P]!="function")b(x,P,j)}}if(f&&h&&h.name!==E){c=true;l=function t(){return h.call(this)}}if((!g||o)&&(k||c||!v[P])){b(v,P,l)}S[r]=l;S[s]=j;if(i){y={values:f?l:u(E),keys:a?l:u(O),entries:p};if(o)for(w in y){if(!(w in v))m(v,w,y[w])}else _(_.P+_.F*(k||c),r,y)}return y}},function(t,r){t.exports=true},function(t,r,e){var n=e(4);var i=e(0).document;var a=n(i)&&n(i.createElement);t.exports=function(t){return a?i.createElement(t):{}}},function(t,r){t.exports=function(t,r){return{enumerable:!(t&1),configurable:!(t&2),writable:!(t&4),value:r}}},function(t,r){var e=Math.ceil;var n=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?n:e)(t)}},function(t,r,e){var n=e(39)("keys");var i=e(28);t.exports=function(t){return n[t]||(n[t]=i(t))}},function(t,r){var e=0;var n=Math.random();t.exports=function(t){return"Symbol(".concat(t===undefined?"":t,")_",(++e+n).toString(36))}},function(t,r,e){var n=e(21);t.exports=function(t){return Object(n(t))}},function(t,r,e){var n=e(31);var i=e(1)("iterator");var a=e(10);t.exports=e(2).getIteratorMethod=function(t){if(t!=undefined)return t[i]||t["@@iterator"]||a[n(t)]}},function(t,r,e){var i=e(11);var a=e(1)("toStringTag");var o=i(function(){return arguments}())=="Arguments";var u=function(t,r){try{return t[r]}catch(t){}};t.exports=function(t){var r,e,n;return t===undefined?"Undefined":t===null?"Null":typeof(e=u(r=Object(t),a))=="string"?e:o?i(r):(n=i(r))=="Object"&&typeof r.callee=="function"?"Arguments":n}},function(t,r,e){var i=e(9);t.exports=function(t,r,e){for(var n in r){if(e&&t[n])t[n]=r[n];else i(t,n,r[n])}return t}},function(t,r){t.exports=function(t,r,e,n){if(!(t instanceof r)||n!==undefined&&n in t){throw TypeError(e+": incorrect invocation!")}return t}},function(t,r,e){"use strict";var i=e(12);function n(t){var e,n;this.promise=new t(function(t,r){if(e!==undefined||n!==undefined)throw TypeError("Bad Promise constructor");e=t;n=r});this.resolve=i(e);this.reject=i(n)}t.exports.f=function(t){return new n(t)}},function(t,r){t.exports=function(t,r){return{value:r,done:!!t}}},function(t,r,e){var n=e(11);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return n(t)=="String"?t.split(""):Object(t)}},function(t,r,a){var i=a(7);var o=a(65);var u=a(40);var s=a(27)("IE_PROTO");var f=function(){};var c="prototype";var v=function(){var t=a(24)("iframe");var r=u.length;var e="<";var n=">";var i;t.style.display="none";a(41).appendChild(t);t.src="javascript:";i=t.contentWindow.document;i.open();i.write(e+"script"+n+"document.F=Object"+e+"/script"+n);i.close();v=i.F;while(r--)delete v[c][u[r]];return v()};t.exports=Object.create||function t(r,e){var n;if(r!==null){f[c]=i(r);n=new f;f[c]=null;n[s]=r}else n=v();return e===undefined?n:o(n,e)}},function(t,r,e){var n=e(66);var i=e(40);t.exports=Object.keys||function t(r){return n(r,i)}},function(t,r,e){var n=e(2);var i=e(0);var a="__core-js_shared__";var o=i[a]||(i[a]={});(t.exports=function(t,r){return o[t]||(o[t]=r!==undefined?r:{})})("versions",[]).push({version:n.version,mode:e(23)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(t,r){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,r,e){var n=e(0).document;t.exports=n&&n.documentElement},function(t,r,e){t.exports={default:e(77),__esModule:true}},function(t,r,e){var a=e(7);t.exports=function(r,t,e,n){try{return n?t(a(e)[0],e[1]):t(e)}catch(t){var i=r["return"];if(i!==undefined)a(i.call(r));throw t}}},function(t,r,e){var n=e(10);var i=e(1)("iterator");var a=Array.prototype;t.exports=function(t){return t!==undefined&&(n.Array===t||a[i]===t)}},function(t,r,e){var a=e(1)("iterator");var o=false;try{var n=[7][a]();n["return"]=function(){o=true};Array.from(n,function(){throw 2})}catch(t){}t.exports=function(t,r){if(!r&&!o)return false;var e=false;try{var n=[7];var i=n[a]();i.next=function(){return{done:e=true}};n[a]=function(){return i};t(n)}catch(t){}return e}},function(t,r){},function(t,r,e){"use strict";var n=e(0);var i=e(2);var a=e(6);var o=e(8);var u=e(1)("species");t.exports=function(t){var r=typeof i[t]=="function"?i[t]:n[t];if(o&&r&&!r[u])a.f(r,u,{configurable:true,get:function(){return this}})}},function(t,r,e){var n=e(28)("meta");var i=e(4);var a=e(13);var o=e(6).f;var u=0;var s=Object.isExtensible||function(){return true};var f=!e(16)(function(){return s(Object.preventExtensions({}))});var c=function(t){o(t,n,{value:{i:"O"+ ++u,w:{}}})};var v=function(t,r){if(!i(t))return typeof t=="symbol"?t:(typeof t=="string"?"S":"P")+t;if(!a(t,n)){if(!s(t))return"F";if(!r)return"E";c(t)}return t[n].i};var h=function(t,r){if(!a(t,n)){if(!s(t))return true;if(!r)return false;c(t)}return t[n].w};var l=function(t){if(f&&p.NEED&&s(t)&&!a(t,n))c(t);return t};var p=t.exports={KEY:n,NEED:false,fastKey:v,getWeak:h,onFreeze:l}},function(t,r,e){var n=e(4);t.exports=function(t,r){if(!n(t)||t._t!==r)throw TypeError("Incompatible receiver, "+r+" required!");return t}},function(t,r,e){var i=e(7);var a=e(12);var o=e(1)("species");t.exports=function(t,r){var e=i(t).constructor;var n;return e===undefined||(n=i(e)[o])==undefined?r:a(n)}},function(t,r,e){var n=e(5);var i=e(99);var a=e(41);var o=e(24);var u=e(0);var s=u.process;var f=u.setImmediate;var c=u.clearImmediate;var v=u.MessageChannel;var h=u.Dispatch;var l=0;var p={};var d="onreadystatechange";var y,w,x;var g=function(){var t=+this;if(p.hasOwnProperty(t)){var r=p[t];delete p[t];r()}};var _=function(t){g.call(t.data)};if(!f||!c){f=function t(r){var e=[];var n=1;while(arguments.length>n)e.push(arguments[n++]);p[++l]=function(){i(typeof r=="function"?r:Function(r),e)};y(l);return l};c=function t(r){delete p[r]};if(e(11)(s)=="process"){y=function(t){s.nextTick(n(g,t,1))}}else if(h&&h.now){y=function(t){h.now(n(g,t,1))}}else if(v){w=new v;x=w.port2;w.port1.onmessage=_;y=n(x.postMessage,x,1)}else if(u.addEventListener&&typeof postMessage=="function"&&!u.importScripts){y=function(t){u.postMessage(t+"","*")};u.addEventListener("message",_,false)}else if(d in o("script")){y=function(t){a.appendChild(o("script"))[d]=function(){a.removeChild(this);g.call(t)}}}else{y=function(t){setTimeout(n(g,t,1),0)}}}t.exports={set:f,clear:c}},function(t,r){t.exports=function(t){try{return{e:false,v:t()}}catch(t){return{e:true,v:t}}}},function(t,r,e){var i=e(7);var a=e(4);var o=e(34);t.exports=function(t,r){i(t);if(a(r)&&r.constructor===t)return r;var e=o.f(t);var n=e.resolve;n(r);return e.promise}},function(t,r,e){"use strict";var n=e(55);t.exports=i;function i(t,r){this.version=1;this.name=null;this.extent=4096;this.length=0;this._pbf=t;this._keys=[];this._values=[];this._features=[];t.readFields(a,this,r);this.length=this._features.length}function a(t,r,e){if(t===15)r.version=e.readVarint();else if(t===1)r.name=e.readString();else if(t===5)r.extent=e.readVarint();else if(t===2)r._features.push(e.pos);else if(t===3)r._keys.push(e.readString());else if(t===4)r._values.push(o(e))}function o(t){var r=null,e=t.readVarint()+t.pos;while(t.pos<e){var n=t.readVarint()>>3;r=n===1?t.readString():n===2?t.readFloat():n===3?t.readDouble():n===4?t.readVarint64():n===5?t.readVarint():n===6?t.readSVarint():n===7?t.readBoolean():null}return r}i.prototype.feature=function(t){if(t<0||t>=this._features.length)throw new Error("feature index out of bounds");this._pbf.pos=this._features[t];var r=this._pbf.readVarint()+this._pbf.pos;return new n(this._pbf,r,this.extent,this._keys,this._values)}},function(t,r,e){"use strict";var f=e(107);t.exports=l;function l(t,r,e,n,i){this.properties={};this.extent=e;this.type=0;this._pbf=t;this._geometry=-1;this._keys=n;this._values=i;t.readFields(a,this,r)}function a(t,r,e){if(t==1)r.id=e.readVarint();else if(t==2)n(e,r);else if(t==3)r.type=e.readVarint();else if(t==4)r._geometry=e.pos}function n(t,r){var e=t.readVarint()+t.pos;while(t.pos<e){var n=r._keys[t.readVarint()],i=r._values[t.readVarint()];r.properties[n]=i}}l.types=["Unknown","Point","LineString","Polygon"];l.prototype.loadGeometry=function(){var t=this._pbf;t.pos=this._geometry;var r=t.readVarint()+t.pos,e=1,n=0,i=0,a=0,o=[],u;while(t.pos<r){if(n<=0){var s=t.readVarint();e=s&7;n=s>>3}n--;if(e===1||e===2){i+=t.readSVarint();a+=t.readSVarint();if(e===1){if(u)o.push(u);u=[]}u.push(new f(i,a))}else if(e===7){if(u){u.push(u[0].clone())}}else{throw new Error("unknown command "+e)}}if(u)o.push(u);return o};l.prototype.bbox=function(){var t=this._pbf;t.pos=this._geometry;var r=t.readVarint()+t.pos,e=1,n=0,i=0,a=0,o=Infinity,u=-Infinity,s=Infinity,f=-Infinity;while(t.pos<r){if(n<=0){var c=t.readVarint();e=c&7;n=c>>3}n--;if(e===1||e===2){i+=t.readSVarint();a+=t.readSVarint();if(i<o)o=i;if(i>u)u=i;if(a<s)s=a;if(a>f)f=a}else if(e!==7){throw new Error("unknown command "+e)}}return[o,s,u,f]};l.prototype.toGeoJSON=function(t,r,e){var i=this.extent*Math.pow(2,e),a=this.extent*t,o=this.extent*r,n=this.loadGeometry(),u=l.types[this.type],s,f;function c(t){for(var r=0;r<t.length;r++){var e=t[r],n=180-(e.y+o)*360/i;t[r]=[(e.x+a)*360/i-180,360/Math.PI*Math.atan(Math.exp(n*Math.PI/180))-90]}}switch(this.type){case 1:var v=[];for(s=0;s<n.length;s++){v[s]=n[s][0]}n=v;c(n);break;case 2:for(s=0;s<n.length;s++){c(n[s])}break;case 3:n=p(n);for(s=0;s<n.length;s++){for(f=0;f<n[s].length;f++){c(n[s][f])}}break}if(n.length===1){n=n[0]}else{u="Multi"+u}var h={type:"Feature",geometry:{type:u,coordinates:n},properties:this.properties};if("id"in this){h.id=this.id}return h};function p(t){var r=t.length;if(r<=1)return[t];var e=[],n,i;for(var a=0;a<r;a++){var o=u(t[a]);if(o===0)continue;if(i===undefined)i=o<0;if(i===o<0){if(n)e.push(n);n=[t[a]]}else{n.push(t[a])}}if(n)e.push(n);return e}function u(t){var r=0;for(var e=0,n=t.length,i=n-1,a,o;e<n;i=e++){a=t[e];o=t[i];r+=(o.x-a.x)*(a.y+o.y)}return r}},function(L,G,t){"use strict";var r=t(57);var y=l(r);var e=t(72);var w=l(e);var n=t(42);var o=l(n);var i=t(80);var a=l(i);var u=t(96);var v=l(u);var s=t(104);var x=l(s);var f=t(105);var c=t(108);var h=l(c);function l(t){return t&&t.__esModule?t:{default:t}}importScripts("../../../geowin3D/Geowin3D/Geowin3D.js");var p={};function d(t,r){p[t]=r}function g(r,t){var e=p[r];if(typeof e!="function")throw new Error("指定的method非法！");var n=e.apply(undefined,(0,x.default)(t));if(n instanceof v.default)n.then(function(t){postMessage({result:t,method:r})}).catch(function(t){return console.error(t)});else postMessage({result:n,method:r})}var _=undefined;var m=19;var b=14;var S=undefined;var M=16;var F=3e3;var V={};var P=[];var k=new a.default;self.onmessage=function(t){var r=t.data;g(r.method,r.args)};d("setAttr",T);function T(t){_=Geowin3D.defaultValue(t.urlTemplate,_);S=Geowin3D.defaultValue(t.vectorLayers,S);b=Geowin3D.defaultValue(t.minimumLevel,b);m=Geowin3D.defaultValue(t.maximumLevel,m)}d("requestTileGeo",O);function O(t,r,e){if(e<16)return;var n=M-e;var u=void 0,s=void 0,f=void 0,c=void 0;if(n>0){u=(t+1)*Math.pow(2,n);s=(r+1)*Math.pow(2,n);f=t*Math.pow(2,n);c=r*Math.pow(2,n)}else if(n<=0){f=parseInt(t*Math.pow(2,n)),u=f+1;c=parseInt(r*Math.pow(2,n)),s=c+1}return new v.default(function(r,t){var e=void 0,n=0;for(var i=f;i<u;i++){for(var a=c;a<s;a++){var o=E([i,a,M]);if(o instanceof v.default){n++;o.then(function(t){n--;if(n==0)r(t)})}else e=o}}if(n==0)r(e)})}d("loadTileGeo",E);function E(i){var a=arguments.length>1&&arguments[1]!==undefined?arguments[1]:z(i);if(!a)return new Error("未设置urlTemplate或url参数");if(i[2]!=16)return P;var t=V[i.join(",")];if(!t){return new v.default(function(e,n){Geowin3D.Resource.fetchArrayBuffer({url:a}).then(function(t){if(!t)n();var r=new f.VectorTile(new h.default(t));I.apply(undefined,[r.layers].concat((0,x.default)(i)));e(E(i,a))}).otherwise(function(t){return n(t)})})}if(t.length<1)return P;t.forEach(function(t){return k.add(t)});P=(0,o.default)(k);R(P);return P}function I(t,r,e,n){var i=r+","+e+","+n;var a=(0,w.default)(t);if(S)a=a.filter(function(t){return S.indexOf(t.name)>-1});var o=[];var u=true;var s=false;var f=undefined;try{for(var c=(0,y.default)(a),v;!(u=(v=c.next()).done);u=true){var h=v.value;for(var l=0;l<h.length;l++){var p=h.feature(l);var d=p.toGeoJSON(r,e,n);o.push.apply(o,(0,x.default)(j(d)))}}}catch(t){s=true;f=t}finally{try{if(!u&&c.return){c.return()}}finally{if(s){throw f}}}V[i]=o}d("parseGeoJsons",C);function C(t,r){var e=[];for(var n=0;n<t.length;n++){var i=t[n];e.push.apply(e,(0,x.default)(j(i)))}return{geoInstances:e,xyz:r}}function j(t){var r=t.geometry,e=r.coordinates,n=t.properties.height,i=[];switch(r.type){case"Polygon":i.push(A(e,n));break;case"MultiPlygon":var a=true;var o=false;var u=undefined;try{for(var s=(0,y.default)(e),f;!(a=(f=s.next()).done);a=true){var c=f.value;i.push(A(e,n))}}catch(t){o=true;u=t}finally{try{if(!a&&s.return){s.return()}}finally{if(o){throw u}}}break;default:break}return i}function A(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:Math.random()*200;var e=t.shift().map(function(t){var r;return(r=Geowin3D.Cartesian3).fromDegrees.apply(r,(0,x.default)(t))}),n=[];var i=new Geowin3D.PolygonHierarchy(e,n);var a=true;var o=false;var u=undefined;try{for(var s=(0,y.default)(t),f;!(a=(f=s.next()).done);a=true){var c=f.value;var v=c.map(function(t){var r;return(r=Geowin3D.Cartesian3).fromDegrees.apply(r,(0,x.default)(t))});n.push(new Geowin3D.PolygonHierarchy(v))}}catch(t){o=true;u=t}finally{try{if(!a&&s.return){s.return()}}finally{if(o){throw u}}}var h=new Geowin3D.PolygonGeometry({polygonHierarchy:i,extrudedHeight:r});var l=Geowin3D.PolygonGeometry.createGeometry(h);return new Geowin3D.GeometryInstance({geometry:l})}d("test",N);function N(t){return t}function R(t){while(t.length>F){t.shift()}}function W(t,r,e,i){B(t,r,e,n);D(t,r,e,n);function n(t,r,e){var n=V[t+","+r+","+e];if(!n)return;i=i.filter(function(t){return n.indexOf(t)<0})}return i}function B(t,r,e,n){if(e<=b)return;console.log(e,b,(new Date).getMilliseconds());var i=parseInt(t/2),a=parseInt(r/2),o=e-1;if(typeof n=="function")n(i,a,o);B(i,a,o,n)}function z(t){if(!_)return;return _.replace("{x}",t[0]).replace("{y}",t[1]).replace("{z}",t[2])}function D(t,r,e,n){if(e>=m)return;var i=t*2,a=t*2+1,o=r*2,u=r*2+1;e+=1;n(i,o,e);n(i,u,e);n(a,o,e);n(a,u,e);console.log(e,m,(new Date).getMilliseconds());D(i,o,e,n);D(i,u,e,n);D(a,o,e,n);D(a,u,e,n)}},function(t,r,e){t.exports={default:e(58),__esModule:true}},function(t,r,e){e(20);e(19);t.exports=e(71)},function(t,r,e){"use strict";var n=e(60);var i=e(35);var a=e(10);var o=e(15);t.exports=e(22)(Array,"Array",function(t,r){this._t=o(t);this._i=0;this._k=r},function(){var t=this._t;var r=this._k;var e=this._i++;if(!t||e>=t.length){this._t=undefined;return i(1)}if(r=="keys")return i(0,e);if(r=="values")return i(0,t[e]);return i(0,[e,t[e]])},"values");a.Arguments=a.Array;n("keys");n("values");n("entries")},function(t,r){t.exports=function(){}},function(t,r,e){t.exports=!e(8)&&!e(16)(function(){return Object.defineProperty(e(24)("div"),"a",{get:function(){return 7}}).a!=7})},function(t,r,e){var i=e(4);t.exports=function(t,r){if(!i(t))return t;var e,n;if(r&&typeof(e=t.toString)=="function"&&!i(n=e.call(t)))return n;if(typeof(e=t.valueOf)=="function"&&!i(n=e.call(t)))return n;if(!r&&typeof(e=t.toString)=="function"&&!i(n=e.call(t)))return n;throw TypeError("Can't convert object to primitive value")}},function(t,r,e){t.exports=e(9)},function(t,r,e){"use strict";var n=e(37);var i=e(25);var a=e(18);var o={};e(9)(o,e(1)("iterator"),function(){return this});t.exports=function(t,r,e){t.prototype=n(o,{next:i(1,e)});a(t,r+" Iterator")}},function(t,r,e){var u=e(6);var s=e(7);var f=e(38);t.exports=e(8)?Object.defineProperties:function t(r,e){s(r);var n=f(e);var i=n.length;var a=0;var o;while(i>a)u.f(r,o=n[a++],e[o]);return r}},function(t,r,e){var o=e(13);var u=e(15);var s=e(67)(false);var f=e(27)("IE_PROTO");t.exports=function(t,r){var e=u(t);var n=0;var i=[];var a;for(a in e)if(a!=f)o(e,a)&&i.push(a);while(r.length>n)if(o(e,a=r[n++])){~s(i,a)||i.push(a)}return i}},function(t,r,e){var s=e(15);var f=e(17);var c=e(68);t.exports=function(u){return function(t,r,e){var n=s(t);var i=f(n.length);var a=c(e,i);var o;if(u&&r!=r)while(i>a){o=n[a++];if(o!=o)return true}else for(;i>a;a++)if(u||a in n){if(n[a]===r)return u||a||0}return!u&&-1}}},function(t,r,e){var n=e(26);var i=Math.max;var a=Math.min;t.exports=function(t,r){t=n(t);return t<0?i(t+r,0):a(t,r)}},function(t,r,e){var n=e(13);var i=e(29);var a=e(27)("IE_PROTO");var o=Object.prototype;t.exports=Object.getPrototypeOf||function(t){t=i(t);if(n(t,a))return t[a];if(typeof t.constructor=="function"&&t instanceof t.constructor){return t.constructor.prototype}return t instanceof Object?o:null}},function(t,r,e){var s=e(26);var f=e(21);t.exports=function(u){return function(t,r){var e=String(f(t));var n=s(r);var i=e.length;var a,o;if(n<0||n>=i)return u?"":undefined;a=e.charCodeAt(n);return a<55296||a>56319||n+1===i||(o=e.charCodeAt(n+1))<56320||o>57343?u?e.charAt(n):a:u?e.slice(n,n+2):(a-55296<<10)+(o-56320)+65536}}},function(t,r,e){var n=e(7);var i=e(30);t.exports=e(2).getIterator=function(t){var r=i(t);if(typeof r!="function")throw TypeError(t+" is not iterable!");return n(r.call(t))}},function(t,r,e){t.exports={default:e(73),__esModule:true}},function(t,r,e){e(74);t.exports=e(2).Object.values},function(t,r,e){var n=e(3);var i=e(75)(false);n(n.S,"Object",{values:function t(r){return i(r)}})},function(t,r,e){var s=e(8);var f=e(38);var c=e(15);var v=e(76).f;t.exports=function(u){return function(t){var r=c(t);var e=f(r);var n=e.length;var i=0;var a=[];var o;while(n>i){o=e[i++];if(!s||v.call(r,o)){a.push(u?[o,r[o]]:r[o])}}return a}}},function(t,r){r.f={}.propertyIsEnumerable},function(t,r,e){e(19);e(78);t.exports=e(2).Array.from},function(t,r,e){"use strict";var l=e(5);var n=e(3);var p=e(29);var d=e(43);var y=e(44);var w=e(17);var x=e(79);var g=e(30);n(n.S+n.F*!e(45)(function(t){Array.from(t)}),"Array",{from:function t(r){var e=p(r);var n=typeof this=="function"?this:Array;var i=arguments.length;var a=i>1?arguments[1]:undefined;var o=a!==undefined;var u=0;var s=g(e);var f,c,v,h;if(o)a=l(a,i>2?arguments[2]:undefined,2);if(s!=undefined&&!(n==Array&&y(s))){for(h=s.call(e),c=new n;!(v=h.next()).done;u++){x(c,u,o?d(h,a,[v.value,u],true):v.value)}}else{f=w(e.length);for(c=new n(f);f>u;u++){x(c,u,o?a(e[u],u):e[u])}}c.length=u;return c}})},function(t,r,e){"use strict";var n=e(6);var i=e(25);t.exports=function(t,r,e){if(r in t)n.f(t,r,i(0,e));else t[r]=e}},function(t,r,e){t.exports={default:e(81),__esModule:true}},function(t,r,e){e(46);e(19);e(20);e(82);e(89);e(92);e(94);t.exports=e(2).Set},function(t,r,e){"use strict";var n=e(83);var i=e(49);var a="Set";t.exports=e(84)(a,function(r){return function t(){return r(this,arguments.length>0?arguments[0]:undefined)}},{add:function t(r){return n.def(i(this,a),r=r===0?0:r,r)}},n)},function(t,r,e){"use strict";var o=e(6).f;var u=e(37);var s=e(32);var f=e(5);var c=e(33);var v=e(14);var n=e(22);var i=e(35);var a=e(47);var h=e(8);var l=e(48).fastKey;var p=e(49);var d=h?"_s":"size";var y=function(t,r){var e=l(r);var n;if(e!=="F")return t._i[e];for(n=t._f;n;n=n.n){if(n.k==r)return n}};t.exports={getConstructor:function(t,a,e,n){var i=t(function(t,r){c(t,i,a,"_i");t._t=a;t._i=u(null);t._f=undefined;t._l=undefined;t[d]=0;if(r!=undefined)v(r,e,t[n],t)});s(i.prototype,{clear:function t(){for(var r=p(this,a),e=r._i,n=r._f;n;n=n.n){n.r=true;if(n.p)n.p=n.p.n=undefined;delete e[n.i]}r._f=r._l=undefined;r[d]=0},delete:function(t){var r=p(this,a);var e=y(r,t);if(e){var n=e.n;var i=e.p;delete r._i[e.i];e.r=true;if(i)i.n=n;if(n)n.p=i;if(r._f==e)r._f=n;if(r._l==e)r._l=i;r[d]--}return!!e},forEach:function t(r){p(this,a);var e=f(r,arguments.length>1?arguments[1]:undefined,3);var n;while(n=n?n.n:this._f){e(n.v,n.k,this);while(n&&n.r)n=n.p}},has:function t(r){return!!y(p(this,a),r)}});if(h)o(i.prototype,"size",{get:function(){return p(this,a)[d]}});return i},def:function(t,r,e){var n=y(t,r);var i,a;if(n){n.v=e}else{t._l=n={i:a=l(r,true),k:r,v:e,p:i=t._l,n:undefined,r:false};if(!t._f)t._f=n;if(i)i.n=n;t[d]++;if(a!=="F")t._i[a]=n}return t},getEntry:y,setStrong:function(t,e,r){n(t,e,function(t,r){this._t=p(t,e);this._k=r;this._l=undefined},function(){var t=this;var r=t._k;var e=t._l;while(e&&e.r)e=e.p;if(!t._t||!(t._l=e=e?e.n:t._t._f)){t._t=undefined;return i(1)}if(r=="keys")return i(0,e.k);if(r=="values")return i(0,e.v);return i(0,[e.k,e.v])},r?"entries":"values",!r,true);a(e)}}},function(t,r,e){"use strict";var v=e(0);var h=e(3);var l=e(48);var p=e(16);var d=e(9);var y=e(32);var w=e(14);var x=e(33);var g=e(4);var _=e(18);var m=e(6).f;var b=e(85)(0);var S=e(8);t.exports=function(e,t,r,n,i,a){var o=v[e];var u=o;var s=i?"set":"add";var f=u&&u.prototype;var c={};if(!S||typeof u!="function"||!(a||f.forEach&&!p(function(){(new u).entries().next()}))){u=n.getConstructor(t,e,i,s);y(u.prototype,r);l.NEED=true}else{u=t(function(t,r){x(t,u,e,"_c");t._c=new o;if(r!=undefined)w(r,i,t[s],t)});b("add,clear,delete,forEach,get,has,set,keys,values,entries,toJSON".split(","),function(n){var i=n=="add"||n=="set";if(n in f&&!(a&&n=="clear"))d(u.prototype,n,function(t,r){x(this,u,n);if(!i&&a&&!g(t))return n=="get"?undefined:false;var e=this._c[n](t===0?0:t,r);return i?this:e})});a||m(u.prototype,"size",{get:function(){return this._c.size}})}_(u,e);c[e]=u;h(h.G+h.W+h.F,c);if(!a)n.setStrong(u,e,i);return u}},function(t,r,e){var g=e(5);var _=e(36);var m=e(29);var b=e(17);var n=e(86);t.exports=function(v,t){var h=v==1;var l=v==2;var p=v==3;var d=v==4;var y=v==6;var w=v==5||y;var x=t||n;return function(t,r,e){var n=m(t);var i=_(n);var a=g(r,e,3);var o=b(i.length);var u=0;var s=h?x(t,o):l?x(t,0):undefined;var f,c;for(;o>u;u++)if(w||u in i){f=i[u];c=a(f,u,n);if(v){if(h)s[u]=c;else if(c)switch(v){case 3:return true;case 5:return f;case 6:return u;case 2:s.push(f)}else if(d)return false}}return y?-1:p||d?d:s}}},function(t,r,e){var n=e(87);t.exports=function(t,r){return new(n(t))(r)}},function(t,r,e){var n=e(4);var i=e(88);var a=e(1)("species");t.exports=function(t){var r;if(i(t)){r=t.constructor;if(typeof r=="function"&&(r===Array||i(r.prototype)))r=undefined;if(n(r)){r=r[a];if(r===null)r=undefined}}return r===undefined?Array:r}},function(t,r,e){var n=e(11);t.exports=Array.isArray||function t(r){return n(r)=="Array"}},function(t,r,e){var n=e(3);n(n.P+n.R,"Set",{toJSON:e(90)("Set")})},function(t,r,e){var n=e(31);var i=e(91);t.exports=function(r){return function t(){if(n(this)!=r)throw TypeError(r+"#toJSON isn't generic");return i(this)}}},function(t,r,e){var n=e(14);t.exports=function(t,r){var e=[];n(t,false,e.push,e,r);return e}},function(t,r,e){e(93)("Set")},function(t,r,e){"use strict";var n=e(3);t.exports=function(t){n(n.S,t,{of:function t(){var r=arguments.length;var e=new Array(r);while(r--)e[r]=arguments[r];return new this(e)}})}},function(t,r,e){e(95)("Set")},function(t,r,e){"use strict";var n=e(3);var u=e(12);var s=e(5);var f=e(14);t.exports=function(t){n(n.S,t,{from:function t(r){var e=arguments[1];var n,i,a,o;u(this);n=e!==undefined;if(n)u(e);if(r==undefined)return new this;i=[];if(n){a=0;o=s(e,arguments[2],2);f(r,false,function(t){i.push(o(t,a++))})}else{f(r,false,i.push,i)}return new this(i)}})}},function(t,r,e){t.exports={default:e(97),__esModule:true}},function(t,r,e){e(46);e(19);e(20);e(98);e(102);e(103);t.exports=e(2).Promise},function(L,G,e){"use strict";var n=e(23);var o=e(0);var i=e(5);var t=e(31);var r=e(3);var a=e(4);var u=e(12);var s=e(33);var f=e(14);var c=e(50);var v=e(51).set;var h=e(100)();var l=e(34);var p=e(52);var d=e(101);var y=e(53);var w="Promise";var x=o.TypeError;var g=o.process;var _=g&&g.versions;var m=_&&_.v8||"";var b=o[w];var S=t(g)=="process";var M=function(){};var F,V,P,k;var T=V=l.f;var O=!!function(){try{var t=b.resolve(1);var r=(t.constructor={})[e(1)("species")]=function(t){t(M,M)};return(S||typeof PromiseRejectionEvent=="function")&&t.then(M)instanceof r&&m.indexOf("6.6")!==0&&d.indexOf("Chrome/66")===-1}catch(t){}}();var E=function(t){var r;return a(t)&&typeof(r=t.then)=="function"?r:false};var j=function(c,e){if(c._n)return;c._n=true;var n=c._c;h(function(){var s=c._v;var f=c._s==1;var t=0;var r=function(t){var r=f?t.ok:t.fail;var e=t.resolve;var n=t.reject;var i=t.domain;var a,o,u;try{if(r){if(!f){if(c._h==2)C(c);c._h=1}if(r===true)a=s;else{if(i)i.enter();a=r(s);if(i){i.exit();u=true}}if(a===t.promise){n(x("Promise-chain cycle"))}else if(o=E(a)){o.call(a,e,n)}else e(a)}else n(s)}catch(t){if(i&&!u)i.exit();n(t)}};while(n.length>t)r(n[t++]);c._c=[];c._n=false;if(e&&!c._h)I(c)})};var I=function(a){v.call(o,function(){var t=a._v;var r=A(a);var e,n,i;if(r){e=p(function(){if(S){g.emit("unhandledRejection",t,a)}else if(n=o.onunhandledrejection){n({promise:a,reason:t})}else if((i=o.console)&&i.error){i.error("Unhandled promise rejection",t)}});a._h=S||A(a)?2:1}a._a=undefined;if(r&&e.e)throw e.v})};var A=function(t){return t._h!==1&&(t._a||t._c).length===0};var C=function(r){v.call(o,function(){var t;if(S){g.emit("rejectionHandled",r)}else if(t=o.onrejectionhandled){t({promise:r,reason:r._v})}})};var B=function(t){var r=this;if(r._d)return;r._d=true;r=r._w||r;r._v=t;r._s=2;if(!r._a)r._a=r._c.slice();j(r,true)};var D=function(t){var e=this;var n;if(e._d)return;e._d=true;e=e._w||e;try{if(e===t)throw x("Promise can't be resolved itself");if(n=E(t)){h(function(){var r={_w:e,_d:false};try{n.call(t,i(D,r,1),i(B,r,1))}catch(t){B.call(r,t)}})}else{e._v=t;e._s=1;j(e,false)}}catch(t){B.call({_w:e,_d:false},t)}};if(!O){b=function t(r){s(this,b,w,"_h");u(r);F.call(this);try{r(i(D,this,1),i(B,this,1))}catch(t){B.call(this,t)}};F=function t(r){this._c=[];this._a=undefined;this._s=0;this._d=false;this._v=undefined;this._h=0;this._n=false};F.prototype=e(32)(b.prototype,{then:function t(r,e){var n=T(c(this,b));n.ok=typeof r=="function"?r:true;n.fail=typeof e=="function"&&e;n.domain=S?g.domain:undefined;this._c.push(n);if(this._a)this._a.push(n);if(this._s)j(this,false);return n.promise},catch:function(t){return this.then(undefined,t)}});P=function(){var t=new F;this.promise=t;this.resolve=i(D,t,1);this.reject=i(B,t,1)};l.f=T=function(t){return t===b||t===k?new P(t):V(t)}}r(r.G+r.W+r.F*!O,{Promise:b});e(18)(b,w);e(47)(w);k=e(2)[w];r(r.S+r.F*!O,w,{reject:function t(r){var e=T(this);var n=e.reject;n(r);return e.promise}});r(r.S+r.F*(n||!O),w,{resolve:function t(r){return y(n&&this===k?b:this,r)}});r(r.S+r.F*!(O&&e(45)(function(t){b.all(t)["catch"](M)})),w,{all:function t(r){var o=this;var e=T(o);var u=e.resolve;var s=e.reject;var n=p(function(){var n=[];var i=0;var a=1;f(r,false,function(t){var r=i++;var e=false;n.push(undefined);a++;o.resolve(t).then(function(t){if(e)return;e=true;n[r]=t;--a||u(n)},s)});--a||u(n)});if(n.e)s(n.v);return e.promise},race:function t(r){var e=this;var n=T(e);var i=n.reject;var a=p(function(){f(r,false,function(t){e.resolve(t).then(n.resolve,i)})});if(a.e)i(a.v);return n.promise}})},function(t,r){t.exports=function(t,r,e){var n=e===undefined;switch(r.length){case 0:return n?t():t.call(e);case 1:return n?t(r[0]):t.call(e,r[0]);case 2:return n?t(r[0],r[1]):t.call(e,r[0],r[1]);case 3:return n?t(r[0],r[1],r[2]):t.call(e,r[0],r[1],r[2]);case 4:return n?t(r[0],r[1],r[2],r[3]):t.call(e,r[0],r[1],r[2],r[3])}return t.apply(e,r)}},function(t,r,e){var u=e(0);var s=e(51).set;var f=u.MutationObserver||u.WebKitMutationObserver;var c=u.process;var v=u.Promise;var h=e(11)(c)=="process";t.exports=function(){var e,n,i;var t=function(){var t,r;if(h&&(t=c.domain))t.exit();while(e){r=e.fn;e=e.next;try{r()}catch(t){if(e)i();else n=undefined;throw t}}n=undefined;if(t)t.enter()};if(h){i=function(){c.nextTick(t)}}else if(f&&!(u.navigator&&u.navigator.standalone)){var r=true;var a=document.createTextNode("");new f(t).observe(a,{characterData:true});i=function(){a.data=r=!r}}else if(v&&v.resolve){var o=v.resolve(undefined);i=function(){o.then(t)}}else{i=function(){s.call(u,t)}}return function(t){var r={fn:t,next:undefined};if(n)n.next=r;if(!e){e=r;i()}n=r}}},function(t,r,e){var n=e(0);var i=n.navigator;t.exports=i&&i.userAgent||""},function(t,r,e){"use strict";var n=e(3);var i=e(2);var a=e(0);var o=e(50);var u=e(53);n(n.P+n.R,"Promise",{finally:function(r){var e=o(this,i.Promise||a.Promise);var t=typeof r=="function";return this.then(t?function(t){return u(e,r()).then(function(){return t})}:r,t?function(t){return u(e,r()).then(function(){throw t})}:r)}})},function(t,r,e){"use strict";var n=e(3);var i=e(34);var a=e(52);n(n.S,"Promise",{try:function(t){var r=i.f(this);var e=a(t);(e.e?r.reject:r.resolve)(e.v);return r.promise}})},function(t,r,e){"use strict";r.__esModule=true;var n=e(42);var i=a(n);function a(t){return t&&t.__esModule?t:{default:t}}r.default=function(t){if(Array.isArray(t)){for(var r=0,e=Array(t.length);r<t.length;r++){e[r]=t[r]}return e}else{return(0,i.default)(t)}}},function(t,r,e){t.exports.VectorTile=e(106);t.exports.VectorTileFeature=e(55);t.exports.VectorTileLayer=e(54)},function(t,r,e){"use strict";var i=e(54);t.exports=n;function n(t,r){this.layers=t.readFields(a,{},r)}function a(t,r,e){if(t===3){var n=new i(e,e.readVarint()+e.pos);if(n.length)r[n.name]=n}}},function(t,r,e){"use strict";t.exports=n;function n(t,r){this.x=t;this.y=r}n.prototype={clone:function(){return new n(this.x,this.y)},add:function(t){return this.clone()._add(t)},sub:function(t){return this.clone()._sub(t)},multByPoint:function(t){return this.clone()._multByPoint(t)},divByPoint:function(t){return this.clone()._divByPoint(t)},mult:function(t){return this.clone()._mult(t)},div:function(t){return this.clone()._div(t)},rotate:function(t){return this.clone()._rotate(t)},rotateAround:function(t,r){return this.clone()._rotateAround(t,r)},matMult:function(t){return this.clone()._matMult(t)},unit:function(){return this.clone()._unit()},perp:function(){return this.clone()._perp()},round:function(){return this.clone()._round()},mag:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},equals:function(t){return this.x===t.x&&this.y===t.y},dist:function(t){return Math.sqrt(this.distSqr(t))},distSqr:function(t){var r=t.x-this.x,e=t.y-this.y;return r*r+e*e},angle:function(){return Math.atan2(this.y,this.x)},angleTo:function(t){return Math.atan2(this.y-t.y,this.x-t.x)},angleWith:function(t){return this.angleWithSep(t.x,t.y)},angleWithSep:function(t,r){return Math.atan2(this.x*r-this.y*t,this.x*t+this.y*r)},_matMult:function(t){var r=t[0]*this.x+t[1]*this.y,e=t[2]*this.x+t[3]*this.y;this.x=r;this.y=e;return this},_add:function(t){this.x+=t.x;this.y+=t.y;return this},_sub:function(t){this.x-=t.x;this.y-=t.y;return this},_mult:function(t){this.x*=t;this.y*=t;return this},_div:function(t){this.x/=t;this.y/=t;return this},_multByPoint:function(t){this.x*=t.x;this.y*=t.y;return this},_divByPoint:function(t){this.x/=t.x;this.y/=t.y;return this},_unit:function(){this._div(this.mag());return this},_perp:function(){var t=this.y;this.y=this.x;this.x=-t;return this},_rotate:function(t){var r=Math.cos(t),e=Math.sin(t),n=r*this.x-e*this.y,i=e*this.x+r*this.y;this.x=n;this.y=i;return this},_rotateAround:function(t,r){var e=Math.cos(t),n=Math.sin(t),i=r.x+e*(this.x-r.x)-n*(this.y-r.y),a=r.y+n*(this.x-r.x)+e*(this.y-r.y);this.x=i;this.y=a;return this},_round:function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this}};n.convert=function(t){if(t instanceof n){return t}if(Array.isArray(t)){return new n(t[0],t[1])}return t}},function(t,r,e){"use strict";t.exports=i;var n=e(109);function i(t){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0);this.pos=0;this.type=0;this.length=this.buf.length}i.Varint=0;i.Fixed64=1;i.Bytes=2;i.Fixed32=5;var a=(1<<16)*(1<<16),o=1/a;var u=12;var s=typeof TextDecoder==="undefined"?null:new TextDecoder("utf8");i.prototype={destroy:function(){this.buf=null},readFields:function(t,r,e){e=e||this.length;while(this.pos<e){var n=this.readVarint(),i=n>>3,a=this.pos;this.type=n&7;t(i,r,this);if(this.pos===a)this.skip(n)}return r},readMessage:function(t,r){return this.readFields(t,r,this.readVarint()+this.pos)},readFixed32:function(){var t=F(this.buf,this.pos);this.pos+=4;return t},readSFixed32:function(){var t=P(this.buf,this.pos);this.pos+=4;return t},readFixed64:function(){var t=F(this.buf,this.pos)+F(this.buf,this.pos+4)*a;this.pos+=8;return t},readSFixed64:function(){var t=F(this.buf,this.pos)+P(this.buf,this.pos+4)*a;this.pos+=8;return t},readFloat:function(){var t=n.read(this.buf,this.pos,true,23,4);this.pos+=4;return t},readDouble:function(){var t=n.read(this.buf,this.pos,true,52,8);this.pos+=8;return t},readVarint:function(t){var r=this.buf,e,n;n=r[this.pos++];e=n&127;if(n<128)return e;n=r[this.pos++];e|=(n&127)<<7;if(n<128)return e;n=r[this.pos++];e|=(n&127)<<14;if(n<128)return e;n=r[this.pos++];e|=(n&127)<<21;if(n<128)return e;n=r[this.pos];e|=(n&15)<<28;return f(e,t,this)},readVarint64:function(){return this.readVarint(true)},readSVarint:function(){var t=this.readVarint();return t%2===1?(t+1)/-2:t/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var t=this.readVarint()+this.pos;var r=this.pos;this.pos=t;if(t-r>=u&&s){return T(this.buf,r,t)}return k(this.buf,r,t)},readBytes:function(){var t=this.readVarint()+this.pos,r=this.buf.subarray(this.pos,t);this.pos=t;return r},readPackedVarint:function(t,r){if(this.type!==i.Bytes)return t.push(this.readVarint(r));var e=c(this);t=t||[];while(this.pos<e)t.push(this.readVarint(r));return t},readPackedSVarint:function(t){if(this.type!==i.Bytes)return t.push(this.readSVarint());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readSVarint());return t},readPackedBoolean:function(t){if(this.type!==i.Bytes)return t.push(this.readBoolean());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readBoolean());return t},readPackedFloat:function(t){if(this.type!==i.Bytes)return t.push(this.readFloat());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readFloat());return t},readPackedDouble:function(t){if(this.type!==i.Bytes)return t.push(this.readDouble());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readDouble());return t},readPackedFixed32:function(t){if(this.type!==i.Bytes)return t.push(this.readFixed32());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readFixed32());return t},readPackedSFixed32:function(t){if(this.type!==i.Bytes)return t.push(this.readSFixed32());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readSFixed32());return t},readPackedFixed64:function(t){if(this.type!==i.Bytes)return t.push(this.readFixed64());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readFixed64());return t},readPackedSFixed64:function(t){if(this.type!==i.Bytes)return t.push(this.readSFixed64());var r=c(this);t=t||[];while(this.pos<r)t.push(this.readSFixed64());return t},skip:function(t){var r=t&7;if(r===i.Varint)while(this.buf[this.pos++]>127){}else if(r===i.Bytes)this.pos=this.readVarint()+this.pos;else if(r===i.Fixed32)this.pos+=4;else if(r===i.Fixed64)this.pos+=8;else throw new Error("Unimplemented type: "+r)},writeTag:function(t,r){this.writeVarint(t<<3|r)},realloc:function(t){var r=this.length||16;while(r<this.pos+t)r*=2;if(r!==this.length){var e=new Uint8Array(r);e.set(this.buf);this.buf=e;this.length=r}},finish:function(){this.length=this.pos;this.pos=0;return this.buf.subarray(0,this.length)},writeFixed32:function(t){this.realloc(4);V(this.buf,t,this.pos);this.pos+=4},writeSFixed32:function(t){this.realloc(4);V(this.buf,t,this.pos);this.pos+=4},writeFixed64:function(t){this.realloc(8);V(this.buf,t&-1,this.pos);V(this.buf,Math.floor(t*o),this.pos+4);this.pos+=8},writeSFixed64:function(t){this.realloc(8);V(this.buf,t&-1,this.pos);V(this.buf,Math.floor(t*o),this.pos+4);this.pos+=8},writeVarint:function(t){t=+t||0;if(t>268435455||t<0){h(t,this);return}this.realloc(4);this.buf[this.pos++]=t&127|(t>127?128:0);if(t<=127)return;this.buf[this.pos++]=(t>>>=7)&127|(t>127?128:0);if(t<=127)return;this.buf[this.pos++]=(t>>>=7)&127|(t>127?128:0);if(t<=127)return;this.buf[this.pos++]=t>>>7&127},writeSVarint:function(t){this.writeVarint(t<0?-t*2-1:t*2)},writeBoolean:function(t){this.writeVarint(Boolean(t))},writeString:function(t){t=String(t);this.realloc(t.length*4);this.pos++;var r=this.pos;this.pos=O(this.buf,t,this.pos);var e=this.pos-r;if(e>=128)d(r,e,this);this.pos=r-1;this.writeVarint(e);this.pos+=e},writeFloat:function(t){this.realloc(4);n.write(this.buf,t,this.pos,true,23,4);this.pos+=4},writeDouble:function(t){this.realloc(8);n.write(this.buf,t,this.pos,true,52,8);this.pos+=8},writeBytes:function(t){var r=t.length;this.writeVarint(r);this.realloc(r);for(var e=0;e<r;e++)this.buf[this.pos++]=t[e]},writeRawMessage:function(t,r){this.pos++;var e=this.pos;t(r,this);var n=this.pos-e;if(n>=128)d(e,n,this);this.pos=e-1;this.writeVarint(n);this.pos+=n},writeMessage:function(t,r,e){this.writeTag(t,i.Bytes);this.writeRawMessage(r,e)},writePackedVarint:function(t,r){if(r.length)this.writeMessage(t,y,r)},writePackedSVarint:function(t,r){if(r.length)this.writeMessage(t,w,r)},writePackedBoolean:function(t,r){if(r.length)this.writeMessage(t,_,r)},writePackedFloat:function(t,r){if(r.length)this.writeMessage(t,x,r)},writePackedDouble:function(t,r){if(r.length)this.writeMessage(t,g,r)},writePackedFixed32:function(t,r){if(r.length)this.writeMessage(t,m,r)},writePackedSFixed32:function(t,r){if(r.length)this.writeMessage(t,b,r)},writePackedFixed64:function(t,r){if(r.length)this.writeMessage(t,S,r)},writePackedSFixed64:function(t,r){if(r.length)this.writeMessage(t,M,r)},writeBytesField:function(t,r){this.writeTag(t,i.Bytes);this.writeBytes(r)},writeFixed32Field:function(t,r){this.writeTag(t,i.Fixed32);this.writeFixed32(r)},writeSFixed32Field:function(t,r){this.writeTag(t,i.Fixed32);this.writeSFixed32(r)},writeFixed64Field:function(t,r){this.writeTag(t,i.Fixed64);this.writeFixed64(r)},writeSFixed64Field:function(t,r){this.writeTag(t,i.Fixed64);this.writeSFixed64(r)},writeVarintField:function(t,r){this.writeTag(t,i.Varint);this.writeVarint(r)},writeSVarintField:function(t,r){this.writeTag(t,i.Varint);this.writeSVarint(r)},writeStringField:function(t,r){this.writeTag(t,i.Bytes);this.writeString(r)},writeFloatField:function(t,r){this.writeTag(t,i.Fixed32);this.writeFloat(r)},writeDoubleField:function(t,r){this.writeTag(t,i.Fixed64);this.writeDouble(r)},writeBooleanField:function(t,r){this.writeVarintField(t,Boolean(r))}};function f(t,r,e){var n=e.buf,i,a;a=n[e.pos++];i=(a&112)>>4;if(a<128)return v(t,i,r);a=n[e.pos++];i|=(a&127)<<3;if(a<128)return v(t,i,r);a=n[e.pos++];i|=(a&127)<<10;if(a<128)return v(t,i,r);a=n[e.pos++];i|=(a&127)<<17;if(a<128)return v(t,i,r);a=n[e.pos++];i|=(a&127)<<24;if(a<128)return v(t,i,r);a=n[e.pos++];i|=(a&1)<<31;if(a<128)return v(t,i,r);throw new Error("Expected varint not more than 10 bytes")}function c(t){return t.type===i.Bytes?t.readVarint()+t.pos:t.pos+1}function v(t,r,e){if(e){return r*4294967296+(t>>>0)}return(r>>>0)*4294967296+(t>>>0)}function h(t,r){var e,n;if(t>=0){e=t%4294967296|0;n=t/4294967296|0}else{e=~(-t%4294967296);n=~(-t/4294967296);if(e^4294967295){e=e+1|0}else{e=0;n=n+1|0}}if(t>=0x10000000000000000||t<-0x10000000000000000){throw new Error("Given varint doesn't fit into 10 bytes")}r.realloc(10);l(e,n,r);p(n,r)}function l(t,r,e){e.buf[e.pos++]=t&127|128;t>>>=7;e.buf[e.pos++]=t&127|128;t>>>=7;e.buf[e.pos++]=t&127|128;t>>>=7;e.buf[e.pos++]=t&127|128;t>>>=7;e.buf[e.pos]=t&127}function p(t,r){var e=(t&7)<<4;r.buf[r.pos++]|=e|((t>>>=3)?128:0);if(!t)return;r.buf[r.pos++]=t&127|((t>>>=7)?128:0);if(!t)return;r.buf[r.pos++]=t&127|((t>>>=7)?128:0);if(!t)return;r.buf[r.pos++]=t&127|((t>>>=7)?128:0);if(!t)return;r.buf[r.pos++]=t&127|((t>>>=7)?128:0);if(!t)return;r.buf[r.pos++]=t&127}function d(t,r,e){var n=r<=16383?1:r<=2097151?2:r<=268435455?3:Math.floor(Math.log(r)/(Math.LN2*7));e.realloc(n);for(var i=e.pos-1;i>=t;i--)e.buf[i+n]=e.buf[i]}function y(t,r){for(var e=0;e<t.length;e++)r.writeVarint(t[e])}function w(t,r){for(var e=0;e<t.length;e++)r.writeSVarint(t[e])}function x(t,r){for(var e=0;e<t.length;e++)r.writeFloat(t[e])}function g(t,r){for(var e=0;e<t.length;e++)r.writeDouble(t[e])}function _(t,r){for(var e=0;e<t.length;e++)r.writeBoolean(t[e])}function m(t,r){for(var e=0;e<t.length;e++)r.writeFixed32(t[e])}function b(t,r){for(var e=0;e<t.length;e++)r.writeSFixed32(t[e])}function S(t,r){for(var e=0;e<t.length;e++)r.writeFixed64(t[e])}function M(t,r){for(var e=0;e<t.length;e++)r.writeSFixed64(t[e])}function F(t,r){return(t[r]|t[r+1]<<8|t[r+2]<<16)+t[r+3]*16777216}function V(t,r,e){t[e]=r;t[e+1]=r>>>8;t[e+2]=r>>>16;t[e+3]=r>>>24}function P(t,r){return(t[r]|t[r+1]<<8|t[r+2]<<16)+(t[r+3]<<24)}function k(t,r,e){var n="";var i=r;while(i<e){var a=t[i];var o=null;var u=a>239?4:a>223?3:a>191?2:1;if(i+u>e)break;var s,f,c;if(u===1){if(a<128){o=a}}else if(u===2){s=t[i+1];if((s&192)===128){o=(a&31)<<6|s&63;if(o<=127){o=null}}}else if(u===3){s=t[i+1];f=t[i+2];if((s&192)===128&&(f&192)===128){o=(a&15)<<12|(s&63)<<6|f&63;if(o<=2047||o>=55296&&o<=57343){o=null}}}else if(u===4){s=t[i+1];f=t[i+2];c=t[i+3];if((s&192)===128&&(f&192)===128&&(c&192)===128){o=(a&15)<<18|(s&63)<<12|(f&63)<<6|c&63;if(o<=65535||o>=1114112){o=null}}}if(o===null){o=65533;u=1}else if(o>65535){o-=65536;n+=String.fromCharCode(o>>>10&1023|55296);o=56320|o&1023}n+=String.fromCharCode(o);i+=u}return n}function T(t,r,e){return s.decode(t.subarray(r,e))}function O(t,r,e){for(var n=0,i,a;n<r.length;n++){i=r.charCodeAt(n);if(i>55295&&i<57344){if(a){if(i<56320){t[e++]=239;t[e++]=191;t[e++]=189;a=i;continue}else{i=a-55296<<10|i-56320|65536;a=null}}else{if(i>56319||n+1===r.length){t[e++]=239;t[e++]=191;t[e++]=189}else{a=i}continue}}else if(a){t[e++]=239;t[e++]=191;t[e++]=189;a=null}if(i<128){t[e++]=i}else{if(i<2048){t[e++]=i>>6|192}else{if(i<65536){t[e++]=i>>12|224}else{t[e++]=i>>18|240;t[e++]=i>>12&63|128}t[e++]=i>>6&63|128}t[e++]=i&63|128}}return e}},function(t,r){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
r.read=function(t,r,e,n,i){var a,o;var u=i*8-n-1;var s=(1<<u)-1;var f=s>>1;var c=-7;var v=e?i-1:0;var h=e?-1:1;var l=t[r+v];v+=h;a=l&(1<<-c)-1;l>>=-c;c+=u;for(;c>0;a=a*256+t[r+v],v+=h,c-=8){}o=a&(1<<-c)-1;a>>=-c;c+=n;for(;c>0;o=o*256+t[r+v],v+=h,c-=8){}if(a===0){a=1-f}else if(a===s){return o?NaN:(l?-1:1)*Infinity}else{o=o+Math.pow(2,n);a=a-f}return(l?-1:1)*o*Math.pow(2,a-n)};r.write=function(t,r,e,n,i,a){var o,u,s;var f=a*8-i-1;var c=(1<<f)-1;var v=c>>1;var h=i===23?Math.pow(2,-24)-Math.pow(2,-77):0;var l=n?0:a-1;var p=n?1:-1;var d=r<0||r===0&&1/r<0?1:0;r=Math.abs(r);if(isNaN(r)||r===Infinity){u=isNaN(r)?1:0;o=c}else{o=Math.floor(Math.log(r)/Math.LN2);if(r*(s=Math.pow(2,-o))<1){o--;s*=2}if(o+v>=1){r+=h/s}else{r+=h*Math.pow(2,1-v)}if(r*s>=2){o++;s/=2}if(o+v>=c){u=0;o=c}else if(o+v>=1){u=(r*s-1)*Math.pow(2,i);o=o+v}else{u=r*Math.pow(2,v-1)*Math.pow(2,i);o=0}}for(;i>=8;t[e+l]=u&255,l+=p,u/=256,i-=8){}o=o<<i|u;f+=i;for(;f>0;t[e+l]=o&255,l+=p,o/=256,f-=8){}t[e+l-p]|=d*128}}]);