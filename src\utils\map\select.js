class Select {
    constructor(_viewer) {
        this.viewer = _viewer
        this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
        this.selectedEntity = new Cesium.Entity();
        var fragmentShaderSource =
            "uniform sampler2D colorTexture;\n" +
            "varying vec2 v_textureCoordinates;\n" +
            "uniform vec4 highlight;\n" +
            "void main() {\n" +
            "    vec4 color = texture2D(colorTexture, v_textureCoordinates);\n" +
            "    if (czm_selected()) {\n" +
            "        vec3 highlighted = highlight.a * highlight.rgb + (1.0 - highlight.a) * color.rgb;\n" +
            "        gl_FragColor = vec4(highlighted, 1.0);\n" +
            "    } else { \n" +
            "        gl_FragColor = color;\n" +
            "    }\n" +
            "}\n";
        this.stage = this.viewer.scene.postProcessStages.add(
            new Cesium.PostProcessStage({
                fragmentShader: fragmentShaderSource,
                uniforms: {
                    highlight: function () {
                        return new Cesium.Color(1.0, 1.0, 0.0, 1.0);
                    },
                },
            })
        );
        this.stage.selected = [];
    }

    start() {  
        this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        this.handler.setInputAction((movement) => {
            var pickedObject = viewer.scene.pick(movement.position);
            if (Cesium.defined(pickedObject)) {
                //选中要素
                this.stage.selected = [pickedObject];
               
            } else {
                this.stage.selected = [];
            }
            this.viewer.scene.forceRender();
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }
   
    cancel() {
        this.stage.selected = [];
        this.viewer.scene.forceRender();
        this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

}

export default Select