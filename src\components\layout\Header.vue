<template>
  <div class="head">
    <div class="head_ttitle">智能矿山地质保障平台</div>
    <div class="head_left">
      <div
        :class="headerIndex == 1 ? 'head_text head_active' : 'head_text'"
        @click="GoTo(0)"
      >
        <span class="head_text_span">地质三维</span>
      </div>
      <div
        :class="headerIndex == 2 ? 'head_text head_active' : 'head_text'"
        @click="GoTo(1)"
      >
        <span class="head_text_span">透明掘进</span>
      </div>
      <div
        :class="headerIndex == 3 ? 'head_text head_active' : 'head_text'"
        @click="GoTo(2)"
      >
        <span class="head_text_span">透明回采</span>
      </div>
    </div>
    <div class="head_right">
      <div
        :class="headerIndex == 4 ? 'head_text head_active' : 'head_text'"
        @click="GoTo(3)"
      >
        <span class="head_text_span">地质分析</span>
      </div>
      <div
        :class="headerIndex == 5 ? 'head_text head_active' : 'head_text'"
        @click="GoTo(4)"
      >
        <span class="head_text_span">地质推演</span>
      </div>
      <div
        :class="headerIndex == 6 ? 'head_text head_active' : 'head_text'"
        @click="GoTo(5)"
      >
        <span class="head_text_span">实时监测</span>
      </div>
    </div>
    <img :src="show?hideImg:openImg" class="head_icon" @click="handelPanel()" alt="收缩两侧" title="收缩两侧面板"/>
  </div>
</template>

<script setup>
import { onBeforeMount, onMounted, ref, watch } from "vue";
import router from "../../router";
import { useHomeStore } from "@/store/home";
import { usePanelStore } from "@/store/panel";
import { storeToRefs } from "pinia";
import hideImg from "@/assets/img/common/hide.png";
import openImg from "@/assets/img/common/open.png";
const homeStore = useHomeStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { headerIndex } = storeToRefs(homeStore);
//生命周期
onMounted(() => {});

const GoTo = (name) => {
  const rouerLink = ["/index/Home", "/index/Tunnel", "/index/Stoping",'/index/Analyse','/index/Deduction',"/index/Monitoring"];
  router.push(rouerLink[name]);
};
const panelStore = usePanelStore();
const {show}=storeToRefs(panelStore)
const handelPanel=()=>{
  console.log('收缩展开按钮被点击，当前状态:', show.value);
  //左右两侧案板状态
  panelStore.show = !show.value;
  console.log('切换后状态:', panelStore.show);
}

</script>
<style lang="less" scoped>
.head {
  width: 100%;
  height: 60px;
  position: relative;
  // padding: 0 47px;
  display: flex;
  justify-content: space-between;
  background-image: url("../../assets/img/home/<USER>");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .head_ttitle {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    color: rgb(255, 255, 255);
    font-family: FZDeSaiHeiS 512B;
    font-size: 29px;
    font-weight: undefined;
    line-height: 43px;
    letter-spacing: 2.319999933242798px;
    text-align: center;
  }
  .head_left {
    padding-left: 143px;
    display: flex;
    justify-content: space-between;
    // box-sizing: border-box;
    .head_active {
      color: rgb(75, 201, 255);
      border-bottom: 2px solid #42E6FF;
      // background-image: url("../../assets/img/home/<USER>");
      // background-position: center center;
      // background-size: 100% 100%;
      // background-repeat: no-repeat;
    }
  }
  .head_right {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding-right: 143px;
    .head_text {
      margin-right: 36px;
    }
    .head_active {
      color: rgb(75, 201, 255);
      border-bottom: 2px solid #42E6FF;
    }
  }
  .head_text {
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: undefined;
    line-height: 30px;
    letter-spacing: 0px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    cursor: pointer;
    color: #ffffff;
    margin-right: 35px;
    .head_text_span {
      font-size: 20px;
     
    }
  }
  .head_icon{
    position: absolute;
    right: 10px;
    top: 24px;
    width: 22px;
    height: 22px;
    cursor: pointer;
    z-index: 1000;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}
</style>
>
