function Geowin3DAPP(e,i,t){this.elementId=0,this.layers=[],i=Geowin3D.defaultValue(i,{}),this.layers=[],this._viewer=null;var n=Geowin3D.defaultValue(i.defaultViewerBounds,[80,5,135,55]);Geowin3D.Camera.DEFAULT_VIEW_RECTANGLE=Geowin3D.Rectangle.fromDegrees(n[0],n[1],n[2],n[3]);var a=Geowin3D.defaultValue(i.tileName,"sate");Geowin3D.defaultValue(i.demName,"dem");i.imageryProvider=Geowin3D.defaultValue(i.defaultImageProvider,new Geowin3D.GeowinMapImageryProvider({tilename:a})),this._viewer=Geowin3D.GwApp.createDefaultViewer(e,i,t)}Geowin3DAPP.prototype.getViewer=function(){return this._viewer},Geowin3DAPP.prototype.addDefaultTerrain=function(e){if(null==this.terrainManager)return this.terrainManager=new Geowin3D.GwTools.GwTerrain(this._viewer),void this.terrainManager.setTerrain(e,"geowindem",[]);this.terrainManager.visible=!0},Geowin3DAPP.prototype.setImageProvider=function(e){this._viewer.scene.imageryLayers.removeAll(),this._viewer.imageryLayers.addImageryProvider(new Geowin3D.GeowinMapImageryProvider({tilename:e}))},Geowin3DAPP.prototype.removeTerrain=function(){this.terrainManager.visible=!1},Geowin3DAPP.prototype.show=function(e){null!=e.show&&(e.show=!0)},Geowin3DAPP.prototype.hide=function(e){e.show=!1},Geowin3DAPP.prototype.load3DTileset=function(e){e.maximumMemoryUsage=1024,e.maximumAbsoluteMemoryUsage=2048;var i=new Geowin3D.Cesium3DTileset(e);this._viewer.scene.primitives.add(i);var t=this;return i.readyPromise.then(function(e){t._viewer.flyTo(e)}),this.layers.push(i),i},Geowin3DAPP.prototype.remove3DTileset=function(e){this._viewer.scene.primitives.remove(e)},Geowin3DAPP.prototype.addImageLayer=function(e){return null!=e.tileName?this.getViewer().imageryLayers.addImageryProvider(new Geowin3D.GeowinMapImageryProvider({tilename:e.tileName})):null!=e.url?this.getViewer().imageryLayers.addImageryProvider(new Geowin3D.UrlTemplateImageryProvider({url:e.url})):null!=e.wmtsUrl?this.getViewer().imageryLayers.addImageryProvider(new Geowin3D.WebMapTileServiceImageryProvider({url:e.url})):null},Geowin3DAPP.prototype.removeImageLayer=function(e){this._viewer.imageryLayers.remove(e)},Geowin3DAPP.prototype.setView=function(e){this._viewer.camera.flyTo({destination:Geowin3D.Rectangle.fromDegrees(e[0],e[1],e[2],e[3])})},Geowin3DAPP.prototype.flyToByPosition=function(e){6==e.length?Geowin3D.GwCamera.flyToByCameraInfo(this._viewer.camera,e):Geowin3D.GwCamera.flyToByPosition(this._viewer.camera,e)},Geowin3DAPP.prototype.addEntity=function(e,i,t){return null==this.moduleManager&&(this.moduleManager=new Geowin3D.GwModel.GwModelManager(this._viewer)),this.moduleManager.add(e,i,t)},Geowin3DAPP.prototype.addPopupInfo=function(e,i,t,n){this.closePopupInfo(),this.elementId++,null==e&&(e="信息");var a=document.createElement("div");a.id="_div__"+this.elementId,a.className="popup";var o=document.createElement("div");o.className="popup-tit";var r=document.createElement("div");r.className="popup-tit-text",r.innerHTML=e,o.append(r);var s=document.createElement("div");s.className="popup-button",s.innerHTML="x",o.append(s);var l=this;s.addEventListener("click",function(){l.closePopupInfo()},!1),a.append(o);var p=document.createElement("div");p.className="bx-popup-content-ctn";var u=document.createElement("div");u.className="bx-popup-content",p.append(u),p.innerHTML=i,a.append(p);var d=document.createElement("div");d.className="bx-popup-tip-container";var w=document.createElement("div");w.className="bx-popup-tip",w.append(u),d.append(w),a.append(d),this._viewer.container.append(a),this.popup=a;var m={};return m.position=t,m.divId=a.id,m.offset=n,m.pixelOffset={x:-a.offsetWidth/2,y:-a.offsetHeight-17},this.addEntity("DivLabel",m)},Geowin3DAPP.prototype.closePopupInfo=function(){null!=this.popup&&(this.popup.remove(),this.popup=null)},Geowin3DAPP.prototype.addMarker=function(e,i){return i.position=e,this.addEntity("Label",i)},Geowin3DAPP.prototype.addPoint=function(e,i){return i.position=e,this.addEntity("Point",i)},Geowin3DAPP.prototype.addPolyline=function(e,i){return i.pts=e,this.addEntity("Polyline",i)},Geowin3DAPP.prototype.removeEntity=function(e){this.moduleManager.remove(e)},Geowin3DAPP.prototype.measureLength=function(){null==this._AnalysisManager&&(this._AnalysisManager=new Geowin3D.GwMicroApp.GwAnalysisManager(this._viewer)),this._AnalysisManager.start("SEGMENTS_DISTANCE"),this._AnalysisManager.setSumPointMode(!0)},Geowin3DAPP.prototype.measureArea=function(){null==this._AnalysisManager&&(this._AnalysisManager=new Geowin3D.GwMicroApp.GwAnalysisManager(this._viewer)),this._AnalysisManager.start("AREA")},Geowin3DAPP.prototype.measureHeight=function(){null==this._AnalysisManager&&(this._AnalysisManager=new Geowin3D.GwMicroApp.GwAnalysisManager(this._viewer)),this._AnalysisManager.start("HEIGHT")},Geowin3DAPP.prototype.measureAngle=function(){null==this._AnalysisManager&&(this._AnalysisManager=new Geowin3D.GwMicroApp.GwAnalysisManager(this._viewer)),this._AnalysisManager.start("ANGLE")},Geowin3DAPP.prototype.closeMeasure=function(){null==this._AnalysisManager&&(this._AnalysisManager=new Geowin3D.GwMicroApp.GwAnalysisManager(this._viewer)),this._AnalysisManager.removeAll(),this._AnalysisManager.cancel()},Geowin3DAPP.prototype.loadEntity=function(e,i,t,n){if("KML"==i.toUpperCase()||"TOPOJSON"==i.toUpperCase())return t=Geowin3D.defaultValue(t,{camera:this.getViewer().scene.camera,canvas:this.getViewer().scene.canvas,clampToGround:!0}),this.getViewer().dataSources.add(Geowin3D.KmlDataSource.load(e,t)),{name:e,type:i};if("VEDIO"==i.toUpperCase())return t.position=Geowin3D.Cartographic.fromDegrees(n.position[0],n.position[1],t.altitude),new Geowin3D.GwTools.GwVideo(e,this._viewer,t);null==this.moduleManager&&(this.moduleManager=new Geowin3D.GwModel.GwModelManager(this._viewer)),t=this.getEntityOption(t,i,e,n);let a=this.moduleManager.add(i,t);return"GLTF"===i&&a._model.readyPromise.then(function(e){e.activeAnimations.addAll({loop:Geowin3D.ModelAnimationLoop.REPEAT})}),a},Geowin3DAPP.prototype.removeEntity=function(e){if(!e.type||"KML"!=e.type.toUpperCase()&&"TOPOJSON"!=e.type.toUpperCase())e.type&&"VEDIO"==e.type.toUpperCase()?video.destroy():null!=this.moduleManager&&this.moduleManager.remove(e);else for(var i=this.getViewer().dataSources.length-1;i>=0;i--)-1!=this.getViewer().dataSources.get(i).name.indexOf(e.name)&&this.getViewer().remove(this.getViewer().dataSources.get(i))},Geowin3DAPP.prototype.getEntityOption=function(e,i,t,n){return e=Geowin3D.defaultValue(e,{}),"GeoJson"==i?e=_.assign({disableDepthTestDistance:Number.POSITIVE_INFINITY,jsonURL:t},e):"ShapeFile"==i?e=_.assign({disableDepthTestDistance:Number.POSITIVE_INFINITY,shpUrl:t},e):"GLTF"==i&&(e=_.assign({url:t,position:n.position},e)),e},Geowin3DAPP.prototype.getEntityOption=function(e,i,t,n){return e=Geowin3D.defaultValue(e,{}),"GeoJson"==i?e=_.assign({disableDepthTestDistance:Number.POSITIVE_INFINITY,jsonURL:t},e):"ShapeFile"==i?e=_.assign({disableDepthTestDistance:Number.POSITIVE_INFINITY,shpUrl:t},e):"GLTF"==i&&(e=_.assign({url:t,position:n.position},e)),e},Geowin3DAPP.prototype.addHeatMapLayer=function(e,i,t){var n=this;CDownload(e,function(e){for(var t=CParseObj(e),a=[],o=[180,90,-180,-190],r=0;r<t.length;r++){var s=parseFloat(t[r][i.latField]),l=parseFloat(t[r][i.lngField]);a.push({x:l,y:s,value:parseFloat(t[r][i.valueField])}),o[0]=Math.min(o[0],l),o[1]=Math.min(o[1],s),o[2]=Math.max(o[2],l),o[3]=Math.max(o[3],s)}Geowin3DHeatmap.create(n._viewer,{west:o[0],south:o[1],east:o[2],north:o[3]},{radius:i.radius,maxOpacity:i.opacity,blur:i.blur}).set3DData(0,i.maxValue,a).then(function(e){n._viewer.scene.primitives.add(e)}),n._viewer.zoomTo(n._viewer.entities)})};