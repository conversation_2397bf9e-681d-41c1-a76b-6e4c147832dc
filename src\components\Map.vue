<template>
  <div class="map_box">
    <div id="EarthContainer" class="map"></div>
    <div class="subassembly">
      <router-view> </router-view>
    </div>
    <Common />
  </div>
</template>

<script setup>
// import { ElMessage } from "element-plus";
import { onBeforeMount, onMounted, onBeforeUnmount } from "vue";
import { useViwerStore } from "@/store/viewer";
import Common from "@/components/layout/Common.vue";
import Earth from "@/utils/map/earth";
import { useToolsStore } from "@/store/tools";
import threeImg from "@/assets/img/common/three.png";
import GwSlope2 from "@/utils/map/GwSlope2.js";
// import Buffer from "@/utils/map/buffer3";
// import Mesuare from "@/utils/map/mesuare";
// import Tooltip from "@/utils/map/tooltip";
// import ClipPlane from "@/utils/map/clipPlane";
// import MV from "@/utils/map/measureVolume";
import cameraInfo from "@/utils/map/getCameraInfo";
import { storeToRefs } from "pinia";
import CameraVisual from "@/utils/map/cameraVisual";
import CameraManager from "@/utils/map/cameraManager";
import { openLoading, closeLoading } from "../utils/loading";
import { useLoadStore } from "@/store/load";
import errorHandler from "@/utils/errorHandler";
const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);
const viwerStore = useViwerStore();

//获取摄像机实时参数
let { lng, lat, height, heading, pitch, roll } = cameraInfo();
var handler;
// 组件状态管理
let isComponentMounted = false;
let initTimeoutId = null;
//生命周期
onMounted(() => {
  isComponentMounted = true;
  console.log('Map组件开始初始化...');

  errorHandler.safeExecute(() => {
    console.log('开始初始化Earth实例...');
    var earth = new Earth("EarthContainer");

    // 初始化模型管理工具
    if (typeof earth.initModelManagerTools === 'function') {
      earth.initModelManagerTools();
    }

    window.earth = earth;
    var viewer = earth.getViewer ? earth.getViewer() : earth.viewer;

    if (!viewer) {
      console.error('无法获取viewer实例');
      return;
    }

    viwerStore.setViewer(viewer);
    console.log('Earth和Viewer初始化完成');

    // 检查组件是否仍然挂载
    if (!isComponentMounted) {
      console.log('组件已卸载，停止初始化');
      return;
    }

    handler = new window.Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

    handler.setInputAction((event) => {
      errorHandler.safeExecute(() => {
        // 检查组件状态
        if (!isComponentMounted) return;

        let cartesian = viewer.scene.pickPosition(event.position);
        if (!cartesian) return;

        let cartographic = window.Cesium.Cartographic.fromCartesian(cartesian);
        let lng = window.Cesium.Math.toDegrees(cartographic.longitude); // 经度
        let lat = window.Cesium.Math.toDegrees(cartographic.latitude); // 纬度
        let alt = cartographic.height; // 高度
        let coordinate = {
          longitude: Number(lng),
          latitude: Number(lat),
          altitude: Number(alt)
        };
        var result = lng + ',' + lat + ',' + alt;
        console.log(result);
      }, 'Map右键点击事件');
    }, window.Cesium.ScreenSpaceEventType.RIGHT_CLICK);

    if (isLoadEnd.value == true) {
      init();
    }
  }, 'Map组件初始化');
});

const mousePlane=()=>{
  var position=new Cesium.Cartesian3(0,0,0);
  var plane = viewer.entities.add({
        name: "plane",
        position: position,
        plane: {
          plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, 0), //指定平面的法线和距离
          dimensions: new Cesium.Cartesian2(100, 100), //指定平面的宽度和高度。
          fill: true, //是否填充
          material: Cesium.Color.WHITE.withAlpha(0.5),
          outline: true, //显示边框
          outlineWidth: 4,
          outlineColor: Cesium.Color.YELLOW, //边框颜色
        },
      });

    handler.setInputAction((event) => {
    let cartesian = viewer.scene.pickPosition(event.endPosition);
    let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
    let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
    let alt = cartographic.height; // 高度
    let coordinate = {
      longitude: Number(lng),
      latitude: Number(lat),
      altitude: Number(alt)

    };
    plane.position=cartesian
    mouse_height=alt-1000
    // viewer.scene.screenSpaceCameraController.minimumZoomDistance=alt
    console.log(alt)
    // var result=lng+','+lat+','+alt
    // console.log(result)
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
}

var mouse_height = undefined;
const manyou = () => {
  var scene = viewer.scene;
  // disable the default event handlers
  scene.screenSpaceCameraController.enableRotate = false;
  scene.screenSpaceCameraController.enableTranslate = false;
  scene.screenSpaceCameraController.enableZoom = false;
  scene.screenSpaceCameraController.enableTilt = false;
  scene.screenSpaceCameraController.enableLook = false;

  var startMousePosition;
  var mousePosition;
  var flags = {
    looking: false,
    moveForward: false,
    moveBackward: false,
    moveUp: false,
    moveDown: false,
    moveLeft: false,
    moveRight: false,
  };

  var handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  handler.setInputAction(function (movement) {
    flags.looking = true;
    mousePosition = startMousePosition = Cesium.Cartesian3.clone(
      movement.position
    );
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

  handler.setInputAction(function (movement) {
    mousePosition = movement.endPosition;
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  handler.setInputAction(function (position) {
    flags.looking = false;
  }, Cesium.ScreenSpaceEventType.LEFT_UP);

  function getFlagForKeyCode(keyCode) {
    switch (keyCode) {
      case "W".charCodeAt(0):
        return "moveForward";
      case "S".charCodeAt(0):
        return "moveBackward";
      case "Q".charCodeAt(0):
        return "moveUp";
      case "E".charCodeAt(0):
        return "moveDown";
      case "D".charCodeAt(0):
        return "moveRight";
      case "A".charCodeAt(0):
        return "moveLeft";
      default:
        return undefined;
    }
  }

  document.addEventListener(
    "keydown",
    function (e) {
      var flagName = getFlagForKeyCode(e.keyCode);
      if (typeof flagName !== "undefined") {
        flags[flagName] = true;
      }
    },
    false
  );

  document.addEventListener(
    "keyup",
    function (e) {
      var flagName = getFlagForKeyCode(e.keyCode);
      if (typeof flagName !== "undefined") {
        flags[flagName] = false;
      }
    },
    false
  );

  viewer.clock.onTick.addEventListener(function (clock) {
    var camera = viewer.camera;

    if (flags.looking) {
      var width = viewer.scene.canvas.clientWidth;
      var height = viewer.scene.canvas.clientHeight;

      // Coordinate (0.0, 0.0) will be where the mouse was clicked.
      var x = (mousePosition.x - startMousePosition.x) / width;
      var y = -(mousePosition.y - startMousePosition.y) / height;

      var lookFactor = 0.05;
      camera.lookRight(x * lookFactor);
      camera.lookUp(y * lookFactor);
    }

    var cameraHeight = viewer.scene.camera.positionCartographic.height;
    var moveRate = cameraHeight / 100.0;

    if (flags.moveForward) {
      camera.moveForward(moveRate);
    }
    if (flags.moveBackward) {
      camera.moveBackward(moveRate);
    }
    if (flags.moveUp) {
      camera.moveUp(moveRate);
    }
    if (flags.moveDown) {
      camera.moveDown(moveRate);
    }
    if (flags.moveLeft) {
      camera.moveLeft(moveRate);
    }
    if (flags.moveRight) {
      camera.moveRight(moveRate);
    }
  });
};

const kj=()=>{
  viewer.canvas.addEventListener('wheel', function(event) {
    event.preventDefault();
    var factor = 0.1;
    var distance = viewer.scene.camera.positionCartographic.distance;
    var delta = event.deltaY;
    var newDistance = distance + factor * delta * distance;
    viewer.scene.camera.setView({
        destination: viewer.scene.camera.positionCartographic.clone().normalize().multiplyScalar(newDistance)
    });
}, false);
}

const setCameraWhell = () => {
  viewer.scene.screenSpaceCameraController.enableZoom = false;
  handler.setInputAction((event) => {
    // let cartesian = viewer.scene.pickPosition(event.position);
    // let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    // let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
    // let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
    // let alt = cartographic.height; // 高度
    // let coordinate = {
    //   longitude: Number(lng),
    //   latitude: Number(lat),
    //   altitude: Number(alt),
    // };
    // mouse_height = alt;

    // var result = lng + "," + lat + "," + alt;
    let cameraPosition = Cesium.Cartesian3.fromDegrees(
      lat.value,
      lng.value,
      height.value
    );
    let mousePosition = viewer.scene.pickPosition(event.position);
    // let newPosition = Cesium.Cartesian3.lerp(
    //   cameraPosition,
    //   mousePosition,
    //   1 / 100,
    //   new Cesium.Cartesian3()
    // );
    // console.log(cameraPosition);
    // console.log(mousePosition);
    // console.log(newPosition);
    // var newHeading = _getHeading(cameraPosition, mousePosition);
    // var newPitch = _getPitch(cameraPosition, mousePosition);
    // viewer.camera.setView({
    //   destination: newPosition,
    //   orientation: {
    //     heading: newHeading, // east, default value is 0.0 (north)
    //     pitch: newPitch, // default value (looking down)
    //     roll: roll.value,
    //   },
    // });
   
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
};

const _getHeading = (pointA, pointB) => {
  //建立以点A为原点，X轴为east,Y轴为north,Z轴朝上的坐标系
  const transform = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
  //向量AB
  const positionvector = Cesium.Cartesian3.subtract(
    pointB,
    pointA,
    new Cesium.Cartesian3()
  );
  //因transform是将A为原点的eastNorthUp坐标系中的点转换到世界坐标系的矩阵
  //AB为世界坐标中的向量
  //因此将AB向量转换为A原点坐标系中的向量，需乘以transform的逆矩阵。
  const vector = Cesium.Matrix4.multiplyByPointAsVector(
    Cesium.Matrix4.inverse(transform, new Cesium.Matrix4()),
    positionvector,
    new Cesium.Cartesian3()
  );
  //归一化
  const direction = Cesium.Cartesian3.normalize(
    vector,
    new Cesium.Cartesian3()
  );
  //heading
  const heading =
    Math.atan2(direction.y, direction.x) - Cesium.Math.PI_OVER_TWO;
  return (
    Cesium.Math.TWO_PI -
    Cesium.Math.zeroToTwoPi(heading) -
    Cesium.Math.toRadians(90)
  );
};

const _getPitch = (pointA, pointB) => {
  let transfrom = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
  const vector = Cesium.Cartesian3.subtract(
    pointB,
    pointA,
    new Cesium.Cartesian3()
  );
  let direction = Cesium.Matrix4.multiplyByPointAsVector(
    Cesium.Matrix4.inverse(transfrom, transfrom),
    vector,
    vector
  );
  Cesium.Cartesian3.normalize(direction, direction);
  //因为direction已归一化，斜边长度等于1，所以余弦函数等于direction.z
  return Cesium.Math.PI_OVER_TWO - Cesium.Math.acosClamped(direction.z);
};

// 组件销毁时清理资源
onBeforeUnmount(() => {
  console.log('Map组件开始销毁，清理资源...');

  // 标记组件为未挂载状态
  isComponentMounted = false;

  // 清理定时器
  if (initTimeoutId) {
    clearTimeout(initTimeoutId);
    initTimeoutId = null;
  }

  // 清理事件监听器
  if (handler && !handler.isDestroyed()) {
    handler.destroy();
    handler = null;
  }

  // 清理3D场景和viewer
  if (window.viewer) {
    try {
      // 移除所有实体
      window.viewer.entities.removeAll();
      // 移除所有图元
      window.viewer.scene.primitives.removeAll();
      // 停止渲染循环
      if (window.viewer.clock) {
        window.viewer.clock.shouldAnimate = false;
      }
      // 销毁viewer
      if (window.viewer.destroy && typeof window.viewer.destroy === 'function') {
        window.viewer.destroy();
      }
    } catch (error) {
      console.warn('清理viewer时出错:', error);
    }
    window.viewer = null;
  }

  // 清理earth实例
  if (window.earth) {
    try {
      if (window.earth.destroy && typeof window.earth.destroy === 'function') {
        window.earth.destroy();
      }
    } catch (error) {
      console.warn('清理earth时出错:', error);
    }
    window.earth = null;
  }

  // 清理geoapp
  if (window.geoapp) {
    try {
      if (window.geoapp.destroy && typeof window.geoapp.destroy === 'function') {
        window.geoapp.destroy();
      }
    } catch (error) {
      console.warn('清理geoapp时出错:', error);
    }
    window.geoapp = null;
  }

  // 清理摄像机相关
  if (window.cameraVisual) {
    try {
      if (window.cameraVisual.destroy && typeof window.cameraVisual.destroy === 'function') {
        window.cameraVisual.destroy();
      }
    } catch (error) {
      console.warn('清理cameraVisual时出错:', error);
    }
    window.cameraVisual = null;
  }

  // 清理全局变量
  window.camera = null;

  console.log('Map组件资源清理完成');
});

watch(
  isLoadEnd,
  (newVal, oldVal) => {
    init();
  },
  200
);
const init = () => {
  errorHandler.safeExecute(() => {
    if (!isComponentMounted) {
      console.log('组件已卸载，跳过初始化');
      return;
    }
    initCamera();
    // kj()
  }, 'Map组件初始化');
};

const initCamera = () => {
  errorHandler.safeExecute(() => {
    if (!isComponentMounted) {
      console.log('组件已卸载，跳过摄像机初始化');
      return;
    }

    //加载默认视角
    var cameraVisual = new CameraVisual();
    window.cameraVisual = cameraVisual;

    //设置摄像机范围，限制超出
    var cameraManager = new CameraManager(window.earth);
    cameraManager.setExtent({
      centrePoint: [116.93852197724831, 34.84953652122788],
      limitLength: 600000,
    });
    cameraManager.inputExtent();

    // 使用可取消的定时器
    initTimeoutId = setTimeout(() => {
      errorHandler.safeExecute(() => {
        if (!isComponentMounted) {
          console.log('组件已卸载，跳过摄像机飞行');
          return;
        }
        cameraVisual.flytoDefault();
      }, '摄像机飞行动画');
    }, 1000);
  }, '摄像机初始化');
};
</script>
<style lang="less" scoped>
.map_box {
  width: 100%;
  // flex: 1;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;

  .map {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-content: center;
    z-index: 1;
    position: absolute;
    top: 0px;
    left: 0px;
  }

  .subassembly {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    padding: 7px 0px 0 0px;
    box-sizing: border-box;
  }

  slide-enter-from {
    transform: translateX(100%);
  }

  .slide-enter-to {
    // scale 缩放
    // translateX  X轴平移
    // rotate 旋转角度
    // opacity 透明度
    // transform-origin 旋转中心
    transform: translateX(-100%) scale(0) rotate(180deg);
    opacity: 0;
    transform-origin: center;
  }

  .slide-enter-from,
  .slide-enter-to {
    transition: all 0.5s linear; // 过度内容，时间，是否匀速
  }
}
</style>