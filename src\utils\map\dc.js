/**
 * 获取异常区数据
 */
import {
    ref,
    toRaw
} from "vue";
import $ from "jquery";
import {
    storeToRefs
} from "pinia";
import {
    useLoadStore
} from "@/store/load";
import LayerManager from "./layerManager";
const loadStore = useLoadStore();
const {
    loadData
} = storeToRefs(loadStore);
class DC {
    constructor() {
        this.load()
    }

    //加载断层数据及其属性数据
    load() {
        $.ajax({
            url: "/pdata/dcp.json",
            type: "GET",
            dataType: "json",
            success: (dataList) => {

                var dx = 116.90768855496233 - 116.90849039301136
                var dy = 34.8549784807236 - 34.861522715007375
                var dz = 716.3350156569218 - 1592.110979210106
                for (var data of dataList) {
                    this.DCMList.value.push({
                        title: data.name,
                        type: "断层",
                        position: Cesium.Cartesian3.fromDegrees(data.x, data.y, data.z + 1900),
                        distance: 0, //空间距离
                        distance2: 0, //垂直距离
                        distance3: 0 //水平距离
                    })
                }
                console.log(this.DCMList.value)
                this.isLoadEnd = true
            },
            error: () => {}
        });
        $.ajax({
            url: "/attribute/duanceng.json",
            type: "GET",
            dataType: "json",
            success: (dataList) => {
                this.DCMAttributeList.value = dataList
            },
            error: () => {}
        });
        setTimeout(() => {
            this.initSelect()
        }, 2000);


    }

    initSelect() {
        this.selectedEntity = new Cesium.Entity();
        var fragmentShaderSource =
            "uniform sampler2D colorTexture;\n" +
            "varying vec2 v_textureCoordinates;\n" +
            "uniform vec4 highlight;\n" +
            "void main() {\n" +
            "    vec4 color = texture2D(colorTexture, v_textureCoordinates);\n" +
            "    if (czm_selected()) {\n" +
            "        vec3 highlighted = highlight.a * highlight.rgb + (1.0 - highlight.a) * color.rgb;\n" +
            "        gl_FragColor = vec4(highlighted, 1.0);\n" +
            "    } else { \n" +
            "        gl_FragColor = color;\n" +
            "    }\n" +
            "}\n";
        this.stage = viewer.scene.postProcessStages.add(
            new Cesium.PostProcessStage({
                fragmentShader: fragmentShaderSource,
                uniforms: {
                    highlight: function () {
                        return new Cesium.Color(1.0, 1.0, 0.0, 1.0);
                    },
                },
            })
        );
        this.stage.selected = [];
       
    }



    //不断更新断层和小车之间的距离
    updataDistanceByAm(am) {

        this.timer = setInterval(() => {

            var amPosition = am.getCurrentPosition()
            for (var i = 0; i < this.DCMList.value.length; i++) {
                var DCPosition = this.DCMList.value[i].position
                if (DCPosition != 0) {
                    var distance = Cesium.Cartesian3.distance(toRaw(DCPosition), amPosition)
                    this.DCMList.value[i].distance = distance.toFixed(2)

                    var distance2 = Geowin3D.Cartographic.fromCartesian(amPosition).height - Geowin3D.Cartographic.fromCartesian(toRaw(DCPosition)).height

                    this.DCMList.value[i].distance2 = distance2.toFixed(2)

                    var distance3 = Math.sqrt(distance ** 2 - distance2 ** 2)
                    this.DCMList.value[i].distance3 = distance3.toFixed(2)
                }
            }
        }, 200);
    }




    getIndexByLabel(label) {
        for (var i = 0; i < this.DCMList.value.length; i++) {
            if (label == this.DCMList.value[i].title) {
                return i
            }
        }
        return -1
    }

    //移除异常区距离监控内容
    removeUpdataDistance() {
        clearInterval(this.timer)
        for (var i = 0; i < this.DCMList.value.length; i++) {
            this.DCMList.value[i].distance = 0
            this.DCMList.value[i].distance2 = 0
            this.DCMList.value[i].distance3 = 0
        }


    }

    getTitleIdx = (list, property) => {
        for (var i = 0; i < list.length; i++) {
            if (list[i].title == property) {
                return i;
            }
        }
        return -1;
    };

    getData() {
        return this.DCMList;
    }

    getDataByList(DCMListlist) {
        var timer = () => {
            console.log('this.isLoadEnd', this.isLoadEnd)
            if (this.isLoadEnd) {
                for (var ycq of this.DCMList.value) {

                    DCMListlist.value.push(ycq)
                }
            } else {
                setTimeout(() => {
                    timer()
                }, 200);

            }
        }
        timer()
    }

    selectFeatureByTitle(title) {
        var dcModelId = window.layersManager.getIdByLabel('断层模型')
        for (var item of this.DCMAttributeList.value) {
            if (title == item['断层编号']) {
                var dcFeatureId = Number(item['序号']) - 1
                var dcFeatures = window.earth.layerManager[dcModelId].features[dcFeatureId]
                if(dcFeatures){
                    this.stage.selected = [dcFeatures];
                }
                
            }
        }
    }

    removeSelect(){
        this.stage.selected = [];
    }


    isLoadEnd = false
    DCMList = ref([])
    DCMAttributeList = ref([])
}

export default DC;