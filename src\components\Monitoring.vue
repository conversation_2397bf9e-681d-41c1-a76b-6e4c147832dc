<template>
  <div class="monitoring">
    <!-- 缓冲区面板 -->
    <div class="home_buffer_box" v-show="bufferShow">
      <div class="buffer_line_text">
        半径：
        <el-input-number :max="500" v-model="bufferValue"></el-input-number>
        <span class="buffer_unit">M</span>
      </div>
      <div class="buffer_line">
        <el-slider v-model="bufferValue" class="home_buffer" :max="500" placement="right"
          @click="stopPropagation($event)" />
      </div>
      <div class="buffer_line">
        <span class="buffer_line_title">颜色：</span>
        <el-color-picker v-model="bufferColor" show-alpha />
      </div>
      <div class="buffer_btn_box">
        <div class="buffer_btn" @click="bufferReset()">重置</div>
        <div class="buffer_btn buffer_cancel" @click="bufferCancel()">取消</div>
      </div>
    </div>
    <transition appear name="animate__animated animate__pulse" enter-active-class="animate__fadeInLeft"
      leave-active-class="animate__fadeOutLeft">
      <div class="left" v-show="show">
        <div class="left_box3">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>实时监测</span>
          </div>
          <div class="left_box3_content">
            <div class="left_box3_btn"  :class="btnActive == index?'left_box3_btn_active':''" @click="handelAnalyse(index, item)" v-for="(item, index) in btnArr" :key="index">
              <img class="left_box3_btn_img" :src="btnActive == index ? btnImgA : btnImg" />
              {{ item }}
            </div>
          </div>
        </div>
        <div class="left_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>基础信息统计</span>
          </div>
          <div class="left_box1_content">
            <div class="left_box1_li">
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>10</span>个</div>
                <span class="left_box1_text">矿压监测点数量</span>
              </div>
            </div>
            <div class="left_box1_li">
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>12</span>个</div>
                <span class="left_box1_text">水文监测点数量</span>
              </div>
            </div>
            <div class="left_box1_li">
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>20</span>个</div>
                <span class="left_box1_text">瓦斯监测点数量</span>
              </div>
            </div>
            <div class="left_box1_li">
              <div class="left_box_li_right">
                <div class="left_box1_num"><span>0</span>个</div>
                <span class="left_box1_text">微震监测点数量</span>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="left_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            工具菜单
          </div>
          <div class="left_box2_content">
            <div class="left_box3_btn" @click="handelTool(index)" v-for="(item, index) in btnToolArr" :key="index">
              <img class="left_box3_btn_img" :src="btnToolActive == index ? btnImgA : btnImg" />
              {{ item }}
            </div>
          </div>
          <div class="left_box2_legend">图例:</div>
        </div> -->
      </div>
    </transition>
    <transition appear name="animate__animated animate__bounce" enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight">
      <div class="right" v-show="show">
        <div class="right_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>成果列表</span>
          </div>
          <ul class="right_box1_ul">
            <li class="right_box1_li" @click="changeResult(index)"
              :class="index == resultIndex ? 'right_box1_li_active' : ''" v-for="(item, index) in resultList"
              :key="index">
              {{ item }}
            </li>
          </ul>
          <div class="right_content">
            <div class="right_box1_table_head">
              <div class="right_box1_index right_box1_head_text">序号</div>
              <div class="right_box1_name right_box1_head_text">名称</div>
              <div class="right_box1_num right_box1_head_text">类型</div>
              <div class="right_box1_time right_box1_head_text">时间</div>
              <div class="right_box1_time right_box1_head_text">操作</div>
            </div>
            <vue3-seamless-scroll :list="ResultDataList" class="right_three_scroll" :step="0.5" v-model="scroll" :hover="hover" wheel="true"
              :limitScrollNum="limitScrollNum">
              <div class="" v-for="(item, index) in ResultDataList" :key="index">
                <div class="right_box1_table_head right_box1_table_line" :class="
                  index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                ">
                  <div class="right_box1_index">{{ index+1 }}</div>
                  <div class="right_box1_name">{{ item.title }}</div>
                  <div class="right_box1_num">{{ item.type }}</div>
                  <div class="right_box1_time">2022/09/06</div>
                  <div class="right_box1_time">
                    <span class="right_box1_see" @click="handleOpen(item)">查看</span>
                  </div>
                </div>
              </div>
            </vue3-seamless-scroll>
          </div>
        </div>
        <div class="right_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>数据查询</span>
          </div>
          <!-- <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick"> -->
          <!-- <el-tab-pane label="分析结果" name="first">
              <div id="receptionTrend" class="myChart"></div>
            </el-tab-pane> -->
          <!-- <el-tab-pane label="属性查询" name="third"> -->
          <div class="right_content">
            <div class="right_box1_table_head">
              <div class="right_box1_index right_box1_head_text">序号</div>
              <div class="right_box1_name right_box1_head_text">名称</div>
              <div class="right_box1_name right_box1_head_text">监测项</div>
              <div class="right_box1_time right_box1_head_text">数值</div>
              <div class="right_box1_time right_box1_head_text">
                采集时间
              </div>
            </div>
            <vue3-seamless-scroll :list="currentData" class="right_three_scroll" :step="0.5" v-model="scroll" wheel="true"
              :hover="hover" :limitScrollNum="limitScrollNum">
              <div class="" v-for="(item, index) in currentData" :key="index">
                <div class="right_box1_table_head right_box1_table_line" v-on:click="clickItem(item)" :class="
                  index % 2 == 0
                    ? 'right_table_radix'
                    : 'right_table_even'
                " :style="{ color: (item.monitor_id==selectDataId?  'yellow':'white') }">
                  <div class="right_box1_index" style="white-space: pre-wrap">
                    {{ index + 1 }}
                  </div>
                  <div class="right_box1_name" style="white-space: pre-wrap">
                    {{
                      item.monitor_name
                    }}
                  </div>
                  <div class="right_box1_name">
                    {{ item.monitor_type }}
                  </div>
                  <div class="right_box1_time">
                    {{ Number(item.monitor_val).toFixed(2) + item.monitor_company }}
                  </div>
                  <div class="right_box1_time" style="white-space: pre-wrap">
                    {{ item.data_time }}
                  </div>
                </div>
              </div>
            </vue3-seamless-scroll>
          </div>
          <!-- </el-tab-pane> -->
          <!-- </el-tabs> -->
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import titleImg from "@/assets/img/home/<USER>";
import leftImg1 from "@/assets/img/stoping/1-1.png";
import leftImg2 from "@/assets/img/stoping/1-2.png";
import leftImg3 from "@/assets/img/stoping/1-2.png";
import leftImg4 from "@/assets/img/stoping/1-2.png";
import btnImgA from "@/assets/img/stoping/btnA.png";
import btnImg from "@/assets/img/stoping/btn.png";
import * as echarts from "echarts";
import Animation2 from "@/utils/map/animation2";
import { useToolsStore } from "@/store/tools";
import Buffer from "@/utils/map/buffer3";
import Measure from "@/utils/map/mesuare";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef, computed } from "vue";
import $ from "jquery";
import { getDataBackMine, getJuejinModel, getJYeyaModel } from "@/api/home/<USER>";
import InitModelShow from "@/utils/map/initModelShow";
import { useLoadStore } from "@/store/load";
const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);
let list = ref(["", "", "", "", "", "", ""]);
const toolsStore = useToolsStore();
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 10,
});
let { scroll, hover, limitScrollNum } = toRefs(state);
let resultList = ref(["台账", "日报", "周报", "月报"]);
const ResultDataList = computed(() => {
  var taizhangList=[
  {
    title:btnItem.value+"台账",
    type:"台账"
  }
  ]
  var dayList=[{
    title:btnItem.value+"日报",
    type:"日报"
  }]
  var weekList=[{
    title:btnItem.value+"周报",
    type:"周报"
  }]
  var moonList=[{
    title:btnItem.value+"月报",
    type:"月报"
  }]
  
  if(resultIndex.value==1){
    return dayList
  }
  else if(resultIndex.value==2){
    return weekList
  }
  else if(resultIndex.value==3){
    return moonList
  }else{
    return taizhangList
  }
  
})
let resultIndex = ref(0);
const changeResult = (index) => {
  resultIndex.value = index;
};
const handleOpen = () => {
  // 查看
};
let timer1
let timer2
let timer3
let btnActive = ref(0);
let btnArr = [
  "矿压监测",
  "水文监测",
  "瓦斯监测",
  "微震监测",
  
];
let btnItem = computed(() => {
  return btnArr[btnActive.value]
})
watch(
  btnItem,
  (newVal, oldVal) => {
    window.monitoringPoint.setCurrentList(btnItem)
    monitoringPoint.flyToPoints(btnItem)
    selectDataId.value=-1
  },
  200
);

// 按钮点击
const handelAnalyse = (index, item) => {
  btnActive.value = index;
  var monitoringPoint = window.monitoringPoint;
  monitoringPoint.hideAll();
  monitoringPoint.show(item);
  viewer.scene.forceRender();
};
// 数据查询
const activeName = ref("first");
const handleClick = (tab, event) => {
};

const clickItem = (node) => {
  monitoringPoint.enlargePoint(node, btnItem);
  // console.log(node)
  selectDataId.value=node.monitor_id
}

//加载模型
var am;
var timer4;
const loadModel = () => {
  am = new Animation2(viewer);
  var juejinModelUrl = getJuejinModel()
  am.setModel({ url: juejinModelUrl });
    am.setPath([
    Cesium.Cartesian3.fromDegrees(116.9771865681275, 34.86098492771089, 1116.6161126308125),
    Cesium.Cartesian3.fromDegrees(116.97577440462048, 34.86218424474127, 1051.082545197303),
  ]);
  var yeyaModelUrl = getJYeyaModel()
  am.setyeyaModel(yeyaModelUrl);
  timer4 = setTimeout(() => {
    am.setPlanningDistance(200);
    am.setSpeed(1);
    am.setLoop(true);
    am.setRate(0.65);
    am.play();
    am.flyToModel();
    am.unShowMeasurePathLine()
    am.showPathLine();
    am.addInputAction()
  }, 1000);
};

// 工具菜单
let btnToolActive = ref(-1);
let btnToolArr = ["缓冲区分析工具", '点绘制', '线绘制', '面绘制', "清除"];
// 按钮点击
const handelTool = (index) => {
  btnToolActive.value = index;

  measure.cancel();
  bufferShow.value = false;
  buffer.clear();
  buffer.cancel()
  //缓冲区分析
  switch (true) {
    case index == 0:
      buffer.add3(currentData, btnArr[btnActive.value]);
      break
    case index == 1:
      measure.elevation();
      break
    case index == 2:
      measure.segmentsDistance2();
      break
    case index == 3:
      measure.area();
      break
    case index == 4:
      btnToolActive.value = -1;
      measure.removeAll();
      break
  }
  viewer.scene.forceRender();
};
// 面板
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);

//生命周期
let currentData = ref([]);
let selectDataId=ref(-1);

onMounted(() => {
  // 初始化 左右两侧 确保展开  工具栏 默认到1  图层关闭
  initStore(6);
  // initEcharts();
  // setEchartsList("/pdata/hydrology_getDataById_610824050103.json");
  if (isLoadEnd.value == true) {
    init();
  }
});
watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);

onUnmounted(() => {
  console.log('Monitoring组件开始销毁，清理资源...');

  try {
    // 清理监控点
    if (window.monitoringPoint) {
      window.monitoringPoint.removeClick();
      window.monitoringPoint.hideAll();
    }

    // 清理缓冲区
    if (buffer) {
      buffer.clear();
      buffer.cancel();
      buffer = null;
    }

    // 清理测量工具
    if (measure) {
      measure.removeAll();
      measure.cancel();
      measure = null;
    }

    // 销毁动画模型
    if (am) {
      am.destroy();
      am = null;
    }

    // 清理定时器
    removeTimeout();

    console.log('Monitoring组件资源清理完成');
  } catch (error) {
    console.error('Monitoring组件销毁时出错:', error);
  }


});

const init = () => {
  // console.log('')
  // debugger
  window.monitoringPoint.startClick(btnItem,selectDataId);
  window.monitoringPoint.setCurrentDatas(currentData);
  window.monitoringPoint.setCurrentList(btnItem)
  handelAnalyse(btnActive.value, btnArr[btnActive.value]);
  // setModelShow(true);
  initModelsShow()
  loadModel()
  monitoringPoint.flyToPoints(btnItem)
  // cameraVisual.flytoDefault();
}

//初始化图层列表和模型显隐
const initModelsShow = () => {
  var initModelShow = new InitModelShow()
  initModelShow.initShow()
}


//绘制工具相关
//初始化
let measure;
timer3 = setTimeout(() => {
  measure = new Measure(viewer);
}, 1000);


//缓冲区相关
let buffer;
timer2 = setTimeout(() => {
  let [red, green, blue, alpha] = bufferColor.value
    .match(/\d+(\.\d+)?/g)
    .map(Number);
  buffer = new Buffer(viewer, bufferShow, {
    radius: bufferValue.value,
    color: [red / 255, green / 255, blue / 255, alpha],
  });
  // buffer = new Buffer(viewer);

  buffer.getButterData("实时监测");
}, 200);

// 工具栏-缓冲区
let bufferShow = ref(false);
var bufferValue = ref(20);
var bufferColor = ref("rgba(255, 255, 0, 0.5)");
watch(
  bufferValue,
  (newVal, oldVal) => {
    if (bufferShow.value == true) {
      buffer.setRadius(newVal);
    }
  },
  200
);
watch(
  bufferColor,
  (newVal, oldVal) => {
    if (bufferShow.value == true) {
      let [red, green, blue, alpha] = newVal.match(/\d+(\.\d+)?/g).map(Number);
      buffer.setColor([red / 255, green / 255, blue / 255, alpha]);
    }
  },
  200
);
const bufferReset = () => {
  // 重置
  bufferValue.value = 100;
  buffer.setRadius(bufferValue.value);

  bufferColor.value = "rgba(255, 255, 0, 0.5)";
  let [red, green, blue, alpha] = bufferColor.value
    .match(/\d+(\.\d+)?/g)
    .map(Number);
  buffer.setColor([red / 255, green / 255, blue / 255, alpha]);
};
const stopPropagation = (e) => {
  e.stopPropagation();
};
const bufferCancel = () => {
  bufferReset();
  bufferShow.value = false;
  buffer.clear();
  buffer.cancel();
  list.value = [];
  viewer.scene.forceRender();
  btnToolActive.value = -1;
};

//echart相关
const echart = echarts;
let myChart = null;
let myChartList = ref({
  type: btnArr[btnActive.value],
  x: [
    "21/7/2022 21:49:28",
    "21/7/2022 21:50:28",
    "21/7/2022 21:51:28",
    "21/7/2022 21:49:28",
    "21/7/2022 21:50:28",
    "21/7/2022 21:51:28",
  ],
  data: [
    {
      name: "水文数据",
      type: "line",
      stack: "Total",
      data: [120, 132, 101],
    },
  ],
});

watch(
  currentData,
  (newVal, oldVal) => {
    // console.log(newVal);
  },
  200
);

const setEchartsList = (url) => {
  $.ajax({
    url: url,
    type: "GET",
    dataType: "json",
    success: (datalist) => {
      if (datalist.length == 0) return;
      myChartList = ref({
        type: btnArr[btnActive.value],
        x: [],
        data: [],
      });
      // console.log(datalist);
      myChartList.value.data.push({
        name: datalist[0].roadway_name,
        type: "line",
        stack: "Total",
        data: [],
      });
      var index = myChartList.value.data.length - 1;
      for (let item of datalist) {
        myChartList.value.x.push(item.data_time);
        myChartList.value.data[index].data.push(
          Number(item.monitor_val).toFixed(2)
        );
      }

      setEcharts();
    },
  });
};

// 分析结果图表
var option = {
  title: {
    text: myChartList.value.type,
    textStyle: {
      color: "rgb(55,241,241)",
      fontSize: 12,
    },
  },
  textStyle: {
    color: "#FFFFFF",

    fontSize: 12,
  },
  tooltip: {
    trigger: "axis",
    confine: true,
  },
  boundaryGap: [0, 0.2],
  color: ["#0DD6E3"],

  grid: {
    left: "2%",
    right: "3%",
    bottom: "4%",
    // top:'1%',
    containLabel: true,
  },
  xAxis: {
    type: "category",
    boundaryGap: true,
    data: myChartList.value.x,
    axisLine: {
      lineStyle: {
        color: "#0DD6E3",
      },
    },
    axisLabel: {
      // interval:0,
      // rotate:"5",
      // align:'',
      overflow: "breakAll",
      formatter: function (value, index) {
        var res = value;
        if (res.length > 4) {
          res = res.substring(5, 10) + "..";
        }
        return index;
      },
    },
  },
  yAxis: {
    type: "value",
    splitLine: {
      lineStyle: {
        color: "#0DD6E3",
        type: "dashed",
      },
    },
  },
  series: myChartList.value.data,
};
const initEcharts = () => {
  myChart = echart.init(document.getElementById("receptionTrend"));

  myChart.setOption(option);
};
const setEcharts = () => {
  var option = {
    title: {
      text: myChartList.value.type,
      textStyle: {
        color: "rgb(55,241,241)",
        fontSize: 16,
      },
    },
    textStyle: {
      color: "#FFFFFF",

      fontSize: 12,
    },
    tooltip: {
      trigger: "axis",
      confine: true,
    },
    boundaryGap: [0, 0.2],
    color: ["#0DD6E3"],

    grid: {
      left: "2%",
      right: "3%",
      bottom: "4%",
      // top:'1%',
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: true,
      data: myChartList.value.x,
      axisLine: {
        lineStyle: {
          color: "#0DD6E3",
        },
      },
      axisLabel: {
        interval: 0,
        rotate: "38",
        // align:'',
        overflow: "breakAll",
        formatter: function (value, index) {
          // var res = value;
          // if (res.length > 4) {
          //   res = res.substring(5, 10) + "..";
          // }
          // return res;
          var ret = "";//拼接加\n返回的类目项  
          var maxLength = 8;//每项显示文字个数         
          value = value.split(' ')[1].split('.')[0]
          var valLength = value.length;//X轴类目项的文字个数 
          var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数  
          if (rowN > 1)//如果类目项的文字大于3,  
          {
            for (var i = 0; i < rowN; i++) {
              var temp = "";//每次截取的字符串  
              var start = i * maxLength;//开始截取的位置  
              var end = start + maxLength;//结束截取的位置  
              //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧  
              temp = value.substring(start, end) + "\n";
              ret += temp; //凭借最终的字符串  
            }
            return ret;
          }
          else {
            return value;
          }
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          color: "#0DD6E3",
          type: "dashed",
        },
      },
    },
    series: myChartList.value.data,
  };
  myChart.setOption(option);
};

//进入该页面后模型的显隐和移动设置
var dataTree;
var checkedKeys;
const setModelShow = (flag) => {
  if (flag) {
    toolsStore.$patch((state) => {
      dataTree = state.dataTree;
      checkedKeys = dataTree.getCheckedKeys();
      var labels = window.layersManager.getIdsByLabels([
        "井巷模型",
        "掘进工作面",
      ]);
      timer1 = setTimeout(() => {
        dataTree.setCheckedKeys(labels);
      }, 2000);
    });
  } else {
    if (dataTree && checkedKeys) dataTree.setCheckedKeys(checkedKeys);
  }
};



const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  } if (timer2) {
    clearInterval(timer2);
  } if (timer3) {
    clearInterval(timer3);
  }
  if (timer4) {
    clearInterval(timer4);
  }
}

window.onresize = function () {
  myChart.resize();
};
</script>
<style lang="less" scoped>
.monitoring {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  .left {
    z-index: 1;
    width: 420px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    padding-right: 18px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .left_box3 {
      width: 100%;
      // height: 240px;
      box-sizing: border-box;
      padding: 0px 0px 24px 0px;
      display: flex;
      flex-direction: column;
      background: rgba(11, 24, 36,.7);
      .left_box3_content {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        box-sizing: border-box;
        padding: 26px 0 0px 20px;
        .left_box3_btn {
          position: relative;
          width: 95px;
          height: 38px;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 20px 24px 0px;
          cursor: pointer;
          &:hover {
            transform: scale(1.2);
          }
          .left_box3_btn_img {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
          }
          
        }
        .left_box3_btn_active{
              color: #FFE326;
          }
      }
    }

    .left_box1 {
      width: 100%;
      // height: 256px;
      box-sizing: border-box;
      padding: 0px 0px 24px 0px;
      display: flex;
      flex-direction: column;
      background: rgba(11, 24, 36,.7);
      .left_box1_content {
        display: flex;
        width: 100%;
        flex: 1;
        justify-content: flex-start;
        box-sizing: border-box;
        padding: 30px 20px 0 20px;
        flex-wrap: wrap;
        .left_box1_li {
          display: flex;
          align-items: center;
          width: 50%;
          margin-bottom: 30px;
          font-size: 16px;

          .left_box_li_right {
            display: flex;
            flex-direction: column;
            background-image: url("../assets/img/home/<USER>");
            background-position: center center;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 147.6px;
            height: 153.79px;
            justify-content: space-between;
            align-items: center;
          }

          .left_box1_num {
            /* 安全监控： */
            margin-top: 13px;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: left;
            color: rgb(255, 255, 255);
            span {
              font-family: FZLanTingHei-B-GBK;
              font-size: 38px;
              font-weight: undefined;
              line-height: 56px;
              letter-spacing: 0px;
              text-align: left;
              color: rgb(17, 177, 255);
            }
          }

          .left_box1_text {
            /* 安全监控： */
            color: rgb(45, 199, 210);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: left;
          }
          .left_box_li_img {
            width: 46px;
            height: 36px;
            margin-right: 10px;
          }
        }
      }
    }
    .left_box2 {
      margin-top: 17px;
      width: 100%;
      height: 321px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 23px 0 0px 18px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .left_box2_content {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        box-sizing: border-box;
        padding: 14px 0 0 10px;

        .left_box3_btn {
          position: relative;
          width: 135px;
          height: 33px;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 20px 24px 0px;
          cursor: pointer;

          &:hover {
            transform: scale(1.2);
          }

          .left_box3_btn_img {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
          }
        }
      }

      .left_box2_legend {
        padding-left: 10px;
        font-size: 20px;
        color: #26c1d1;
      }
    }
  }
  .right {
    z-index: 1;
    width: 364px;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    // background: rgba(11, 24, 36,.7);
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-left: 18px;
    .right_box1 {
      width: 100%;
      height: 400px;
      box-sizing: border-box;
      padding: 0px 22px 0 0px;
      display: flex;
      flex-direction: column;
      .right_box1_ul {
        margin-top: 10px;
        width: 100%;
        height: 24px;
        display: flex;
        padding-left: 10px;
        .right_box1_li {
          height: 30px;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          cursor: pointer;
          color: rgb(198, 198, 198);
          font-family: PingFang SC;
          font-size: 16px;
          background: rgba(148, 148, 148, 0.4);
          border: 1px solid rgb(116, 116, 116);
          border-radius: 15px;
          padding: 0 12px;
          margin-right: 12px;
          margin-bottom: 6px;
        }
        .right_box1_li_active {
          background: rgba(49, 131, 194, 0.4);
          border: 1px solid rgb(0, 179, 255);
          border-radius: 15px;
          color: rgb(255, 255, 255);
        }
      }
      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 20px 0px 0 10px;
        overflow: hidden;
        .right_three_scroll {
          height: 560px;
          overflow: hidden;
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 50px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          // justify-content: center;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

          .right_box1_head_text {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(143, 199, 255);
            text-align: center;
            display: flex;
            justify-content: center;
            height: 36px;
            display: flex;
            align-items: center;
          }
          .right_box1_index {
            width: 100px;
            text-align: center;
          }

          .right_box1_num {
            width: 120px;
            text-align: center;
          }

          .right_box1_name {
            width: 300px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: flex;
            justify-content: center;
            text-align: center;
          }

          .right_box1_time {
            width: 144px;
            text-align: center;
            box-sizing: border-box;
            display: flex;
            justify-content: center;

            .right_box1_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
              width: 40px;
              height: 28px;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }

        .right_box1_table_line {
          height: 36px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }
    .right_box2 {
      width: 100%;
      height: 513px;
      box-sizing: border-box;
      padding: 0px 10px 10px 0px;
      display: flex;
      flex-direction: column;
      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 20px 0px 20px 10px;
        overflow: hidden;
        .right_three_scroll {
          height: 490px;
          overflow: hidden;
        }
        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 40px;
          color: #ffffff;
          font-size: 16px;
          display: flex;
          // justify-content: center;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);
          .right_box1_head_text {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(143, 199, 255);
            text-align: center;
            display: flex;
            justify-content: center;
            height: 36px;
            display: flex;
            align-items: center;
          }
          .right_box1_index {
            width: 100px;
            text-align: center;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
          }
          .right_box1_num {
            width: 120px;
            text-align: center;
          }
          .right_box1_name {
            width: 100px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: flex;
            justify-content: center;
            text-align: center;
          }
          .right_box1_time {
            width: 144px;
            text-align: center;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            .right_box1_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
              width: 40px;
              height: 28px;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
        .right_box1_table_line {
          height: 48px;
        }
        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }
        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }

      .demo-tabs {
        font-size: 14px;
        :deep(.el-tabs__item) {
          font-size: 14px;
          color: #fff;
        }
        :deep(.is-active) {
          color: #409eff;
        }
        .data_chart {
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;
          font-size: 18px;
          box-sizing: border-box;
          padding-top: 30px;
          .data_chart_li {
            list-style: none;
            width: 100%;
            color: #fff;
            display: flex;

            .data_chart_li_name {
              width: 120px;
              margin-right: 10px;
              margin-bottom: 20px;
            }


          }
        }
      }

      .myChart {
        width: 300px;
        height: 350px;
        box-sizing: border-box;
        padding: 15px 0px 0 0;
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }

  .time_box {
    position: absolute;
    bottom: 10px;
    left: 0px;
    width: 100%;
    height: 200px;
  }

  .home_buffer_box {
    width: 300px;
    height: 200px;
    // box-sizing: border-box;
    padding: 10px;
    position: absolute;
    top: 240px;
    right: 400px;
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: RGBA(5, 31, 89, 0.3);
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 10px;
    border-radius: 6px;
    z-index: 999;

    .buffer_line_text {
      font-size: 18px;

      .buffer_unit {
        width: 20px;
        margin-left: 10px;
      }
    }

    .buffer_line {
      display: flex;
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .buffer_line_title {
        width: 60px;
        font-size: 18px;
      }
    }

    .buffer_btn_box {
      width: 100%;
      display: flex;
      justify-content: space-around;
      margin-top: 20px;

      .buffer_btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100px;
        height: 32px;
        cursor: pointer;
        border: 1px solid;
        border-radius: 4px;
        font-size: 16px;

        &:hover {
          border: 1px solid #73d897;
          transform: scale(1.05);
        }
      }

      .buffer_cancel {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
