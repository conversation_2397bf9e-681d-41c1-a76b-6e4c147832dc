<template>
  <div class="stoping">
    <transition appear name="animate__animated animate__pulse" enter-active-class="animate__fadeInLeft"
      leave-active-class="animate__fadeOutLeft">
      <div class="left" v-show="show">
        <div class="left_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>回采基础信息</span>
          </div>
          <div class="left_box1_content">
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg1" />
              <div class="left_box_li_right">
                <span class="left_box1_text">工作面编号</span>
                <span class="left_box1_num">{{ backMine.workFaceNo }}</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg2" />
              <div class="left_box_li_right">
                <span class="left_box1_text">平均回采率</span>
                <span class="left_box1_num">{{ backMine.averageRate }}</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg3" />
              <div class="left_box_li_right">
                <span class="left_box1_text">累计开采量</span>
                <span class="left_box1_num">{{ backMine.cumulativeMineVolume }}万吨</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg4" />
              <div class="left_box_li_right">
                <span class="left_box1_text">剩余开采量</span>
                <span class="left_box1_num">{{ backMine.remainMineVolume }}万吨</span>
              </div>
            </div>
          </div>
        </div>
        <div class="left_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>回采超前探</span>
          </div>
          <ul class="left_box2_ul">
            <li class="right_box1_li" @click="changeResult(index)"
              :class="index == resultIndex ? 'right_box1_li_active' : ''" v-for="(item, index) in resultList"
              :key="index">
              {{ item }}
            </li>
          </ul>
          <div class="left_box2_content">
            <div class="right_box1_table_head">
              <div class="right_box1_title right_box1_head_text">图层<br>控制</div>
              <div class="right_box1_index right_box1_head_text">编号</div>
              <div class="right_box1_name right_box1_head_text">类型</div>
              <div class="right_box1_time right_box1_head_text">时间</div>
              <div class="right_box1_oper right_box1_head_text">状态</div>
            </div>
            <vue3-seamless-scroll :list="resultInfoIist[resultIndex]" class="right_three_scroll" :step="0.5"
              v-model="scroll" :hover="hover" :limitScrollNum="limitScrollNum">
              <div class="" v-for="(item, index) in resultInfoIist[resultIndex]" :key="index">
                <div class="right_box1_table_head right_box1_table_line" :class="
                  index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                ">
                  <div class="right_box1_index right_box1_line_text">
                    <el-checkbox v-model="item.check" label="" @change="changeClick(item, resultIndex)"
                      :disabled="!item.imgUrl && !item.position" />
                  </div>
                  <div class="right_box1_index right_box1_line_text"
                    :style="{ color: (item.imgUrl || item.position ? 'white' : 'grey') }">
                    {{ item.index }}
                  </div>
                  <div class="right_box1_name right_box1_line_text"
                    :style="{ color: (item.imgUrl || item.position ? 'white' : 'grey') }" :title="item.title">
                    <text> {{ item.title }}</text>
                  </div>
                  <div class="right_box1_time right_box1_line_text"
                    :style="{ color: (item.imgUrl || item.position ? 'white' : 'grey') }">
                    2022/10/{{ index + 10 }}
                  </div>
                  <el-popconfirm confirm-button-text="docx" cancel-button-text="dwg" :icon="InfoFilled"
                    icon-color="#626AEF" title="选择下载文件类型" @confirm="confirmEvent(item)" @cancel="cancelEvent(item)">
                    <template #reference>
                      <div class="right_box1_oper right_box1_line_text"
                        :style="{ cursor: (item.dwgUrl || item.docUrl ? 'pointer' : 'wait'), color: (item.dwgUrl || item.docUrl ? 'white' : 'grey') }"
                        v-show="(!item.dwgUrl && !item.docUrl ? false : true)">
                        打开</div>
                    </template>
                  </el-popconfirm>
                  <div class="right_box1_oper right_box1_line_text"
                    :style="{ cursor: (item.dwgUrl || item.docUrl ? 'pointer' : 'wait'), color: (item.dwgUrl || item.docUrl ? 'white' : 'grey') }"
                    v-show="(!item.dwgUrl && !item.docUrl ? true : false)">
                    打开</div>
                </div>
              </div>
            </vue3-seamless-scroll>
          </div>
        </div>
      </div>
    </transition>
    <transition appear name="animate__animated animate__bounce" enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight">
      <div class="right" v-show="show">
        <div class="right_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>规划截割</span>
          </div>
          <div class="right_contentBox"></div>
          <div class="right_content1">
            <!-- <div id="receptionTrend2" class="myChart2"></div> -->
            <div class="myChartBox3">
            <div id="receptionTrend3" class="myChart3"></div>
          </div>
            <div class="myChartBox2">
            <div id="receptionTrend2" class="myChart2"></div>
          </div>
            <div class="myChartBox">
            <div id="receptionTrend" class="myChart"></div>
          </div>
          </div>
        </div>
        <div class="right_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>预测预报</span>
          </div>
          <div class="right_content">
            <div class="right_table_box">
              <div class="right_box1_table_head right_box1_table_sticky">
                <div class="right_box1_index right_box1_head_text">序号</div>
                <div class="right_box1_type right_box1_head_text">
                  类型
                </div>
                <div class="right_box1_name right_box1_head_text">
                  预测预报内容
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  水平距离
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  垂直距离
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  空间距离
                </div>
                <!-- <div class="right_box1_oper right_box1_head_text">操作</div> -->
              </div>
              <div class="right_box2_content">
                <div class="" v-for="(item, index) in forecastList" :key="index">
                  <div class="right_box1_table_head right_box1_table_line" :class="
                    index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                  " @click="handleOpen3(item)" :style="{ color: (item == selectItem ? 'yellow' : 'white') }">
                    <div class="right_box1_index right_box1_line_text">
                      {{ index + 1 }}
                    </div>
                    <div class="right_box1_type right_box1_line_text">
                      {{ item.type }}
                    </div>
                    <div class="right_box1_name right_box1_line_text" style="text-align: center">
                      {{ item.title }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance3 }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance2 }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance }}
                    </div>
                    <!-- <div class="right_box1_oper right_box1_line_text">
                      <span class="right_box1_see" @click="handleOpen(item, true)"
                        v-show="!item.show && item.type == '异常区'" style="color: greenyellow">显示</span>
                      <span class="right_box1_see" @click="handleOpen(item, false)"
                        v-show="item.show && item.type == '异常区'" style="color: yellow">隐藏</span>
                      <span class="right_box1_see" @click="handleOpen2(item)" v-show="item.type == '断层'">查看</span>
                    </div> -->
                  </div>
                </div>
              </div>

              </div>
            </div>
          </div>
        </div>
     
    </transition>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import titleImg from "@/assets/img/home/<USER>";
import lineImg from "@/assets/img/home/<USER>";
import resImg from "@/assets/img/home/<USER>";
import upImg from "@/assets/img/home/<USER>";
import downImg from "@/assets/img/home/<USER>";
import FileSaver from "file-saver";
import leftImg1 from "@/assets/img/tunnel/1-1.png";
import leftImg2 from "@/assets/img/tunnel/1-2.png";
import leftImg3 from "@/assets/img/tunnel/1-2.png";
import leftImg4 from "@/assets/img/tunnel/1-2.png";
import * as echarts from "echarts";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef, reactive, onUnmounted, toRaw } from "vue";
import $ from "jquery";
import Animation2 from "@/utils/map/animation2";
import StopingBox from "@/utils/map/stopingBox";
import SetAlphaTool from "@/utils/map/setAlpha";
import { useToolsStore } from "@/store/tools";
import  {myChartData,myChartData2,myChartData3}  from "@/utils/map/stopingEcharts"
import ForecastSort from "@/utils/map/forecastSort";
import InitModelShow from "@/utils/map/initModelShow";
import { InfoFilled } from '@element-plus/icons-vue'
import { getDataBackMine, getJuejinModel, getJYeyaModel } from "@/api/home/<USER>";
import { useLoadStore } from "@/store/load";
const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);


const toolsStore = useToolsStore();




let list = ref([
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
]);
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 10,
});
let { scroll, hover, limitScrollNum } = toRefs(state);
const echart = echarts;
let myChart = null;
let myChart2 = null;
let myChart3 = null;
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
//生命周期
onMounted(() => {
  // 初始化 左右两侧 确保展开  工具栏 默认到1  图层关闭
  initStore(3);
  initEcharts();

  if (isLoadEnd.value == true) {
    init();
  }
  getDataBackMine().then((res)=>res.data).then((res) => {
    if (res) {
      backMine.value = res
    }
  })
});

watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);
// 左上角 回采基础信息
let backMine = ref({})
onUnmounted(() => {
  console.log('Stoping组件开始销毁，清理资源...');

  try {
    // 清理定时器
    removeTimeout();

    // 移除物探图
    if (stopingBox) {
      stopingBox.remove();
      stopingBox = null;
    }

    // 恢复巷道显示
    try {
      setHangdao(true);
    } catch (error) {
      console.warn('恢复巷道显示失败:', error);
    }

    // 销毁动画模型
    if (am) {
      am.destroy();
      am = null;
    }

    // 清理异常区域
    if (ycq) {
      ycq.removeUpdataDistance();
      ycq = null;
    }

    // 清理距离计算
    if (dc) {
      dc.removeUpdataDistance();
      dc = null;
    }

    // 移除排序定时器
    if (forecastTimer) {
      clearInterval(forecastTimer);
      forecastTimer = null;
    }

    // 移除选择输入动作
    removeSelectInputAction();

    // 强制渲染
    if (viewer && viewer.scene) {
      viewer.scene.forceRender();
    }

    console.log('Stoping组件资源清理完成');
  } catch (error) {
    console.error('Stoping组件销毁时出错:', error);
  }
});

var timer1
var timer2
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  } if (timer2) {
    clearInterval(timer2);
  }
  if (sectionFunTimer) {
    clearInterval(sectionFunTimer);
  }


}

const init = () => {
  setHangdao(false);
  loadModel();
  loadGeoJson();
  initModelsShow()
  addSelectInputAction()
  viewer.scene.forceRender();
};

var am;
const loadModel = () => {
  am = new Animation2(viewer);
  var juejinModelUrl = getJuejinModel()
  am.setModel({ url: juejinModelUrl });
  am.setPath([
    Cesium.Cartesian3.fromDegrees(116.9771865681275, 34.86098492771089, 1116.6161126308125),
    Cesium.Cartesian3.fromDegrees(116.97577440462048, 34.86218424474127, 1051.082545197303),
  ]);
  var yeyaModelUrl = getJYeyaModel()
  am.setyeyaModel(yeyaModelUrl);
  timer1 = setTimeout(() => {
    am.setPlanningDistance(200);
    am.setSpeed(1);
    am.setLoop(true);
    am.setRate(0.65);
    am.play();
    am.flyToModel();
    am.unShowMeasurePathLine()
    am.showPathLine();
    am.addInputAction()

    ycq.updataDistanceByAm(am)
    dc.updataDistanceByAm(am)
    window.cameraVisual.setVisualByAm(window.location.hash, am)
    setsectionFun()
  }, 1000);
};


//规划截割
var sectionFunTimer;
const setsectionFun = () => {
  sectionFunTimer = setInterval(() => {
    sectionFun(
      new Cesium.Cartesian3.fromDegrees(116.97713463095383, 34.860987551276686, 1121.3944665732251),
      new Cesium.Cartesian3.fromDegrees(116.97577475900343, 34.86214486343088, 1057.4408039487728), 'up'
    )
    // console.log(heightlist)
    sectionFun(
      new Cesium.Cartesian3.fromDegrees(116.97713470996747, 34.86099406141692, 1115.472968728732),
      new Cesium.Cartesian3.fromDegrees(116.97578037346287, 34.86215269952917, 1051.3626316553696), 'down'
    )
  }, 5000);

}
const sectionFun = (p1, p2, upordowm) => {
  var cartesian1 = p1
  var cartesian2 = p2

  var distance = Cesium.Cartesian3.distance(p1, p2);
  // 通过偏移的方式 获取30个点，这30个点在一条直线上
  var interval = 25;
  var cartesians = [];
  for (var i = 0; i < distance; i += interval) {
    var offset = i / distance;
    cartesians.push(Cesium.Cartesian3.lerp(
      cartesian1,
      cartesian2,
      offset,
      new Cesium.Cartesian3())
    );
  }
  //排除某些要素
  var excludeLayers = window.layersManager.getLayersByLabels(['断层模型', '7号煤层', '8号煤层', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层'])

  // excludeLayers.push()
  //排除异常区线
  excludeLayers.push(...window.ycq.getPrimitives())
  //排除液压装置和采煤机
  excludeLayers.push(...am.getYeyaModels())
  excludeLayers.push(am.getModel())
  //排除超前探图片
  excludeLayers.push(...stopingBox.getAllEntityList())
  viewer.scene
    .clampToHeightMostDetailed(cartesians, excludeLayers)
    .then((clampedCartesians) => {
      // 添加点
      // console.log('clampedCartesians', clampedCartesians)
      // for (var i = 0; i < count; ++i) {
      //   viewer.entities.add({
      //     position: clampedCartesians[i],
      //     ellipsoid: {
      //       radii: new Cesium.Cartesian3(0.2, 0.2, 0.2),
      //       material: Cesium.Color.RED,
      //     },
      //   });
      // }
      // // 连线
      // viewer.entities.add({
      //   polyline: {
      //     positions: clampedCartesians,
      //     // arcType: Cesium.ArcType.NONE,
      //     width: 2,
      //     material: new Cesium.PolylineOutlineMaterialProperty({
      //       color: Cesium.Color.YELLOW,
      //     }),
      //     depthFailMaterial: new Cesium.PolylineOutlineMaterialProperty(
      //       {
      //         color: Cesium.Color.YELLOW,
      //       }
      //     ),
      //   },
      // });
      var data = [[], []]
      for (var point of clampedCartesians) {
        data[0].push((Geowin3D.Cartographic.fromCartesian(point).height - 1900).toFixed(2))
        data[1].push(Geowin3D.Cartesian3.distance(clampedCartesians[0], point).toFixed(0))
      }
      // console.log("data", data)
      resetEcharts(data[1], data[0], upordowm)
      return data
    })
}



//右下角——异常区和断层
let forecastList = ref([]);
var ycq;
var dc
//右下角——加载异常区和断层json文件
const loadGeoJson = () => {
  ycq = window.ycq
  ycq.getDataByList(forecastList)
  dc = window.dc
  dc.getDataByList(forecastList)
  //  console.log(dc.getData().value)
  //  for(var point of dc.getData().value){
  //   viewer.entities.add({
  //           position: point.position,
  //           ellipsoid: {
  //             radii: new Cesium.Cartesian3(10, 10, 10),
  //             material: Cesium.Color.YELLOW,
  //           },
  //         });
  //  }

};
var forecastSort = new ForecastSort()
var forecastTimer = setInterval(() => {
  forecastSort.bubbleSort(forecastList)
}, 5000);

//右下角——异常区按钮显隐
var dataTree;
const handleOpen = (item, show) => {
  toolsStore.$patch((state) => {
    dataTree = state.dataTree
    var id = window.layersManager.getIdByLabel(item.title)
    if (id != -1) {
      dataTree.setChecked(id, show);
    } else {
      ycq.show(item.index, show)
    }
  });
  viewer.scene.forceRender();
};
const handleOpen2 = (item) => {
  var dcPosition = item.position
  var camreaPosition = _getMoveUpCoordinate(dcPosition, 1400)
  viewer.camera.flyTo({
    duration: 1,
    destination: camreaPosition,
    offset: {
      heading: Geowin3D.Math.toRadians(0), // 水平偏角，默认正北 0
      pitch: Geowin3D.Math.toRadians(-90), // 俯视角，默认-90，垂直向下
      // range: 200, //镜头（屏幕）到定位目标点（实体）的距离
    },
  }, 3000)
  // viewer.entities.add({
  //       position: position,
  //       ellipsoid: {
  //         radii: new Cesium.Cartesian3(10, 10, 10),
  //         material: Cesium.Color.YELLOW,
  //       },
  //     });
  function _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }
}
let selectItem = ref("")
const handleOpen3 = (item) => {
  selectItem.value = item
  var camreaPosition = ""
  switch (item.type) {
    case "异常区":
      toolsStore.$patch((state) => {
        dataTree = state.dataTree
        var id = window.layersManager.getIdByLabel(item.title)
        if (id != -1) {
          dataTree.setChecked(id, true);
        } else {
          ycq.show(item.index, true)
        }
      });
      viewer.scene.forceRender();
      if (item.centre) {
        camreaPosition = _getMoveUpCoordinate(item.centre, 300)
      }
      break
    case "断层":
      camreaPosition = _getMoveUpCoordinate(item.position, 1400)
      dc.selectFeatureByTitle(item.title)
      break
    default:
      break
  }
  if (camreaPosition != "") {
    viewer.camera.flyTo({
      duration: 1,
      destination: camreaPosition,
      offset: {
        heading: Geowin3D.Math.toRadians(0), // 水平偏角，默认正北 0
        pitch: Geowin3D.Math.toRadians(-90), // 俯视角，默认-90，垂直向下
        // range: 200, //镜头（屏幕）到定位目标点（实体）的距离
      },
    }, 3000)
  }
  function _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }
}
var selectHandler;
const addSelectInputAction = () => {
  selectHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  selectHandler.setInputAction((movement) => {
    selectItem.value = ''
    window.dc.removeSelect()
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
const removeSelectInputAction = () => {
  selectItem.value = ''
  window.dc.removeSelect()
  selectHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

//设置71巷道透明度
const setHangdao = (show) => {
  var setAlpha = new SetAlphaTool();
  if (!show) {
    setAlpha.setHangdaoAlphaByID("271", 0.2);
    setAlpha.hideOthersHangdaoByIds(['271'])
  } else {
    setAlpha.setHangdaoAlphaByID("271", 1);
    setAlpha.showAllHangdao()
  }
};

function initEcharts() {
  document.getElementById("receptionTrend").removeAttribute('_echarts_instance_')
  myChart = echart.init(document.getElementById("receptionTrend"));
  document.getElementById("receptionTrend2").removeAttribute('_echarts_instance_')
  myChart2 = echart.init(document.getElementById("receptionTrend2"));
  document.getElementById("receptionTrend3").removeAttribute('_echarts_instance_')
  myChart3 = echart.init(document.getElementById("receptionTrend3"));
  
  // myChart.setOption(myChartData);
  myChart.showLoading({
    text: '正在对工作面高度进行采样...',
    color: '#228da8',
    textColor: '#ffffc2',
    maskColor: 'rgba(255, 255, 255, 0)',
    zlevel: 0
  });
}
const resetEcharts = (xlist, ylist, upordowm) => {
  myChartData.xAxis.data = xlist
  if (upordowm == 'down') {
    myChartData.series[1].data = ylist
  } else if (upordowm == 'up') {
    myChartData.series[0].data = ylist
  }
  myChart.hideLoading()
  myChart.setOption(myChartData)
  // myChart.resize();

  myChartData2.xAxis.data = xlist
  if (upordowm == 'down') {
    myChartData2.series[1].data = ylist
  } else if (upordowm == 'up') {
    myChartData2.series[0].data = ylist
  }
  myChart2.setOption(myChartData2)

  myChartData3.xAxis.data = xlist
  if (upordowm == 'down') {
    myChartData3.series[1].data = ylist
  } else if (upordowm == 'up') {
    myChartData3.series[0].data = ylist
  }
  myChart3.setOption(myChartData3)

}
// 下载
const handleDownload = () => { };
window.onresize = function () {
  myChart.resize();
  myChart2.resize();
  myChart3.resize();
};


// 左下角

var stopingBox = new StopingBox();
let resultInfoIist = stopingBox.stopingBoxInfoIist;
let resultList = stopingBox.stopingBoxList;
let resultIndex = stopingBox.stopingBoxIndex;
const changeResult = (index) => {
  resultIndex.value = index;
};
const confirmEvent = (item) => {
  stopingBox.downLoadDoc(item)
}
const cancelEvent = (item) => {
  stopingBox.downLoadDWG(item)
}
//回采超前探 图层控制 点击事件
const changeClick = (item, stopingIndex) => {
  // console.log('resultIndex', resultList.value[stopingIndex])
  if (resultList.value[stopingIndex] == "钻探") {
    if (!item.position) {
      return;
    }
    if (item.check) {
      if (item.zuantanIdx == -1) {
        stopingBox.loadzuantan(item);

      } else {
        stopingBox.showzuantan(item.zuantanIdx, true);

      }
    } else {
      stopingBox.showzuantan(item.zuantanIdx, false);

    }

  } else {
    if (!item.imgUrl) {
      return;
    }
    if (item.check) {
      if (item.imageIdx == -1) {
        stopingBox.loadImage(item);

      } else {
        stopingBox.showImage(item.imageIdx, true);

      }
    } else {
      stopingBox.showImage(item.imageIdx, false);

    }
  }

  viewer.scene.forceRender();
};



//初始化图层列表和模型显隐
const initModelsShow = () => {
  var initModelShow = new InitModelShow()
  initModelShow.initShow()
}







</script>
<style lang="less" scoped>
.stoping {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  // overflow: hidden;
  .left {
    z-index: 1;
    width: 420px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
    // background: rgba(11, 24, 36,.7);
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    padding-right: 18px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .left_box1 {
      width: 100%;
      box-sizing: border-box;
      padding: 0px 0px 24px 0px;
      display: flex;
      flex-direction: column;
      .left_box1_content {
        display: flex;
        width: 100%;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 22px 12px 0 16px;
        flex-wrap: wrap;
        .left_box1_li {
          display: flex;
          width: 49%;
          margin-bottom: 24px;
          justify-content: flex-start;
          .left_box_li_right {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .left_box1_num {
              color: rgb(17, 177, 255);
              font-size: 18px;
              font-weight: undefined;
              line-height: 27px;
              letter-spacing: 0px;
              text-align: left;
            }

            .left_box1_text {
              color: rgb(255, 255, 255);
              font-family: PingFang SC;
              font-size: 16px;
              font-weight: undefined;
              line-height: 24px;
              letter-spacing: 0px;
              text-align: left;
            }
          }


          .left_box_li_img {
            width: 57px;
            height: 52px;
            margin-right: 9px;
          }
        }
      }
    }

    .left_box2 {
      width: 100%;
      height: 702px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .left_box2_ul {
        margin-top: 10px;
        width: 100%;
        height: 24px;
        display: flex;
        padding-left: 10px;
        .right_box1_li {
          height: 30px;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          cursor: pointer;
          color: rgb(198, 198, 198);
          font-family: PingFang SC;
          font-size: 16px;
          background: rgba(148, 148, 148, 0.4);
          border: 1px solid rgb(116, 116, 116);
          border-radius: 15px;
          padding: 0 12px;
          margin-right: 12px;
          margin-bottom: 6px;
        }

        .right_box1_li_active {
          background: rgba(49, 131, 194, 0.4);
          border: 1px solid rgb(0, 179, 255);
          border-radius: 15px;
          color: rgb(255, 255, 255);
        }
      }

      .left_box2_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 10px 20px 0 10px;
        overflow: hidden;

        .right_three_scroll {
          height: 550px;
          overflow: hidden;
        }
        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

          .right_box1_head_text {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(143, 199, 255);
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_index {
            width: 50px;
          }

          .right_box1_title {
            width: 60px;
            font-size: 12px;
            text-align: center;
            display: flex;
            flex-wrap: wrap;
          }

          .right_box1_name {
            width: 100px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            text-align: center;
            padding: 0 8px
          }

          .right_box1_time {
            width: 100px;
            display: flex;
            justify-content: center;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_oper {
            width: 80px;
            display: flex;
            justify-content: center;
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }
  }

  .right {
    z-index: 2;
    width: 344px;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    // background: rgba(11, 24, 36,.7);
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-left: 18px;
    .right_box1 {
      width: 100%;
      height: 366px;
      box-sizing: border-box;
      padding: 0px 22px 0 0px;
      display: flex;
      flex-direction: column;
      .right_contentBox{
        width: 100%;
        height: 340px;
      }
      .right_content1 {
        width: 100%;
        height: 320px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 5px 5px 0 0;
        // overflow: hidden;
        position: absolute;
        top: 40px;
        // background-color:#26c1d1;

        .myChartBox3{
          width: 320px;
          height: 30px;
          overflow: hidden;
          position: absolute;
          left:0px;
          top: 25px;
          z-index: 999;
          // top: 20px;
          // background-color: #fdff4d;
          
        }
        
        .myChartBox2{
          width: 50px;
          height: 294px;
          overflow: hidden;
          position: absolute;
          left:0px;
          top: 20px;
          // background-color: #fdff4d;
          
        }
        .myChartBox{
          width: 270px;
          height: 294px;
          overflow: hidden;
          position: absolute;
          left:45px;
          top: 20px;
          // background-color:red;
          overflow-x: scroll;

          // 优化滑动条样式
          &::-webkit-scrollbar {
            height: 8px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(51, 214, 255, 0.6);
            border-radius: 4px;
            cursor: pointer;

            &:hover {
              background: rgba(51, 214, 255, 0.8);
            }
          }
        }
        .myChart {
          width: 520px;
          height: 294px;
          box-sizing: border-box;
          padding: 5px 0px 0 0;
          
        }

        .myChart2 {
          width: 520px;
          height: 294px;
          box-sizing: border-box;
          padding: 5px 0px 0 0;
          
        }
        .myChart3 {
          width: 320px;
          height: 294px;
          box-sizing: border-box;
          padding: 0px 0px 0 0;
          
        }

      }
    }

    .right_box2 {
      width: 100%;
      // height: 552px;
      flex:1;
      box-sizing: border-box;
      padding: 15px 0 10px 0px;
      display: flex;
      flex-direction: column;
      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 20px 20px 0 10px;
        overflow-y: hidden;
        overflow-x: scroll;

        // 优化滑动条样式
        &::-webkit-scrollbar {
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(51, 214, 255, 0.6);
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            background: rgba(51, 214, 255, 0.8);
          }
        }

        .right_three_scroll {
          // overflow-x: scroll;
          height: 340px;
          overflow: hidden;
        }
        .right_box1_table_sticky {
          position: sticky;
          overflow-y: hidden;
          /*纵向不滚动*/
          overflow-x: auto;
          /*横向滚动*/
        }

        .right_table_box {
          width: 500px;
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          // justify-content: center;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

          .right_box1_head_text {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(143, 199, 255);
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_index {
            width: 40px;
          }

          .right_box1_name {
            width: 140px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            justify-content: flex-start;
          }

          .right_box1_type {
            width: 80px;
          }

          .right_box1_distance {
            width: 90px;
          }

          .right_box1_time {
            width: 120px;
          }

          .right_box1_upTime {
            width: 120px;
          }

          .right_box1_oper {
            width: 100px;
            display: flex;
            justify-content: center;

            span {
              margin-left: 10px;
              width: 49px;
              height: 20px;
              font-size: 12px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;

              &:hover {
                filter: brightness(1.1);
              }
            }

            .right_box1_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }

            .right_box1_down {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
      .right_box2_content{
        overflow-x: scroll;
        height: 490px;

        // 优化滑动条样式
        &::-webkit-scrollbar {
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(51, 214, 255, 0.6);
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            background: rgba(51, 214, 255, 0.8);
          }
        }
      }
    }
    
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>
