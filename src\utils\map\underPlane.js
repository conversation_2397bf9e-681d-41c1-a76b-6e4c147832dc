class UnderPlane {
    constructor(viewer, position) {
        this.viewer = viewer
        this.position = position
        this.plane=undefined
        this.createPlane()
    }

    createPlane(){
        this.plane = this.viewer.entities.add({
            name: "plane",
            position: this.position,
            plane: {
                plane: new window.Cesium.Plane(window.Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
                dimensions: new window.Cesium.Cartesian2(1400000, 1400000), //指定平面的宽度和高度。
                material: new window.Cesium.Color(0 / 255, 23 / 255, 45 / 255),
                fill: true, //是否填充
            },
        });
        this.plane._plane._material._color._value.alpha=0.01
        
    }

    setPosition(position){
        this.plane.position=position
    }

    resetPosition(){
        this.plane.position=this.position
    }

    setAlpha(){

    }


}

export default UnderPlane