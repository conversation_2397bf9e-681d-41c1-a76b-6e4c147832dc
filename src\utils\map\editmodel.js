class Transform{
    constructor(viewer){
        this._viewer = viewer;
        this.th = new Geowin3D.GwMicroApp.GwTransformHelper(this._viewer);
    }
    /**
     * tileset数组
     * @param {*} tilesets 
     */
    start(tilesets){
        this.th.cancel();
        this.th.setData(tilesets);
        this.th.start("MOVE|ROTATE|SCALE");
    }
    
    cancel(){
        this.th.cancel();
    }
}
export default Transform