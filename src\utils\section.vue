<template>
  <div ref="sectionContainer" v-loading="loading" class="sectionContainer" :class="{'open':isShowCanvas,'close':!isShowCanvas}" @mousemove="handleMouseMove">
    <canvas v-show="isSection" ref="canvas" class="canvas" />
    <div v-show="isSection" ref="cursor" class="cursor" />
    <div v-show="selectCoal.name !== ''" ref="label" class="label">
      <div>
        <span class="selectLabel">煤层：</span>
        <span class="selectValue">{{ selectCoal.name }}</span>
      </div>
      <div>
        <span class="selectLabel">最高点：</span>
        <span class="selectValue">{{ selectCoal.maxHeight }} m</span>
      </div>
      <div>
        <span class="selectLabel">最低点：</span>
        <span class="selectValue">{{ selectCoal.minHeight }} m</span>
      </div>
      <div>
        <span class="selectLabel">总体积：</span>
        <span class="selectValue">{{ selectCoal.name === '地表' ? '--' : selectCoal.volume }} m³</span>
      </div>
      <div>
        <span class="selectLabel">煤层厚度：</span>
        <span class="selectValue">{{ selectCoal.name === '地表' ? '--' : selectCoal.thickness }} m</span>
      </div>
      <!--      <span>-->
      <!--        煤层：{{ selectCoal.name }}<br>最高点：{{ selectCoal.maxHeight }} m<br>-->
      <!--        最低点：{{ selectCoal.minHeight }} m<br>总体积：{{ selectCoal.volume }} m³<br>-->
      <!--        此处煤层厚度：{{ selectCoal.thickness }} m-->
      <!--      </span>-->
    </div>
    <div class="float-section">
      <img src="@/assets/images/section/section_off.png" title="煤层剖面" alt="" @click="onDrawPolyline">
      <img src="@/assets/images/measure/measure_clear.png" title="清空" alt="" @click="clear">
    </div>
    <span v-show="isSection" class="uploadButton" @click="toImage">导出</span>
  </div>
</template>

<script>
export default {
    name: 'Section',
    data() {
        return {
            tileSet: [
                { file: '1.b3dm', name: '2-1煤', color: '#00FF00', batchId: '87DB30C8-D103-468d-BC0B-A9E8A2E74E3F' },
                { file: '2.b3dm', name: '1-2+3煤2', color: '#FFFF00', batchId: '05516842-9B71-4547-A333-668EE05E0AAD' },
                { file: '3.b3dm', name: '1-2煤4', color: '#00FFFF', batchId: 'C93C8FE9-671F-45be-AB10-BA6700E8F144' },
                { file: '4.b3dm', name: '2-1+2煤', color: '#FFFF00', batchId: 'E3C22306-22C3-40ac-AC32-4BEABD6755B1' },
                { file: '5.b3dm', name: '3-1煤3', color: '#00FF00', batchId: 'C38E7859-3A49-436d-A175-AFF250C5BD1B' },
                { file: '6.b3dm', name: '3煤', color: '#00FFFF', batchId: '09F4CE30-6B18-458a-A6F6-A585198FBA7A' },
                { file: '7.b3dm', name: '3-2+3煤', color: '#FFFF00', batchId: 'E5535EE6-B323-4352-95B2-84C2DEEB2725' },
                { file: '8.b3dm', name: '3-2煤', color: '#FF00FF', batchId: '834E064F-C79D-4143-B597-E82AB60ABF61' },
                { file: '9.b3dm', name: '3-3煤上盘', color: '#FFFF00', batchId: '2DBADAAC-FE30-48ac-801A-2B59079340B4' },
                { file: '10.b3dm', name: 'B煤', color: '#FF0000', batchId: '660A7F3A-6A15-439a-A9AF-CAFE2A2C6CF1' },
                { file: '11.b3dm', name: '2-1煤下盘', color: '#00FF00', batchId: 'E8687F47-484A-4574-9D7A-8092E33816C0' },
                { file: '12.b3dm', name: '2-1+2煤', color: '#FFFF00', batchId: 'DC96D15C-4226-4371-A327-4131158B3DDB' },
                { file: '13.b3dm', name: '1-2煤3', color: '#0000FF', batchId: 'C99D29BE-FF58-444a-8978-777C4126701F' },
                { file: '14.b3dm', name: '1-2+3煤1', color: '#FF0000', batchId: '741B1E16-AD49-4ef6-A666-03C40B0AD32D' },
                { file: '15.b3dm', name: '3-1煤2', color: '#FF00FF', batchId: '3A62188E-223D-4039-A5DD-ED3F6E4E0182' },
                { file: '16.b3dm', name: '3-3煤下盘', color: '#00FFFF', batchId: '87AF736B-5946-4414-8BCC-28EA7DE65676' },
                { file: '17.b3dm', name: '3-3煤2', color: '#FF00FF', batchId: 'EBF2DB26-04AB-45ab-BEA0-6B1D35204B27' },
                { file: '18.b3dm', name: '1-2煤2', color: '#00FF00', batchId: 'DC04ACDA-9A3A-474e-9C6A-6760D4DB4595' },
                { file: '19.b3dm', name: '1-2煤1', color: '#FF0000', batchId: '2159916E-3428-4cce-A983-08210266D565' },
                { file: '20.b3dm', name: '3-1煤1', color: '#FFFF00', batchId: 'F1AC3C8B-91AC-4a03-BDB7-CDEF96961240' },
                { file: '21.b3dm', name: '3-3煤1', color: '#FF0000', batchId: '0F77E5CE-62BC-4d7a-B6C7-E8C0A6A01B1C' }
            ],
            loading: false,
            vlinePositions: [],
            sectionLineStart: null,
            sectionLineEnd: null,
            blank: 50,
            blankY: 30,
            groundLinePositions: [],
            isSection: false,
            selectCoal: {
                name: '',
                thickness: -1,
                minHeight: -1,
                maxHeight: -1,
                volume: -1
            },
            groundBatchId: '',
            groundFile: '',
            isShowCanvas: false
        }
    },
    mounted() {
        this.initSection()
        this.clear()
    },
    methods: {
        initSection() {
            this.viewer = window.designViewer
            this.add3DTiles()
        },
        add3DTiles() {
            const coalTileset = new Cesium.Cesium3DTileset({
                url: '/3dtiles/coal/tileset.json'
            })
            this.viewer.scene.primitives.add(coalTileset)

            // const longitude = 119.748350542974
            // const latitude = 49.4081974919347
            // const height = 500.0865
            // const heading = 0
            // coalTileset.readyPromise.then((argument) => {
            //     // 经纬度、高转笛卡尔坐标
            //     const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height)
            //     const mat = Cesium.Transforms.eastNorthUpToFixedFrame(position)
            //     const rotationX = Cesium.Matrix4.fromRotationTranslation(Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(heading)))
            //     Cesium.Matrix4.multiply(mat, rotationX, mat)
            //     coalTileset._root.transform = mat
            // })

            this.viewer.zoomTo(coalTileset, new Cesium.HeadingPitchRange(0.0, -0.5, 0.0))
            this.coalTileset = coalTileset

            coalTileset.allTilesLoaded.addEventListener(() => {
                this.tileSet = []
                const childrens = coalTileset.root.children
                childrens.forEach(children => {
                    const url = children._header.content.url
                    const batchId = children.content.getFeature(0).getProperty('batchId')
                    const name = children.content.getFeature(0).getProperty('name')
                    let nameArray = []
                    if (name && name !== '') {
                        nameArray = name.replace(/\[|\]/g, '').split('_')
                    }

                    if (nameArray.length < 5) {
                        this.tileSet.push({
                            file: url,
                            batchId: batchId,
                            color: 'rgb(0, 0, 0)',
                            minHeight: -1,
                            maxHeight: -1,
                            volume: -1
                        })
                    }

                    this.tileSet.push({
                        file: url,
                        name: nameArray[1],
                        batchId: batchId,
                        color: 'rgb(' + (nameArray[2]) + ')',
                        minHeight: parseFloat((nameArray[3].split(','))[0]).toFixed(2),
                        maxHeight:  parseFloat((nameArray[3].split(','))[1]).toFixed(2),
                        volume: parseFloat(nameArray[4]).toFixed(2)
                    })
                    if (nameArray[1] === '地表') {
                        this.groundBatchId = batchId
                        this.groundFile = url
                    }
                })
                this.showGround(false)
            })
        },
        showGround(isShow) {
            if (isShow) {
                this.coalTileset.style = new Cesium.Cesium3DTileStyle()
            } else {
                const groundContent = "${batchId} === \'" + this.groundBatchId + "\'"
                this.coalTileset.style = new Cesium.Cesium3DTileStyle({
                    color: {
                        conditions: [
                            [groundContent, 'rgba(255, 255, 255, 0)']
                        ]
                    }
                })
            }
        },
        showSingleCoal(fileName) {
            if (!fileName || fileName === '') {
            //     this.coalTileset.style = new Cesium.Cesium3DTileStyle()
            //
            //     // const ground = this.tileSet.find((x) => {
            //     //     return x.name === '地表'
            //     // })
            //     const groundContent = "${batchId} === \'" + groundBatchId + "\'"
            //     this.coalTileset.style = new Cesium.Cesium3DTileStyle({
            //         color: {
            //             conditions: [
            //                 [groundContent, 'rgba(255, 255, 255, 0)']
            //             ]
            //         }
            //     })

                this.showGround(false)
                return
            }
            const coal = this.tileSet.find(x => {
                return x.file === fileName
            })
            if (!coal) {
                return
            }

            const selectContent = "${batchId} !== \'" + coal.batchId + "\'"
            this.coalTileset.style = new Cesium.Cesium3DTileStyle({
                color: {
                    conditions: [
                        [selectContent, 'rgba(255, 255, 255, 0)']
                    ]
                }
            })
            console.log(this.coalTileset.style)
        },
        section(start, end) {
            this.isShowCanvas = true
            console.time('section')
            this.isSection = true
            this.loading = true
            let interval = 5.0 // 分辨率：米s

            this.sectionLineStart = start
            this.sectionLineEnd = end

            const startC = Cesium.Cartographic.fromCartesian(start)
            const endC = Cesium.Cartographic.fromCartesian(end)

            const geodesic = new Cesium.EllipsoidGeodesic(startC, endC)
            const distance = geodesic.surfaceDistance
            let count = Math.round((distance / interval) + 0.5)
            count = count > 100 ? 100 : count
            interval = distance / count
            // count += 1

            let groundFinish = false
            let d3dmFinish = false
            const positions = []
            const positionsCartographic = []
            const crossPointsRs = {}
            const crossPointsRe = {}
            const groundHeights = []
            let maxHeight = -9999
            let minHeight = 9999
            for (let i = 0; i <= count; i++) {
                const clipPoint = Cesium.Cartesian3.lerp(start, end, i / count, new Cesium.Cartesian3())
                positions.push(clipPoint)
                positionsCartographic.push(Cesium.Cartographic.fromCartesian(clipPoint))
            }
            this.viewer.scene.globe.depthTestAgainstTerrain = false
            // 地形
            // const promise = Cesium.sampleTerrainMostDetailed(this.viewer.terrainProvider, positionsCartographic)
            // Cesium.when(promise, updatedPositions => {
            //     this.groundLinePositions = []
            //     updatedPositions.forEach(p => {
            //         this.groundLinePositions.push(Cesium.Cartographic.toCartesian(p))
            //         groundHeights.push(p.height)
            //         if (maxHeight < p.height) {
            //             maxHeight = p.height
            //         } else if (minHeight > p.height && p.height !== 0) {
            //             minHeight = p.height
            //         }
            //     })
            //     groundFinish = true
            //     if (groundFinish && d3dmFinish) {
            //         this.allFinished(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights)
            //     }
            // })

            groundFinish = true

            // 模型
            // 异步
            const allCrossPromise = this.getCrossPointsPromise(positions)
            console.time('3dtiles')
            Cesium.when.all(allCrossPromise, crossResults => {
                console.timeEnd('3dtiles')
                // console.log(crossResults)
                for (let i = 0; i < crossResults.length; i++) {
                    const crossResult = crossResults[i]
                    if (i % 2 === 0) {
                        const index = i / 2
                        crossResult.forEach(crossPositon => {
                            const b3dm = crossPositon.object._content._tile.content.tile._header.content.url
                            const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
                            if (b3dm in crossPointsRs) {
                                crossPointsRs[b3dm].push([index, pnt.height])
                            } else {
                                const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
                                crossPointsRs[b3dm] = [[index, pnt.height]]
                            }

                            if (maxHeight < pnt.height) {
                                maxHeight = pnt.height
                            }
                        })
                    } else {
                        const index = (i - 1) / 2
                        crossResult.forEach(crossPositon => {
                            const b3dm = crossPositon.object._content._tile.content.tile._header.content.url
                            const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
                            if (b3dm in crossPointsRe) {
                                crossPointsRe[b3dm].push([index, pnt.height])
                            } else {
                                const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
                                crossPointsRe[b3dm] = [[index, pnt.height]]
                            }

                            if (minHeight > pnt.height && pnt.height !== 0) {
                                minHeight = pnt.height
                            }
                        })
                    }
                }
                d3dmFinish = true

                if (groundFinish && d3dmFinish) {
                    this.allFinished(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights)
                }
            })

            // // 同步
            // for (let i = 0; i <= count; i++) {
            //     const crossResult = this.getCrossPoints(positions[i])
            //     crossResult.rs.forEach(crossPositon => {
            //         const b3dm = crossPositon.object._content._tile.content.tile._header.content.url
            //         const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
            //         if (b3dm in crossPointsRs) {
            //             crossPointsRs[b3dm].push([i, pnt.height])
            //         } else {
            //             const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
            //             crossPointsRs[b3dm] = [[i, pnt.height]]
            //         }
            //
            //         if (maxHeight < pnt.height) {
            //             maxHeight = pnt.height
            //         }
            //     })
            //     crossResult.re.forEach(crossPositon => {
            //         const b3dm = crossPositon.object._content._tile.content.tile._header.content.url
            //         const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
            //         if (b3dm in crossPointsRe) {
            //             crossPointsRe[b3dm].push([i, pnt.height])
            //         } else {
            //             const pnt = Cesium.Cartographic.fromCartesian(crossPositon.position)
            //             crossPointsRe[b3dm] = [[i, pnt.height]]
            //         }
            //
            //         if (minHeight > pnt.height && pnt.height !== 0) {
            //             minHeight = pnt.height
            //         }
            //     })
            // }
            // d3dmFinish = true
            // if (groundFinish && d3dmFinish) {
            //     this.allFinished(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights)
            // }
        },
        allFinished(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights) {
            this.showGround(false)
            console.timeEnd('section')
            if (this.polyline) {
                this.viewer.entities.remove(this.polyline)
                this.polyline = null
            }

            this.makeSection(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights)
            this.drawAuxiliaryLineOnMap()
            this.drawClippingPlane()
            this.loading = false
        },
        getCrossPointsPromise(positions) {
            const allPromise = []
            positions.forEach(position => {
                const pnt = Cesium.Cartographic.fromCartesian(position)
                const startP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, 1000)
                const endP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, -300)

                // 计算射线的方向，目标点 视域点
                const directionS = Cesium.Cartesian3.normalize(Cesium.Cartesian3.subtract(endP, startP, new Cesium.Cartesian3()), new Cesium.Cartesian3())
                const directionE = new Cesium.Cartesian3(-1.0 * directionS.x, -1.0 * directionS.y, -1.0 * directionS.z)
                const rayS = new Cesium.Ray(startP, directionS)
                const rayE = new Cesium.Ray(endP, directionE)

                const resultsS = this.viewer.scene.drillPickFromRayMostDetailed(rayS, 10, [this.polyline])
                const resultsE = this.viewer.scene.drillPickFromRayMostDetailed(rayE, 10, [this.polyline])
                allPromise.push(resultsS, resultsE)
            })
            return allPromise
        },
        getCrossPoints(position) {
            const pnt = Cesium.Cartographic.fromCartesian(position)
            const startP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, 1000)
            const endP = Cesium.Cartesian3.fromRadians(pnt.longitude, pnt.latitude, -300)

            // 计算射线的方向，目标点 视域点
            const directionS = Cesium.Cartesian3.normalize(Cesium.Cartesian3.subtract(endP, startP, new Cesium.Cartesian3()), new Cesium.Cartesian3())
            const directionE = new Cesium.Cartesian3(-1.0 * directionS.x, -1.0 * directionS.y, -1.0 * directionS.z)
            const rayS = new Cesium.Ray(startP, directionS)
            const rayE = new Cesium.Ray(endP, directionE)

            const resultsS = this.viewer.scene.drillPickFromRay(rayS, 10, [this.polyline])
            const resultsE = this.viewer.scene.drillPickFromRay(rayE, 10, [this.polyline])

            return { rs: resultsS, re: resultsE }
        },
        toImage() {
            const canvas = this.$refs.canvas
            canvas.toBlob(function(blob) {
                const a = document.createElement('a')
                const body = document.getElementsByTagName('body')
                document.body.appendChild(a)
                a.download = 'img' + '.jpg'
                a.href = window.URL.createObjectURL(blob)

                a.click()
                body.removeChild('a')
            })
        },
        makeSection(start, end, distance, interval, count, maxHeight, minHeight, crossPointsRs, crossPointsRe, groundHeights) {
            maxHeight = (parseInt(maxHeight / 10) + 1) * 10
            minHeight = (parseInt(minHeight / 10) - 1) * 10

            const container = this.$refs.sectionContainer
            const canvas = this.$refs.canvas
            canvas.width = container.offsetWidth
            canvas.height = container.offsetHeight
            const context = canvas.getContext('2d')
            const dataWidth = canvas.width - this.blank * 2
            const dataHeight = canvas.height - this.blankY * 2

            // background
            this.renderBackground(context, dataWidth, dataHeight)
            // this.renderTerrain(context, dataWidth, dataHeight, count, maxHeight, minHeight, groundHeights)
            this.renderCoal(context, dataWidth, dataHeight, count, maxHeight, minHeight, crossPointsRs, crossPointsRe)

            this.renderHeight(context, dataWidth, dataHeight, maxHeight, minHeight)
            this.renderDistance(context, dataWidth, dataHeight, distance)
        },
        renderBackground(context, width, height) {
            context.save()
            context.beginPath()
            context.fillStyle = '#5A5A5AEE'
            context.strokeStyle = '#5A5A5AEE'
            context.fillRect(0, 0, width + this.blank * 2, height + this.blankY * 2)
            context.stroke()
            context.restore()
        },
        renderTerrain(context, width, height, count, maxHeight, minHeight, groundHeights) {
            context.save()
            const xAvg = width / count
            let x = 0
            let y = 0
            let y1 = 0
            let terrain = minHeight
            context.lineWidth = 1
            context.strokeStyle = '#B7B3AB'
            const linearGrad = context.createLinearGradient(0, this.blankY, 0, height)
            linearGrad.addColorStop(0.0, '#EED6A044')
            linearGrad.addColorStop(1.0, '#FBF5E844')
            context.fillStyle = linearGrad
            context.beginPath()
            const deltaMeter = Math.abs(maxHeight - minHeight)
            for (let i = 0; i < count; i++) {
                terrain = groundHeights[i]
                terrain = terrain === 0 ? minHeight : terrain
                x = xAvg * i + xAvg / 2 + this.blank
                y = height - (terrain - minHeight) / deltaMeter * height + this.blankY
                if (i === 0) {
                    context.moveTo(x, y)
                    y1 = y
                } else {
                    context.lineTo(x, y)
                }
            }
            context.lineTo(x, height + this.blankY)
            context.lineTo(this.blank, height + this.blankY)
            context.lineTo(this.blank, y1)
            context.stroke()
            context.fill()
            context.restore()
        },
        renderCoal(context, width, height, count, maxHeight, minHeight, crossPointsRs, crossPointsRe) {
            this.allRegions = []
            const xAvg = width / (count + 1)
            const deltaYMeter = Math.abs(maxHeight - minHeight) / height
            context.save()

            Object.keys(crossPointsRs).forEach(key => {
                const curRs = crossPointsRs[key]
                const curRe = crossPointsRe[key]

                context.lineWidth = 0.5
                const tile = this.tileSet.find(function(x) {
                    return x.file === key
                })
                context.strokeStyle = tile.color
                context.fillStyle = context.strokeStyle

                if (!curRs || !curRe) {
                    return
                }

                const crossPointsLength = curRs.length <= curRe.length ? curRs.length : curRe.length

                let rsPos = []
                let rePos = []
                const savePos = []

                const isGround = key === this.groundFile

                for (let i = 0; i < crossPointsLength; ++i) {
                    const xRs = Math.round(xAvg * curRs[i][0] + xAvg / 2 + this.blank)
                    const yRs = Math.round(height - (curRs[i][1] - minHeight) / deltaYMeter + this.blankY)
                    rsPos.push([xRs, yRs])

                    const xRe = Math.round(xAvg * curRe[i][0] + xAvg / 2 + this.blank)
                    const yRe = Math.round(height - (curRe[i][1] - minHeight) / deltaYMeter + this.blankY)
                    rePos.push([xRe, yRe])

                    // const isStop = (curRs[i + 1][0] - curRs[i][0] > 1 || curRe[i + 1][0] - curRe[i][0] > 1 || Math.abs(curRe[i + 1][1] - curRe[i][1]) > 18)
                    if (isGround ? i === crossPointsLength - 1 : i === crossPointsLength - 1 || curRs[i + 1][0] - curRs[i][0] > 1 || curRe[i + 1][0] - curRe[i][0] > 1 || Math.abs(curRe[i + 1][1] - curRe[i][1]) > 18) {
                        context.beginPath()
                        context.moveTo(rsPos[0][0], rsPos[0][1])
                        // savePos.push({ x: rsPos[0][0], y: rsPos[0][1] })
                        savePos.push([rsPos[0][0], rsPos[0][1]])
                        for (let m = 1; m < rsPos.length; ++m) {
                            context.lineTo(rsPos[m][0], rsPos[m][1])
                            // savePos.push({ x: rsPos[m][0], y: rsPos[m][1] })
                            savePos.push([rsPos[m][0], rsPos[m][1]])
                        }
                        for (let m = rePos.length - 1; m >= 0; --m) {
                            context.lineTo(rePos[m][0], rePos[m][1])
                            // savePos.push({ x: rePos[m][0], y: rePos[m][1] })
                            savePos.push([rePos[m][0], rePos[m][1]])
                        }
                        context.lineTo(rsPos[0][0], rsPos[0][1])
                        // savePos.push({ x: rsPos[0][0], y: rsPos[0][1] })
                        savePos.push([rsPos[0][0], rsPos[0][1]])
                        context.stroke()
                        context.fill()
                        rsPos = []
                        rePos = []
                        this.allRegions.push({ pos: savePos, name: tile.name, file: tile.file,
                            minHeight: tile.minHeight, maxHeight: tile.maxHeight, volume: tile.volume })
                    }
                }
                context.restore()
            })
        },
        renderHeight(context, width, height, maxHeight, minHeight) {
            context.save()
            context.lineWidth = 0.5
            context.strokeStyle = '#00000099'
            context.fillStyle = '#FFFFFF'
            context.font = '14px Arial'
            context.textBaseline = 'top'
            context.setLineDash([10, 10])
            this.deltaYMeter = Math.abs(maxHeight - minHeight) / height
            const tenCounts = (maxHeight - minHeight) / 10
            const sevenNum = tenCounts % 7
            if (sevenNum === 0) {
                for (let i = 1; i < 7; ++i) {
                    const hY = (tenCounts / 7) * i * 10
                    const y = Math.round(height - hY / this.deltaYMeter) + this.blankY

                    context.beginPath()
                    context.moveTo(this.blank, y)
                    context.lineTo(this.blank + width, y)
                    context.fillText(hY + minHeight + 'm ', 15, y - 5)
                    context.stroke()
                }
            } else {
                for (let i = 0; i < 7; ++i) {
                    const hY = (((tenCounts - sevenNum) / 7) * i + sevenNum) * 10
                    const y = Math.round(height - hY / this.deltaYMeter) + this.blankY

                    context.beginPath()
                    context.moveTo(this.blank, y)
                    context.lineTo(this.blank + width, y)
                    context.save()
                    context.fillText(hY + minHeight, 15, y - 5)
                    context.save()
                    context.stroke()
                }
            }
            context.beginPath()
            context.lineWidth = 1.0
            context.strokeStyle = '#FFFFFF'
            context.setLineDash([0])
            context.font = '14px Arial'
            context.textBaseline = 'bottom'
            context.moveTo(this.blank, height + this.blankY)
            context.lineTo(this.blank + width, height + this.blankY)
            context.moveTo(this.blank, height + this.blankY)
            context.lineTo(this.blank, this.blankY)
            context.fillText('高程(m)', 15, this.blankY - 5)
            context.fillText('距离(m)', this.blank + width - 25, this.blankY + height + 20)
            context.stroke()
            context.restore()
        },
        renderDistance(context, width, height, distance) {
            context.save()
            context.lineWidth = 0.5
            context.strokeStyle = '#FFFFFF'
            context.fillStyle = '#FFFFFF'
            context.font = '14px Arial'
            context.textBaseline = 'top'

            const tenDistance = parseInt(distance / 10) * 10
            const deltaXMeter = distance / width
            const tenCounts = tenDistance / 10
            const num = tenCounts % 30

            if (num === 0) {
                for (let i = 1; i < 30; ++i) {
                    const hX = (tenCounts / 30) * i * 10
                    const x = Math.round(hX / deltaXMeter) + this.blank

                    context.beginPath()
                    context.moveTo(x, this.blankY + height)
                    context.lineTo(x, this.blankY + height + 5)
                    context.fillText(hX + 'm ', x, this.blankY + height + 6)
                    context.stroke()
                }
            } else {
                for (let i = 0; i < 30; ++i) {
                    const hX = (((tenCounts - num) / 30) * i + num) * 10
                    const x = Math.round(hX / deltaXMeter) + this.blank

                    context.beginPath()
                    context.moveTo(x, this.blankY + height)
                    context.lineTo(x, this.blankY + height + 5)
                    context.fillText(hX, x - context.measureText(hX).width / 2, this.blankY + height + 6)
                    context.stroke()
                }
            }
        },
        getCurPoints(time, result) {
            return this.vlinePositions
        },
        handleMouseMove(event) {
            if (!this.isSection) {
                return
            }
            if (event.offsetX < 50 || event.offsetY < 0) {
                this.vlinePositions = []
                return
            }
            console.log(event.offsetX, event.offsetY)
            this.$refs.cursor.style.marginLeft = event.offsetX + 'px'

            const start = this.sectionLineStart
            const end = this.sectionLineEnd

            const clipPoint = Cesium.Cartesian3.lerp(start, end, (event.offsetX - this.blank) / (this.$refs.sectionContainer.offsetWidth - this.blank * 2), new Cesium.Cartesian3())
            let topP = Cesium.Cartographic.fromCartesian(clipPoint)
            let bottomP = Cesium.Cartographic.fromCartesian(clipPoint)
            topP.height = 1000
            bottomP.height = -1000
            topP = Cesium.Cartographic.toCartesian(topP)
            bottomP = Cesium.Cartographic.toCartesian(bottomP)
            this.vlinePositions = [topP, bottomP]

            if (Array.isArray(this.allRegions)) {
                for (let i = 0; i < this.allRegions.length; ++i) {
                    const region = this.allRegions[i]
                    const result = this.isInPolygon([event.offsetX, event.offsetY], region.pos)
                    if (result.inner) {
                        this.selectCoal = {
                            name: region.name,
                            thickness: (((result.height + 1) * this.deltaYMeter) + (((Math.random() - 0.5) * 0.2) * this.deltaYMeter)).toFixed(2),
                            minHeight: region.minHeight,
                            maxHeight: region.maxHeight,
                            volume: region.volume
                        }
                        this.$refs.label.style.marginLeft = (this.$refs.sectionContainer.offsetWidth - event.offsetX < 200 ? event.offsetX - 200 : event.offsetX + 10) + 'px'
                        this.$refs.label.style.top = (event.offsetY > 120 ? event.offsetY - 100 : event.offsetY - 25) + 'px'
                        this.coalName = region.name + '  高度：' + (((result.height + 1) * this.deltaYMeter) + (((Math.random() - 0.5) * 0.2) * this.deltaYMeter)).toFixed(2) + 'm'
                        this.showSingleCoal(region.file)
                        return
                    }
                }
            }

            this.showSingleCoal('')
            this.selectCoal = {
                name: '',
                thickness: -1,
                minHeight: -1,
                maxHeight: -1,
                volume: -1
            }
        },
        isInPolygon(checkPoint, polygonPoints) {
            let counter = 0
            let i
            let xInters
            let p1, p2
            const height = []
            let inner = false
            const pointCount = polygonPoints.length
            p1 = polygonPoints[0]

            for (i = 1; i <= pointCount; i++) {
                p2 = polygonPoints[i % pointCount]
                if (checkPoint[0] > Math.min(p1[0], p2[0]) && checkPoint[0] <= Math.max(p1[0], p2[0])) {
                    height.push(p2[1])
                    if (checkPoint[1] <= Math.max(p1[1], p2[1])) {
                        if (p1[0] !== p2[0]) {
                            xInters = ((checkPoint[0] - p1[0]) * (p2[1] - p1[1])) / (p2[0] - p1[0]) + p1[1]
                            if (checkPoint[1] === xInters || Math.abs(checkPoint[1] - xInters) < 1.5) {
                                inner = true
                            }
                            if (p1[1] === p2[1] || checkPoint[1] <= xInters) {
                                counter++
                            }
                        }
                    }
                }
                p1 = p2
            }
            if (inner) {
                return { inner: true, height: Math.abs(height[1] - height[0]) }
            }
            if (counter % 2 === 0) {
                return { inner: false, height: 0 }
            } else {
                return { inner: true, height: Math.abs(height[1] - height[0]) }
            }
        },
        drawAuxiliaryLineOnMap() {
            this.groundLine = this.viewer.entities.add({
                polyline: {
                    positions: this.groundLinePositions,
                    width: 2,
                    material: Cesium.Color.YELLOW
                }
            })

            this.footLine = this.viewer.entities.add({
                polyline: {
                    positions: new Cesium.CallbackProperty(this.getCurPoints, false),
                    width: 1,
                    material: new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.BLACK,
                        dashLength: 10
                    })
                }
            })
        },
        drawClippingPlane() {
            const inverseTransform = this.getInverseTransform(this.coalTileset)
            const clippingPlane = this.createPlane(this.sectionLineEnd, this.sectionLineStart, inverseTransform)
            this.clippingPlanes = new Cesium.ClippingPlaneCollection({
                planes: [
                    clippingPlane
                ],
                edgeWidth: 2.0,
                edgeColor: Cesium.Color.BLACK
            })
            this.coalTileset.clippingPlanes = this.clippingPlanes

            this.sectionWall = this.viewer.entities.add({
                name: 'SectionWall',
                wall: {
                    positions: [this.sectionLineStart, this.sectionLineEnd],
                    minimumHeights: [-1000.0, -1000.0],
                    maximumHeights: [1000.0, 1000.0],
                    material: Cesium.Color.BLACK.withAlpha(0.1)
                }
            })

            let heading = Math.atan2(clippingPlane.normal.y, clippingPlane.normal.x) - Cesium.Math.PI_OVER_TWO
            heading = Cesium.Math.TWO_PI - Cesium.Math.zeroToTwoPi(heading)
            this.viewer.zoomTo(this.sectionWall, new Cesium.HeadingPitchRange(heading - 0.2, -0.4, 0.0))
            this.viewer.scene.globe.translucency.frontFaceAlpha = 1.0
        },
        /**
         * 计算坐标转换矩阵
         */
        getInverseTransform(tileSet) {
            let transform
            const tmp = tileSet.root.transform
            if ((tmp && tmp.equals(Cesium.Matrix4.IDENTITY)) || !tmp) {
                // 如果root.transform不存在，则3DTiles的原点变成了boundingSphere.center
                transform = Cesium.Transforms.eastNorthUpToFixedFrame(tileSet.boundingSphere.center)
            } else {
                transform = Cesium.Matrix4.fromArray(tileSet.root.transform)
            }
            return Cesium.Matrix4.inverseTransformation(transform, new Cesium.Matrix4())
        },
        /**
         * 坐标点转换
         */
        getOriginCoordinateSystemPoint(point, inverseTransform) {
            return Cesium.Matrix4.multiplyByPoint(
                inverseTransform, point, new Cesium.Cartesian3(0, 0, 0))
        },
        createPlane(p1, p2, inverseTransform) {
            // 将仅包含经纬度信息的p1,p2，转换为相应坐标系的cartesian3对象
            const p1C3 = this.getOriginCoordinateSystemPoint(p1, inverseTransform)
            const p2C3 = this.getOriginCoordinateSystemPoint(p2, inverseTransform)

            // 定义一个垂直向上的向量up
            const up = new Cesium.Cartesian3(0, 0, 10)
            //  right 实际上就是由p1指向p2的向量
            const right = Cesium.Cartesian3.subtract(p2C3, p1C3, new Cesium.Cartesian3())

            // 计算normal， right叉乘up，得到平面法向量，这个法向量指向right的右侧
            let normal = Cesium.Cartesian3.cross(right, up, new Cesium.Cartesian3())
            normal = Cesium.Cartesian3.normalize(normal, normal)

            // 由于已经获得了法向量和过平面的一点，因此可以直接构造Plane,并进一步构造ClippingPlane
            const planeTmp = Cesium.Plane.fromPointNormal(p1C3, normal)
            return Cesium.ClippingPlane.fromPlane(planeTmp)
        },
        onDrawPolyline() {
            this.clear()
            // this.showSingleCoal('')
            this.viewer._container.style.cursor = 'crosshair'
            this.viewer.zoomTo(this.coalTileset, new Cesium.HeadingPitchRange(0.0, Math.PI / -2, 0.0))
            let arr = []
            this.polyline = null
            this.eventHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas)
            this.eventHandler.setInputAction((movement) => {
                const position = this.viewer.camera.pickEllipsoid(movement.endPosition, this.viewer.scene.globe.ellipsoid)
                if (!Cesium.defined(position)) {
                    return
                }
                if (arr.length >= 1) {
                    if (!Cesium.defined(this.polyline)) {
                        this.polyline = this.drawPolyline(arr)
                    } else {
                        arr.pop()
                        arr.push(position)
                    }
                }
            }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

            this.eventHandler.setInputAction((movement) => {
                const position = this.viewer.camera.pickEllipsoid(movement.position, this.viewer.scene.globe.ellipsoid)
                if (!Cesium.defined(position)) {
                    return
                }
                if (arr.length === 2) {
                    this.viewer._container.style.cursor = 'default'
                    arr.pop()
                    arr.push(position)

                    this.eventHandler.destroy()
                    this.eventHandler = null
                    this.loading = true
                    this.showGround(true)
                    setTimeout(() => {
                        this.section(arr[0], arr[1])
                        arr = []
                        return
                    }, 50)
                }
                if (arr.length === 0) {
                    arr.push(position.clone())
                }
                arr.push(position)
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
        },
        drawPolyline(positions) {
            const polylineGeometry = this.viewer.entities.add({
                polyline: {
                    positions: new Cesium.CallbackProperty(() => {
                        return positions
                    }, false),
                    width: 3,
                    material: Cesium.Color.RED,
                    classificationType: Cesium.ClassificationType.BOTH,
                    clampToGround: true
                }
            })
            return polylineGeometry
        },
        clear() {
            this.viewer.scene.globe.translucency.frontFaceAlpha = 0.6
            this.isSection = false
            if (this.eventHandler) {
                this.eventHandler.destroy()
                this.eventHandler = null
            }

            if (this.polyline) {
                this.viewer.entities.remove(this.polyline)
                this.polyline = null
            }

            if (this.groundLine) {
                this.viewer.entities.remove(this.groundLine)
                this.groundLine = null
            }

            if (this.footLine) {
                this.viewer.entities.remove(this.footLine)
                this.footLine = null
            }

            if (this.sectionWall) {
                this.viewer.entities.remove(this.sectionWall)
                this.sectionWall = null
            }
            if (this.clippingPlanes) {
                this.clippingPlanes.removeAll()
                this.clippingPlanes = null
            }

            // background
            const container = this.$refs.sectionContainer
            const canvas = this.$refs.canvas
            canvas.width = container.offsetWidth
            canvas.height = container.offsetHeight
            const context = canvas.getContext('2d')
            this.renderBackground(context, canvas.width, canvas.height)

            this.isShowCanvas = false

            this.viewer._container.style.cursor = 'default'
        }
    }
}
</script>

<style lang="scss" scoped>

.sectionContainer {
    width: calc(100%);
    //height: 200px;
    padding: 0px;
    float: left;
    bottom: 0px;
    position: absolute;

    &.open {
        height: 200px;
    }

    &.close {
        height: 0px;
    }
}
.canvas {
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    bottom: 0px;
    pointer-events: none;
}
.cursor {
    position: absolute;
    width: 0;
    top: 5px;
    bottom: 5px;
    margin-left: 0px;
    border-left: #222222 1px dashed;
}
.label {
    user-select:none;
    position: absolute;
    width: 180px;
    min-height: 59px;
    max-height: 96px;
    top: 0px;
    bottom: 0px;
    margin-left: 0px;
    margin-bottom: 0px;
    border-left: #DDDDDD 1px dashed;
    border-right: #DDDDDD 1px dashed;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 3px;
    font-size: 14px;
    .selectLabel {
        color: #B5B5B5;
    }
    .selectValue {
        color: #DDDDDD;
    }
}

.float-section {
    position: absolute;
    bottom: 205px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 32px;
    height: 80px;
    cursor: pointer;
    pointer-events: auto;
    right: 10px;

    img {
        width: 34px;
        height: 34px;
    }
}
.uploadButton {
    border: 2px solid #1CDAFF;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
    background: #096D81;
    border-radius: 4px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    cursor: pointer;
    height: 40px;
    width: 65px;
    text-align: center;
    display: inline-block;
    line-height: 34px;
    position: fixed;
    right: 5px;
    bottom: 155px;
}
</style>
