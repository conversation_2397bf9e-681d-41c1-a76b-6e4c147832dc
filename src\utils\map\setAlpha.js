/**
 * 设置3dtiles或要素透明度
 */
import {
    storeToRefs
} from "pinia";
import {
    useLoadStore
} from "@/store/load";
const loadStore = useLoadStore();
const {
    loadData
} = storeToRefs(loadStore);
class SetAlphaTool {

    setHangdaoAlphaByID(id, alpha) {
        var hangdaoID = window.layersManager.getIdByLabel('井巷模型')
        var hangdaoFeatures = window.earth.layerManager[hangdaoID].features;
        // var hangdaoLayer = earth.layerManager[2]
        // var hangdaoFeatures = hangdaoLayer.features
        //轮询次数
        var s = 0
        var timer = setInterval(() => {
            for (var feature of hangdaoFeatures) {
                if (feature.getProperty("batchId") == id) {
                    feature.color = new Cesium.Color(1, 1, 1, alpha);
                    clearInterval(timer);
                }
            }
            s++;
            if (s > 1200) {
                clearInterval(timer);
            }
        }, 200); //每0.2秒轮询一次
    }

    hideOthersHangdaoByIds(idList) {
        var hangdaoID = window.layersManager.getIdByLabel('井巷模型')
        var hangdaoFeatures = window.earth.layerManager[hangdaoID].features;
        var s = 0
        var timer = setInterval(() => {
            if (loadData.value.hasOwnProperty(hangdaoID) && loadData.value[hangdaoID] == true) {
                for (var feature of hangdaoFeatures) {
                    if (idList.indexOf(feature.getProperty("batchId")) == -1) {
                        feature.color = new Cesium.Color(1, 1, 1, 0);

                    }
                }
                clearInterval(timer);
            }
            s++;
            if (s > 1200) {
                clearInterval(timer);
            }
        }, 200); //每0.2秒轮询一次
    }

    showAllHangdao() {
        var hangdaoID = window.layersManager.getIdByLabel('井巷模型')
        var hangdaoFeatures = window.earth.layerManager[hangdaoID].features;

        var s = 0
        var timer = setInterval(() => {
            if (loadData.value.hasOwnProperty(hangdaoID) && loadData.value[hangdaoID] == true) {
                for (var feature of hangdaoFeatures) {
                    feature.color = new Cesium.Color(1, 1, 1, 1);
                }
                clearInterval(timer);
            }
            s++;
            if (s > 1200) {
                clearInterval(timer);
            }
        }, 200); //每0.2秒轮询一次
    }

    hideOthersModelByIds(title,idList) {
        var modelID = window.layersManager.getIdByLabel(title)
        var modelFeatures = window.earth.layerManager[modelID].features;
        var s = 0
        var timer = setInterval(() => {
            if (loadData.value.hasOwnProperty(modelID) && loadData.value[modelID] == true) {
                for (var feature of modelFeatures) {
                    if (idList.indexOf(feature.getProperty("batchId")) == -1) {
                        feature.color = new Cesium.Color(1, 1, 1, 0);

                    }
                }
                clearInterval(timer);
            }
            s++;
            if (s > 1200) {
                clearInterval(timer);
            }
        }, 200); //每0.2秒轮询一次
    }

    hideModelByIds(title,idList) {
        var modelID = window.layersManager.getIdByLabel(title)
        var modelFeatures = window.earth.layerManager[modelID].features;
        var s = 0
        var timer = setInterval(() => {
            if (loadData.value.hasOwnProperty(modelID) && loadData.value[modelID] == true) {
                for (var feature of modelFeatures) {
                    if (idList.indexOf(feature.getProperty("batchId")) != -1) {
                        feature.color = new Cesium.Color(1, 1, 1, 0);

                    }
                }
                clearInterval(timer);
            }
            s++;
            if (s > 1200) {
                clearInterval(timer);
            }
        }, 200); //每0.2秒轮询一次
    }

    showAllModel(title) {
        var modelID = window.layersManager.getIdByLabel(title)
        var modelFeatures = window.earth.layerManager[modelID].features;

        var s = 0
        var timer = setInterval(() => {
            if (loadData.value.hasOwnProperty(modelID) && loadData.value[modelID] == true) {
                for (var feature of modelFeatures) {
                    feature.color = new Cesium.Color(1, 1, 1, 1);
                }
                clearInterval(timer);
            }
            s++;
            if (s > 1200) {
                clearInterval(timer);
            }
        }, 200); //每0.2秒轮询一次
    }

    set3dtileAlpha(layer, alpha) {
        //轮询次数
        var s = 0
        var timer = setInterval(() => {
            var color = "rgba(255,255,255," + alpha + ")"
            //业务逻辑
            layer.layer.style = new Cesium.Cesium3DTileStyle({
                color: color,
            });
            s++;
            if (s > 1200 || layer.layer.style._color._expression == color) {
                clearInterval(timer);
            }
        }, 200); //每0.2秒轮询一次
    }


    // setFeatureAlphaByPropertys(layer, propertys, alpha) {
    //     var featureList = layer.feature
    //     //轮询次数
    //     var s = 0
    //     var timer = setInterval(() => {
    //         //判断条件：3dtiles加载完成
    //         if (layer.isLoadEnd) {
    //             //业务逻辑
    //             for (var feature of featureList) {
    //                 for (var property in propertys) {
    //                     if (feature.getProperty(property) == propertys[property]) {
    //                         feature.color = new Cesium.Color(1, 1, 1, alpha);
    //                     }
    //                 }
    //             }
    //             clearInterval(timer);
    //         }
    //         s++;
    //         if (s > 29) {
    //             clearInterval(timer);
    //         }
    //     }, 200); //每0.2秒轮询一次
    // }
}

export default SetAlphaTool