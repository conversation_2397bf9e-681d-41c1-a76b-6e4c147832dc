<template>
  <div class="common">
    <transition appear name="animate__animated animate__bounce" enter-active-class="animate__fadeInUp"
      leave-active-class="animate__fadeOutUp">
      <div class="center_box">
        <div class="center_box_box">
          <img class="home_layer_btn" @click="checkLayer()" :src="layerActive ? layerBtnImg : layerBtnImg1"
            title="图层控制" />
          <div class="center_box_content">
            经度:{{ lng.toFixed(2) }} 纬度:{{ lat.toFixed(2) }} 高度:{{ (height-modelMoveHeight).toFixed(2) }}米 方向:{{
            heading.toFixed(2)
            }}
            俯仰角:{{ pitch.toFixed(2) }} 旋转角:{{ roll.toFixed(2) }}
          </div>
          <img class="home_layer_btn" @click="toolCheck()" :src="toolShow ? toolBtnImg1 : toolBtnImg" title="工具栏控制" />
        </div>
      </div>
    </transition>
    <!-- 图层控制 -->
    <transition appear name="animate__animated " enter-active-class="animate__zoomIn"
      leave-active-class="animate__zoomOut">
      <div class="home_layer" v-draggable v-show="layerActive"
        :style="show ? '' : 'left:0px;transition: all 0.6s ease-in-out;'">
        <div class="home_layer_title">图层</div>
        <div class="home_layer_tree">
          <el-tree :data="treeData" show-checkbox :indent="0" node-key="id" ref="dataTree"
            :default-expanded-keys="[13,18,10]"
            :default-checked-keys="[]" :props="defaultProps"
            @check-change="checkChange" @node-click="nodeClick">
            <template #default="scope">
              <span class="custom-tree-node">
                <img :src="scope.data.icon" />
                <span>
                  {{ scope.node.label }}
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
    </transition>
    <!-- 图层 工具 -->
    <transition appear name="animate__animated animate__pulse" enter-active-class="animate__zoomIn"
      leave-active-class="animate__zoomOut">
      <div class="home_legend" v-draggable v-show="toolShow" :class="[legendShow ? 'home_legend_show' : '']"
        :style="show ? '' : 'right:0px;transition: all 0.6s ease-in-out;'">
        <div>
          <el-radio-group v-model="viewRadio" class="home_view" v-show="viewerBox">
            <el-radio v-for="(item, index) in views" :key="index" :label="index" size="large"
              @click="changeView(item.option)">{{ item.indexName }}</el-radio>
          </el-radio-group>
          <!-- 图例 -->
          <ul class="home_legend_ul">
            <li class="home_legend_li" v-for="(item, index) in toolData" :key="index"
              :class="{ legend_active: item.active }" @click="handleLegend(index)">
              <img :src="getImageUrl(item.img)" class="home_legend_img" />
              {{ item.name }}
            </li>
          </ul>
          <div class="home_up" @click="legendCheck()">
            <img v-show="legendShow" :src="upImg" />
            <img v-show="!legendShow" :src="downImg" />
          </div>
        </div>
        <!-- 缓冲区面板 -->
        <div class="home_buffer_box" v-show="bufferShow">
          <div class="buffer_line_text">
            半径：
            <el-input-number :max="4000" v-model="bufferValue"></el-input-number>
            <span class="buffer_unit">M</span>
          </div>
          <div class="buffer_line">
            <el-slider v-model="bufferValue" class="home_buffer" :max="4000" placement="right"
              @click="stopPropagation($event)" />
          </div>
          <div class="buffer_line">
            <span class="buffer_line_title">颜色：</span>
            <el-color-picker v-model="bufferColor" show-alpha />
          </div>
          <div class="buffer_btn_box">
            <div class="buffer_btn" @click="bufferReset()">重置</div>
            <div class="buffer_btn buffer_cancel" @click="bufferCancel()">
              取消
            </div>
          </div>
        </div>
        
      </div>
    </transition>
    <!-- 坡度面板 -->
    <div class="home_slope_box" v-show="slopeShow">
          <div class="home_slope_box1">
              <div class="slope_line_text">
              插值：
              <el-input-number :max="4000" v-model="slopeInterpolations"></el-input-number>
            </div>
            <div class="slope_line_text">
              单元格采样：
              <el-input-number :max="4000" v-model="slopeSamples"></el-input-number>
            </div>
            <div class="slope_line_text">
              箭头宽度：
              <el-input-number :max="4000" v-model="slopeArrowWidth"></el-input-number>
            </div>
            <div class="slope_line">
              <span class="slope_line_title">箭头颜色：</span>
              <el-color-picker v-model="slopeArrowColor" show-alpha />
            </div>
            <div class="slope_line_text">
              填充半径：
              <el-input-number :max="4000" v-model="slopeMapRadius"></el-input-number>
            </div>
          </div>
          <div class="slope_btn_box">
            <div class="slope_btn" @click="slopeReset()">重置</div>
            <div class="slope_btn slope_cancel" @click="slopeOK()">确定</div>
          </div>
        </div>
  </div>
</template>

<script setup>
import { usePanelStore } from "@/store/panel";
import { useToolsStore } from "@/store/tools";
import { storeToRefs } from "pinia";
import Earth from "@/utils/map/earth";
import upImg from "@/assets/img/home/<USER>";
import downImg from "@/assets/img/home/<USER>";
import layerBtnImg from "@/assets/img/home/<USER>";
import layerBtnImg1 from "@/assets/img/home/<USER>";
import toolBtnImg from "@/assets/img/home/<USER>";
import toolBtnImg1 from "@/assets/img/home/<USER>";
import { getCurrentInstance, ref, toRef, watchEffect } from "vue";
import { onMounted, onUpdated, onBeforeUpdate, watch } from "vue";
import Measure from "@/utils/map/mesuare";
import cameraInfo from "@/utils/map/getCameraInfo";
import Buffer from "@/utils/map/buffer3";
import ClipPlane from "@/utils/map/clipPlane";
import Transform from "@/utils/map/editmodel";
import GwSlope2 from "@/utils/map/GwSlope2";
import Select from "@/utils/map/select2";
import Section from "@/utils/map/section";
import Attribute from "@/utils/map/attribute";
import LayerManager from "@/utils/map/layerManager";
import ModelMove from "@/utils/map/modelMove";
import modelMoveHeight from "@/utils/map/modelMoveHeight"
//console.log(window.location.hash)
const panelStore = usePanelStore();
const toolsStore = useToolsStore();
const { show, layerActive, toolShow } = storeToRefs(panelStore);
const setDrag = () => {
  // 增加拖拽
  var moveEl = document.getElementsByClassName('slope_btn_box')[0];
  var moveEl2 = document.getElementsByClassName('home_slope_box')[0];
  const mouseDown = (e) => {
    let X = e.clientX - moveEl2.offsetLeft;
    let Y = e.clientY - moveEl2.offsetTop;
    const move = (e) => {
      moveEl2.style.left = e.clientX - X + "px";
      moveEl2.style.top = e.clientY - Y + "px";
    };
    document.addEventListener("mousemove", move);
    document.addEventListener("mouseup", () => {
      document.removeEventListener("mousemove", move);
    });
  };
  moveEl.addEventListener("mousedown", mouseDown);
}
//生命周期
onMounted(() => {
  // 初始化 左右两侧 确保展开
  // panelStore.show = true;
  // 获取tree data 初始化 赋值store
  let initTreeData = dataTree.value.getCheckedNodes(true, false);
  const { proxy } = getCurrentInstance()
  setDrag()
  //   nextTick(()=>{
  //   console.log(proxy.$refs.dataTree.setCurrentKey(16))
  // })
  // setTimeout(() => {
  //   console.log(proxy.$refs.dataTree.getCheckedKeys())

  //   // proxy.$refs.dataTree.setCheckedKeys([16])
  // }, 2000);

  toolsStore.$patch((state) => {
    state.layerArr.push(...initTreeData);
    state.dataTree = proxy.$refs.dataTree
  });
});

const defaultProps = {
  children: "children",
  label: "label",
};

var toolData = ref([
  {
    name: "选择",
    img: 1,
    active: false,
    addFun: function () {
      select.start();
    },
    delFun: function () {
      select.cancel();
    },
  },
  // {
  //   name: "移动",
  //   img: 2,
  //   active: false,
  //   addFun: function () {
  //     console.warn("待补充")
  //   },
  //   delFun: function () {
  //     console.warn("未定义移除功能")
  //   }
  // },
  {
    name: "旋转",
    img: 3,
    active: false,
    addFun: function () {
      rotateMethods();
    },
    delFun: function () {
      //旋转弹框
      deleteRotatePopup();
      removeRotateMethods();
    },
  },
  // {
  //   name: "旋转中心",
  //   img: 5,
  //   active: false,
  //   addFun: function () {
  //     drawPoint();
  //   },
  //   delFun: function () {
  //     //旋转中心视角锁定
  //     removeTransform();
  //   },
  // },
  {
    name: "视角",
    img: 5,
    active: false,
    addFun: function () {
      angleOfViewMethods();
    },
    delFun: function () {
      // 还原面板选择
      viewRadio.value = 0;
      // 关闭面板
      viewerBox.value = false;
      // 还原初始视角
    },
  },
  {
    name: "定位",
    img: 5,
    active: false,
    addFun: function () {
      flytoPosition();
    },
    delFun: function () {
      
    },
  },
  {
    name: "属性",
    img: 7,
    active: false,
    addFun: function () {
      attribute.start();
      // select.start(true);
    },
    delFun: function () {
      attribute.cancel();
      // select.cancel(true);
    },
  },
  {
    name: "全屏",
    img: 8,
    active: false,
    addFun: function () {
      fullScreenMethods();
    },
    delFun: function () {
      isFullScreen = ref(true);
      fullScreenMethods();
    },
  },
  {
    name: "测距",
    img: 9,
    active: false,
    addFun: function () {
      measure.segmentsDistance2();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "测高",
    img: 9,
    active: false,
    addFun: function () {
      measure.elevation();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "量面积",
    img: 10,
    active: false,
    addFun: function () {
      measure.area();
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "量角度",
    img: 11,
    active: false,
    addFun: function () {
      measure.angle();
      console.warn("待补充");
    },
    delFun: function () {
      measure.removeAll();
      measure.cancel();
    },
  },
  {
    name: "坡度",
    img: 14,
    active: false,
    addFun: function () {
      startSlope();
    },
    delFun: function () {
      endSlope();
    },
  },
  // {
  //   name: "量体积",
  //   img: 11,
  //   active: false,
  //   addFun: function () {
  //     console.warn("待补充");
  //   },
  //   delFun: function () {
  //     measure.removeAll();
  //     measure.cancel();
  //   },
  // },
  // {
  //   name: "缓冲区",
  //   img: 12,
  //   active: false,
  //   addFun: function () {
  //     buffer.add();
  //   },
  //   delFun: function () {
  //     //取消并清除缓冲区操作
  //     bufferCancel();
  //     bufferReset();
  //   },
  // },
  {
    name: "模型编辑",
    img: 13,
    active: false,
    addFun: function () {
      editModels(true);
    },
    delFun: function () {
      editModels(false)
      editTools.cancel();
      viewer.scene.forceRender();
    },
  },
  {
    name: "模型拉伸",
    img: 13,
    active: false,
    addFun: function () {
      modelStretching(true)
    },
    delFun: function () {
      modelStretching(false)
    },
  },
  {
    name: "模型恢复",
    img: 13,
    active: false,
    addFun: function () {
      modelRecovery()
    },
    delFun: function () {
      modelRecovery()
    },
  },
  {
    name: "剖切",
    img: 13,
    active: false,
    addFun: function () {
      clipPlaneMethods();
    },
    delFun: function () {
      clipPlane.cancel();
      viewer.scene.forceRender();
    },
  },
  // {
  //   name: "剖面分析",
  //   img: 13,
  //   active: false,
  //   addFun: function () {
  //     // clipPlaneMethods();
  //     section.start()
  //   },
  //   delFun: function () {
  //     // clipPlane.cancel();
  //     section.cancle()
  //     viewer.scene.forceRender();
  //   },
  // },
  
  {
    name: "清除",
    img: 1,
    active: false,
  },
]);
let legendIndex = ref(0);
let iconName = ref("Fold");
//初始化工具
let buffer;
let measure;
let clipPlane;
let attribute;
let select;
let editTools;
let slope;
let section;

setTimeout(() => {
  let [red, green, blue, alpha] = bufferColor.value
    .match(/\d+(\.\d+)?/g)
    .map(Number);
  buffer = new Buffer(viewer, bufferShow, {
    radius: bufferValue.value,
    color: [red / 255, green / 255, blue / 255, alpha],
  });
  measure = new Measure(viewer);
  clipPlane = new ClipPlane(viewer);
  attribute = new Attribute(viewer);
  select = new Select(viewer);
  editTools = new Transform(viewer);
  section= new Section(viewer)
  window.slope = slope = new GwSlope2(viewer);

}, 1000);

const handleLegend = (item) => {
  // 清除功能
  if (item == toolData.value.length - 1) {
    for (let i in toolData.value) {
      try {
        toolData.value[i].delFun();
      } catch { }
      toolData.value[i].active = false;
    }
    
  } else {
    // 切换选择状态
    toolData.value[item].active = !toolData.value[item].active;
    // 如果选中就执行功能
    if (toolData.value[item].active) {
      toolData.value[item].addFun();
    } else {
      try {
        toolData.value[item].delFun();
      } catch { }
      viewer.scene.forceRender();
    }
    if (['模型恢复','定位'].indexOf(toolData.value[item].name)>-1) {
      toolData.value[item].active = false;
    }
  }
  viewer.scene.forceRender();
};

let legendShow = ref(true);
const legendCheck = () => {
  legendShow.value = !legendShow.value;
};

// let toolShow = ref(true);
const toolCheck = () => {
  toolShow.value = !toolShow.value;
};

//获取摄像机实时参数
let { lng, lat, height, heading, pitch, roll } = cameraInfo()


//工具栏——旋转
const rotateMethods = () => {
  var handler = new Geowin3D.ScreenSpaceEventHandler(viewer.scene.canvas);
  handler.setInputAction(function (movement) {
    var pickedObject = viewer.scene.pick(movement.position);
    if (Geowin3D.defined(pickedObject)) {
      earth.trackModel([pickedObject.primitive]);
    }
    handler.removeInputAction(Geowin3D.ScreenSpaceEventType.LEFT_CLICK);
  }, Geowin3D.ScreenSpaceEventType.LEFT_CLICK);
  // 增加提示窗口
  let opt = ["鼠标点选空间实体", "鼠标左键拖动旋转"];
  if (!document.getElementById("infoPanel")) {
    Sandcastle.createTipsDOM(opt);

    document.getElementById("openTip").style =
      "position: absolute;top: 90px;right: 0px;width: 20px;background: #333333;text-align: center;cursor: pointer;padding: 15px 5px;border-top-left-radius: 4px;border-bottom-left-radius: 4px;z-index: 11;display: none;color: white;";
    document.getElementById("closeTip").style =
      "position: absolute;right: 20px;top: 100px;font-size: 15px;border-radius: 50%;border: 1px solid #fff;width: 20px;height: 20px;text-align: center;line-height: 16px;cursor: pointer;color:white;z-index:10";
  }
};
//移除旋转操作
const removeRotateMethods = () => {
  earth.exitTrackModel();
};
// 移除旋转提示框
const deleteRotatePopup = () => {
  document.getElementById("infoPanel").remove();
  document.getElementById("openTip").remove();
  document.getElementById("closeTip").remove();
};

//工具栏——定位
const flytoPosition=()=>{
  var cameraVisual=window.cameraVisual
  cameraVisual.flytoDefault(window.location.hash)
}

// 工具栏-缓冲区
let bufferValue = ref(100);
let bufferColor = ref("rgba(255, 255, 0, 0.5)");
watch(bufferValue, (newVal, oldVal) => {
  if (bufferShow.value == true) {
    buffer.setRadius(newVal);
  }
},200);
watch(bufferColor, (newVal, oldVal) => {
  if (bufferShow.value == true) {
    let [red, green, blue, alpha] = newVal.match(/\d+(\.\d+)?/g).map(Number);
    buffer.setColor([red / 255, green / 255, blue / 255, alpha]);
  }
},200);
let bufferShow = ref(false);
const handleBuffer = () => {
  bufferShow.value = true;
};
// 阻止冒泡
const stopPropagation = (e) => {
  e.stopPropagation();
};
const bufferReset = () => {
  // 重置
  bufferValue.value = 100;
  bufferColor.value = "rgba(255, 255, 0, 0.5)";
};
const bufferCancel = () => {
  // 取消
  // bufferReset()
  if (toolData.value[12].name == "缓冲区") {
    toolData.value[12].active = false;
  }
  bufferShow.value = false;
  buffer.clear();
  buffer.cancel();

  viewer.scene.forceRender();
};

//工具栏——剖切
const clipPlaneMethods = () => {
  var earth = window.earth;
  var layerItems = earth.layerManager;
  var layerItemsList = [];

  for (var key in layerItems) {
    layerItemsList.push(layerItems[key].layer);
  }
// if (window.location.hash == "#/index/Tunnel") {
    // console.log("run")
//   clipPlane.start(layerItemsList, 0, 1);
//  }
// else {
    clipPlane.start(layerItemsList, 0, 0);
// }

};

//工具栏——剖面分析



//工具栏——模型编辑
var editModelsHandler;
const editModels = (flag) => {
  if (flag) {
    editModelsHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    editModelsHandler.setInputAction((event) => {
      let pickedObject = viewer.scene.pick(event.position);
      if (Cesium.defined(pickedObject) && pickedObject.tileset) {
        editTools.start([pickedObject.tileset]);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
  } else {
    if (editModelsHandler) {
      editModelsHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }
  }
  viewer.scene.forceRender();
  // var earth = window.earth;
  // var ents = earth.getEntities();
  // editTools.start(ents);
};

//工具栏——坡度

let slopeShow = ref(false);
let slopeInterpolations = ref(8);
let slopeSamples = ref(8);
let slopeArrowWidth = ref(8);
let slopeMapRadius = ref(120);
let slopeArrowColor = ref("rgba(255, 255, 0, 0.8)");
//重置
const slopeReset = () => {
  slopeInterpolations.value = 8;
  slopeSamples.value = 8;
  slopeArrowWidth.value = 8;
  slopeMapRadius.value = 120;
  slopeArrowColor.value = "rgba(255, 255, 0, 0.8)";
};
//确定
const slopeOK = () => {
  slopeShow.value = false;
  slopeMethods();
};
const slopeMethods = () => {
  slope.resetOptions();
  let [red, green, blue, alpha] = slopeArrowColor.value
    .match(/\d+(\.\d+)?/g)
    .map(Number);
  var aColor = new Geowin3D.Color(red / 255, green / 255, blue / 255, alpha);
  let option = {
    id: "slefSlopeTooltip",
    interpolations: slopeInterpolations.value, // 多边形包围矩形短边插值数
    samples: slopeSamples.value, // 单元格采样点数
    arrowWidth: slopeArrowWidth.value, // 坡向箭头宽度
    arrowColor: aColor, // 坡向箭头颜色
    mapRadius: slopeMapRadius.value, // 坡面填充色半径
    mapGradient: {
      // 坡面填充渐变色
      0.25: "rgb(0, 255, 255)",
      0.55: "rgb(0, 255, 0)",
      0.85: "rgb(255, 255, 0)",
      1: "rgb(255, 0, 0)",
    },
  };
  //排除异常区和断层影响
  var excludeLayers = window.layersManager.getLayersByLabels(['断层模型'])
  slope.setExcludeLayers([...window.ycq.getPrimitives(),...excludeLayers])
  slope.set(option);
  slope.start();
};
const startSlope = () => {
  slopeShow.value = true;
};
const endSlope = () => {
  slopeShow.value = false;
  slope.destroy();
};

//工具栏——全屏
var isFullScreen = ref(false);
const fullScreenMethods = () => {
  if (isFullScreen.value == false) {
    if (document.documentElement.RequestFullScreen) {
      document.documentElement.RequestFullScreen();
    }
    //兼容火狐
    if (document.documentElement.mozRequestFullScreen) {
      document.documentElement.mozRequestFullScreen();
    }
    //兼容谷歌等可以webkitRequestFullScreen也可以webkitRequestFullscreen
    if (document.documentElement.webkitRequestFullScreen) {
      document.documentElement.webkitRequestFullScreen();
    }
    //兼容IE,只能写msRequestFullscreen
    if (document.documentElement.msRequestFullscreen) {
      document.documentElement.msRequestFullscreen();
    }
  } else {
    if (document.exitFullScreen) {
      document.exitFullscreen();
    }
    //兼容火狐
    if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    }
    //兼容谷歌等
    if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    }
    //兼容IE
    if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  }
  isFullScreen.value = !isFullScreen.value;
};

//工具栏——视角
const views = [
  {
    indexName: "正视",
    option: [0, 0, 7000],
  },
  {
    indexName: "侧视",
    option: [90, 0, 6000],
  },
  {
    indexName: "俯视",
    option: [0, -90, 20000],
  },
  {
    indexName: "45度",
    option: [0, -45, 20000],
  },
];
let viewerBox = ref(false);
let viewRadio = ref(0);
const angleOfViewMethods = () => {
  // 打开视角开关
  
  viewerBox.value = !viewerBox.value;
  console.log('打开视角开关',viewerBox.value)
  var earth = window.earth;
  var meicengID=window.layersManager.getIdByLabel('7号煤层')
  var layerItem = earth.layerManager[meicengID];
  viewer.flyTo(layerItem.layer, {
    duration: 2, //执行定位动画的时间
    offset: {
      heading: Geowin3D.Math.toRadians(0), // 水平偏角，默认正北 0
      pitch: Geowin3D.Math.toRadians(0), // 俯视角，默认-90，垂直向下
      range: 7000, //镜头（屏幕）到定位目标点（实体）的距离
    },
  });
};
// 切换视角
const changeView = (options) => {
  var earth = window.earth;
  var meicengID=window.layersManager.getIdByLabel('7号煤层')
  var layerItem = earth.layerManager[meicengID];
  viewer.flyTo(layerItem.layer, {
    duration: 1, //执行定位动画的时间
    // maximumHeight: 500, //执行定位动画时镜头上跳的最大高度
    offset: {
      heading: Geowin3D.Math.toRadians(options[0]), // 水平偏角，默认正北 0
      pitch: Geowin3D.Math.toRadians(options[1]), // 俯视角，默认-90，垂直向下
      range: options[2], //镜头（屏幕）到定位目标点（实体）的距离
    },
  });
};
//工具栏——图层控制
// let layerActive = ref(true)
let layerIndex;
for (let i in toolData.value) {
  if (toolData.value[i].name == "图层") {
    layerIndex = i;
    break;
  }
}
// 切换显隐
const checkLayer = () => {
  layerActive.value = !layerActive.value;
  toolData.value[layerIndex].active = layerActive.value;
};

// 工具栏——旋转中心
const drawPoint = () => {
  let pointObj = null;
  var handler = new Geowin3D.ScreenSpaceEventHandler(viewer.scene.canvas);
  handler.setInputAction(function (clickEvent) {
    var ray = viewer.camera.getPickRay(clickEvent.position);
    var Cartesian3 = viewer.scene.globe.pick(ray, viewer.scene);
    var pick = viewer.scene.pickPosition(clickEvent.position);
    var pickModel = viewer.scene.pick(clickEvent.position);
    if (pickModel && pick && !pickModel.id) {
      var height = Cesium.Cartographic.fromCartesian(pick).height;
      var lat = Cesium.Math.toDegrees(
        Cesium.Cartographic.fromCartesian(pick).latitude
      );
      var lng = Cesium.Math.toDegrees(
        Cesium.Cartographic.fromCartesian(pick).longitude
      );
      Cartesian3 = Cesium.Cartesian3.fromDegrees(lng, lat, height);
    }
    // 清除之前的点
    if (pointObj) {
      viewer.entities.removeById("viewCenter");
    }
    // 绘制点
    pointObj = viewer.entities.add({
      id: "viewCenter",
      position: Cartesian3,
      point: {
        pixelSize: 20,
        color: Geowin3D.Color.YELLOW,
      },
    });
    const transform = Geowin3D.Transforms.eastNorthUpToFixedFrame(Cartesian3);
    viewer.camera.lookAtTransform(transform);
    handler.removeInputAction(Geowin3D.ScreenSpaceEventType.LEFT_CLICK);
  }, Geowin3D.ScreenSpaceEventType.LEFT_CLICK);
};
const removeTransform = () => {
  // 取消视角锁定
  viewer.camera.lookAtTransform(Geowin3D.Matrix4.IDENTITY);
  // 删除视角中心
  viewer.entities.removeById("viewCenter");
};


//工具栏——模型拉伸
var modelMove = new ModelMove()
const modelStretching = (flag) => {
  var dizhitiLayersup=window.layersManager.getLayersByLabels(['第四系','上侏罗～下白垩统'])
  var dizhitiLayersdowm=window.layersManager.getLayersByLabels(['二叠系地层','石炭系地层'])
  if (flag) {
    modelMove.up2(dizhitiLayersup, 2000)
    modelMove.up2(dizhitiLayersdowm, -2000)
   
  } else {
    modelMove.up2(dizhitiLayersup, 0)
    modelMove.up2(dizhitiLayersdowm, 0)
    
  }
  viewer.scene.forceRender();
}

//工具栏——模型恢复
const modelRecovery = () => {
  for (var tool of toolData.value) {
    if (tool.name == '模型拉伸' || tool.name == '模型编辑') {
      tool.active = false
      tool.delFun()
      modelMove.recoveryAll()
    }
  }
  viewer.scene.forceRender();
}

//图层管理
let layerManager = new LayerManager();
window.layersManager = layerManager
//获取图层树
var treeData = layerManager.treeData;
//点击图层管理跳转至模型处
const nodeClick = layerManager.nodeClick();
// 图层控制点击功能
const checkChange = layerManager.checkChange();

const dataTree = ref(null);
// 图层与 图例联动
// watch(toolsStore, (newVal, oldVal) => {
//   if (toolsStore.disableTreeId) {
//     // dataTree.value.setChecked(toolsStore.disableTreeId, false);
//   }
// });

function getImageUrl(name) {
  return new URL(`../../assets/img/home/<USER>
    .href;
}

//上下移动摄像机
const moveCamera = (height) => {
  var cameraInfo = earth.getCameraInfo()
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      cameraInfo.lontitude,
      cameraInfo.latitude,
      cameraInfo.height + height
    ),
    orientation: {
      heading: viewer.scene.camera.heading,
      pitch: viewer.scene.camera.pitch,
      roll: viewer.scene.camera.roll,
    },
  });
  viewer.scene.forceRender();
}
</script>
<style lang="less" scoped>
.common {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 0px;
  left: 0px;

  .center_box {
    z-index: 1;
    position: absolute;
    bottom: 22px;
    left: 0;
    right: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    height: 34px;

    .center_box_box {
      width: 1027px;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .home_layer_btn {
        width: 30px;
        height: 30px;
        cursor: pointer;
      }
    }

    .center_box_content {
      width: 927px;
      height: 100%;
      color: #ffffff;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      background-image: url("../../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }

  .home_layer {
    z-index: 1;
    opacity: 1;
    position: fixed;
    top: 108px;
    left: 360px;
    max-height: 610px;
    overflow-y: scroll;
    overflow-x: hidden;
    border-radius: 6px;
    padding: 7px 10px 10px 11px;
    box-sizing: border-box;
    background-color: RGBA(34, 67, 99, 0.6);
    transition: all 0.4s ease-in-out;

    .home_layer_title {
      width: 100%;
      font-size: 16px;
      font-family: Source Han Sans SC;
      font-weight: 800;
      color: #ffffff;
      margin-bottom: 8px;
      margin-left: 10px;
      cursor: pointer;
    }

    .home_layer_tree {
      opacity: 1;

      :deep(.el-tree) {
        background: none;

        .el-tree-node__label {
          color: #fff;
        }

        .custom-tree-node {
          font-size: 14px;
          color: #fff;
          display: flex;
          align-items: center;
          margin-left: -5px;
        }

        .el-tree-node__content:hover {
          background-color: #04548c;
        }

        .el-tree-node:focus>.el-tree-node__content {
          background-color: rgba(4, 84, 140, 0.3);
        }

        .el-tree-node__label {
          margin-right: 2px;
        }

        .el-tree-node {
          position: relative;
          padding-left: 16px; // 缩进量
        }

        .el-tree-node__children {
          padding-left: 16px; // 缩进量
        }

        // 竖线
        .el-tree-node::before {
          content: "";
          height: 100%;
          width: 1px;
          position: absolute;
          left: -3px;
          top: -26px;
          border-width: 1px;
          border-left: 1px dashed #52627c;
        }

        // 当前层最后一个节点的竖线高度固定
        .el-tree-node:last-child::before {
          height: 38px; // 可以自己调节到合适数值
        }

        // 横线
        .el-tree-node::after {
          content: "";
          width: 24px;
          height: 20px;
          position: absolute;
          left: -3px;
          top: 12px;
          border-width: 1px;
          border-top: 1px dashed #52627c;
        }

        // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
        &>.el-tree-node::after {
          border-top: none;
        }

        &>.el-tree-node::before {
          border-left: none;
        }

        // 展开关闭的icon
        .el-tree-node__expand-icon {
          font-size: 16px;

          // 叶子节点（无子节点）
          &.is-leaf {
            color: transparent;
            // display: none; // 也可以去掉
          }
        }
      }
    }
  }

  // 图例
  .home_legend {
    z-index: 2;
    width: 126px;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    // box-sizing: border-box;
    position: relative;
    // padding: 8px 11px 0 14px;
    position: fixed;
    top: 108px;
    right: 360px;
    // background-image: url("../../assets/img/home/<USER>");
    // background-position: center center;
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    // border: 1px solid RGBA(34, 67, 99,0.7);
    // background-color: RGBA(5, 31, 89, 0.3);
    border: 1px solid rgba(34, 67, 99, 0.7);
    background-color: rgb(20 49 75 / 60%);
    transition: all 0.4s ease-in-out;

    .home_legend_ul {
      width: 100%;
      display: flex;
      flex-direction: column;
      height: 635px;
      overflow: hidden;
      transition: all 0.5s ease-in-out;
      flex-wrap: wrap;
      box-sizing: border-box;
      padding: 8px 0px 0 0px;
      cursor: pointer;

      .home_legend_li {
        font-size: 14px;
        height: 32px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        display: flex;
        align-items: center;
        width: 100%;
        // background-image: url("../../assets/img/home/<USER>");
        // background-position: center center;
        // background-size: 100% 100%;
        // background-repeat: no-repeat;
        background-color: RGBA(34, 67, 99,0.7);
        justify-content: center;
        margin-bottom: 6px;
        cursor: pointer;
        margin-right: 12px;

        &:hover {
          transform: scale(1.1);
        }

        .home_legend_img {
          width: 15px;
          height: 15px;
          margin-right: 5px;
        }
      }

      .legend_active {
        background-image: url("../../assets/img/home/<USER>");
      }
    }

    .home_up {
      width: 100%;
      display: flex;
      justify-content: center;
      width: 100%;
      margin-bottom: 5px;

      img {
        width: 24px;
        height: 13px;
        margin-top: 4px;
      }

      cursor: pointer;

      &:hover {
        transform: scale(1.3);
      }
    }

    .home_view {
      // z-index: 999;
      width: 80px;
      box-sizing: border-box;
      padding: 10px;
      position: absolute;
      top: 140px;
      left: -90px;
      background-image: url("../../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-color: RGBA(5, 31, 89, 0.6);
      color: #fff;

      :deep(.el-radio__label) {
        color: #fff;
      }

      :deep(.is-checked) {
        .el-radio__label {
          color: #409eff;
        }
      }

      span {
        color: #fff;
      }
    }

    .home_buffer_box {
      width: 300px;
      box-sizing: border-box;
      padding: 10px;
      position: absolute;
      top: 240px;
      left: -310px;
      background-image: url("../../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-color: RGBA(5, 31, 89, 0.3);
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 10px;
      border-radius: 6px;

      .buffer_line_text {
        font-size: 18px;

        .buffer_unit {
          width: 20px;
          margin-left: 10px;
        }
      }

      .buffer_line {
        display: flex;
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .buffer_line_title {
          width: 60px;
          font-size: 18px;
        }
      }

      .buffer_btn_box {
        width: 100%;
        display: flex;
        justify-content: space-around;
        margin-top: 20px;

        .buffer_btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 32px;
          cursor: pointer;
          border: 1px solid;
          border-radius: 4px;
          font-size: 16px;

          &:hover {
            border: 1px solid #73d897;
            transform: scale(1.05);
          }
        }

        .buffer_cancel {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    
  }
  .home_slope_box {
      width: 300px;
      box-sizing: border-box;
      padding: 10px;
      position: absolute;
      top: 240px;
      right: 40%;
      background-image: url("../../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-color: RGBA(5, 31, 89, 0.3);
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 10px;
      border-radius: 6px;
      z-index: 999;
      height: 280px;
      display: flex;
      align-items: flex-end;
      .home_slope_box1{
        position: absolute;
        top: 10px;
        left: 20px;
      }
      .slope_line_text {
        font-size: 18px;
        margin-bottom: 10px;
        .slope_unit {
          width: 20px;
          margin-left: 10px;
        }
      }

      .slope_line {
        display: flex;
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .slope_line_title {
          width: 100px;
          font-size: 18px;
        }
      }

      .slope_btn_box {
        width: 100%;
        display: flex;
        justify-content: space-around;
        padding-top: 30px;
        .slope_btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 32px;
          cursor: pointer;
          border: 1px solid;
          border-radius: 4px;
          font-size: 16px;

          &:hover {
            border: 1px solid #73d897;
            transform: scale(1.05);
          }
        }

        .slope_cancel {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  
}
</style>
