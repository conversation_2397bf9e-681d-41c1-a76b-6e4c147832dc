<template>
  <div class="tunnel">
    <transition appear name="animate__animated animate__pulse" enter-active-class="animate__fadeInLeft"
      leave-active-class="animate__fadeOutLeft">
      <div class="left" v-show="show">
        <div class="left_box2">          
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>资源情况</span>
          </div>
          <div class="left_box2_content">
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.mineArea }}</span>
              <span class="left_box2_content_num_unit">KM²</span>
              <span class="left_box2_content_title">井田面积</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.extend2 }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">保有资源量</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.developingCoalQuantity }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">开拓煤量</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.prepareCoalQuantity }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">准备煤量</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.backCoalQuantity }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">回采煤量</span>
            </div>
            
          </div>
        </div>
        <div class="left_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>掘进基础信息</span>
          </div>
          <div class="left_box1_content">
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg1" />
              <div class="left_box_li_right">
                <span class="left_box1_text">巷道编号</span>
                <span class="left_box1_num">{{ excavationInfo.tunnelNo }}</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg2" />
              <div class="left_box_li_right">
                <span class="left_box1_text">规划掘进距离</span>
                <span class="left_box1_num">{{ excavationInfo.planHeadingDistance }}m</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg3" />
              <div class="left_box_li_right">
                <span class="left_box1_text">已掘进距离</span>
                <span class="left_box1_num">{{ excavationInfo.earnHeadingDistance }}m</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg4" />
              <div class="left_box_li_right">
                <span class="left_box1_text">允许掘进距离</span>
                <span class="left_box1_num">{{ excavationInfo.allowHeadingDistance }}m</span>
              </div>
            </div>
            <!-- <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg3" />
              <div class="left_box_li_right">
                <span class="left_box1_num">{{ excavationInfo.overHeadingDistance }}m</span>
                <span class="left_box1_text">超期距离</span>
              </div>
            </div>
            <div class="left_box1_li">
              <img class="left_box_li_img" :src="leftImg4" />
              <div class="left_box_li_right">
                <span class="left_box1_num">{{ excavationInfo.unclearHeadingDistance }}m</span>
                <span class="left_box1_text">未探测距离</span>
              </div>
            </div> -->
          </div>
        </div>
        <div class="left_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>掘进视角</span>
          </div>
          <div class="left_box2_content">
            <ul class="let_box1_ul">
              <li class="left_box1_btn" @click="changeJujin(index)"
                :class="index == jueJinIndex ? 'left_box1_li_active' : ''" v-for="(item, index) in jueJinList"
                :key="index">
                {{ item }}
              </li>
            </ul>
          </div>
        </div>
        <!-- <div class="left_box3">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>掘进超前探</span>
          </div>
          <ul class="right_box1_ul">
            <li class="right_box1_li" @click="changeResult(index)"
              :class="index == resultIndex ? 'right_box1_li_active' : ''" v-for="(item, index) in resultList"
              :key="index">
              {{ item }}
            </li>
          </ul>
          <div class="left_box3_content">
            <div class="right_box1_table_head">
              <div class="right_box1_title right_box1_head_text">图层控制</div>
              <div class="right_box1_index right_box1_head_text">编号</div>
              <div class="right_box1_name right_box1_head_text">类型</div>
              <div class="right_box1_time right_box1_head_text">时间</div>
              <div class="right_box1_oper right_box1_head_text">操作</div>
            </div>
            <vue3-seamless-scroll :list="resultInfoIist[resultIndex]" class="right_three_scroll" :step="0.5"
              wheel="true" v-model="scroll" :hover="hover" :limitScrollNum="limitScrollNum">
              <div class="" v-for="(item, index) in resultInfoIist[resultIndex]" :key="index">
                <div class="right_box1_table_head right_box1_table_line" :class="
                  index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                ">
                  <div class="right_box1_index right_box1_line_text">
                    <el-checkbox v-model="item.check" label="" @change="changeClick(item)" :disabled="!item.imgUrl" />
                  </div>
                  <div class="right_box1_index right_box1_line_text"
                    :style="{ color: (item.imgUrl ? 'white' : 'grey') }">
                    {{ item.index }}
                  </div>
                  <div class="right_box1_name right_box1_line_text" :style="{ color: (item.imgUrl ? 'white' : 'grey') }"
                    :title="item.title">
                    {{ item.title }}
                  </div>
                  <div class="right_box1_time right_box1_line_text"
                    :style="{ color: (item.imgUrl ? 'white' : 'grey') }">
                    2022/10/{{ index + 10 }}
                  </div>
                  <el-popconfirm confirm-button-text="docx" cancel-button-text="dwg" :icon="InfoFilled"
                    icon-color="#626AEF" title="选择下载文件类型" @confirm="confirmEvent(item)" @cancel="cancelEvent(item)">
                    <template #reference>
                      <div class="right_box1_oper right_box1_line_text"
                        :style="{ cursor: (item.dwgUrl ? 'pointer' : 'wait'), color: (item.imgUrl ? '#15FFEF' : 'grey') }"
                        v-show="(!item.dwgUrl && !item.docUrl ? false : true)">
                        打开</div>
                    </template>
                  </el-popconfirm>
                  <div class="right_box1_oper right_box1_line_text"
                    :style="{ cursor: (item.dwgUrl ? 'pointer' : 'wait'), color: (item.imgUrl ? '#15FFEF' : 'grey') }"
                    v-show="(!item.dwgUrl && !item.docUrl ? true : false)">
                    打开</div>

                </div>
              </div>
            </vue3-seamless-scroll>
          </div>
        </div> -->
      </div>
    </transition>
    <transition appear name="animate__animated animate__bounce" enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight">
      <div class="right" v-show="show">
        <div class="right_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>掘进传感器数据</span>
          </div>
          <div id="receptionTrend" class="myChart"></div>
        </div>
        <div class="right_box3">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>预测预报</span>
          </div>
          <div class="right_content">
            <div class="right_table_box">
              <div class="right_box1_table_head right_box1_table_sticky">
                <div class="right_box1_index right_box1_head_text">序号</div>
                <div class="right_box1_type right_box1_head_text">
                  类型
                </div>
                <div class="right_box1_name right_box1_head_text">
                  预测预报内容
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  水平距离
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  垂直距离
                </div>
                <div class="right_box1_distance right_box1_head_text">
                  空间距离
                </div>
                <!-- <div class="right_box1_oper right_box1_head_text">操作</div> -->
              </div>
              <!-- <vue3-seamless-scroll :list="forecastList" class="right_three_scroll" :step="0.5" v-model="yuScroll"
                wheel="true" :hover="yuHover" :limitScrollNum="yuLimitScrollNum"> -->
              <div class="right_box2_content">
                <div class="" v-for="(item, index) in forecastList" :key="index">
                  <div class="right_box1_table_head right_box1_table_line"
                    :class="index % 2 == 0 ? 'right_table_radix' : 'right_table_even'" @click="handleOpen3(item)"
                    :style="{ color: (item == selectItem ? 'yellow' : 'white') }">
                    <div class="right_box1_index right_box1_line_text">
                      {{ index + 1 }}
                    </div>
                    <div class="right_box1_type right_box1_line_text">
                      {{ item.type }}
                    </div>
                    <div class="right_box1_name right_box1_line_text" style="text-align: center">
                      {{ item.title }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance3 }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance2 }}
                    </div>
                    <div class="right_box1_distance right_box1_line_text" style="text-align: center">
                      {{ item.distance }}
                    </div>
                    <!-- <div class="right_box1_oper right_box1_line_text">
                      <span class="right_box1_see" @click="handleOpen(item, true)"
                        v-show="!item.show && item.type == '异常区'" style="color: greenyellow">显示</span>
                      <span class="right_box1_see" @click="handleOpen(item, false)"
                        v-show="item.show && item.type == '异常区'" style="color: yellow">隐藏</span>
                      <span class="right_box1_see" @click="handleOpen2(item)" v-show="item.type == '断层'">查看</span>
                    </div> -->
                  </div>
                </div>
              </div>
              <!-- </vue3-seamless-scroll> -->
            </div>
          </div>
        </div>
      </div>
    </transition>
    <dizhi v-show="dizhiShow" :dizhiShow="dizhiShow" @changeDizhi="changeDizhi"></dizhi>
    <div class="jiankong" v-show="jianKongShwo" v-draggable>
      <div class="box1_title">
        <img :src="titleImg" class="box_img" />
        掘进机实时监控
      </div>
      <ul class="jiankong_ul">
        <li class="jiankong_li">
          <span class="jiankong_span">裁割高度：</span>0米
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">左行走电流：</span>100A
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">右行走故障码：</span>0
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">掘槽深度：</span>0米
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">右行走电流：</span>0A
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">左截割电流：</span>120A
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">系统供电电压：</span>120V
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">左行走电机温度：</span>110°C
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">左截割电机绕阻温度：</span>70°C
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">左行走给定速度：</span>0
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">右行走电机温度：</span>0°C
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">右截割电流：</span>0A
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">右行走给定速度：</span>0
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">左行走故障码：</span>0
        </li>
        <li class="jiankong_li">
          <span class="jiankong_span">右截割电机绕阻温度：</span> 42°C
        </li>
      </ul>
      <img :src="closeImg" class="close_img" @click="closeJiankong()" />
    </div>
  </div>
</template>

<script setup>
import { usePanelStore } from "@/store/panel";
import { useAmStore } from "@/store/amS";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import FileSaver from "file-saver";
import titleImg from "@/assets/img/home/<USER>";
import leftImg1 from "@/assets/img/tunnel/1-1.png";
import leftImg2 from "@/assets/img/tunnel/1-2.png";
import leftImg3 from "@/assets/img/tunnel/1-2.png";
import leftImg4 from "@/assets/img/tunnel/1-2.png";
import closeImg from "@/assets/img/home/<USER>";
import * as echarts from "echarts";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef, reactive, onUnmounted, toRaw, watch } from "vue";
import Animation from "@/utils/map/animation";
import SetAlphaTool from "@/utils/map/setAlpha";
import { InfoFilled } from '@element-plus/icons-vue'
import TunnelBox from "@/utils/map/TunnelBox";
import ForecastSort from "@/utils/map/forecastSort";
import dizhi from "@/components/drawer/dizhi.vue";
import $ from "jquery";
import { useToolsStore } from "@/store/tools";
import { useLoadStore } from "@/store/load";
import { getDataExcavation, getCaimeiModel } from "@/api/home/<USER>";
import InitModelShow from "@/utils/map/initModelShow";
import ClipModelsBox from "@/utils/map/clipModelsBox";
import { getDataCoalBaseInfo, getDatageology } from "@/api/home/<USER>";
import resImg from "@/assets/img/home/<USER>";

const amStore = useAmStore();
const loadStore = useLoadStore();
const toolsStore = useToolsStore();
let list = ref(["", "", "", "", "", "", "", "", "", ""]);
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 10,
  yuLimitScrollNum: 10,
  yuScroll: false,
  yuHover: true
});
let { scroll, hover, limitScrollNum, yuLimitScrollNum, yuScroll, yuHover } = toRefs(state);
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
const { isLoadEnd } = storeToRefs(loadStore);
//生命周期
onMounted(() => {
  initEcharts();
  // 初始化 左右两侧 确保展开  工具栏 默认到1  图层关闭
  console.log("测试0")
  initStore(2);
  console.log("测试1")
  initEcharts();
  console.log("测试2")
  findData()
  getDataCoalBaseInfo().then((res) => res.data).then((res) => {
    if (res) {
      console.log('getDataCoalBaseInfo', res)

      let responed = res
      let arr = []
      arr[0] = responed.baseInfo
      arr[1] = responed.coalBed
      arr[2] = responed.structure
      arr[3] = responed.water
      arr[4] = responed.gas
      arr[5] = responed.safeCoalQuantity

      responed.leftContentArr = arr
      coalBaseInfo.value = responed
    }
  })
  if (isLoadEnd.value == true) {
    init();
  }
});
watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);
//销毁生命周期
// 左侧数据
let coalBaseInfo = ref({
  leftContentArr: [],
  mineGeologicalType: '',
  gasLevel: '',
  complex: '',
  mineArea: '',//井田面积
  developingCoalQuantity: '',//开拓煤量
  prepareCoalQuantity: '',//准备煤量
  backCoalQuantity: '',//回采煤量
  safeCoalQuantity: '',//安全煤量
})
onUnmounted(() => {
  console.log('Tunnel组件开始销毁，清理资源...');

  try {
    // 清理隧道盒子
    if (tunnelBox) {
      tunnelBox.remove();
      tunnelBox = null;
    }

    // 清理异常区域
    if (ycq) {
      ycq.removeUpdataDistance();
      ycq = null;
    }

    // 清理距离计算
    if (dc) {
      dc.removeUpdataDistance();
      dc = null;
    }

    // 移除掘进机
    if (amStore) {
      amStore.removeAm();
    }

    if (am) {
      am.destroy();
      am = null;
    }

    // 恢复巷道透明度
    try {
      setModelsShow(true);
    } catch (error) {
      console.warn('恢复巷道透明度失败:', error);
    }

    // 恢复场景亮度
    if (viewer && viewer.scene && viewer.scene.brightness) {
      viewer.scene.brightness.enabled = false;
    }

    // 强制渲染
    if (viewer && viewer.scene) {
      viewer.scene.forceRender();
    }

    // 移除掘进机状态监听
    if (valueTimer) {
      clearInterval(valueTimer);
      valueTimer = null;
    }

    // 移除包围盒
    if (clipModelsBox) {
      clipModelsBox.destroy();
      clipModelsBox = null;
    }

    // 移除排序定时器
    if (forecastTimer) {
      clearInterval(forecastTimer);
      forecastTimer = null;
    }

    // 清理其他定时器
    removeTimeout();

    // 移除选择输入动作
    removeSelectInputAction();

    console.log('Tunnel组件资源清理完成');
  } catch (error) {
    console.error('Tunnel组件销毁时出错:', error);
  }
});

let timer1
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  }
}



//进入页面时的初始化操作
const init = () => {
  //设置巷道透明度为0.2
  setModelsShow(false);
  loadModel();
  setYCQShow(true);
  loadGeoJson();
  initModelsShow()
  addSelectInputAction()
  viewer.scene.forceRender();
};
// 左上角数据
let excavationInfo = ref({})
const findData = () => {
  getDataExcavation().then((res) => res.data).then((res) => {
    if (res) {
      excavationInfo.value = res
    }
  })
}
//掘进机状态
var valueTimer;
//1：规划掘进距离 2：已掘进距离 3：允许掘进距离 4：超期距离 5：未探测距离
var value1 = ref(0);
var value2 = ref(0);
var value3 = ref(0);
var value4 = ref(0);
var value5 = ref(0);
var am;
var amRate = ref(0.668)
//加载掘进车模型
const loadModel = () => {

  am = new Animation(window.viewer);

  window.amc = am
  var modelUrl = getCaimeiModel()
  am.setModel({ url: modelUrl });
  // am.setModel({ url: "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1612797327182860288" });
  // am.setModel({ url: "http://************:48080/app-api/geo/function-server-gltf/get-code/1610659017244012544" });
  var path = [Cesium.Cartesian3.fromDegrees(
    116.96771561897405, 34.85740341715105, 1066.4720546906249
  ),
  Cesium.Cartesian3.fromDegrees(
    116.959271172362, 34.853173728115216, 1048.630659214466
  )]
  var L1 = Cesium.Cartesian3.subtract(path[1], path[0], new Cesium.Cartesian3());
  var offsetVector = Cesium.Cartesian3.multiplyByScalar(
    L1,
    1.5,
    new Cesium.Cartesian3()
  );
  var endP = Cesium.Cartesian3.add(
    path[0],
    offsetVector,
    new Cesium.Cartesian3()
  );
  amStore.setOption({
    pathlist: [path[0], endP],
    speed: 1,
    loop: true,
    rateValue: amRate.value
  })
  // var path2=[...path,endP]
  // am.setPath2(path2)
  // am.getPositionByIndex(5000)
  // am.getPositionByIndex(95000)
  amStore.addAm(am);

  var clipLayers = window.layersManager.getLayersByLabels(['7号煤层', '8号煤层', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层'])
  am.setClipModels(clipLayers, 5, window.earth)
  // var clipLayers2 = window.layersManager.getLayersByLabels(['断层模型', '井巷模型'])
  // am.setClipModels(clipLayers2, -500, window.earth)
  timer1 = setTimeout(() => {

    if (window.location.hash != '#/index/Deduction') {
      am.play();
    }

    am.flyToModel();
    am.showPathLine();
    window.cameraVisual.setVisualByAm(window.location.hash, am)
    clipModels(am.getCurrentPath())
    ycq.updataDistanceByAm(am)
    dc.updataDistanceByAm(am)

  }, 1000);
  //修改平面的位置
  // var lat = 116.992786
  // var lng = 34.909266
  // window.plane.position = Cesium.Cartesian3.fromDegrees(lat, lng, -1200 + 1900)
};
//向外暴露组件
defineExpose({
  am: am,
  amStore: amStore,
  amRate: amRate
})

var clipModelsBox
const clipModels = (parh) => {
  var clipModelsList = window.layersManager.getLayersByLabels(['断层模型', '陷落柱', '积水区', '7煤采空区', '8煤采空区'])
  var path = parh
  clipModelsBox = new ClipModelsBox(
    clipModelsList,
    path, 1500
  );
  clipModelsBox.clipStart();
}

//设置13巷道透明度和煤层
const setModelsShow = (show) => {
  var setAlpha = new SetAlphaTool();
  if (!show) {
    setAlpha.setHangdaoAlphaByID("349", 0.2);
    // setAlpha.hideOthersModelByIds("断层模型", ['25','24','4','22','5','10','1','14','30','29']);
    // setAlpha.hideOthersModelByIds("井巷模型", [ '7' ,'188' ,'159', '209' ,'186' ,'76' ,'98' ,'158' ,'163'  ,'130' ,'152', '82' , '101', '187' ,'84' ,'75', '172' ,'219' ,'43', '147', '30' ,'217' ,'120', '218' ,'219' ,'23' ,'127' ,'198' ,'141' ,'128' ,'0' ,'99' ,'85', '4', '146' ,'25' ,'73', '38', '64' ,'183', '109', '16']);
    // setAlpha.hideOthersModelByIds("7煤采空区", ['2','4']);
    // setAlpha.hideOthersModelByIds("8煤采空区", ['5','6','3']);
  } else {
    setAlpha.setHangdaoAlphaByID("349", 1);
    // setAlpha.showAllModel("断层模型")
    // setAlpha.showAllModel("井巷模型")
    // setAlpha.showAllModel("7煤采空区")
    // setAlpha.showAllModel("8煤采空区")
  }
};


window.onresize = function () {
  myChart.resize();

};
// 左中间按钮
let jueJinList = ref(["顶面剖切", "正面剖切", "侧面剖切", "掘进地质剖面"]);
let jueJinIndex = ref(-1);
let drawer = ref(false);
let dizhiShow = ref(false);
const changeJujin = (index) => {
  if (index == 0) {
    am.setViewer(0);
  }
  if (index == 1) {
    am.setViewer(1);
  }
  if (index == 2) {
    am.setViewer(2);
  }
  if (index == 3) {
    // 关闭面板 工具栏
    panelStore.show = false;
    panelStore.layerActive = false;
    panelStore.toolShow = false;
    dizhiShow.value = !dizhiShow.value;
    addInputAction();
    // jianKongShwo.value = true
  }
  if (jueJinIndex.value == index) {
    // 再次点击 取消选中状态
    jueJinIndex.value = -1;
    return;
  }
  jueJinIndex.value = index;
};

//初始化图层列表和模型显隐
const initModelsShow = () => {
  var initModelShow = new InitModelShow()
  initModelShow.initShow()
}

//打开掘进地质剖面后
const changeDizhi = (val) => {
  dizhiShow.value = val;
  panelStore.show = true;
  panelStore.layerActive = true;
  panelStore.toolShow = true;
  jueJinIndex.value = -1;
  removeInputAction();
  closeJiankong();
};

// 实时监控信息
let jianKongShwo = ref(false);
const closeJiankong = () => {
  jianKongShwo.value = false;
};

//左下角-掘进超前探列表
var tunnelBox = new TunnelBox();
let resultInfoIist = tunnelBox.tunnelBoxInfoIist;
let resultList = tunnelBox.tunnelBoxTitleList;
let resultIndex = tunnelBox.tunnelBoxIndex;
const changeResult = (index) => {
  resultIndex.value = index;
};

//左下角-掘进超前探 图层控制 点击事件
const changeClick = (item) => {
  if (!item.imgUrl) {
    return;
  }
  if (item.check) {
    if (item.imageIdx == -1) {
      tunnelBox.loadImage(item);
    } else {
      tunnelBox.showImage(item.imageIdx, true);
    }
  } else {
    tunnelBox.showImage(item.imageIdx, false);
  }
};
//左下角-掘进超前探 操作 点击事件
const confirmEvent = (item) => {
  tunnelBox.downLoadDoc(item)
}
const cancelEvent = (item) => {
  tunnelBox.downLoadDWG(item)
}


//右下角——异常区和断层
var forecastList = ref([]);
var ycq;
var dc
//右下角——加载异常区和断层json文件
const loadGeoJson = () => {
  ycq = window.ycq
  ycq.getDataByList(forecastList)
  dc = window.dc
  dc.getDataByList(forecastList)
};
var forecastSort = new ForecastSort()
var forecastTimer = setInterval(() => {
  forecastSort.bubbleSort(forecastList)
}, 5000);



//右下角——异常区按钮显隐
const handleOpen = (item, show) => {
  toolsStore.$patch((state) => {
    dataTree = state.dataTree
    var id = window.layersManager.getIdByLabel(item.title)
    if (id != -1) {
      dataTree.setChecked(id, show);
    } else {
      ycq.show(item.index, show)
    }
  });
  viewer.scene.forceRender();

  // ycq.show(item.index, show)
  // viewer.scene.forceRender();
};
const handleOpen2 = (item) => {
  var dcPosition = item.position
  var camreaPosition = _getMoveUpCoordinate(dcPosition, 1400)
  viewer.camera.flyTo({
    duration: 1,
    destination: camreaPosition,
    offset: {
      heading: Geowin3D.Math.toRadians(0), // 水平偏角，默认正北 0
      pitch: Geowin3D.Math.toRadians(-90), // 俯视角，默认-90，垂直向下
      // range: 200, //镜头（屏幕）到定位目标点（实体）的距离
    },
  }, 3000)
  function _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }
}

let selectItem = ref("")
const handleOpen3 = (item) => {
  selectItem.value = item
  var camreaPosition = ""
  switch (item.type) {
    case "异常区":
      toolsStore.$patch((state) => {
        dataTree = state.dataTree
        var id = window.layersManager.getIdByLabel(item.title)
        if (id != -1) {
          dataTree.setChecked(id, true);
        } else {
          ycq.show(item.index, true)
        }
      });
      viewer.scene.forceRender();
      if (item.centre) {
        camreaPosition = _getMoveUpCoordinate(item.centre, 300)
      }
      break
    case "断层":
      camreaPosition = _getMoveUpCoordinate(item.position, 1400)
      dc.selectFeatureByTitle(item.title)
      break
    default:
      break
  }
  if (camreaPosition != "") {
    viewer.camera.flyTo({
      duration: 1,
      destination: camreaPosition,
      offset: {
        heading: Geowin3D.Math.toRadians(0), // 水平偏角，默认正北 0
        pitch: Geowin3D.Math.toRadians(-90), // 俯视角，默认-90，垂直向下
        // range: 200, //镜头（屏幕）到定位目标点（实体）的距离
      },
    }, 3000)
  }
  function _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }
}
var selectHandler;
const addSelectInputAction = () => {
  selectHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  selectHandler.setInputAction((movement) => {
    selectItem.value = ''
    window.dc.removeSelect()
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
const removeSelectInputAction = () => {
  selectItem.value = ''
  window.dc.removeSelect()
  selectHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};



//获取实时监控信息的点击事件
var handler;
const addInputAction = () => {
  handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  handler.setInputAction((movement) => {

    var pick = viewer.scene.drillPick(movement.position);
    for (var obj of pick) {
      if (obj.id && obj.id._id && obj.id._id == "tunnelingMachine") {
        jianKongShwo.value = true;
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
const removeInputAction = () => {
  handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

//进入该页面后模型的显隐和移动设置
var dataTree;
var checkedKeys;
const setYCQShow = (flag) => {
  if (flag) {
    toolsStore.$patch((state) => {
      dataTree = state.dataTree;
      checkedKeys = dataTree.getCheckedKeys();

      var labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '掘进工作面', '陷落柱', '积水区', '采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层', '瞬变电磁顶板', '瞬变电磁顺层', '瞬变电磁底板', '电法异常区', '坑透异常区'])
      dataTree.setCheckedKeys(labels);

      // var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
      // window.ycq.show(sid, true)
      // var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
      // window.ycq.show(sid, true)
      // var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
      // window.ycq.show(sid, true)
    });
  } else {
    if (dataTree && checkedKeys)
      dataTree.setCheckedKeys(checkedKeys);

    var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
    window.ycq.show(sid, false)
    var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
    window.ycq.show(sid, false)
    var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
    window.ycq.show(sid, false)
  }
};

// 下载
const handleDownload = () => { };
const echart = echarts;
let myChart = null;
const initEcharts = () => {
  document.getElementById("receptionTrend").removeAttribute('_echarts_instance_')
  var titleArr = [],
    seriesArr = [],
    colors = [
      ["#389af4", "#dfeaff"],
      ["#ff8c37", "#ffdcc3"],
      ["#ffc257", "#ffedcc"],
      ["#fd6f97", "#fed4e0"],
      ["#a181fc", "#e3d9fe"],
    ];
  titleArr = [
    {
      text: "风速",
      left: "24%",
      top: "25%",
      textAlign: "center",
      textStyle: {
        fontWeight: "normal",
        fontSize: "14",
        color: "#fff",
        textAlign: "center",
      },
    },
    {
      text: "温度",
      left: "73%",
      top: "25%",
      textAlign: "center",
      textStyle: {
        fontWeight: "normal",
        fontSize: "14",
        color: "#fff",
        textAlign: "center",
      },
    },
    {
      text: "CO浓度",
      left: "24%",
      top: "58%",
      textAlign: "center",
      textStyle: {
        fontWeight: "normal",
        fontSize: "14",
        color: "#fff",
        textAlign: "center",
      },
    },
    {
      text: "甲烷浓度",
      left: "73%",
      top: "58%",
      textAlign: "center",
      textStyle: {
        fontWeight: "normal",
        fontSize: "14",
        // lineHeight :"40",
        color: "#fff",
        textAlign: "center",
      },
    },
    {
      text: "粉尘浓度",
      left: "52%",
      top: "93%",
      textAlign: "center",
      textStyle: {
        fontWeight: "normal",
        fontSize: "14",
        color: "#fff",
        textAlign: "center",
      },
    },
  ];

  (seriesArr = [
    {
      type: "gauge",
      radius: "35%",
      min: 0,
      max: 2,
      startAngle: 180,
      endAngle: 0,
      center: ["25%", "24%"],
      axisLine: {
        lineStyle: {
          width: 10,
          color: [
            [0.3, "#67e0e3"],
            [0.7, "#37a2da"],
            [1, "#fd666d"],
          ],
        },
      },
      pointer: {
        itemStyle: {
          color: "auto",
        },
      },
      axisTick: {
        // 刻度样式
        distance: -10,
        length: 1,
        lineStyle: {
          color: "#fff",
          width: 2,
        },
      },
      splitLine: {
        // show:false
        // 分割线
        distance: 0,
        length: 3,
        lineStyle: {
          color: "#fff",
          width: 1,
        },
      },
      axisLabel: {
        color: "auto",
        distance: -23,
        fontSize: 12,
      },
      detail: {
        valueAnimation: true,
        formatter: "{value}m/s",
        color: "auto",
        fontSize: 14,
        offsetCenter: [0, "-20%"],
      },
      pointer: {
        // icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: "10%",
        width: 5,
        offsetCenter: [0, "-60%"],
        itemStyle: {
          color: "auto",
        },
      },
      data: [
        {
          value: 0.3,
        },
      ],
    },
    {
      type: "gauge",
      radius: "35%",
      min: 0,
      max: 40,
      startAngle: 180,
      endAngle: 0,
      center: ["74%", "24%"],
      axisLine: {
        lineStyle: {
          width: 10,
          color: [
            [0.3, "#67e0e3"],
            [0.7, "#37a2da"],
            [1, "#fd666d"],
          ],
        },
      },
      pointer: {
        itemStyle: {
          color: "auto",
        },
      },
      axisTick: {
        // 刻度样式
        distance: -10,
        length: 1,
        lineStyle: {
          color: "#fff",
          width: 2,
        },
      },
      splitLine: {
        // show:false
        // 分割线
        distance: 0,
        length: 3,
        lineStyle: {
          color: "#fff",
          width: 1,
        },
      },
      axisLabel: {
        color: "auto",
        distance: -24,
        fontSize: 12,
      },
      detail: {
        valueAnimation: true,
        formatter: "{value}°C",
        color: "auto",
        fontSize: 14,
        offsetCenter: [0, "-20%"],
      },
      pointer: {
        // icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: "10%",
        width: 5,
        offsetCenter: [0, "-60%"],
        itemStyle: {
          color: "auto",
        },
      },
      data: [
        {
          value: 37,
        },
      ],
    },
    {
      type: "gauge",
      radius: "35%",
      min: 0,
      max: 100,
      center: ["25%", "56%"],
      startAngle: 180,
      endAngle: 0,
      axisLine: {
        lineStyle: {
          width: 10,
          color: [
            [0.3, "#67e0e3"],
            [0.7, "#37a2da"],
            [1, "#fd666d"],
          ],
        },
      },
      axisTick: {
        // 刻度样式
        distance: -10,
        length: 1,
        lineStyle: {
          color: "#fff",
          width: 2,
        },
      },
      splitLine: {
        // show:false
        // 分割线
        distance: 0,
        length: 3,
        lineStyle: {
          color: "#fff",
          width: 1,
        },
      },
      axisLabel: {
        color: "auto",
        distance: -23,
        fontSize: 12,
      },
      detail: {
        valueAnimation: true,
        formatter: "{value}ppm",
        color: "auto",
        fontSize: 14,
        offsetCenter: [0, "-20%"],
      },
      pointer: {
        // icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: "10%",
        width: 5,
        offsetCenter: [0, "-60%"],
        itemStyle: {
          color: "auto",
        },
        itemStyle: {
          color: "auto",
        },
      },
      data: [
        {
          value: 60,
        },
      ],
    },
    {
      type: "gauge",
      radius: "35%",
      min: 0,
      max: 1,
      center: ["74%", "56%"],
      startAngle: 180,
      endAngle: 0,
      axisLine: {
        lineStyle: {
          width: 10,
          color: [
            [0.3, "#67e0e3"],
            [0.7, "#37a2da"],
            [1, "#fd666d"],
          ],
        },
      },
      pointer: {
        itemStyle: {
          color: "auto",
        },
      },
      axisTick: {
        // 刻度样式
        distance: -10,
        length: 1,
        lineStyle: {
          color: "#fff",
          width: 2,
        },
      },
      splitLine: {
        // show:false
        // 分割线
        distance: 0,
        length: 3,
        lineStyle: {
          color: "#fff",
          width: 1,
        },
      },
      axisLabel: {
        color: "auto",
        distance: -23,
        fontSize: 12,
      },
      detail: {
        valueAnimation: true,
        formatter: "{value}%",
        color: "auto",
        fontSize: 14,
        offsetCenter: [0, "-20%"],
      },
      pointer: {
        // icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: "10%",
        width: 5,
        offsetCenter: [0, "-60%"],
        itemStyle: {
          color: "auto",
        },
      },
      data: [
        {
          value: 0.8,
        },
      ],
    },
    {
      type: "gauge",
      radius: "38%",
      min: 0,
      max: 100,
      center: ["52%", "90%"],
      startAngle: 180,
      endAngle: 0,
      axisLine: {
        lineStyle: {
          width: 10,
          color: [
            [0.3, "#67e0e3"],
            [0.7, "#37a2da"],
            [1, "#fd666d"],
          ],
        },
      },
      pointer: {
        itemStyle: {
          color: "auto",
        },
      },
      axisTick: {
        // 刻度样式
        distance: -10,
        length: 1,
        lineStyle: {
          color: "#fff",
          width: 2,
        },
      },
      splitLine: {
        // show:false
        // 分割线
        distance: 0,
        length: 3,
        lineStyle: {
          color: "#fff",
          width: 1,
        },
      },
      axisLabel: {
        color: "auto",
        distance: -26,
        fontSize: 12,
      },
      detail: {
        valueAnimation: true,
        formatter: "{value}g/cm³",
        color: "auto",
        fontSize: 14,
        offsetCenter: [0, "-8%"],
      },
      pointer: {
        // icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: "10%",
        width: 5,
        offsetCenter: [0, "-60%"],
        itemStyle: {
          color: "auto",
        },
      },
      data: [
        {
          value: 20,
        },
      ],
    },
  ]),

    (myChart = echart.init(document.getElementById("receptionTrend")));

  myChart.setOption({
    // backgroundColor: "#fff",
    title: titleArr,
    series: seriesArr,
  });
  myChart.resize()
};

</script>
<style lang="less" scoped>
.tunnel {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;

  .left {
    z-index: 1;
    width: 420px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
    // background: rgba(11, 24, 36,.7);
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    padding-right: 18px;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .left_box2 {
      width: 100%;
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      
      .left_box2_ul {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        box-sizing: border-box;
        padding: 0 25px 0 29px;
        margin-top: 20px;
        .left_box2_li {
          height: 62px;
          display: flex;
          position: relative;
          width: 100%;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          background-image: url("../assets/img/home/<USER>");
          background-position: center center;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin-bottom: 20px;
          .left_box2_li_content {
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 1.600000023841858px;
            text-align: left;
            padding-left: 50px;
          }

          .left_box2_li_span {
            /* 安全监控： */
            color: rgb(255, 230, 65);
            font-family: PingFang SC;
            font-size: 18px;
            font-weight: undefined;
            line-height: 27px;
            letter-spacing: 0px;
            text-align: left;
            padding-right: 50px;
          }
          .left_box2_li_line_bottom {
            position: absolute;
            bottom: 0px;
            left: 0px;
            width: 100%;
          }
        }
      }

      // 资源情况
      .left_box2_content {
        flex: 1;
        display: flex;
        justify-content:center;
        width: 100%;
        box-sizing: border-box;
        padding-top: 16px;
        flex-wrap: wrap;

        .left_box2_content_line {
          width: 105px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-size: 14px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #ffffff;
          overflow: hidden;
          margin-bottom: 14px;
          position: relative;
          flex-direction: column;
          margin-right: 22px;
          .left_box2_content_img {
            width: 105px;
            height: 110px;
            position: absolute;
            left: 0px;
            top: 0px;
          }

          .left_box2_content_title {
            font-size: 14px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #00f0e8;
            margin-left: 3px;
            margin-right: 7px;
            width: 90px;
            height: 26px;
            text-align: center;
            z-index: 1;
          }
          .left_box2_content_num{
            /* 安全监控： */
            color: rgb(17, 177, 255);
            font-family: FZLanTingHei-B-GBK;
            font-size: 24px;
            font-weight: undefined;
            line-height: 36px;
            letter-spacing: 0px;
            text-align: left;
            z-index: 1;
            margin-top: 10px;
          }
          .left_box2_content_num_unit{
            margin-bottom: 30.91px;
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: left;
            z-index: 1;
          }
        }
      }
    }

    .left_box1 {
      width: 100%;
      box-sizing: border-box;
      padding: 0px 0px 0px 0px;
      display: flex;
      flex-direction: column;


      .left_box1_content {
        display: flex;
        width: 100%;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 22px 12px 0 16px;
        flex-wrap: wrap;

        .left_box1_li {
          display: flex;
          width: 46%;
          margin-bottom: 24px;
          justify-content: flex-start;

          .left_box_li_right {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }

          .left_box1_num {
            /* 安全监控： */
            color: rgb(17, 177, 255);
            font-size: 18px;
            font-weight: undefined;
            line-height: 27px;
            letter-spacing: 0px;
            text-align: left;
          }

          .left_box1_text {
            /* 安全监控： */
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: left;
          }

          .left_box_li_img {
            width: 57px;
            height: 52px;
            margin-right: 9px;
          }
        }
      }
    }

    .left_box2 {
      width: 100%;
      // height: 160px;
      box-sizing: border-box;
      padding: 6px 0 0px 0px;
      display: flex;
      flex-direction: column;

      .left_box2_content {
        flex: 1;
        width: 100%;
        margin-top: 18px;

        .left_box1_btn {
          display: flex;
          justify-content: center;
          align-items: center;
          color: #ffffff;
          background-image: url("../assets/img/tunnel/btn.png");
          background-position: center center;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin-bottom: 18px;
          width: 193px;
          height: 64px;
          cursor: pointer;
          font-family: PingFang SC;
          font-size: 16px;
          font-weight: undefined;
          line-height: 24px;
          letter-spacing: 1.600000023841858px;
          color: #fff;

          &:hover {
            transform: scale(1.1);
          }
        }

        .let_box1_ul {
          width: 100%;
          display: flex;
          justify-content: space-around;
          align-items: center;
          flex-wrap: wrap;
          height: 100%;
        }

        .left_box1_li_active {
          color: rgb(23, 249, 255);
          background-image: url("../assets/img/tunnel/bntA.png");
        }
      }
    }

    .left_box3 {
      width: 100%;
      height: 520px;
      box-sizing: border-box;
      padding: 10px 0 0px 0px;
      display: flex;
      flex-direction: column;

      .right_box1_ul {
        margin-top: 10px;
        width: 100%;
        height: 24px;
        display: flex;
        padding-left: 10px;

        .right_box1_li {
          height: 30px;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          cursor: pointer;
          color: rgb(198, 198, 198);
          font-family: PingFang SC;
          font-size: 16px;
          background: rgba(148, 148, 148, 0.4);
          border: 1px solid rgb(116, 116, 116);
          border-radius: 15px;
          padding: 0 12px;
          margin-right: 12px;
          margin-bottom: 6px;
        }

        .right_box1_li_active {
          background: rgba(49, 131, 194, 0.4);
          border: 1px solid rgb(0, 179, 255);
          border-radius: 15px;
          color: rgb(255, 255, 255);
        }
      }

      .left_box3_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 12px 10px 0 10px;
        overflow: hidden;

        .right_three_scroll {
          height: 342px;
          overflow: hidden;
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);
          overflow-y: hidden;
          /*纵向不滚动*/

          overflow-x: auto;

          /*横向滚动*/
          .right_box1_head_text {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(143, 199, 255);
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_index {
            width: 40px;
          }

          .right_box1_title {
            width: 34px;
            font-size: 12px;
            text-align: center;
          }

          .right_box1_name {
            width: 120px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: flex;
            justify-content: center;
          }

          .right_box1_time {
            width: 100px;
            display: flex;
            justify-content: center;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_oper {
            width: 80px;
            display: flex;
            justify-content: center;

            &:hover {
              color: #00f0ff;
            }
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }
  }

  .right {
    z-index: 2;
    width: 350px;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    // background: rgba(11, 24, 36,.7);
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-left: 18px;

    .right_box1 {
      width: 100%;
      height: 490px;
      box-sizing: border-box;
      padding: 0px 0px 0 0px;
      display: flex;
      flex-direction: column;

      .box1_title {
        box-sizing: border-box;

      }

      .myChart {
        width: 344px;
        height: 400px;
        box-sizing: border-box;
        padding: 15px 0px 0 0;
      }
    }

    .right_box3 {
      width: 100%;
      // height: 583px;
      flex: 1;
      box-sizing: border-box;
      padding: 10px 0 10px 0px;
      display: flex;
      flex-direction: column;

      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 10px 10px 0 10px;
        overflow-y: hidden;
        overflow-x: scroll;

        .right_three_scroll {
          height: 374px;
          overflow: hidden;
          // overflow: auto;
        }

        .right_table_box {
          width: 500px;
        }

        .right_box1_table_sticky {
          position: sticky;
          overflow-y: hidden;
          /*纵向不滚动*/
          overflow-x: auto;
          /*横向滚动*/
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 16px;
          display: flex;
          // justify-content: center;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

          .right_box1_head_text {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(143, 199, 255);
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_index {
            width: 40px;
          }

          .right_box1_distance {
            width: 80px;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_name {
            width: 140px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            justify-content: flex-start;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_time {
            width: 120px;
          }

          .right_box1_upTime {
            width: 120px;
          }

          .right_box1_oper {
            width: 100px;
            display: flex;
            justify-content: center;

            span {
              margin-left: 10px;
              width: 49px;
              height: 20px;
              font-size: 12px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;

              &:hover {
                filter: brightness(1.1);
              }
            }

            .right_box1_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }

            .right_box1_down {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }

      .right_box2_content {
        overflow-x: scroll;
        height: 440px;
      }
    }
  }

  // 图框 标题
  .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;

    span {
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

  .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }

  .jiankong {
    position: absolute;

    left: 600px;
    bottom: 300px;
    z-index: 2;
    width: 660px;
    height: 260px;
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;
    padding: 10px 20px 0 20px;
    background-color: RGBA(5, 31, 89, 0.6);

    .close_img {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 30px;
      height: 30px;
      cursor: pointer;
    }

    .jiankong_ul {
      margin-top: 20px;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .jiankong_li {
        color: #ffffff;
        width: 32%;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        margin-bottom: 20px;

        .jiankong_span {
          color: #fdff4d;
        }
      }
    }
  }
}
</style>
