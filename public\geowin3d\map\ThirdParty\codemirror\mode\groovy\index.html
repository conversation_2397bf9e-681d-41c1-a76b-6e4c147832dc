<!doctype html>

<title>CodeMirror: Groovy mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="groovy.js"></script>
<style>.CodeMirror {border-top: 1px solid #500; border-bottom: 1px solid #500;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Groovy</a>
  </ul>
</div>

<article>
<h2>Groovy mode</h2>
<form><textarea id="code" name="code">
//Pattern for groovy script
def p = ~/.*\.groovy/
new File( 'd:\\scripts' ).eachFileMatch(p) {f ->
  // imports list
  def imports = []
  f.eachLine {
    // condition to detect an import instruction
    ln -> if ( ln =~ '^import .*' ) {
      imports << "${ln - 'import '}"
    }
  }
  // print thmen
  if ( ! imports.empty ) {
    println f
    imports.each{ println "   $it" }
  }
}

/* Coin changer demo code from http://groovy.codehaus.org */

enum UsCoin {
  quarter(25), dime(10), nickel(5), penny(1)
  UsCoin(v) { value = v }
  final value
}

enum OzzieCoin {
  fifty(50), twenty(20), ten(10), five(5)
  OzzieCoin(v) { value = v }
  final value
}

def plural(word, count) {
  if (count == 1) return word
  word[-1] == 'y' ? word[0..-2] + "ies" : word + "s"
}

def change(currency, amount) {
  currency.values().inject([]){ list, coin ->
     int count = amount / coin.value
     amount = amount % coin.value
     list += "$count ${plural(coin.toString(), count)}"
  }
}
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-groovy"
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-groovy</code></p>
  </article>
