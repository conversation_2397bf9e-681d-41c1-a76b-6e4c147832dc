/**
 * 全局兼容性检查和修复
 * 检查所有依赖geowin3d的功能并提供兼容性解决方案
 */

// 检查并修复全局变量
export function checkAndFixGlobals() {
  const fixes = []

  // 检查Cesium
  if (typeof window.Cesium === 'undefined') {
    fixes.push('Cesium未定义')
    try {
      // 尝试动态导入Cesium
      import('cesium').then(Cesium => {
        window.Cesium = Cesium
        console.log('Cesium已动态加载')
      }).catch(error => {
        console.error('无法加载Cesium:', error)
        // 创建空的Cesium对象防止错误
        window.Cesium = {
          Viewer: class MockViewer {},
          Cartesian3: class MockCartesian3 {},
          Cartographic: class MockCartographic {},
          Math: {
            toDegrees: (radians) => radians * 180 / Math.PI,
            toRadians: (degrees) => degrees * Math.PI / 180
          },
          PrimitiveCollection: class MockPrimitiveCollection {},
          Cesium3DTileset: class MockCesium3DTileset {},
          ScreenSpaceEventType: {
            LEFT_CLICK: 'LEFT_CLICK',
            LEFT_DOUBLE_CLICK: 'LEFT_DOUBLE_CLICK',
            RIGHT_CLICK: 'RIGHT_CLICK'
          },
          Ellipsoid: {
            WGS84: {}
          }
        }
      })
    } catch (error) {
      console.error('Cesium加载失败:', error)
    }
  }

  // 检查viewer
  if (typeof window.viewer === 'undefined') {
    fixes.push('viewer未定义')
    window.viewer = {
      camera: {
        position: { x: 0, y: 0, z: 0 },
        heading: 0,
        pitch: 0,
        roll: 0,
        setView: () => {
          console.log('模拟viewer.camera.setView调用');
        },
        pickEllipsoid: () => {
          console.log('模拟viewer.camera.pickEllipsoid调用');
          return null;
        }
      },
      scene: {
        camera: {
          heading: 0,
          pitch: 0,
          roll: 0
        },
        forceRender: () => {
          console.log('模拟viewer.scene.forceRender调用');
        },
        globe: {
          ellipsoid: window.Cesium?.Ellipsoid?.WGS84 || {}
        },
        pick: () => {
          console.log('模拟viewer.scene.pick调用');
          return null;
        },
        pickFromRay: () => {
          console.log('模拟viewer.scene.pickFromRay调用');
          return null;
        },
        drillPickFromRay: () => {
          console.log('模拟viewer.scene.drillPickFromRay调用');
          return [];
        },
        primitives: {
          add: () => {
            console.log('模拟viewer.scene.primitives.add调用');
          },
          remove: () => {
            console.log('模拟viewer.scene.primitives.remove调用');
          }
        },
        canvas: document.createElement('canvas'),
        postProcessStages: {
          add: () => {
            console.log('模拟postProcessStages.add调用');
            return { selected: [] };
          }
        }
      },
      entities: {
        add: () => {
          console.log('模拟viewer.entities.add调用');
          return { id: 'mock-entity-' + Math.random() };
        },
        remove: () => {
          console.log('模拟viewer.entities.remove调用');
        },
        removeAll: () => {
          console.log('模拟viewer.entities.removeAll调用');
        }
      },
      flyTo: () => {
        console.log('模拟viewer.flyTo调用');
        return Promise.resolve();
      },
      zoomTo: () => {
        console.log('模拟viewer.zoomTo调用');
        return Promise.resolve();
      },
      resolutionScale: 1.0
    }
  }

  // 检查earth
  if (typeof window.earth === 'undefined') {
    fixes.push('earth未定义')
    window.earth = {
      getCameraInfo: () => ({
        lontitude: 0,
        latitude: 0,
        height: 0,
        heading: 0,
        pitch: 0,
        roll: 0
      }),
      loadLayer: () => {},
      layerManager: window.layersManager || {}
    }
  }

  // 检查layersManager
  if (typeof window.layersManager === 'undefined') {
    fixes.push('layersManager未定义')
    window.layersManager = {
      addLayer: () => 'mock_id',
      getIdByLabel: () => null,
      getLayer: () => null,
      setLayerVisible: () => {},
      removeLayer: () => {}
    }
  }

  if (fixes.length > 0) {
    console.warn('检测到缺失的全局变量，已创建兼容性对象:', fixes)
  }

  return fixes
}

// 检查特定功能的依赖
export function checkFeatureDependencies(featureName) {
  const dependencies = {
    'camera': ['viewer', 'Cesium'],
    'layers': ['earth', 'layersManager', 'Cesium'],
    'measurement': ['viewer', 'Cesium'],
    'analysis': ['viewer', 'earth', 'Cesium', 'layersManager']
  }

  const required = dependencies[featureName] || []
  const missing = []

  required.forEach(dep => {
    if (typeof window[dep] === 'undefined') {
      missing.push(dep)
    }
  })

  return {
    required,
    missing,
    available: missing.length === 0
  }
}

// 安全执行函数，带有依赖检查
export function safeExecute(func, dependencies = [], fallback = null) {
  const check = checkFeatureDependencies('custom')
  const missing = dependencies.filter(dep => typeof window[dep] === 'undefined')

  if (missing.length > 0) {
    console.warn(`无法执行函数，缺少依赖:`, missing)
    if (typeof fallback === 'function') {
      return fallback()
    }
    return null
  }

  try {
    return func()
  } catch (error) {
    console.error('函数执行失败:', error)
    if (typeof fallback === 'function') {
      return fallback()
    }
    return null
  }
}

// 延迟执行，等待依赖加载
export function executeWhenReady(func, dependencies = [], maxWait = 10000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    
    const check = () => {
      const missing = dependencies.filter(dep => typeof window[dep] === 'undefined')
      
      if (missing.length === 0) {
        try {
          const result = func()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      } else if (Date.now() - startTime > maxWait) {
        reject(new Error(`等待依赖超时: ${missing.join(', ')}`))
      } else {
        setTimeout(check, 100)
      }
    }
    
    check()
  })
}

// 监听全局错误并提供修复建议
export function setupGlobalErrorHandler() {
  const originalError = window.onerror
  
  window.onerror = function(message, source, lineno, colno, error) {
    // 检查是否是已知的兼容性问题
    if (message.includes('is not defined')) {
      const match = message.match(/(\w+) is not defined/)
      if (match) {
        const varName = match[1]
        console.warn(`检测到未定义变量: ${varName}，尝试修复...`)
        checkAndFixGlobals()
      }
    }
    
    // 调用原始错误处理器
    if (originalError) {
      return originalError.call(this, message, source, lineno, colno, error)
    }
    
    return false
  }
}

// 初始化全局兼容性检查
export function initGlobalCompatibility() {
  console.log('初始化全局兼容性检查...')
  
  // 设置错误处理器
  setupGlobalErrorHandler()
  
  // 检查并修复全局变量
  const fixes = checkAndFixGlobals()
  
  // 定期检查（开发模式下）
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      checkAndFixGlobals()
    }, 5000)
  }
  
  console.log('全局兼容性检查初始化完成')
  return fixes
}
