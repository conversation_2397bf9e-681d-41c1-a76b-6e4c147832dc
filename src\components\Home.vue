<template>
  <div class="home">
    <transition appear name="animate__animated animate__pulse" enter-active-class="animate__fadeInLeft"
      leave-active-class="animate__fadeOutLeft">
      <div class="left" v-show="show">
        <div class="left_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>地质情况、构造情况介绍</span>
          </div>
          <ul class="right_box1_ul">
            <li class="right_box1_li" @click="changeResult(index)"
              :class="index == resultIndex ? 'right_box1_li_active' : ''" v-for="(item, index) in resultList"
              :key="index">
              {{ item }}
            </li>
          </ul>
          <div class="left_box1_content">
            {{ coalBaseInfo.leftContentArr[resultIndex] }}
          </div>
        </div>
        <div class="left_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>地质类型等级</span>
          </div>
          <ul class="left_box2_ul">
            <li class="left_box2_li">
              <div class="left_box2_li_content">
                矿井地质类型
              </div>
              <span class="left_box2_li_span">{{ coalBaseInfo.mineGeologicalType }}</span>
            </li>
            <li class="left_box2_li">
              <div class="left_box2_li_content">
                瓦斯类型
              </div>
              <span class="left_box2_li_span">{{ coalBaseInfo.gasLevel }}</span>
            </li>
            <li class="left_box2_li">
              <div class="left_box2_li_content">
                水文地质类型
              </div>
              <span class="left_box2_li_span">{{ coalBaseInfo.extend1 }}</span>
            </li>
            <li class="left_box2_li">
              <div class="left_box2_li_content">
                地质构造复杂程度
              </div>
              <span class="left_box2_li_span">{{ coalBaseInfo.complex}}</span>
            </li>
          </ul>
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>资源情况</span>
          </div>
          <div class="left_box2_content">
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.mineArea }}</span>
              <span class="left_box2_content_num_unit">KM²</span>
              <span class="left_box2_content_title">井田面积</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.extend2 }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">保有资源量</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.developingCoalQuantity }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">开拓煤量</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.prepareCoalQuantity }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">准备煤量</span>
            </div>
            <div class="left_box2_content_line">
              <img class="left_box2_content_img" :src="resImg" />
              <span class="left_box2_content_num">{{ coalBaseInfo.backCoalQuantity }}</span>
              <span class="left_box2_content_num_unit">万吨</span>
              <span class="left_box2_content_title">回采煤量</span>
            </div>
            
          </div>
        </div>
      </div>
    </transition>
    <transition appear name="animate__animated animate__bounce" enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight">
      <div class="right" v-show="show">
        <div class="right_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>地质资料</span>
          </div>
          <ul class="right_box1_ul">
            <li class="right_box1_li" @click="changeZiLiao(index, item)"
              :class="index == ziLiaoTitleIndex ? 'right_box1_li_active' : ''" v-for="(item, index) in ziLiaoList"
              :key="index">
              {{ item }}
            </li>
          </ul>
          <div class="right_box1_table">
            <div class="table_box">
              <div class="right_box1_table_head">
                <div class="right_box1_index right_box1_head_text">序号</div>
                <div class="right_box1_name right_box1_head_text">文件名</div>
                <div class="right_box1_type right_box1_head_text">格式</div>
                <div class="right_box1_time right_box1_head_text">文件时间</div>
                <div class="right_box1_upTime right_box1_head_text">
                  上传时间
                </div>
              </div>
              <div class="" v-for="(item, index) in fileArr[ziLiaoIndex]" :key="index">
                <div class="right_box1_table_head right_box1_table_line" :class="
                  index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                ">
                  <div class="right_box1_index right_box1_line_text" :style="{ color: (ziLiaoIndex=='地质报告' ? 'white' : 'grey')}" >
                    {{ index + 1 }}
                  </div>
                  <div class="right_box1_name right_box1_line_text" :title="item.fileName" :style="{ color: (ziLiaoIndex=='地质报告' ? 'white' : 'grey')}">
                    {{ item.dataName }}
                  </div>
                  <div class="right_box1_type right_box1_line_text" :class="[{ pdfFormat: item.dataFormat === 'pdf' }]" :style="{ color: (ziLiaoIndex=='地质报告' ? 'white' : 'grey')}">
                    {{ item.dataFormat }}
                  </div>
                  <div class="right_box1_time right_box1_line_text" :style="{ color: (ziLiaoIndex=='地质报告' ? 'white' : 'grey')}">
                    {{ item.fileTime }}
                  </div>
                  <div class="right_box1_upTime right_box1_line_text" :style="{ color: (ziLiaoIndex=='地质报告' ? 'white' : 'grey')}">
                    {{ item.fileUploadTime }}
                  </div>
                  <div class="right_box1_oper right_box1_line_text" :style="{ color: (ziLiaoIndex=='地质报告' ? 'white' : 'grey')}">
                    <span class="right_box1_see" @click="handleOpen(item)">查看</span>
                    <span class="right_box1_down" @click="handleDownload(item)">下载</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            <span>场景信息</span>
          </div>
          <ul class="right_box2_content">
            <li class="right_box2_li" v-for="(item, index) in layerArrs" :key="index">
              <div class="right_box2_li_head">
                <div class="right_box2_li_heda_left">
                  {{ item.label }}
                </div>
              
              </div>
              <div class="right_box2_li_content">
                <div class="right_box2_li_legendBox" v-for="(legendItem, legendIndex) in item.legend"
                  :key="legendIndex">
                  {{ legendItem.title }}

                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </transition>

    <!-- PDF查看弹窗 -->
    <PdfViewer
      :visible="pdfViewerVisible"
      :pdf-path="currentPdfPath"
      :title="currentPdfTitle"
      :file-name="currentPdfFileName"
      @close="closePdfViewer"
      @download="handlePdfDownload"
    />
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { initStore } from '@/utils/store'
import { usePanelStore } from "@/store/panel";
import { useToolsStore } from "@/store/tools";
import { storeToRefs } from "pinia";
import FileSaver from "file-saver";
import titleImg from "@/assets/img/home/<USER>";
import PdfViewer from "@/components/common/PdfViewer.vue";
import resImg from "@/assets/img/home/<USER>";
import legendRImg from "@/assets/img/home/<USER>";
import closeImg from "@/assets/img/home/<USER>";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef, computed } from "vue";
import LayerManager from "../utils/map/layerManager";
import { getDataCoalBaseInfo, getDatageology } from "@/api/home/<USER>";
import { useLoadStore } from "@/store/load";

import InitModelShow from "@/utils/map/initModelShow";
const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);

const toolsStore = useToolsStore();
const { layerArr, disableTreeId } = storeToRefs(toolsStore);


const layerArrs = computed(() => {
  var list = []
  for (var item of layerArr.value) {
    // console.log(item)
    if (item.label == '7号煤层' || item.label == '8号煤层') {
      list.push(item)
    }
  }
  return list
})

// 移除图例 label
const removeLabel = (id) => {
  toolsStore.disableTreeId = id
  toolsStore.$patch((state) => {
    let acData = state.layerArr
    let _acData = acData.findIndex(item => item.id == id)
    if (_acData > -1) {
      acData.splice(_acData, 1)
    }
    state.layerArr = acData;

  })
}
//生命周期
onMounted(() => {
  // 初始化 
  initStore(1)
  // 左侧数据展示
  getDataCoalBaseInfo().then((res)=>res.data).then((res) => {
    if (res) {
      console.log('getDataCoalBaseInfo',res)

      let responed = res
      let arr = []
      arr[0] = responed.baseInfo
      arr[1] = responed.coalBed
      arr[2] = responed.structure
      arr[3] = responed.water
      arr[4] = responed.gas
      arr[5] = responed.safeCoalQuantity

      responed.leftContentArr = arr
      coalBaseInfo.value = responed
    }
  })
  //  右上角 地质资料
  getDatageology().then((res)=>res.data).then((res) => {
    if (res) {
      console.log("getDatageology",res)
      fileArr.value = res
      ziLiaoList.value = Object.keys(res)
      ziLiaoIndex.value = Object.keys(res)[0]
      // console.log(ziLiaoList.value)
      // console.log(ziLiaoIndex.value)
    }
  })
  if (isLoadEnd.value == true) {
    init();
  }
});
watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);

const init = () => {
  initModelsShow()
  console.log(viewer.scene)
  // clipModels()
}

//初始化图层列表和模型显隐
const initModelsShow = () => {
  var initModelShow = new InitModelShow()
  initModelShow.initShow()
}



const defaultProps = {
  children: "children",
  label: "label",
};

const handleOpen = (item) => {
  // 统一使用本地PDF文件路径
  const pdfPath = './pdf/20230417.pdf';

  if (item.dataFormat == "pdf") {
    // 使用弹窗打开PDF
    currentPdfPath.value = pdfPath;
    currentPdfTitle.value = item.dataName || '地质报告';
    currentPdfFileName.value = `${item.dataName || 'document'}.pdf`;
    pdfViewerVisible.value = true;
  }
  else if (item.dataFormat == "zip") {
    window.open(item.extend1);
  }
};
// 下载
const handleDownload = (item) => {
  // 统一使用本地PDF文件路径
  const pdfPath = './pdf/20230417.pdf';

  if (item.dataFormat == "pdf") {
    // 下载本地PDF文件
    const link = document.createElement('a');
    link.href = pdfPath;
    link.download = `${item.dataName || 'document'}.pdf`;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  else if (item.dataFormat == "zip") {
    FileSaver.saveAs(item.extend1, item.dataName + ".zip");
  }
};

// PDF弹窗相关方法
const closePdfViewer = () => {
  pdfViewerVisible.value = false;
  currentPdfPath.value = '';
  currentPdfTitle.value = '';
  currentPdfFileName.value = '';
};

const handlePdfDownload = (data) => {
  console.log('PDF下载:', data);
};
// 左侧数据
let coalBaseInfo = ref({
  leftContentArr: [],
  mineGeologicalType: '',
  gasLevel: '',
  complex: '',
  mineArea: '',//井田面积
  developingCoalQuantity: '',//开拓煤量
  prepareCoalQuantity: '',//准备煤量
  backCoalQuantity: '',//回采煤量
  safeCoalQuantity: '',//安全煤量
})
// 左上角
let resultList = ref(["基本信息", "煤层", "构造", "水文", "瓦斯"])
let resultIndex = ref(0)
const changeResult = (index) => {
  resultIndex.value = index
}
let leftContentArr = [
  " 徐庄煤矿隶属于中国中煤能源集团公司上海大屯能源股份有限公司，于1970年10月开工建设，1979年12月正式投产。徐庄煤矿初步设计生产能力为90万吨/年，服务年限97年（19勘探线以西精查范围计算），1985年达产。矿井经过数次改造后，2010年核定矿井生产能力为180万吨/年。",
  "井田内含煤地层有太原组、山西组、下石盒子组，含煤地层总厚度为478.32m，含煤20多层，煤层平均总厚度约为13.73m，可采煤层4层（7、8、17、21号），总厚度平均为10.25m。太原组：地层总厚度平均为157.91m，含煤10多层，煤层平均总厚度为5.63m，可采煤层2层（17、21号煤层），平均总厚度为2.15m。18号煤层在井田范围内仅有少数点可采。山西组：该组地层总厚度平均为102.63m，含煤4层，煤层平均总厚度为8.40m，可采煤层2层（7、8号煤层），两层煤平均厚度为8.10m。下石盒子组：仅“柴煤段”含1～3层煤线，煤层总厚度约为0.92m，全井田范围内无可采点。",
  "井田大部分区域呈一走向N 45°～70°E，倾向北西的单斜构造，倾角10°～36°，平均17.3°。F20断层切割了井田西南部煤层，构成了井田的自然边界。井田内地质构造主要是断层。",
  "徐庄井田北、西、南为大断层隔水边界，其东为半开放边界。在单元隔水上边界之上的孔隙含水层微量而缓慢地补给单元内孔隙含水层，此第四系下部含水层又通过与基岩的直接接触带等途径补给开采煤层的直接充水含水层。",
  "徐庄煤矿2020年对7号煤层的7313、7431等工作面、8号煤层的8194、8331等工作面的瓦斯含量进行了测定，其结果为7号煤层瓦斯含量最大值为0.0864m3/t，8号煤层瓦斯含量最大值为0.0532m3/t，煤层瓦斯含量小于4m3/t，根据《煤矿地质工作规定》井工煤矿地质类型的划分依据，7、8号煤层瓦斯类型为简单，因此徐庄煤矿瓦斯类型属简单。"
]
// 右上角
let ziLiaoList = ref([])
let ziLiaoIndex = ref('')
let ziLiaoTitleIndex = ref(0)

// PDF弹窗相关状态
const pdfViewerVisible = ref(false);
const currentPdfPath = ref('');
const currentPdfTitle = ref('');
const currentPdfFileName = ref('');
const changeZiLiao = (index, item) => {
  // console.log(index,item)
  ziLiaoIndex.value = item
  ziLiaoTitleIndex.value = index
}
let fileArr = ref({
  '矿井生产地质报告': [
    {
      fileName: "徐庄煤矿矿井地质报告",
      format: "PDF",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/贵州省兴仁县兴隆煤矿生产地质报告.pdf",
    },
    {
      fileName:
        "徐庄煤矿矿井地质报告附表",
      format: "PDF",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/新疆哈密巴里坤县三塘湖鑫源煤炭有限责任公司煤矿生产地质报告资源储量核实说明书.pdf",
    },
    {
      fileName:
        "徐庄煤矿矿井地质报告附图",
      format: "ZIP",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/新疆哈密巴里坤县三塘湖鑫源煤炭有限责任公司煤矿生产地质报告资源储量核实说明书.pdf",
    },
  ],
  '水文地址报告': [
    {
      fileName: "徐庄煤矿矿井水文地质类型报告",
      format: "PDF",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/贵州省兴仁县兴隆煤矿生产地质报告.pdf",
    },
    {
      fileName:
        "徐庄煤矿矿井水文地质类型报告附表",
      format: "PDF",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/新疆哈密巴里坤县三塘湖鑫源煤炭有限责任公司煤矿生产地质报告资源储量核实说明书.pdf",
    },
    {
      fileName:
        "徐庄煤矿矿井水文地质类型报告附图",
      format: "ZIP",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/新疆哈密巴里坤县三塘湖鑫源煤炭有限责任公司煤矿生产地质报告资源储量核实说明书.pdf",
    },
  ],
  '瓦斯地质报告': [
    {
      fileName: "徐庄煤矿矿井瓦斯地质类型报告",
      format: "PDF",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/贵州省兴仁县兴隆煤矿生产地质报告.pdf",
    },
    {
      fileName:
        "徐庄煤矿矿井瓦斯地质报告附表",
      format: "PDF",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/新疆哈密巴里坤县三塘湖鑫源煤炭有限责任公司煤矿生产地质报告资源储量核实说明书.pdf",
    },
    {
      fileName:
        "徐庄煤矿矿井瓦斯地质报告附图",
      format: "ZIP",
      fileTime: "2020年",
      uploadTime: "2021/10/21",
      url: "/PDF/新疆哈密巴里坤县三塘湖鑫源煤炭有限责任公司煤矿生产地质报告资源储量核实说明书.pdf",
    },
  ],
})

</script>
<style lang="less" scoped>
.home {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  .left {
    z-index: 1;
    width: 420px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
   
    // background: rgba(11, 24, 36,.7);
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    padding-right: 18px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .left_box1 {
      width: 100%;
      height: 343px;
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 0px 0px 24px 0px;
      display: flex;
      flex-direction: column;
       
      .left_box1_content {
        flex: 1;
        overflow: auto;
        font-size: 14px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #B6E4FF;
        line-height: 30px;
        box-sizing: border-box;
        padding-right: 10px;
        text-indent: 25px;
        padding-left: 20px;
      }
      .right_box1_ul {
        margin-top: 14px;
        width: 100%;
        display: flex;
        padding-left: 18px;
        flex-wrap: wrap;
        .right_box1_li {
          height: 30px;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          cursor: pointer;
          color: rgb(198, 198, 198);
          font-family: PingFang SC;
          font-size: 16px;
          background: rgba(148, 148, 148, 0.4);
          border: 1px solid rgb(116, 116, 116);
          border-radius: 15px;
          padding: 0 12px;
          margin-right: 12px;
          margin-bottom: 6px;
        }
        .right_box1_li_active {
          background: rgba(49, 131, 194, 0.4);
          border: 1px solid rgb(0, 179, 255);
          border-radius: 15px;
          color: rgb(255, 255, 255);
        }
      }
    }

    .left_box2 {
      width: 100%;
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      
      .left_box2_ul {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        box-sizing: border-box;
        padding: 0 25px 0 29px;
        margin-top: 20px;
        .left_box2_li {
          height: 62px;
          display: flex;
          position: relative;
          width: 100%;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          background-image: url("../assets/img/home/<USER>");
          background-position: center center;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin-bottom: 20px;
          .left_box2_li_content {
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 1.600000023841858px;
            text-align: left;
            padding-left: 50px;
          }

          .left_box2_li_span {
            /* 安全监控： */
            color: rgb(255, 230, 65);
            font-family: PingFang SC;
            font-size: 18px;
            font-weight: undefined;
            line-height: 27px;
            letter-spacing: 0px;
            text-align: left;
            padding-right: 50px;
          }
          .left_box2_li_line_bottom {
            position: absolute;
            bottom: 0px;
            left: 0px;
            width: 100%;
          }
        }
      }

      // 资源情况
      .left_box2_content {
        flex: 1;
        display: flex;
        justify-content:center;
        width: 100%;
        box-sizing: border-box;
        padding-top: 16px;
        flex-wrap: wrap;

        .left_box2_content_line {
          width: 105px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-size: 14px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #ffffff;
          overflow: hidden;
          margin-bottom: 14px;
          position: relative;
          flex-direction: column;
          margin-right: 22px;
          .left_box2_content_img {
            width: 105px;
            height: 110px;
            position: absolute;
            left: 0px;
            top: 0px;
          }

          .left_box2_content_title {
            font-size: 14px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #00f0e8;
            margin-left: 3px;
            margin-right: 7px;
            width: 90px;
            height: 26px;
            text-align: center;
            z-index: 1;
          }
          .left_box2_content_num{
            /* 安全监控： */
            color: rgb(17, 177, 255);
            font-family: FZLanTingHei-B-GBK;
            font-size: 24px;
            font-weight: undefined;
            line-height: 36px;
            letter-spacing: 0px;
            text-align: left;
            z-index: 1;
            margin-top: 10px;
          }
          .left_box2_content_num_unit{
            margin-bottom: 30.91px;
            color: rgb(255, 255, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: left;
            z-index: 1;
          }
        }
      }
    }
  }

  .right {
    z-index: 1;
    width: 420px;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    // background: rgba(11, 24, 36,.7);
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-left: 18px;
    // overflow-y: scroll;
    .right_box1 {
      width: 100%;
      height: 400px;
      box-sizing: border-box;
      padding: 0px 5px 0 5px;
      display: flex;
      flex-direction: column;
      // 
      .right_box1_table {
        width: 100%;
        height: 100%;
        padding: 18px 0 0 6px;
        box-sizing: border-box;
        overflow-x: scroll;

        // 优化滑动条样式
        &::-webkit-scrollbar {
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(51, 214, 255, 0.6);
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            background: rgba(51, 214, 255, 0.8);
          }
        }

        .pdfFormat {
          color: red;
        }

        .table_box {
          width: 740px;
        }

        .right_box_btn {
          cursor: pointer;
          margin-right: 10px;
        }

        .right_three_scroll {
          height: 200px;
          overflow: hidden;
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          align-items: center;
          background: rgba(77, 145, 191, 0.5);
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);
          .right_box1_head_text {
            color: rgb(143, 199, 255);
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: undefined;
            line-height: 24px;
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
            color: #ffffff;
          }
          .right_box1_index {
            width: 40px;
          }
          .right_box1_name {
            width: 200px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            justify-content: flex-start;
            text-align: center;
            color: #ffffff;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_time {
            width: 170px;
          }

          .right_box1_upTime {
            width: 170px;
            align-items: center;
          }
          .right_box1_oper {
            width: 100px;

            span {
              margin-left: 10px;
              width: 49px;
              height: 20px;
              font-size: 12px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;

              &:hover {
                filter: brightness(1.1);
              }
            }

            .right_box1_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }

            .right_box1_down {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }

      .right_box1_ul {
        margin-top: 20px;
        width: 100%;
        height: 24px;
        display: flex;
        flex-wrap: wrap;
        padding-left: 20px;
        .right_box1_li {
          height: 30px;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          cursor: pointer;
          color: rgb(198, 198, 198);
          font-family: PingFang SC;
          font-size: 16px;
          background: rgba(148, 148, 148, 0.4);
          border: 1px solid rgb(116, 116, 116);
          border-radius: 15px;
          padding: 0 12px;
          margin-right: 12px;
          margin-bottom: 6px;
        }

        .right_box1_li_active {
          background: rgba(49, 131, 194, 0.4);
          border: 1px solid rgb(0, 179, 255);
          border-radius: 15px;
          color: rgb(255, 255, 255);
        }
      }
    }

    .right_box2 {
      margin-top: 17px;
      width: 100%;
      height: 600px;
      box-sizing: border-box;
      padding: 15px 0 10px 0px;
      display: flex;
      flex-direction: column;

      .right_box2_content {
        flex: 1;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding-right: 20px;
      
        overflow-y: scroll;
        overflow-x: hidden;

        .right_box2_li {
          width: 100%;
          display: flex;
          flex-direction: column;
          .right_box2_li_head {
            width: 100%;
            display: flex;
            justify-content: space-between;
            height: 60px;
            align-items: center;
            padding-left: 10px;
            .right_box2_li_heda_left {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
              width: 165px;
              height: 30px;
              font-size: 20px;
              color: #ffffff;
              padding-left: 30px;
              padding-top: 6px;
            }

            .right_box2_li_colse {
              width: 28px;
              height: 28px;
              cursor: pointer;
            }
          }

          .right_box2_li_content {
            min-height: 80px;
            display: flex;
            flex-direction: column;
            padding-left: 20px;
            .right_box2_li_legendBox {
              color: rgb(182, 228, 255);
              font-family: PingFang SC;
              font-size: 14px;
              font-weight: undefined;
              line-height: 28px;
              letter-spacing: 0px;
              text-align: left;
              display: flex;
              margin-bottom: 6px;
              align-items: center;
            }
          }
        }

        .right_box2_li:last-child {
          border-bottom: none;
        }
      }
    }
  }

  // 图框 标题
  .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

  .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>

