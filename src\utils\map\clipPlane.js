/*
 * @Author: WRB_giser <EMAIL>
 * @Date: 2022-10-19 14:15:22
 * @LastEditors: WRB_giser <EMAIL>
 * @LastEditTime: 2022-10-21 02:53:36
 * @FilePath: \地质保障可视化系统\geo-protect-visual\src\utils\map\clipPlane.js
 * @Description: 
 * Copyright (c) 2022 by WRB_giser <EMAIL>, All Rights Reserved. 
 */
/**
 * 剖切工具
 */
class ClipPlane {
    clipTypeMap = {
        0:'CLIPYZ',
        1:'CLIPXZ'
    }
    clipType = {

    }
    constructor(viewer) {
        this._viewer = viewer;
    }
    /**
     * 
     * @param {*} tilesets tileset数组
     * @param {*} type 剖切方式
     * 0 : 南北方向剖切
     * 1 : 东西方向剖切
     */
    start(tilesets,clipTypeIndex,setTing) {
        this.clipPlane = new Geowin3D.GwMicroApp.GwClipPlane(this._viewer, tilesets);
        this.clipType = this.clipTypeMap[clipTypeIndex];
        this.clipPlane.startClip(this.clipType);
        if(this.clipType == null){
            this.clipType = this.clipTypeMap[0];
        }
        if(setTing == 1){
            this.hideAxis();
        }else {
            this.showClip();
        }
        this.hidePlane();
    }

    //显示剖切面
    showPlane(){
        this.clipPlane.showPlane();
    }
    //隐藏剖切面
    hidePlane(){
        this.clipPlane.hidePlane();
    }
    //显示操作轴
    showClip(){
        this.clipPlane.showAxis();
    }
    //隐藏操作轴
    hideAxis(){
        this.clipPlane.hideAxis();
    }

    cancel() {
        this.clipPlane.cancel();
        this.clipPlane = null;
    }

}
export default ClipPlane