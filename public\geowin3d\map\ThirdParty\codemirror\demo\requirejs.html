<!doctype html>

<head>
  <title>CodeMirror: HTML completion demo</title>
  <meta charset="utf-8"/>
  <link rel=stylesheet href="../doc/docs.css">

  <link rel="stylesheet" href="../lib/codemirror.css">
  <link rel="stylesheet" href="../addon/hint/show-hint.css">
  <script src="//cdnjs.cloudflare.com/ajax/libs/require.js/2.1.14/require.min.js"></script>
  <style type="text/css">
    .CodeMirror {border-top: 1px solid #888; border-bottom: 1px solid #888;}
  </style>
</head>

<body>
  <div id=nav>
    <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>
    <ul>
      <li><a href="../index.html">Home</a>
      <li><a href="../doc/manual.html">Manual</a>
      <li><a href="https://github.com/codemirror/codemirror">Code</a>
    </ul>
    <ul>
      <li><a class=active href="#">HTML completion</a>
    </ul>
  </div>

  <article>
    <h2>RequireJS module loading demo</h2>

    <p>This demo does the same thing as
    the <a href="html5complete.html">HTML5 completion demo</a>, but
    loads its dependencies
    with <a href="http://requirejs.org/">Require.js</a>, rather than
    explicitly. Press <strong>ctrl-space</strong> to activate
    completion.</p>

    <div id="code"></div>

    <button id="markdown">Dynamically load Markdown mode</button>

    <script type="text/javascript">
      require.config({
        packages: [{
          name: "codemirror",
          location: "../",
          main: "lib/codemirror"
        }]
      });
      require(["codemirror", "codemirror/mode/htmlmixed/htmlmixed",
               "codemirror/addon/hint/show-hint", "codemirror/addon/hint/html-hint",
               "codemirror/addon/mode/loadmode"], function(CodeMirror) {
        editor = CodeMirror(document.getElementById("code"), {
          mode: "text/html",
          extraKeys: {"Ctrl-Space": "autocomplete"},
          value: document.documentElement.innerHTML
        });

        CodeMirror.modeURL = "codemirror/mode/%N/%N";
        document.getElementById("markdown").addEventListener("click", function() {
          CodeMirror.requireMode("markdown", function() {
            editor.replaceRange("This is **Markdown**.\n\n", {line: 0, ch: 0});
            editor.setOption("mode", "markdown");
          });
        });
      });
    </script>
  </article>
</body>
