import createEdgeStage from './CesiumEdgeStage/createEdgeStage'
import $ from "jquery";


class Select {
    constructor(_viewer) {
        this.viewer = _viewer || window.viewer;

        // 安全检查viewer
        if (!this.viewer) {
            console.warn('Viewer未初始化，无法创建Select');
            return;
        }

        this.selectedEntity = new window.Cesium.Entity();

        // 安全地创建handler
        if (this.viewer.scene && this.viewer.scene.canvas) {
            this.handler = new window.Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        } else {
            console.warn('无法创建ScreenSpaceEventHandler，scene或canvas未初始化');
            return;
        }

        // 安全地获取resolutionScale
        this.resolutionScale = this.viewer.resolutionScale || 1.0;

        // 安全地获取postProcessStages的fxaa状态
        try {
            this.postProcessStages = this.viewer.postProcessStages &&
                                   this.viewer.postProcessStages.fxaa &&
                                   this.viewer.postProcessStages.fxaa.enabled;
        } catch (error) {
            console.warn('无法获取postProcessStages.fxaa状态:', error);
            this.postProcessStages = false;
        }

        // 安全地获取depthTestAgainstTerrain
        this.depthTestAgainstTerrain = this.viewer.scene &&
                                     this.viewer.scene.globe &&
                                     this.viewer.scene.globe.depthTestAgainstTerrain;
        // 安全地创建edgeStage
        try {
            this.edgeStage = createEdgeStage();
            if (this.edgeStage) {
                this.edgeStage.visibleEdgeColor = window.Cesium.Color.fromCssColorString('#f75e3f');
                this.edgeStage.hiddenEdgeColor = window.Cesium.Color.fromCssColorString('#f75e3f');
                this.edgeStage.enabled = false;
            }
        } catch (error) {
            console.warn('无法创建edgeStage:', error);
            this.edgeStage = null;
        }

        // 安全地创建cesiumStage
        try {
            if (window.Cesium.PostProcessStageLibrary) {
                this.cesiumStage = window.Cesium.PostProcessStageLibrary.createSilhouetteStage();
                this.cesiumStage.enabled = false;
            } else {
                console.warn('PostProcessStageLibrary不可用');
                this.cesiumStage = null;
            }
        } catch (error) {
            console.warn('无法创建cesiumStage:', error);
            this.cesiumStage = null;
        }

        // 安全地添加stages到postProcessStages
        if (this.viewer.postProcessStages) {
            if (this.edgeStage) {
                try {
                    this.viewer.postProcessStages.add(this.edgeStage);
                } catch (error) {
                    console.warn('无法添加edgeStage:', error);
                }
            }

            if (this.cesiumStage) {
                try {
                    this.viewer.postProcessStages.add(this.cesiumStage);
                } catch (error) {
                    console.warn('无法添加cesiumStage:', error);
                }
            }
        } else {
            console.warn('postProcessStages不可用');
        }

        this.selectFeature = undefined

       
    }

    start(attribute = false) {
        this.viewer.resolutionScale = devicePixelRatio;
        this.viewer.postProcessStages.fxaa.enabled = true
        this.viewer.scene.globe.depthTestAgainstTerrain = true


        this.edgeStage.selected = []


        //鼠标点击，拾取对象并高亮显示
        this.handler.setInputAction((e) => {
            var mousePosition = e.position;
            var picked = viewer.scene.pick(mousePosition)

            this.edgeStage.selected = []
            this.edgeStage.enabled = false

            if (picked && picked.primitive) {

                let primitive = picked.primitive
                let pickIds = primitive._pickIds;
                let pickId = picked.pickId;

                if (!pickId && !pickIds && picked.content) {
                    pickIds = picked.content._model._pickIds;
                }

                if (!pickId) {
                    if (picked.id) {
                        pickId = pickIds.find(pickId => {
                            return pickId.object == picked;
                        })
                    } else if (pickIds) {
                        pickId = pickIds[0]
                    }
                }

                if (pickId) {
                    let pickObject = {
                        pickId: pickId
                    }
                    this.edgeStage.selected = [pickObject]
                    this.cesiumStage.selected = [pickObject]
                    this.selectFeature = pickObject.pickId.object;
                    this.edgeStage.enabled = !this.cesiumStage.enabled
                } else {
                    $message.alert('未找到pickId')
                }

            }
            if (attribute == true) {
                this._setAttribute()
            }
            //源码待修改，forceRender后会更改天空盒
            this.viewer.scene.forceRender();
            this.updateSkyBox();
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK)


    }

    cancel(attribute = false) {
        this.viewer.resolutionScale = this.resolutionScale;
        this.viewer.postProcessStages.fxaa.enabled = this.postProcessStages
        this.viewer.scene.globe.depthTestAgainstTerrain = this.depthTestAgainstTerrain
        this.edgeStage.selected = [];
        this.edgeStage.enabled = false
        this.selectFeature = undefined
        if (attribute == true) {
            this.selectedEntity.description = null
            this.selectedEntity.name = null
            this.viewer.selectedEntity = null;
        }
        this.viewer.scene.forceRender();
        if (this.handler) {
            this.handler.removeInputAction(window.Cesium.ScreenSpaceEventType.LEFT_CLICK);
        }
    }
    _setAttribute() {
        if (this.selectFeature != undefined) {
            this.pickModel = this.selectFeature
            console.log(this.selectFeature)
            this.items = {};
            this.items["编号"] = this.pickModel.getProperty("batchId");
            this.items["名称"] = this.pickModel.getProperty("extdata");
            this.items["所在图层"] = this.pickModel.tileset.dataLabel;
            switch (this.pickModel.tileset.dataLabel) {
                case '断层模型':
                    $.ajax({
                        url: "/attribute/duanceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            var dataid = this.pickModel.getProperty("batchId")
                            if (dataid <= data.length) {
                                this.items["名称"] = data[dataid]['断层编号']
                                this.items["断层性质"] = data[dataid]['性质']
                                this.items["倾向"] = data[dataid]['倾向']
                                this.items["倾角（°）"] = data[dataid]['倾角（°）']
                            }
                            this.setContent()
                        }
                    })
                    break
                case '7号煤层':
                    $.ajax({
                        url: "/attribute/meiceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层位"] = data[0]['层位']
                            this.items["平均煤厚"] = data[0]['平均煤厚']
                            this.setContent()
                        }
                    })
                    break
                case '8号煤层':
                    $.ajax({
                        url: "/attribute/meiceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层位"] = data[1]['层位']
                            this.items["平均煤厚"] = data[1]['平均煤厚']
                            this.setContent()
                        }
                    })
                    break
                case '第四系':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[0]['层厚信息']
                            this.items["主要岩性"] = data[0]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                case '上侏罗～下白垩统':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[1]['层厚信息']
                            this.items["主要岩性"] = data[1]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                case '二叠系地层':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[2]['层厚信息']
                            this.items["主要岩性"] = data[2]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                case '石炭系地层':
                    $.ajax({
                        url: "/attribute/diceng.json",
                        type: "GET",
                        dataType: "json",
                        success: (data) => {
                            this.items["层厚信息"] = data[3]['层厚信息']
                            this.items["主要岩性"] = data[3]['主要岩性']
                            this.setContent()
                        }
                    })
                    break
                    case '回采工作面':
                        this.items["名称"] ='7431工作面';
                        this.setContent()
                        break
                default:
                    this.setContent()
                    break

            }


        }


    }
    setContent() {
        this.contentHtml =
            '<table class="infoBoxp cesium-infoBox-defaultTable"><tbody>';
        for (let pro in this.items) {
            this.contentHtml +=
                "<tr><th>" +
                `${pro}` +
                "</th>" +
                "<td>" +
                `${this.items[pro]}` +
                "</td>" +
                "</tr>";
        }
        this.contentHtml += "</tbody></table>";
        var featureName = this.pickModel.tileset.dataLabel;
        this.selectedEntity.name = featureName;
        this.selectedEntity.description = this.contentHtml;
        this.viewer.selectedEntity = this.selectedEntity;

        // 增加拖拽
        var moveEl = document.getElementsByClassName('cesium-infoBox')[0];
        const mouseDown = (e) => {
            let X = e.clientX - moveEl.offsetLeft;
            let Y = e.clientY - moveEl.offsetTop;
            const move = (e) => {
                moveEl.style.left = e.clientX - X + "px";
                moveEl.style.top = e.clientY - Y + "px";
            };
            document.addEventListener("mousemove", move);
            document.addEventListener("mouseup", () => {
                document.removeEventListener("mousemove", move);
            });
        };
        moveEl.addEventListener("mousedown", mouseDown);

    }
    updateSkyBox(){
        //天空和设置
        var imagePaths = [
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png'
        ];
        Geowin3D.GwApp.setSkyBox(this.viewer.scene, {
            imagePaths: imagePaths
        });
    }
}

export default Select