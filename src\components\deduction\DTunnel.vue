<template>
  <div class="tunnel">
    <div class="time">
      <div class="time_box">
        <div class="time_tool">
          <img :src="timeFlag ? startImg : pausedImg" class="time_tool_img" @click="timeChange(timeFlag)" />
        </div>
        <div class="time_right">
          <div class="time_line"></div>
          <div class="time_bottom" v-for="(item, index) in timeList" :key="index"
            :class="timeIndex == index ? 'time_active' : ''" @click="onChangeTime(index)">
            <div class="time_round"></div>
            <div class="time_title">{{ item }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { usePanelStore } from "@/store/panel";
import { useAmStore } from "@/store/amS";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import FileSaver from "file-saver";
import startImg from "@/assets/img/home/<USER>";
import pausedImg from "@/assets/img/home/<USER>";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef, reactive, onUnmounted, toRaw, watch } from "vue";
import Animation from "@/utils/map/animation";
import SetAlphaTool from "@/utils/map/setAlpha";
import $ from "jquery";
import { useToolsStore } from "@/store/tools";
import { useLoadStore } from "@/store/load";
import InitModelShow from "@/utils/map/initModelShow";
import { getCaimeiModel } from "@/api/home/<USER>";
import ClipModelsBox from "@/utils/map/clipModelsBox";

const amStore = useAmStore();
const loadStore = useLoadStore();
const toolsStore = useToolsStore();
let list = ref(["", "", "", "", "", "", "", "", "", ""]);
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 5,
});
let { scroll, hover, limitScrollNum } = toRefs(state);
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
const { isLoadEnd } = storeToRefs(loadStore);
//生命周期
onMounted(() => {
  initStore(5);
  if (isLoadEnd.value == true) {
    init();
  }
  let toolBox = document.getElementsByClassName("home_layer")[0];
  toolBox.style.right = "";
  toolBox.style.left = "10px";
});
watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);
//销毁生命周期

onUnmounted(() => {
  console.log('DTunnel组件开始销毁，清理资源...');

  try {
    // 清理异常区域
    if (window.ycq) {
      window.ycq.removeUpdataDistance();
      window.ycq = null;
    }

    // 清理距离计算
    if (window.dc) {
      window.dc.removeUpdataDistance();
      window.dc = null;
    }

    // 清理包围盒
    if (clipModelsBox) {
      clipModelsBox.destroy();
      clipModelsBox = null;
    }

    // 销毁动画模型
    if (am) {
      am.destroy();
      am = null;
    }

    // 恢复巷道透明度
    try {
      setModelsShow(true);
    } catch (error) {
      console.warn('恢复巷道透明度失败:', error);
    }

    // 清理定时器
    removeTimeout();

    console.log('DTunnel组件资源清理完成');
  } catch (error) {
    console.error('DTunnel组件销毁时出错:', error);
  }
});

let timer1
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  }
}

//进入页面时的初始化操作
const init = () => {
  //设置巷道透明度为0.2
  setModelsShow(false);
  loadModel();
  setYCQShow(true);
  // loadGeoJson();
  initModelsShow()
  viewer.scene.forceRender();
};
//初始化图层列表和模型显隐
const initModelsShow = () => {
  var initModelShow = new InitModelShow()
  initModelShow.initShow()
}
//掘进机状态
var valueTimer;
//1：规划掘进距离 2：已掘进距离 3：允许掘进距离 4：超期距离 5：未探测距离
var value1 = ref(0);
var value2 = ref(0);
var value3 = ref(0);
var value4 = ref(0);
var value5 = ref(0);
var am;
var amRate = ref(0.668)
//加载掘进车模型
const loadModel = () => {
  am = new Animation(window.viewer);
  var modelUrl=getCaimeiModel()
  am.setModel({ url:modelUrl  });
  // am.setModel({ url: "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1612797327182860288" });
  // am.setModel({ url: "http://************:48080/app-api/geo/function-server-gltf/get-code/1610659017244012544" });
  var path = [Cesium.Cartesian3.fromDegrees(
    116.96771561897405, 34.85740341715105, 1066.4720546906249
  ),
  Cesium.Cartesian3.fromDegrees(
    116.959271172362, 34.853173728115216, 1048.630659214466
  )]
  var L1 = Cesium.Cartesian3.subtract(path[1], path[0], new Cesium.Cartesian3());
  var offsetVector = Cesium.Cartesian3.multiplyByScalar(
    L1,
    1.5,
    new Cesium.Cartesian3()
  );
  var endP = Cesium.Cartesian3.add(
    path[0],
    offsetVector,
    new Cesium.Cartesian3()
  );
  amStore.setOption({
    pathlist: [path[0], endP],
    speed: 1,
    loop: true,
    rateValue: amRate.value
  })
  // var path2=[...path,endP]
  // am.setPath2(path2)
  // am.getPositionByIndex(5000)
  // am.getPositionByIndex(95000)
  amStore.addAm(am);

  var clipLayers = window.layersManager.getLayersByLabels(['7号煤层', '8号煤层', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层'])
  am.setClipModels(clipLayers, 5, window.earth)
  // var clipLayers2 = window.layersManager.getLayersByLabels(['断层模型', '井巷模型', '积水区', '陷落柱', '采空区'])
  // am.setClipModels(clipLayers2, -500, window.earth)
  timer1 = setTimeout(() => {
    if (window.location.hash != '#/index/Deduction') {
      am.play();
    }
    am.flyToModel();
    am.showPathLine();
    window.cameraVisual.setVisualByAm(window.location.hash, am)
    clipModels(am.getCurrentPath())
    window.ycq.updataDistanceByAm(am)
    window.dc.updataDistanceByAm(am)
  }, 1000);
  
};
// //向外暴露组件
// defineExpose({
//   am: am,
//   amStore: amStore,
//   amRate: amRate
// })


//设置13巷道透明度和煤层
const setModelsShow = (show) => {
  var setAlpha = new SetAlphaTool();
  if (!show) {
    setAlpha.setHangdaoAlphaByID("349", 0.2);
    // setAlpha.hideOthersModelByIds("断层模型", ['25', '24', '4', '22', '5', '10', '1', '14', '30', '29']);
    // setAlpha.hideOthersModelByIds("井巷模型", ["349",'7', '188', '159', '209', '186', '76', '98', '158', '163', '130', '152', '82', '101', '187', '84', '75', '172', '219', '43', '147', '30', '217', '120', '218', '219', '23', '127', '198', '141', '128', '0', '99', '85', '4', '146', '25', '73', '38', '64', '183', '109', '16']);
    // setAlpha.hideOthersModelByIds("7煤采空区", ['2', '4']);
    // setAlpha.hideOthersModelByIds("8煤采空区", ['5', '6', '3']);
  } else {
    setAlpha.setHangdaoAlphaByID("349", 1);
    // setAlpha.showAllModel("断层模型")
    // setAlpha.showAllModel("井巷模型")
    // setAlpha.showAllModel("7煤采空区")
    // setAlpha.showAllModel("8煤采空区")
  }
};


var clipModelsBox
const clipModels = (parh) => {
  var clipModelsList = window.layersManager.getLayersByLabels(['断层模型','陷落柱', '积水区', '7煤采空区','8煤采空区'])
  var path = parh
   clipModelsBox = new ClipModelsBox(
    clipModelsList,
    path,1500
  );
  clipModelsBox.clipStart();
}



//进入该页面后模型的显隐和移动设置
var dataTree;
var checkedKeys;
const setYCQShow = (flag) => {
  if (flag) {
    toolsStore.$patch((state) => {
      dataTree = state.dataTree;
      checkedKeys = dataTree.getCheckedKeys();

      var labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '掘进工作面', '陷落柱', '积水区', '采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层', '瞬变电磁顶板','瞬变电磁顺层','瞬变电磁底板', '电法异常区', '坑透异常区'])
      dataTree.setCheckedKeys(labels);

      // var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
      // window.ycq.show(sid, true)
      // var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
      // window.ycq.show(sid, true)
      // var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
      // window.ycq.show(sid, true)
    });
  } else {
    if (dataTree && checkedKeys)
      dataTree.setCheckedKeys(checkedKeys);

    var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
    window.ycq.show(sid, false)
    var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
    window.ycq.show(sid, false)
    var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
    window.ycq.show(sid, false)
  }
};

// 时间轴 数据
let timeList = ref([
  "2022-12-10",
  "2022-12-11",
  "2022-12-12",
  "2022-12-13",
  "2022-12-14",
  "2022-12-15",
  "2022-12-16",
  "2022-12-17",
  "2022-12-18",
]);
let timeIndex = ref(0);
let timeFlag = ref(true);
let nowTime = ref(0);
// 手动选择时间
const onChangeTime = (index) => {
  timeIndex.value = index;
};
// 开始 暂停
const timeChange = (value) => {
  if (value) {
    initTime();
  } else {
    timePaused();
  }
  timeFlag.value = !value;
};
let timerLine = null;

//初始化时间
const initTime = () => {
  let currTime = new Date().getTime();
  //1000 毫秒运行一次
  if (currTime - nowTime.value > 1000) {
    nowTime.value = new Date().getTime();
    timeStart();
  }
  timerLine = requestAnimationFrame(initTime);
};
//   开始
const timeStart = () => {

  timeIndex.value += 1;
  if (timeIndex.value == timeList.value.length) {
    timeIndex.value = 0;
  }
};
//   暂停
const timePaused = () => {
  cancelAnimationFrame(timerLine);
  timerLine = null;
};


//监听timeIndex，改变时执行
watch(timeIndex, (newVal, oldVal) => {
  if (newVal >= oldVal) {
    // var startR = oldVal / timeList.value.length
    // var endR = newVal / timeList.value.length
    // var jiange=(endR-startR)/50
    var startR = amRate.value + newVal / 300
    var endR = amRate.value + oldVal / 300
    // console.log(amRate,amRate)
    var jiange = (endR - startR) / 100
    var timer = setInterval(() => {
      am.setRate(startR);
      startR += jiange
      if (startR >= endR) {
        clearInterval(timer)
      }
    }, 10);
  } else {
    am.setRate(amRate.value + newVal / 300);
    setTimeout(() => {
      // window.amc.flyToModel()
    }, 200);
  }
}, 200);

</script>
<style lang="less" scoped>
.tunnel {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;

  .time {
    position: absolute;
    bottom: 60px;
    left: 0px;
    width: 100%;
    height: 100px;
    z-index: 99;
    display: flex;
    justify-content: center;

    .time_box {
      max-width: 1200px;
      overflow: auto;
      height: 100%;
      //  width: 100%;
      display: flex;
      font-size: 20px;
      color: #fff;
      position: relative;
      padding-top: 20px;
      box-sizing: border-box;

      .time_right {
        position: relative;
        display: flex;
      }

      .time_line {
        width: 100%;
        height: 6px;
        background-color: #0e5cae;
        position: absolute;
        top: 18px;
        left: 0px;
        border-radius: 6px;
      }

      .time_tool {
        width: 60px;
        font-size: 14px;
        display: flex;
        padding-top: 2px;
        padding-left: 10px;
        cursor: pointer;

        .time_tool_img {
          width: 40px;
          height: 40px;
        }
      }

      .time_bottom {
        position: relative;
        width: 120px;
        height: 40px;
        cursor: pointer;

        .time_round {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 8px;
          height: 8px;
          background-color: #fff;
          border-radius: 50%;
        }

        .time_title {
          position: absolute;
          left: 50%;
          top: 30px;
          transform: translateX(-50%);
          text-align: center;
          width: 100px;
          line-height: 20px;
          font-size: 16px;
          white-space: normal;
        }
      }

      .time_active {
        .time_round {
          background: #FF8000;
        }

        .time_title {
          color: #FF8000;
        }
      }
    }
  }

  .left {
    z-index: 1;
    width: 344px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;

    .left_box1 {
      width: 100%;
      height: 300px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 10px 0px 0px 14px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .left_box1_content {
        display: flex;
        width: 100%;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 22px 12px 0 0px;
        flex-wrap: wrap;

        .left_box1_li {
          display: flex;
          // align-items: center;
          width: 46%;
          margin-bottom: 20px;
          justify-content: space-between;

          .left_box_li_right {
            display: flex;
            flex-direction: column;
            align-items: center;
          }

          .left_box1_num {
            font-size: 16px;
            font-family: Source Han Sans SC;
            font-weight: bold;
            color: #fdff4d;
          }

          .left_box1_text {
            font-size: 12px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #ffffff;
            margin: 10px 0 0px 0;
            text-align: center;
          }

          .left_box_li_img {
            width: 46px;
            height: 36px;
            margin-right: 10px;
          }
        }
      }
    }

    .left_box2 {
      margin-top: 17px;
      width: 100%;
      // height: 532px;
      height: 200px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 6px 0 0px 18px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .left_box2_content {
        flex: 1;
        width: 100%;

        .left_box1_btn {
          width: 142px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          font-family: Source Han Sans SC;
          font-weight: 800;
          color: #ffffff;
          background-image: url("../assets/img/tunnel/btn.png");
          background-position: center center;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          cursor: pointer;

          &:hover {
            transform: scale(1.1);
          }
        }

        .let_box1_ul {
          width: 100%;
          display: flex;
          justify-content: space-around;
          align-items: center;
          flex-wrap: wrap;
          height: 100%;
        }

        .left_box1_li_active {
          background-image: url("../assets/img/analyse/bntA.png");
        }
      }
    }

    .left_box3 {
      margin-top: 17px;
      width: 100%;
      height: 420px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 10px 0 0px 18px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .right_box1_ul {
        margin-top: 10px;
        width: 100%;
        height: 24px;
        display: flex;

        .right_box1_li {
          height: 24px;
          border: 2px solid #0d3745;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          cursor: pointer;
          padding: 4px;
        }

        .right_box1_li_active {
          color: #fff;
          border: 1px solid #00f0ff;
          background: rgba(4, 37, 66, 0.7);
        }
      }

      .left_box3_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 10px 20px 0 0;
        overflow: hidden;

        .right_three_scroll {
          height: 242px;
          overflow: hidden;
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          align-items: center;
          background: linear-gradient(0deg, rgba(0, 222, 255, 0.24));
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

          .right_box1_head_text {
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: #26c1d1;
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_index {
            width: 40px;
          }

          .right_box1_title {
            width: 40px;
            font-size: 12px;
            text-align: center;
          }

          .right_box1_name {
            width: 120px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: flex;
            justify-content: center;
          }

          .right_box1_time {
            width: 100px;
            display: flex;
            justify-content: center;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_oper {
            width: 80px;
            display: flex;
            justify-content: center;

            &:hover {
              color: #00f0ff;
            }
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }
  }

  .right {
    z-index: 100;
    width: 344px;
    height: 100%;
    bottom: 60px;
    left: 0px;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: absolute;

    .right_box1 {
      width: 100%;
      height: 490px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 14px 0px 0 0px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .box1_title {
        box-sizing: border-box;
        // padding-left: 22px;
        margin-left: 22px;
      }

      .myChart {
        width: 344px;
        height: 400px;
        box-sizing: border-box;
        padding: 15px 0px 0 0;
      }
    }

    .right_box3 {
      margin-top: 10px;
      width: 100%;
      height: 443px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 15px 0 10px 20px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);

      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 20px 20px 0 0;
        // overflow: hidden;
        overflow-x: scroll;

        .right_three_scroll {
          height: 360px;
          overflow: hidden;
        }

        .right_table_box {
          width: 500px;
        }

        .right_box1_table_head {
          display: flex;
          justify-content: flex-start;
          height: 34px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          // justify-content: center;
          align-items: center;
          background: linear-gradient(0deg, rgba(0, 222, 255, 0.24));
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

          .right_box1_head_text {
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: #26c1d1;
            text-align: center;
            display: flex;
            justify-content: center;
          }

          .right_box1_line_text {
            display: flex;
            justify-content: center;
          }

          .right_box1_index {
            width: 40px;
          }

          .right_box1_distance {
            width: 80px;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_name {
            width: 100px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            justify-content: flex-start;
          }

          .right_box1_type {
            width: 60px;
          }

          .right_box1_time {
            width: 120px;
          }

          .right_box1_upTime {
            width: 120px;
          }

          .right_box1_oper {
            width: 100px;
            display: flex;
            justify-content: center;

            span {
              margin-left: 10px;
              width: 49px;
              height: 20px;
              font-size: 12px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;

              &:hover {
                filter: brightness(1.1);
              }
            }

            .right_box1_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }

            .right_box1_down {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }
        }

        .right_box1_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }

  .jiankong {
    position: absolute;

    left: 600px;
    bottom: 300px;
    z-index: 2;
    width: 660px;
    height: 260px;
    background-image: url("../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;
    padding: 10px 20px 0 20px;
    background-color: RGBA(5, 31, 89, 0.6);

    .close_img {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 30px;
      height: 30px;
      cursor: pointer;
    }

    .jiankong_ul {
      margin-top: 20px;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .jiankong_li {
        color: #ffffff;
        width: 32%;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        margin-bottom: 20px;

        .jiankong_span {
          color: #fdff4d;
        }
      }
    }
  }
}
</style>
