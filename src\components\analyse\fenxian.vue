<template>
  <div class="fenxian">
    <!-- 缓冲区面板 -->
    <div class="home_buffer_box" v-show="bufferShow"  >
      <div class="home_buffer_box1">
        <div class="buffer_line_text">
          半径：
          <el-input-number :max="4000" v-model="bufferValue"></el-input-number>
          <span class="buffer_unit">M</span>
        </div>
        <div class="buffer_line_color">
          <span class="buffer_line_title">颜色：</span>
          <el-color-picker v-model="bufferColor" show-alpha />
        </div>
        <div class="buffer_btn_box">
          <div class="buffer_btn" @click="bufferReset()">重置</div>
          <div class="buffer_btn buffer_cancel" @click="bufferCancel()">
            取消
          </div>
        </div>
      </div>
      <div class="buffer_line">
        <el-slider v-model="bufferValue" class="home_buffer" :max="4000" placement="right"
          @click="stopPropagation($event)" />
      </div>
    </div>
    <div class="right_box1">
      <div class="box1_title">
        <img :src="titleImg" class="box_img" />
        <span>风险点缓冲区分析</span>
      </div>
      <div class="right_content">
        <el-select v-model="checkValue" class="right_content_select" placeholder="选择查询内容" size="small">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="right_box1_table_head">
          <div class="right_box1_index right_box1_head_text">序号</div>
          <div class="right_box1_name right_box1_head_text">名称</div>
          <div class="right_box1_num right_box1_head_text">距离</div>
          <div class="right_box1_oper right_box1_head_text">操作</div>
        </div>
        <vue3-seamless-scroll :list="list" class="right_three_scroll" :step="0.5" v-model="scroll" :hover="hover"
          :limitScrollNum="limitScrollNum" wheel="true">
          <div class="" v-for="(item, index) in list" :key="index">
            <div class="right_box1_table_head right_box1_table_line"
              :class="index % 2 == 0 ? 'right_table_radix' : 'right_table_even'">
              <div class="right_box1_index">{{ index+ 1}}</div>
              <div class="right_box1_name">{{ item.label }}</div>
              <div class="right_box1_num">{{ item.distance }}</div>
              <div class="right_box1_oper right_box1_line_text">
                <span class="right_box1_see" @click="handleOpen(item)">查看</span>
              </div>
            </div>
          </div>
        </vue3-seamless-scroll>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef } from "vue";
import ForecastSort from "@/utils/map/forecastSort";
import titleImg from "@/assets/img/home/<USER>";
import * as echarts from "echarts";
import Buffer from "@/utils/map/buffer3";
import { useToolsStore } from "@/store/tools";
import { useLoadStore } from "@/store/load";
const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);
//import { globalBus } from './globalBus';
//import Common from "@/components/layout/Common.vue";
//生命周期
onMounted(() => {
  // console.log("风险点分析")
  let toolBox = document.getElementsByClassName('home_legend')[0]
  toolBox.style.left = ''
  toolBox.style.right = '350px'
  if (isLoadEnd.value == true) {
    setModelShow(true)
  }
  setDrag()

});
watch(isLoadEnd, (newVal, oldVal) => {
  setModelShow(true)
}, 200);
onUnmounted(() => {
  if (buffer) {
    buffer.clear();
    buffer.cancel();
  }
  //移除排序
  if (forecastTimer) {
    clearInterval(forecastTimer);
  }
  setModelShow(false)
  removeTimeout()
  viewer.scene.forceRender();

})
let timer1
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  }
}
//功能
let buffer;
timer1 = setTimeout(() => {
  let [red, green, blue, alpha] = bufferColor.value
    .match(/\d+(\.\d+)?/g)
    .map(Number);
  buffer = new Buffer(viewer, bufferShow, {
    radius: bufferValue.value,
    color: [red / 255, green / 255, blue / 255, alpha],
  });
  // buffer = new Buffer(viewer);
  buffer.add2(bufferReset, list);
  buffer.getButterData(checkValue.value)
}, 200);

;
// 工具栏-缓冲区
var bufferValue = ref(100);
var bufferColor = ref("rgba(255, 255, 0, 0.5)");
watch(bufferValue, (newVal, oldVal) => {
  if (bufferShow.value == true) {
    buffer.setRadius(newVal);
  }
}, 200);
watch(bufferColor, (newVal, oldVal) => {
  if (bufferShow.value == true) {
    let [red, green, blue, alpha] = newVal.match(/\d+(\.\d+)?/g).map(Number);
    buffer.setColor([red / 255, green / 255, blue / 255, alpha]);
  }
}, 200);

let bufferShow = ref(false);
const handleBuffer = () => {
  bufferShow.value = true;
};
// 阻止冒泡
const stopPropagation = (e) => {
  console.log("缓冲区",e)
  e.stopPropagation();
};
const bufferReset = () => {
  // 重置
  bufferValue.value = 100;
  buffer.setRadius(bufferValue.value);

  bufferColor.value = "rgba(255, 255, 0, 0.5)";
  let [red, green, blue, alpha] = bufferColor.value.match(/\d+(\.\d+)?/g).map(Number);
  buffer.setColor([red / 255, green / 255, blue / 255, alpha]);
};

const bufferCancel = () => {
  bufferReset()
  bufferShow.value = false;
  buffer.clear();
  // buffer.cancel();
  list.value = []
  viewer.scene.forceRender();
};
const setDrag = () => {
  // 增加拖拽
  var moveEl = document.getElementsByClassName('home_buffer_box1')[0];
  var moveEl2 = document.getElementsByClassName('home_buffer_box')[0];
  const mouseDown = (e) => {
    let X = e.clientX - moveEl2.offsetLeft;
    let Y = e.clientY - moveEl2.offsetTop;
    const move = (e) => {
      moveEl2.style.left = e.clientX - X + "px";
      moveEl2.style.top = e.clientY - Y + "px";
    };
    document.addEventListener("mousemove", move);
    document.addEventListener("mouseup", () => {
      document.removeEventListener("mousemove", move);
    });
  };
  moveEl.addEventListener("mousedown", mouseDown);
}


/////////////////////////////////////////////
//用于展示缓冲区分析结果的列表
let list = ref([]);
var forecastSort=new ForecastSort()
var forecastTimer=setInterval(() => {
  forecastSort.bubbleSort(list)
}, 400);
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 15,
});
let { scroll, hover, limitScrollNum } = toRefs(state);
const handleOpen = (item) => {
  var position = _getMoveUpCoordinate(item.position, 100)
  viewer.camera.flyTo({
    duration: 1,
    destination: position,
  }, 3000)
  function _getMoveUpCoordinate(cartesian, height) {
    let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
    let lon = Geowin3D.Math.toDegrees(rad.longitude);
    let lat = Geowin3D.Math.toDegrees(rad.latitude);
    return new Cesium.Cartesian3.fromDegrees(lon, lat, rad.height + height);
  }
};
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);

//进入该页面后模型的显隐和移动设置
var dataTree;
var checkedKeys;
const toolsStore = useToolsStore();
const setModelShow = (flag) => {
  if (flag) {
    toolsStore.$patch((state) => {
      dataTree = state.dataTree;
      checkedKeys = dataTree.getCheckedKeys();

      var labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '掘进工作面', '陷落柱', '积水区', '采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层', '异常区范围', '电法异常区', '坑透异常区'])
      dataTree.setCheckedKeys(labels);

      var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
      window.ycq.show(sid, true)
      var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
      window.ycq.show(sid, true)
      var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
      window.ycq.show(sid, true)
    });
  } else {
    if (dataTree && checkedKeys)
      dataTree.setCheckedKeys(checkedKeys);

    var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
    window.ycq.show(sid, false)
    var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
    window.ycq.show(sid, false)
    var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
    window.ycq.show(sid, false)
  }
};


var checkValue = ref('异常区和断层')
watch(checkValue, (newVal, oldVal) => {


  buffer.getButterData(newVal)
  bufferCancel()

}, 200);

const options = [
  // {
  //   value: '异常区',
  //   label: '异常区',
  // },
  // {
  //   value: '断层',
  //   label: '断层',
  // }
  {
    value: '异常区和断层',
    label: '异常区和断层',
  },
  // {
  //   value: '断层',
  //   label: '断层',
  // }
  // {
  //   value: '积水区',
  //   label: '积水区',
  // },
  // {
  //   value: '陷落区',
  //   label: '陷落区',
  // },
  // {
  //   value: '采空区',
  //   label: '采空区',
  // },
]


</script>
<style lang="less" scoped>
.fenxian {
  width: 100%;
  height: 700px;
  display: flex;
  justify-content: space-between;
  position: relative;
  flex-direction: column;

  .right_box1 {
    width: 100%;
    flex: 1;
    padding: 0px 22px 0 0px;
    display: flex;
    flex-direction: column;
    // background-color: RGBA(5, 31, 89, 0.6);
    background-image: url("../../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-left: 18px;
    .right_content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding: 10px 0px  10px 10px;
      overflow: hidden;

      .right_content_select {
        margin-bottom: 2s0px;
      }

      .right_three_scroll {
        height: 650px;
        overflow: hidden;
      }

      .right_box1_table_head {
        display: flex;
        justify-content: flex-start;
        height: 40px;
        color: #ffffff;
        font-size: 14px;
        display: flex;
        // justify-content: center;
        align-items: center;
        background: linear-gradient(0deg, rgba(0, 222, 255, 0.24));
        box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);

        .right_box1_head_text {
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #26c1d1;
          text-align: center;
          display: flex;
          justify-content: center;
        }

        .right_box1_line_text {
          display: flex;
          justify-content: center;
        }

        .right_box1_num {
          width: 120px;
          text-align: center;
        }
        .right_box1_oper {
          width: 100px;
          display: flex;
          justify-content: center;

          span {
            margin-left: 10px;
            width: 49px;
            height: 20px;
            font-size: 12px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            &:hover {
              filter: brightness(1.1);
            }
          }
          .right_box1_see {
            background-image: url("../../assets/img/home/<USER>");
            background-position: center center;
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }
        }

        .right_box1_index {
          width: 40px;
          text-align: center;
        }

        .right_box1_name {
          width: 100px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          display: block;
          justify-content: flex-start;
          text-align: center;
        }

        .right_box1_time {
          width: 144px;
        }
      }

      .right_box1_table_line {
        height: 36px;
      }

      .right_table_even {
        background: rgba(4, 55, 105, 100);
      }

      .right_table_radix {
        background: rgba(4, 48, 98, 100);
      }
    }
  }

  .right_box2 {
    margin-top: 10px;
    width: 100%;
    flex: 1;
    background-image: url("../../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;
    padding: 15px 0 10px 20px;
    display: flex;
    flex-direction: column;
    background-color: RGBA(5, 31, 89, 0.6);

    .left_box2_content3 {
      font-size: 14px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #00f0e8;
      margin-top: 20px;

      .buffer_line_text {
        font-size: 18px;

        .buffer_unit {
          width: 20px;
          margin-left: 10px;
        }
      }

      .buffer_btn_box {
        width: 100%;
        display: flex;
        justify-content: space-around;
        margin-top: 20px;

        .buffer_btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 32px;
          cursor: pointer;
          border: 1px solid;
          border-radius: 4px;
          font-size: 16px;

          &:hover {
            border: 1px solid #73d897;
            transform: scale(1.05);
          }
        }

        .buffer_cancel {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }

  .home_buffer_box {
    width: 300px;
    box-sizing: border-box;
    padding: 10px;
    position: absolute;
    top: 240px;
    left: -310px;
    background-image: url("../../assets/img/home/<USER>");
    background-position: center center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: RGBA(5, 31, 89, 0.3);
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 10px;
    border-radius: 6px;
    .home_buffer{
      position: relative;
    }
    .buffer_line_text {
      font-size: 18px;

      .buffer_unit {
        width: 20px;
        margin-left: 10px;
      }
    }
    .buffer_btn_box {
      width: 100%;
      display: flex;
      justify-content: space-around;
      margin-top: 20px;

      .buffer_btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100px;
        height: 32px;
        cursor: pointer;
        border: 1px solid;
        border-radius: 4px;
        font-size: 16px;

        &:hover {
          border: 1px solid #73d897;
          transform: scale(1.05);
        }
      }

      .buffer_cancel {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .buffer_line {
      display: flex;
      width: 92%;
      display: flex;
      align-items: center;
      position: absolute;
      top: 48px;
      left: 10px;
      .buffer_line_title {
        width: 60px;
        font-size: 18px;
      }
    }
    .buffer_line_color{
      display: flex;
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      margin-top: 40px;
      .buffer_line_title {
        width: 60px;
        font-size: 18px;
      }
    }
}
</style>
