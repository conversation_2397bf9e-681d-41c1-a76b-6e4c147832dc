import yc from "@/assets/img/geology/yc3.png";
import yc2 from "@/assets/img/geology/yc4.png";
class Earth2 {
  layerManager = {};
  entities = [];

  constructor(_container) {
    this.initEarth(_container);
  }

  /**
   * 初始化加载地球
   * <AUTHOR>
   */
  initEarth(_container) {
    window.geoapp2 = this.geoapp2 = new Geowin3DAPP(
      _container,
      {
        sceneMode: Cesium.SceneMode.SCENE3D,
        shouldAnimate: true,
        infoBox: true,
        orderIndependentTranslucency: false,
        contextOptions: {
          webgl: {
            alpha: true,
          },
        },
      },
      {}
    );

    
    window.viewer2 = this.viewer2 = this.geoapp2.getViewer();
    this.viewer2.terrainProvider = new Geowin3D.GeowinTerrainProvider({
      tilename: "dem",
    });
    this.viewer2.showLogo = false;
    this.camera = this.viewer2.scene.camera;

    this.initEarthConfig();
  }

  /**
   * 初始化模型管理
   * @returns
   */
  initModelManagerTools() {
    this.modelManager = new Geowin3D.GwModel.GwModelManager(this.viewer2);
    return this.modelManager;
  }

  getViewer() {
    return this.viewer2;
  }

  getGeoAPP() {
    return this.geoapp2;
  }
  getEntities() {
    return this.entities;
  }
  /**
   * 初始化球配置
   * <AUTHOR>
   */
  initEarthConfig() {
    // this.viewer2.clock.shouldAnimate = true;
    this.viewer2.scene.globe.depthTestAgainstTerrain = true;
    this.viewer2.scene.backgroundColor = new Cesium.Color(0.0, 0.0, 0.0, 0.0);
    this.viewer2.scene.skyBox.show = false;

    //cesium禁止鼠标左键拖动地图、中键转换视角
    this.viewer2.scene.screenSpaceCameraController.tiltEventTypes = [];
    this.viewer2.scene.screenSpaceCameraController.enableRotate = false;

    // 如果为真，则允许用户旋转相机。如果为假，相机将锁定到当前标题。此标志仅适用于2D和3D。
    this.viewer2.scene.screenSpaceCameraController.enableRotate = false;
    // 如果为true，则允许用户平移地图。如果为假，相机将保持锁定在当前位置。此标志仅适用于2D和Columbus视图模式。
    this.viewer2.scene.screenSpaceCameraController.enableTranslate = false;
    // 如果为真，允许用户放大和缩小。如果为假，相机将锁定到距离椭圆体的当前距离
    this.viewer2.scene.screenSpaceCameraController.enableZoom = false;
    // 如果为真，则允许用户倾斜相机。如果为假，相机将锁定到当前标题。这个标志只适用于3D和哥伦布视图。
    this.viewer2.scene.screenSpaceCameraController.enableTilt = false;

    // var stages = this.viewer2.scene.postProcessStages;
    // this.viewer2.scene.brightness =  this.viewer2.scene.brightness || stages.add(Cesium.PostProcessStageLibrary.createBrightnessStage());
    // this.viewer2.scene.brightness.enabled = true;
    // this.viewer2.scene.brightness.uniforms.brightness = Number(2);
    this.viewer2.scene.light = new Cesium.DirectionalLight({
      //去除时间原因影响模型颜色

      direction: new Cesium.Cartesian3(
        -0.35492591601301104,
        -0.8909182691839401,
        -0.2833588392420772
      ),
      intensity: 5,
    });

    //设置鼠标进去地下
    this.viewer2.scene.screenSpaceCameraController.enableCollisionDetection = false;
    // this.undergroundSpace();
  }

  /**
   * 添加图层，支持3dtiles,wmts url,gltf等，后续所有图层类型均通过该方法进行扩展
   * tileset:3dtileset配置别名
   * tileset_url:3dtileset的服务地址
   * geowintms:二维瓦片配置别名
   * geowintms_url:二维瓦片的url服务地址
   * heatmap:热力图
   * @params {*}
   * {
   *  id:layerid,
   *  type:geowindem/tileset/geowintms,heatmap,
   *  position:
   *  options:
   *  bounds:
   *  tileName:
   * }
   *
   * <AUTHOR>
   */
  loadLayer(treeNode) {
    var id = treeNode.id;
    var layer = null;
    switch (treeNode.type) {
      //暂且未启用
      case "tileset":
        if (this.viewer2.camera._mode == 2) return;
        layer = this.geoapp2.load3DTileset({
          tileName: treeNode.layername,
          position: treeNode.position,
          orientation: treeNode.orientation,
        });
        this.geoapp2.show(layer);
        this.entities.push(layer);
        break;
      case "tileset_url":
        if (this.viewer2.camera._mode == 2) return;
        layer = this.geoapp2.load3DTileset({
          url: treeNode.url,
          position: treeNode.position,
          orientation: treeNode.orientation,
        });
        this.geoapp2.show(layer);
        this.entities.push(layer);
        break;
      case "geowintms": //暂且未启用
        layer = this.geoapp2.addImageLayer({
          url: treeNode.url,
          bounds: treeNode["bounds"],
        });

        if (treeNode.options != null) {
          for (var x in treeNode.options) {
            layer[x] = treeNode.options[x];
          }
        }
        break;

      case "geowindem": //暂且未启用
        layer = this.geoapp2.addDefaultTerrain(treeNode.layername);
        break;
      case "dem":
        this.viewer2.terrainProvider = new Geowin3D.Geowin3DTerrainProvider({
          url: treeNode.url,
        });
        break;

      case "heatmap": //暂且未启用
        layer = this.geoapp2.addHeatMapLayer(
          treeNode.layername,
          treeNode.options,
          treeNode
        );
        break;
      default:
        if (this.viewer2.camera._mode == 2) return;
        layer = this.geoapp2.loadEntity(
          treeNode.layername,
          treeNode.type,
          treeNode.options,
          treeNode
        );
        break;
    }

    this.layerManager[id] = {
      treeNode: treeNode,
      layer: layer,
      features: [],
    };

    if (treeNode.type == "tileset_url" || treeNode.type == "tileset") {
      layer.tileLoad.addEventListener((tile) => {
        let content = tile.content;
        let featuresLength = content.featuresLength;
        for (var i = 0; i < featuresLength; i++) {
          this.layerManager[id].features.push(content.getFeature(i));
        }
      });
    }

    // if (treeNode.setStyle != null)
    //     window[treeNode.setStyle].call(this, layer, treeNode);

    // if (treeNode.position != null)
    //     this.geoapp2.flyToByPosition(treeNode.position);
  }

  /**
   * 移除图层
   * @param {*} treeNode
   * @returns
   */
  removeLayer(treeNode) {
    var id = treeNode.id;
    if (this.layerManager[id] == null) return;
    if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
      this.geoapp2.hide(this.layerManager[id].layer);
      return;
    }
    if (treeNode.type == "geowindem") {
      this.geoapp2.removeTerrain();
      return;
    }
    if (this.layerManager[id].layer)
      this.geoapp2.removeEntity(this.layerManager[id].layer);
    delete this.layerManager[id];
  }

  /**
   * 创建相机信息条
   */
  getCameraInfo() {
    var cameraInfo = Geowin3D.GwCamera.getCurrentCameraInfo(this.camera);
    return {
      lontitude: cameraInfo[0],
      latitude: cameraInfo[1],
      height: cameraInfo[2],
      heading: cameraInfo[3],
      pitch: cameraInfo[4],
      roll: cameraInfo[5],
      cameraInfo: cameraInfo,
    };
  }

  /**
   * 裁剪并填充
   */
  undergroundSpace() {
    let globe = this.viewer2.scene.globe;
    globe.depthTestAgainstTerrain = true;
    var lat = 117.003968;
    var lng = 34.816295;
    // var lat=116.997840;
    // var lng=34.909760;
    let position = Cesium.Cartographic.toCartesian(
      new Cesium.Cartographic.fromDegrees(lat, lng, 0)
    );
    globe.clippingPlanes = new Cesium.ClippingPlaneCollection({
      modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(position),
      planes: [
        new Cesium.Plane(new Cesium.Cartesian3(1, 0.0, 0.0), -7000.0),
        new Cesium.Plane(new Cesium.Cartesian3(-1.0, 0.0, 0.0), -7000.0),
        new Cesium.Plane(new Cesium.Cartesian3(0.0, 1.0, 0.0), -7000.0),
        new Cesium.Plane(new Cesium.Cartesian3(0.0, -1.0, 0.0), -7000.0),
      ],
      edgeWidth: 1.0,
      edgeColor: Cesium.Color.BLACK,
    });

    this.viewer2.entities.add({
      name: "plane",
      position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
      plane: {
        plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, 7000), //指定平面的法线和距离
        dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
        material: new Cesium.ImageMaterialProperty({
          image: yc,
        }),
        fill: true, //是否填充
      },
    });
    this.viewer2.entities.add({
      name: "plane",
      position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
      plane: {
        plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, -7000), //指定平面的法线和距离
        dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
        material: new Cesium.ImageMaterialProperty({
          image: yc,
        }),
        fill: true, //是否填充
      },
    });
    this.viewer2.entities.add({
      name: "plane",
      position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
      plane: {
        plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Y, 7000), //指定平面的法线和距离
        dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
        material: new Cesium.ImageMaterialProperty({
          image: yc,
        }),
        fill: true, //是否填充
      },
    });
    this.viewer2.entities.add({
      name: "plane",
      position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
      plane: {
        plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Y, -7000), //指定平面的法线和距离
        dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
        material: new Cesium.ImageMaterialProperty({
          image: yc,
        }),
        fill: true, //是否填充
      },
    });
    this.viewer2.entities.add({
      name: "plane",
      position: Cesium.Cartesian3.fromDegrees(lat, lng, -3000),
      plane: {
        plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
        dimensions: new Cesium.Cartesian2(14000, 14000), //指定平面的宽度和高度。
        material: new Cesium.ImageMaterialProperty({
          image: yc2,
        }),
        fill: true, //是否填充
      },
    });
  }

  /**
   * 移除图层
   * @param {*} treeNode
   * @returns
   */
  removeLayer(treeNode) {
    var id = treeNode.id;
    if (this.layerManager[id] == null) return;
    if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
      this.geoapp2.hide(this.layerManager[id].layer);
      return;
    }
    if (treeNode.type == "geowindem") {
      this.geoapp2.removeTerrain();
      return;
    }
    if (this.layerManager[id].layer)
      this.geoapp2.removeEntity(this.layerManager[id].layer);
    delete this.layerManager[id];
  }

  /**
   * 设置tooltip
   */
  addToolTip(_options) {
    var bb = this.modelManager.add("TextBox2D", {
      position: [_options.lon, _options.lat, _options.height],
      height: _options.hheight, //广告牌的宽度
      width: _options.hwidth, //广告牌的高度,
      text: "测试文字",
      paddingLeft: 50,
      lineColor: "rgba(255,255,0,1)",
      textColor: "rgba(0,255,0,1)",
      lineWidth: 1,
      fontSize: 16,
      moveTop: 30, //数值不可以为负数
    });
    this.entities.push(bb);
  }

  /**
   * 模型旋转
   * @param {*} primitives
   */
  trackModel(primitives) {
    this.trackManager = new Geowin3D.GwCamera.GwTrackManager(
      this.viewer2.scene
    );
    var boundingSphere = this.getBoundingSphere(primitives);
    this.viewer2.camera.flyToBoundingSphere(boundingSphere, {
      duration: 2,
    });
    this.trackManager.trackByBoundingSphere(boundingSphere);
  }

  /**
   * 退出模型旋转
   */
  exitTrackModel() {
    this.trackManager.cancelTrack();
    this.trackManager = null;
  }

  /**
   * 获取模型包围盒
   * @param {*} primitives
   * @returns
   */
  getBoundingSphere(primitives) {
    var bss = [];
    for (var i = 0; i < primitives.length; i++) {
      if (primitives[i].ready) bss.push(primitives[i].boundingSphere);
    }

    return Geowin3D.BoundingSphere.fromBoundingSpheres(bss);
  }

  modelMovement(height) {
    for (var i = 0; i < this.entities.length; i++) {
      const cartographic = Cesium.Cartographic.fromCartesian(
        this.entities[i].boundingSphere.center
      );
      const surface = Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        0.0
      );
      const offset = Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        height
      );
      const translation = Cesium.Cartesian3.subtract(
        offset,
        surface,
        new Cesium.Cartesian3()
      );
      this.entities[i].modelMatrix = Cesium.Matrix4.fromTranslation(
        translation
      );
    }
  }
}
export default Earth2;
