import { useToolsStore } from "@/store/tools";
import { useHomeStore } from "@/store/home";
import { usePanelStore } from "@/store/panel";
// 工具栏默认
const toolsStore = useToolsStore()
// 面板状态
const panelStore = usePanelStore();
// header 状态
/**
 * headerIndex:header索引
 */
const homeStore = useHomeStore();
export function initStore (headerIndex) {
    toolsStore.toolIndex =1
    panelStore.show = true;
    homeStore.headerIndex = headerIndex;
    let layerBox = document.getElementsByClassName('home_layer')[0]
    layerBox.style.left='430px'
    let toolBox = document.getElementsByClassName('home_legend')[0]
    toolBox.style.right='430px'
    toolBox.style.left=''
}
  
