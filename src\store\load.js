// user模块，类似于 vuex 中的moduel
import {
  defineStore
} from 'pinia'
export const useLoadStore = defineStore({
  // 模块ID
  id: 'LoadStore',
  state: () => {
    return {
      loadData: {},
      isLoadEnd: false
    }
  },
  actions: {
    setYCQ(yichangqu) {
      this.yichangqu = yichangqu;
    },
    setIsLoadEnd() {
      var flag = true
      for (var key in this.loadData) {
        if (this.loadData[key] == false) {
          flag = false
          break
        }
      }

      this.isLoadEnd = flag

    }
  },
})