<template>
  <transition appear name="animate__animated animate__bounce" enter-active-class="animate__fadeInRight"
    leave-active-class="animate__fadeOutRight">
    <div class="dizhi" >
      <img :src="closeImg" class="close_img" @click="closeDiZhi()" />
      <div class="box1_title">
        <img :src="titleImg" class="box_img" />
       <span style='z-index:999'>地质剖面</span> 
      </div>
      <div class="dizhi_box">
        <div id="EarthContainer2" class="map2"></div>
      </div>
      <div class="box1_title">
        <img :src="titleImg" class="box_img" />
        <span style='z-index:999'>掘进规划</span> 
        
      </div>
      <div class="dizhi_box2">
        <div id="EarthContainer2d" class="map3"></div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { onBeforeMount, onMounted, onUnmounted, ref, defineEmits } from "vue";
import { useAmStore } from "@/store/amS";
import titleImg from "@/assets/img/home/<USER>";
import closeImg from "@/assets/img/home/<USER>";
import Earth2 from "@/utils/map/earth2";
import Earth2D from "@/utils/map/earth2D";
import { storeToRefs } from "pinia";
import Animation from "@/utils/map/animation";
import modelMoveHeight from "@/utils/map/modelMoveHeight";
import { useLoadStore } from "@/store/load";
import { getCaimeiModel } from "@/api/home/<USER>";
import { configProviderProps } from "element-plus";
const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);

const amStore = useAmStore();
const props = defineProps({
  dizhiShow: String,
});
//console.log("panelShow", props);
var am2;
var earth2;
var earth2d;
var hangdaoFeature = [];
let viewerTimer;
var viewer2dTimer;
let viewer2
var modelList = []
var domLoad=false
//生命周期
//由于数据更改导致的虚拟 DOM 重新渲染和打补丁，在这之后会调用该钩子。
onUpdated(() => {
  earth2 = new Earth2("EarthContainer2");
  earth2d = new Earth2D("EarthContainer2d");
  domLoad=true
  if (isLoadEnd.value == true) {
    init();
  
  }
  
});
onMounted(() => {
  
});
watch(isLoadEnd, (newVal, oldVal) => {
  if(domLoad){
    init();
    
  }
}, 200);

onUnmounted(() => {
  earth2 = undefined;
  earth2d = undefined;
  if (amStore) {
    amStore.removeAm();
  }
  if (am2) {
    am2.destroy();
  }
  setHangdao(true);
  if (viewerTimer) {
    clearInterval(viewerTimer);
  }
  if (viewer2dTimer) {
    clearInterval(viewer2dTimer);
  }
  removeTimeout()
});
let timer1
let timer2
let timer3
let timer4
let timer5
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  } if (timer2) {
    clearInterval(timer2);
  } if (timer3) {
    clearInterval(timer3);
  } if (timer4) {
    clearInterval(timer4);
  } if (timer5) {
    clearInterval(timer5);
  }
}

const init = () => {
  loadAm()
  timer5 = setTimeout(() => {
    load3dtiles(viewer2)
  }, 2000);

}

const loadAm = () => {
  viewer2 = earth2.getViewer();
  am2 = new Animation(viewer2);
  var modelUrl=getCaimeiModel()
  am2.setModel({ url: modelUrl });
  // am2.setModel({ url: "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1610543271542419456" });
  timer4 = setTimeout(() => {
    amStore.addAm(am2);
    am2.play();
  }, 5000);



  viewer2.scene.globe.show = false;
  
  var viewer2d = earth2d.getViewer()
  var modelPoint=viewer2d.entities.add({
    position: Cesium.Cartesian3.fromDegrees(0, 0, 0),
    point: {
      color:new Cesium.Color(131,62,21,1), //点位颜色
      pixelSize: 10, //像素点大小
    },
  })
  timer3 = setTimeout(() => {
    //不断调整掘进车视角
    viewerTimer = setInterval(() => {
      am2.setViewer(2);
    }, 200);
    //使得2d页面视角一直跟掘进车
    viewer2dTimer = setInterval(() => {
      var position = degreeArrayFromCartesian(am2.getCurrentPosition());
      earth2d.setCamera(position[0], position[1], 1000);
      modelPoint.position=Cesium.Cartesian3.fromDegrees(position[0], position[1], 100)
    }, 200);

  }, 5000);
  
  //   var handler = new Cesium.ScreenSpaceEventHandler(viewer2d.scene.canvas);
  //   handler.setInputAction((event) => {
  //     let cartesian = viewer2d.scene.pickPosition(event.position);
  //     // let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
  //     // let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
  //     // let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
  //     // let alt = cartographic.height; // 高度
  //     // let coordinate = {
  //     //   longitude: Number(lng.toFixed(6)),
  //     //   latitude: Number(lat.toFixed(6)),
  //     //   altitude: Number(alt.toFixed(2))

  //     // };
  // console.log(event)
  //   }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
}

const load3dtiles = (viewer2) => {
  //巷道
  var hdnode = window.layersManager.getNodeByLabel('井巷模型')
  var hd = viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: hdnode.layer,
    })
  );
  hd.tileLoad.addEventListener((tile) => {
    let content = tile.content;
    let featuresLength = content.featuresLength;
    for (var i = 0; i < featuresLength; i++) {
      hangdaoFeature.push(content.getFeature(i));
    }
  });
  hd.readyPromise.then((tiles) => {
    upModel(tiles);
  });
  //工作面
  var gzmnode = window.layersManager.getNodeByLabel('掘进工作面')
  var gzm = viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: gzmnode.layer,
    })
  );
  gzm.readyPromise.then((tiles) => {
    upModel(tiles);
  });
  //第四系
  viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: window.layersManager.getNodeByLabel('第四系').layer,
    })
  ).readyPromise.then((tiles) => {
    upModel(tiles);
    modelList.push(tiles)
  });
  //侏罗
  viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: window.layersManager.getNodeByLabel('上侏罗～下白垩统').layer,
    })
  ).readyPromise.then((tiles) => {
    upModel(tiles);
    modelList.push(tiles)
  })
  //二叠系
  viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: window.layersManager.getNodeByLabel('二叠系地层').layer,
    })
  ).readyPromise.then((tiles) => {
    upModel(tiles);
    modelList.push(tiles)
  })
  //石炭系
  viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: window.layersManager.getNodeByLabel('石炭系地层').layer,
    })
  ).readyPromise.then((tiles) => {
    upModel(tiles);
    modelList.push(tiles)
  })
  //8煤
  viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: window.layersManager.getNodeByLabel('8号煤层').layer,
    })
  ).readyPromise.then((tiles) => {
    upModel(tiles);
    modelList.push(tiles)
  })
  //7煤
  viewer2.scene.primitives.add(
    new Cesium.Cesium3DTileset({
      url: window.layersManager.getNodeByLabel('7号煤层').layer,
    })
  ).readyPromise.then((tiles) => {
    upModel(tiles);
    modelList.push(tiles)
  })

  timer2 = setTimeout(() => {
    am2.setClipModels(modelList, 5, earth2)
  }, 6000);


  setHangdao(false);
};

const upModel = (tiles) => {
  const cartographic2 = Cesium.Cartographic.fromCartesian(
    tiles.boundingSphere.center
  );
  const surface2 = Cesium.Cartesian3.fromRadians(
    cartographic2.longitude,
    cartographic2.latitude,
    0.0
  );
  const offset2 = Cesium.Cartesian3.fromRadians(
    cartographic2.longitude,
    cartographic2.latitude,
    modelMoveHeight
  );
  const translation2 = Cesium.Cartesian3.subtract(
    offset2,
    surface2,
    new Cesium.Cartesian3()
  );
  tiles.modelMatrix = Cesium.Matrix4.fromTranslation(translation2);
};

// 关闭弹窗
const emits = defineEmits(["changeDizhi"]);
const closeDiZhi = () => {
  //console.log("panelShow", !props.dizhiShow);
  emits("changeDizhi", !props.dizhiShow);
};

//设置77巷道透明度为0.2
const setHangdao = (show) => {
  var flag = false;
  var hangdaoFeatures = hangdaoFeature;
  for (var i = 0; i < hangdaoFeatures.length; i++) {
    if (hangdaoFeatures[i].getProperty("batchId") == "349") {
      if (show == false) {
        hangdaoFeatures[i].color = new Cesium.Color(1, 1, 1, 0.2);
      } else {
        hangdaoFeatures[i].color = new Cesium.Color(1, 1, 1, 1);
      }
      flag = true;
    }
  }
  if (flag == false) {
    timer1 = setTimeout(() => {
      setHangdao(show);
    }, 1000);
  }
};


const degreeArrayFromCartesian = (cartesian) => {
  let rad = Geowin3D.Cartographic.fromCartesian(cartesian);
  let lon = Geowin3D.Math.toDegrees(rad.longitude);
  let lat = Geowin3D.Math.toDegrees(rad.latitude);
  return [lon, lat, rad.height];
}
</script>
<style lang="less" scoped>
.dizhi {
  height: 100%;
  position: fixed;
  top: 0px;
  right: 0px;
  width: 600px;
  background-image: url("../../assets/img/home/<USER>");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-color: RGBA(5, 31, 89, 0.8);
  box-sizing: border-box;
  padding-top: 30px;
  padding-left: 30px;
  z-index: 2;

  .dizhi_box {
    width: 550px;
    height: 400px;

    .map2 {
      width: 100%;
      height: 100%;
    }
  }

  .dizhi_box2 {
    width: 550px;
    height: 550px;

    .map3 {
      width: 100%;
      height: 100%;
    }
  }
}

.close_img {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  cursor: pointer;
}

// 图框 标题
.box1_title {
  position: relative;
  height: 42px;
  font-size: 20px;
  font-family: Source Han Sans SC;
  font-weight: 800;
  color: #ffffff;
  line-height: 70px;
  padding-left: 30px;
  padding-bottom: 5px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.box_img {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 42px;
}
</style>
