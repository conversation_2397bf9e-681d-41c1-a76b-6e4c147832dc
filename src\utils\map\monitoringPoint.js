import shuiwenImg from "@/assets/img/icon/shuiwen.png";
import kuangyaImg from "@/assets/img/icon/kuangya.png";
import WasiImg from "@/assets/img/icon/wasi.png";
import {
    ref,toRaw
} from "vue";
import $ from "jquery";
import {
    storeToRefs
} from "pinia";
import {
    useLoadStore
} from "@/store/load";
const loadStore = useLoadStore();
const {
    loadData
} = storeToRefs(loadStore);

class MonitoringPoint {
    constructor() {

        this.load('/pdata/shuiwen.json', '水文监测');
        this.load('/pdata/wasi.json', '瓦斯监测');
        this.load('/pdata/kuangya.json', '矿压监测');

        this.enlargedPointList = []
        this.pointCollect=new Cesium.EntityCollection()
    }

    load(url, type) {
        $.ajax({
            url: url,
            type: "GET",
            dataType: "json",
            success: (data) => {
                var dataList = data;
                // console.log(data)
                this.dataList.value[type] = []
                this.monitoringPointEntitys.value[type] = []
                for (var i = 0; i < dataList.length; i++) {
                    this.dataList.value[type].push({});
                    for (let key in dataList[i]) {
                        this.dataList.value[type][i][key] = dataList[i][key];
                    }

                    this.dataList.value[type][i]["position"] = Cesium.Cartesian3.fromDegrees(
                        Number(this.dataList.value[type][i]["X"]),
                        Number(this.dataList.value[type][i]["Y"]),
                        Number(this.dataList.value[type][i]["Z"]) + 1900
                    );
                    // console.log(Number(this.dataList.value[type][i]["z"]))
                    // this.dataList.value[type][i]["position"] = Cesium.Cartesian3.fromDegrees(
                    //     116.95859752090647 + this.getRndInteger(-10, 10),
                    //     34.843233670817575 + this.getRndInteger(-10, 10),
                    //     1915.3636720451707
                    // );
                }
                // console.log('data',this.dataList.value[type])

                for (let item of this.dataList.value[type]) {
                    let point = this.addPoint(item, type, this.imgs[type])
                    this.monitoringPointEntitys.value[type].push(point)
                }


                this.loadFlag[type] = true
                this.isLoadEnd()
            },
        });
    }

    //添加单个监控点
    addPoint(item, type, img) {
        let position = item.position
        let id = item.monitor_id
        var point = viewer.entities.add({
            position: position,
            idp: id,
            type: type,
            monitoringPoint: 'monitoringPoint',
            billboard: {
                image: img,
                scale: 0.4,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT, // default
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
           
               
            },
        });
        // console.log(point)
        point.show = this.showFlag[type]
        return point
    }

    getRndInteger(min, max) {
        return (Math.floor(Math.random() * (max - min)) + min) / 1000;
    }


    show(label, show = true) {
        if (this.monitoringPointEntitys.value.hasOwnProperty(label)) {
            let entitys = this.monitoringPointEntitys.value[label]
            for (let entity of entitys) {
                entity.show = show
            }
            this.showFlag[label] = show
        }
        setTimeout(() => {
            viewer.scene.forceRender();
        }, 200);
    }

    hideAll() {
        for (let label in this.monitoringPointEntitys.value) {
            this.show(label, false)
        }
        this.recoveryPoint()
        setTimeout(() => {
            viewer.scene.forceRender();
        }, 200);

    }

    removeAll() {
        for (let label in this.monitoringPointEntitys.value) {
            for (let entity of this.monitoringPointEntitys.value[label]) {
                viewer.entities.remove(entity);
            }
        }
        this.recoveryPoint()
        this.monitoringPointEntitys.value = {}
    }

    startClick(btnItem,selectDataId) {
        // console.log('startClick')
        this.handler.setInputAction((movement) => {
            var pick = viewer.scene.pick(movement.position);
            console.log(pick)
            if (pick && pick.id && pick.id._id && pick.id.monitoringPoint == "monitoringPoint") {
                // console.log(pick)
                var node = this.getNodeByTypeId(pick.id.idp, pick.id.type)
                console.log(pick.id.idp)
                selectDataId.value=pick.id.idp
                this.enlargePoint2(pick.id.idp, pick.id.type)
                // this.flyToPoint(pick.id.idp)
                this.currentDatas.value = [...node]
            } else {
                this.recoveryPoint()
                selectDataId.value=-1
                this.currentDatas.value = this.dataList.value[btnItem.value]
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    removeClick() {
        this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    imgs = {
        "水文监测": shuiwenImg,
        "矿压监测": kuangyaImg,
        "瓦斯监测": WasiImg,
    }

    getNodeByTypeId(id, type) {
        // let node;
        let nodeList = []
        for (let node of this.dataList.value[type]) {
            if (node.monitor_id == id) {
                nodeList.push(node)
            }
        }

        return nodeList
    }

    enlargePoint(item, type) {
        var pointId = item.monitor_id
        this.recoveryPoint()
        for (var point of this.monitoringPointEntitys.value[type.value]) {
            if (point.idp == pointId) {
                this.enlargedPointList.push(point)
                point.billboard.scale._value = 0.8
                viewer.flyTo(toRaw(point))
            }
        }
        setTimeout(() => {
            viewer.scene.forceRender();
        }, 200);
    }

    enlargePoint2(pointID, type) {
        var pointId = pointID
        this.recoveryPoint()
        for (var point of this.monitoringPointEntitys.value[type]) {
            if (point.idp == pointId) {
                this.enlargedPointList.push(point)
                point.billboard.scale._value = 0.8
            }
        }
        setTimeout(() => {
            viewer.scene.forceRender();
        }, 200);
    }

    recoveryPoint() {
        if (this.enlargedPointList.length > 0) {
            for (var point of this.enlargedPointList) {
                point.billboard.scale._value = 0.4
            }
        }
        setTimeout(() => {
            viewer.scene.forceRender();
        }, 200);
        this.enlargedPointList = []
    }

    setCurrentDatas(list) {
        this.currentDatas = list
    }

    setCurrentList(btnItem) {
        this.currentDatas.value = this.dataList.value[btnItem.value]
    }

    flyToPoints(btnItem) {
        if(btnItem.value=="微震监测"){
            return;
        }
        var pointList = this.monitoringPointEntitys.value[btnItem.value]
        this.pointCollect.removeAll()
        for (var point of pointList) {
            this.pointCollect.add(toRaw(point))
        }
        viewer.flyTo(this.pointCollect ,{
            duration: 2
        })
    }

    

    handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)

    dataList = ref({});

    monitoringPointEntitys = ref({})

    showFlag = {
        "矿压监测": false,
        "水文监测": false,
        "瓦斯监测": false,
        "微震监测": false,
        "电法监测": false,
        "地音监测": false,
        "音频电透视": false,
        "槽波": false,
    }



    loadFlag = {
        "矿压监测": false,
        "水文监测": false,
        "瓦斯监测": false,

    }

    isLoadEnd() {
        var flag = true
        for (var key in this.loadFlag) {
            if (this.loadFlag[key] == false) {
                flag = false
                break
            }
        }

        if (flag) {
            loadData.value['monitoringPoint'] = true
            loadStore.setIsLoadEnd()

        }
    }
    // currentData=ref({id: 1754479, dsId: 139, state: '正常', createTime: '2022-11-25 14:45:56', sensorCode: '610824057001'})
    // currentDatas=ref([12,32,43])
}

export default MonitoringPoint;