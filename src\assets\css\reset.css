/* http://meyerweb.com/eric/tools/css/reset/ */
/* v1.0 | 20080212 */

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	font-size: 100%;
	vertical-align: baseline;
	background: transparent;
	box-sizing: border-box;
}
html{
	height: 100%;
	box-sizing: border-box;
	/* overflow: hidden; */
}
body {
	height: 100%;
	line-height: 1;
	box-sizing: border-box;
	width: 100%;
}
#app{
	height: 100%;
	width: 100%;
}
.main{
	height: 100%;
	width: 100%;
	background-image: url("../img/home/<USER>");
    background-position:center center;
    background-size:100% 100%;
    background-repeat: no-repeat ;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}

/* remember to define focus styles! */
:focus {
	outline: 0;
}

/* remember to highlight inserts somehow! */
ins {
	text-decoration: none;
}
del {
	text-decoration: line-through;
}

/* tables still need 'cellspacing="0"' in the markup */
table {
	border-collapse: collapse;
	border-spacing: 0;
}
::-webkit-scrollbar
{
    width:1px;
    height:4px;
    background-color:#0DD6E3;
	box-sizing: border-box;
}
/*定义滚动条轨道
 内阴影+圆角*/
::-webkit-scrollbar-track
{
    -webkit-box-shadow:inset 0 0 1px rgba(0,0,0,0.3);
    /* border-radius:10%; */
    background-color:#0650A6;
	box-sizing: border-box;
}
/*定义滑块
 内阴影+圆角*/
::-webkit-scrollbar-thumb
{
    border-radius:10px;
    -webkit-box-shadow:inset 0 0 1px rgba(0,0,0,.3);
    background-color:#0DD6E3;
	box-sizing: border-box;
}

.cesium-infoBox {
	top: 700px ;
	left: 450px ;
	background-color: RGBA(5, 31, 89, .8);
  }
  .cesium-infoBox-camera{
	display: none !important;
  }
  .cesium-infoBox .cesium-infoBox-title{
	background-color: RGBA(5, 31, 89, .6);
	cursor: pointer;
  }

  /* 三维视角 弹窗样式 */
  .infoPanel{
	/* background: rgba(42, 42, 42, 0.8); */
	background-color: RGBA(5, 31, 89, 0.9);
    padding: 4px 40px 4px 20px;
    border: 1px solid rgb(68, 68, 68);
    border-radius: 4px;
    position: absolute;
    top: 90px;
    right: 10px;
    line-height: 30px;
    color: #00f0e8;
    z-index: 9;
    width: 216px;
	border-radius: 6px;
	border: 0px;
	box-shadow: 2px 2px 2px #051f59;
	box-sizing: border-box;

  }
  .infoPanel tbody{
	width: 210px;
    padding: 10px 0 10px 10px;
    display: block;
    box-sizing: border-box;
	border-radius: 6px;
  }
  .infoPanel td{
	font-size: 14px;
  }
  .open-tip{
	font-size: 14px;
  }
