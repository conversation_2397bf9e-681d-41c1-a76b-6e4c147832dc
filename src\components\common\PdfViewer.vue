<template>
  <div class="pdf-viewer-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="pdf-viewer-modal" @click.stop>
      <div class="pdf-viewer-header">
        <div class="pdf-viewer-title">
          <img :src="titleImg" class="title-bg" />
          <span>{{ title }}</span>
        </div>
        <div class="pdf-viewer-controls">
          <button class="control-btn download-btn" @click="handleDownload" title="下载">
            <i class="download-icon">⬇</i>
          </button>
          <button class="control-btn close-btn" @click="handleClose" title="关闭">
            <i class="close-icon">✕</i>
          </button>
        </div>
      </div>
      <div class="pdf-viewer-content">
        <iframe 
          :src="pdfUrl" 
          class="pdf-iframe"
          frameborder="0"
          @load="handleIframeLoad"
          @error="handleIframeError"
        ></iframe>
        <div v-if="loading" class="pdf-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载PDF文档...</div>
        </div>
        <div v-if="error" class="pdf-error">
          <div class="error-icon">⚠</div>
          <div class="error-text">PDF文档加载失败</div>
          <button class="retry-btn" @click="handleRetry">重试</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import titleImg from "@/assets/img/home/<USER>"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pdfPath: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: 'PDF文档'
  },
  fileName: {
    type: String,
    default: 'document.pdf'
  }
})

const emit = defineEmits(['close', 'download'])

const loading = ref(true)
const error = ref(false)

// 计算PDF URL
const pdfUrl = computed(() => {
  if (!props.pdfPath) return ''
  // 如果是相对路径，转换为绝对路径
  if (props.pdfPath.startsWith('./') || props.pdfPath.startsWith('/')) {
    return window.location.origin + props.pdfPath.replace('./', '/')
  }
  return props.pdfPath
})

// 监听visible变化，重置状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loading.value = true
    error.value = false
  }
})

const handleOverlayClick = () => {
  handleClose()
}

const handleClose = () => {
  emit('close')
}

const handleDownload = () => {
  try {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = pdfUrl.value
    link.download = props.fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    emit('download', {
      url: pdfUrl.value,
      fileName: props.fileName
    })
  } catch (error) {
    console.error('下载失败:', error)
  }
}

const handleIframeLoad = () => {
  loading.value = false
  error.value = false
}

const handleIframeError = () => {
  loading.value = false
  error.value = true
}

const handleRetry = () => {
  loading.value = true
  error.value = false
  // 重新加载iframe
  const iframe = document.querySelector('.pdf-iframe')
  if (iframe) {
    iframe.src = iframe.src
  }
}
</script>

<style lang="less" scoped>
.pdf-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(2px);
}

.pdf-viewer-modal {
  width: 90%;
  height: 90%;
  max-width: 1200px;
  max-height: 800px;
  background: rgba(11, 24, 36, 0.95);
  border: 1px solid rgba(51, 214, 255, 0.3);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.pdf-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: rgba(4, 48, 98, 0.8);
  border-bottom: 1px solid rgba(51, 214, 255, 0.3);
  border-radius: 8px 8px 0 0;
}

.pdf-viewer-title {
  position: relative;
  display: flex;
  align-items: center;
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  
  .title-bg {
    position: absolute;
    top: -5px;
    left: -10px;
    height: 35px;
    width: 200px;
    z-index: 0;
  }
  
  span {
    position: relative;
    z-index: 1;
    padding-left: 15px;
  }
}

.pdf-viewer-controls {
  display: flex;
  gap: 10px;
}

.control-btn {
  width: 32px;
  height: 32px;
  background: rgba(51, 214, 255, 0.2);
  border: 1px solid rgba(51, 214, 255, 0.5);
  border-radius: 4px;
  color: #33d6ff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(51, 214, 255, 0.3);
    border-color: #33d6ff;
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.download-btn {
  .download-icon {
    font-size: 16px;
    font-weight: bold;
  }
}

.close-btn {
  .close-icon {
    font-size: 14px;
    font-weight: bold;
  }
}

.pdf-viewer-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #ffffff;
}

.pdf-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(11, 24, 36, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #33d6ff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(51, 214, 255, 0.3);
  border-top: 3px solid #33d6ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 16px;
  color: #ffffff;
}

.pdf-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(11, 24, 36, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #ff6b6b;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-text {
  font-size: 18px;
  margin-bottom: 20px;
  color: #ffffff;
}

.retry-btn {
  padding: 8px 16px;
  background: rgba(51, 214, 255, 0.2);
  border: 1px solid #33d6ff;
  border-radius: 4px;
  color: #33d6ff;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(51, 214, 255, 0.3);
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
