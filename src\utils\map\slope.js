/**
 * 
 */
class Slope{
    params = {}
    constructor(viewer){
        this.viewer = viewer;
        this.slp = new Geowin3D.GwTools.GwSlope(this.viewer);
    }

    setParams(options){
        this.params.interpolations = parseInt(options.interpolations);
        this.params.samples = parseInt(options.samples);
    }

    start(){
        this.slp.resetOptions();
        let option = {
            id: 'slefSlopeTooltip',
            interpolations: this.params.interpolations, // 多边形包围矩形短边插值数
            samples: this.params.samples, // 单元格采样点数
            arrowWidth: 8, // 坡向箭头宽度
            arrowColor: Geowin3D.Color.YELLOW.withAlpha(0.8), // 坡向箭头颜色
            mapRadius: 80, // 坡面填充色半径
            mapGradient: { // 坡面填充渐变色
            '0.25': 'rgb(0, 255, 255)',
            '0.55': 'rgb(0, 255, 0)',
            '0.85': 'rgb(255, 255, 0)',
            '1': 'rgb(255, 0, 0)'
            }
        };
        this.slp.showArrows = true;
        this.slp.showHeatmap = true;
        this.slp.showLegend = true;
        this.slp.set(option);
        this.slp.start();
    }
    
    cancle(){
        this.slp.destroy();
    }
}

export default Slope