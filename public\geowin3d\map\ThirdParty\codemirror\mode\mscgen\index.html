<!doctype html>

<title>CodeMirror: MscGen mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="mscgen.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>
  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">MscGen</a>
  </ul>
</div>

<article>
<h2>MscGen mode</h2>

<div><textarea id="mscgen-code">
# Sample mscgen program
# See http://www.mcternan.me.uk/mscgen or
# https://sverweij.github.io/mscgen_js for more samples
msc {
  # options
  hscale="1.2";

  # entities/ lifelines
  a [label="Entity A"],
  b [label="Entity B", linecolor="red", arclinecolor="red", textbgcolor="pink"],
  c [label="Entity C"];

  # arcs/ messages
  a => c [label="doSomething(args)"];
  b => c [label="doSomething(args)"];
  c >> * [label="everyone asked me", arcskip="1"];
  c =>> c [label="doing something"];
  c -x * [label="report back", arcskip="1"];
  |||;
  --- [label="shows's over, however ..."];
  b => a [label="did you see c doing something?"];
  a -> b [label="nope"];
  b :> a [label="shall we ask again?"];
  a => b [label="naah"];
  ...;
}
</textarea></div>

<h2>Xù mode</h2>

<div><textarea id="xu-code">
# Xù - expansions to MscGen to support inline expressions
#      https://github.com/sverweij/mscgen_js/blob/master/wikum/xu.md
# More samples: https://sverweij.github.io/mscgen_js
xu {
  hscale="0.8",
  width="700";

  a,
  b [label="change store"],
  c,
  d [label="necro queue"],
  e [label="natalis queue"],
  f;

  a =>> b [label="get change list()"];
  a alt f [label="changes found"] { /* alt is a xu specific keyword*/
    b >> a [label="list of changes"];
    a =>> c [label="cull old stuff (list of changes)"];
    b loop e [label="for each change"] { // loop is xu specific as well...
      /*
       * Interesting stuff happens.
       */
      c =>> b [label="get change()"];
      b >> c [label="change"];
      c alt e [label="change too old"] {
        c =>> d [label="queue(change)"];
        --- [label="change newer than latest run"];
        c =>> e [label="queue(change)"];
        --- [label="all other cases"];
        ||| [label="leave well alone"];
      };
    };

    c >> a [label="done
    processing"];

    /* shucks! nothing found ...*/
    --- [label="nothing found"];
    b >> a [label="nothing"];
    a note a [label="silent exit"];
  };
}
</textarea></div>

<h2>MsGenny mode</h2>
<div><textarea id="msgenny-code">
# MsGenny - simplified version of MscGen / Xù
#           https://github.com/sverweij/mscgen_js/blob/master/wikum/msgenny.md
# More samples: https://sverweij.github.io/mscgen_js
a -> b   : a -> b  (signal);
a => b   : a => b  (method);
b >> a   : b >> a  (return value);
a =>> b  : a =>> b (callback);
a -x b   : a -x b  (lost);
a :> b   : a :> b  (emphasis);
a .. b   : a .. b  (dotted);
a -- b   : "a -- b straight line";
a note a : a note a\n(note),
b box b  : b box b\n(action);
a rbox a : a rbox a\n(reference),
b abox b : b abox b\n(state/ condition);
|||      : ||| (empty row);
...      : ... (omitted row);
---      : --- (comment);
</textarea></div>

    <p>
      Simple mode for highlighting MscGen and two derived sequence
      chart languages.
    </p>

    <script>
      var mscgenEditor = CodeMirror.fromTextArea(document.getElementById("mscgen-code"), {
        lineNumbers: true,
        mode: "text/x-mscgen",
      });
      var xuEditor = CodeMirror.fromTextArea(document.getElementById("xu-code"), {
        lineNumbers: true,
        mode: "text/x-xu",
      });
      var msgennyEditor = CodeMirror.fromTextArea(document.getElementById("msgenny-code"), {
        lineNumbers: true,
        mode: "text/x-msgenny",
      });
    </script>

    <p><strong>MIME types defined:</strong>
      <code>text/x-mscgen</code>
      <code>text/x-xu</code>
      <code>text/x-msgenny</code>
    </p>

</article>
