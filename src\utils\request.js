import $axios from '@/utils/axios';
// import server from './server.config';
import Qs from 'qs';
import {
  ElMessage
} from 'element-plus';
import router from '@/router/index';
/**
 * code为非1000是抛错 可结合自己业务进行修改
 * 
 * **/
const dealResponse = function (response, resolve) {
  let res = response.data;
  if (res.code|| res.code ==0) {
    //console.log(res.data,"res.data")
    if (res.code === 0 || res.code === 200) {
      resolve(res.data);
    } else if (res.code === 401) {
      ElMessage({
        type: 'error',
        message: `['尚未登录授权或授权过期，请登录']`
      });
      router.replace({
        path: '/login'
      });
      resolve(res);
    } else {
      ElMessage({
        type: 'error',
        message: `错误编号：${res.code},MSG:${res.msg}]`
      });
      resolve(res.data);
    }
  } else {
    // 二进制无res.code
    resolve(response);
  }
};

// 封装GET方法
const get = function (url, ...args) {
  return new Promise((resolve, reject) => {
    $axios.get(url, Object.assign({}, ...args)).then(res => {
      dealResponse(res, resolve);
    }).catch(err => {
      reject(err);
    });
  });
};

// 封装POST方法
const post = function (url, data = {}, ...args) {
  return new Promise((resolve, reject) => {
    $axios.post(url, data, ...args).then(res => {
      dealResponse(res, resolve);
    }).catch(err => {
      reject(err);
    });
  });
};

// 封装get请求方法,自动添加参数
const addArrayParamsByGet = function (url, [params, ...args]) {
  let search = Qs.stringify(params, {
    arrayFormat: 'repeat'
  });
  url = url + '?' + search;
  return new Promise((resolve, reject) => {
    $axios.get(url, ...args).then(res => {
      dealResponse(res, resolve);
    }).catch(err => {
      reject(err);
    });
  });
};

// 封装post请求方法,自动添加参数
const addFormDataParamsByPost = (url, [params, ...args]) => {
  let search = Qs.stringify(params, {
    arrayFormat: 'repeat'
  });
  url = url + '?' + search;
  return new Promise((resolve, reject) => {
    $axios.post(url, ...args).then(res => {
      dealResponse(res, resolve);
    }).catch(err => {
      reject(err);
    });
  });
};

export default {
  get,
  post,
  addArrayParamsByGet,
  addFormDataParamsByPost
};
