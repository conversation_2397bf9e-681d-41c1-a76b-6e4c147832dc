<!doctype html>

<title>CodeMirror: N-Triples mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="ntriples.js"></script>
<style type="text/css">
      .CodeMirror {
        border: 1px solid #eee;
        height: auto;
      }
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">N-Triples/N-Quads</a>
  </ul>
</div>

<article>
  <h2><a href="https://www.w3.org/TR/n-triples/">N-Triples</a> mode</h2>
  <p>The N-Triples mode also works well with on
    <a href="https://www.w3.org/TR/n-quads/">N-Quad</a> documents.
  </p>
<form>
<textarea id="ntriples" name="ntriples">    
<http://Sub1>     <http://pred1>     <http://obj> .
<http://Sub2>     <http://pred2#an2> "literal 1" .
<http://Sub3#an3> <http://pred3>     _:bnode3 .
_:bnode4          <http://pred4>     "literal 2"@lang .
_:bnode5          <http://pred5>     "literal 3"^^<http://type> .
</textarea>
</form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("ntriples"), {});
    </script>
    <p><strong>MIME types defined:</strong> <code>application/n-triples</code>.</p>

    <hr />
    <p><a href="https://www.w3.org/TR/n-quads/">N-Quads</a> add a fourth
    element to the statement to track which graph the statement is from.
    Otherwise, it's identical to N-Triples.</p>
    <form>
    <textarea id="nquads" name="nquads">

    <http://Sub1>     <http://pred1>     <http://obj>   <http://graph3> .
    <http://Sub2>     <http://pred2#an2> "literal 1"    <http://graph2> .
    <http://Sub3#an3> <http://pred3>     _:bnode3     <http://graph2> .
    _:bnode4          <http://pred4>     "literal 2"@lang     <http://graph2> .
    # if a graph labe
    _:bnode5          <http://pred5>     "literal 3"^^<http://type> .
    </textarea>
    </form>

    <script>
      var nquads_editor = CodeMirror.fromTextArea(document.getElementById("nquads"), {});
    </script>
    <p><strong>MIME types defined:</strong> <code>application/n-quads</code>.</p>
  </article>
