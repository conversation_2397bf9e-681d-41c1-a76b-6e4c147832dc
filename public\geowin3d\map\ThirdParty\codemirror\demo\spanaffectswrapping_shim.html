<!doctype html>

<title>CodeMirror: Automatically derive odd wrapping behavior for your browser</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Automatically derive odd wrapping behavior for your browser</a>
  </ul>
</div>

<article>
<h2>Automatically derive odd wrapping behavior for your browser</h2>


    <p>This is a hack to automatically derive
    a <code>spanAffectsWrapping</code> regexp for a browser. See the
    comments above that variable
    in <a href="../lib/codemirror.js"><code>lib/codemirror.js</code></a>
    for some more details.</p>

    <div style="white-space: pre-wrap; width: 50px;" id="area"></div>
    <pre id="output"></pre>

    <script id="script">
      var a = document.getElementById("area"), bad = Object.create(null);
      var chars = "a~`!@#$%^&*()-_=+}{[]\\|'\"/?.>,<:;", l = chars.length;
      for (var x = 0; x < l; ++x) for (var y = 0; y < l; ++y) {
        var s1 = "foooo" + chars.charAt(x), s2 = chars.charAt(y) + "br";
        a.appendChild(document.createTextNode(s1 + s2));
        var h1 = a.offsetHeight;
        a.innerHTML = "";
        a.appendChild(document.createElement("span")).appendChild(document.createTextNode(s1));
        a.appendChild(document.createElement("span")).appendChild(document.createTextNode(s2));
        if (a.offsetHeight != h1)
          bad[chars.charAt(x)] = (bad[chars.charAt(x)] || "") + chars.charAt(y);
        a.innerHTML = "";
      }

      var re = "";
      function toREElt(str) {
        if (str.length > 1) {
          var invert = false;
          if (str.length > chars.length * .6) {
            invert = true;
            var newStr = "";
            for (var i = 0; i < l; ++i) if (str.indexOf(chars.charAt(i)) == -1) newStr += chars.charAt(i);
            str = newStr;
          }
          str = str.replace(/[\-\.\]\"\'\\\/\^a]/g, function(orig) { return orig == "a" ? "\\w" : "\\" + orig; });
          return "[" + (invert ? "^" : "") + str + "]";
        } else if (str == "a") {
          return "\\w";
        } else if (/[?$*()+{}[\]\.|/\'\"]/.test(str)) {
          return "\\" + str;
        } else {
          return str;
        }
      }

      var newRE = "";
      for (;;) {
        var left = null;
        for (var left in bad) break;
        if (left == null) break;
        var right = bad[left];
        delete bad[left];
        for (var other in bad) if (bad[other] == right) {
          left += other;
          delete bad[other];
        }
        newRE += (newRE ? "|" : "") + toREElt(left) + toREElt(right);
      }

      document.getElementById("output").appendChild(document.createTextNode("Your regexp is: " + (newRE || "^$")));
    </script>
  </article>
