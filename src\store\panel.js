// user模块，类似于 vuex 中的moduel
import { defineStore } from 'pinia'
export const usePanelStore = defineStore({
  // 模块ID
  id: 'panelStore',
  state: () => {
    return {
      show: true,
      layerActive: true,
      toolShow: true
    }
  },
  actions: {
    setShow(show) {
      //console.log(show, "home")
      this.show = show;
    },
    setLayerActive(show) {
      this.layerActive = show;
    },
    setToolShow(show) {
      this.toolShow = show;
    }
  },
}
)