<template>
  <div class="tunnel">
    <transition
      appear
      name="animate__animated animate__pulse"
      enter-active-class="animate__fadeInLeft"
      leave-active-class="animate__fadeOutLeft"
    >
      <div class="left" v-show="show">
        <div class="left_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            掘进基础信息
          </div>
        </div>
        <div class="left_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            掘进视角
          </div>
        </div>
      </div>
    </transition>
    <transition
      appear
      name="animate__animated animate__bounce"
      enter-active-class="animate__fadeInRight"
      leave-active-class="animate__fadeOutRight"
    >
      <div class="right" v-show="show">
        <div class="right_box1">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            掘进探放水(或瓦斯监测) 
          </div>
        </div>
        <div class="right_box2">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            其它监测方式  
          </div>
        </div>
        <div class="right_box3">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            预测预报  
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { useHomeStore } from "@/store/home";
import { usePanelStore } from "@/store/panel";
import { storeToRefs } from "pinia";
import titleImg from "@/assets/img/home/<USER>";
import lineImg from "@/assets/img/home/<USER>";
import leftImg1 from "@/assets/img/home/<USER>";
import leftImg2 from "@/assets/img/home/<USER>";
import leftImg3 from "@/assets/img/home/<USER>";
import resImg from "@/assets/img/home/<USER>";
import upImg from "@/assets/img/home/<USER>";
import downImg from "@/assets/img/home/<USER>";

import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef } from "vue";
const panelStore = usePanelStore();
const homeStore = useHomeStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
//生命周期
onMounted(() => {
  // 初始化 左右两侧 确保展开
  panelStore.show = true;
  homeStore.headerIndex = 2;
});
const defaultProps = {
  children: "children",
  label: "label",
};

const handleOpen = () => {
  // 打开PDF
 // console.log("点击了");
};
// 下载
const handleDownload = () => {};

</script>
<style lang="less" scoped>
.tunnel {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  // overflow: hidden;
  .left {
    z-index: 1;
    width: 196px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 21px;
    // overflow-y: scroll;
    // overflow-x: hidden;
    .left_box1 {
      width: 100%;
      height: 98px;
      // flex: 1;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      // background-size:cover;
      background-size:100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 16px 0px 24px 24px;
      display: flex;
      flex-direction: column;
      background-color:RGBA(5, 31, 89, .6);
    }
    .left_box2 {
      margin-top: 17px;
      width: 100%;
      height: 326px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size:100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 23px 0 0px 18px;
      display: flex;
      flex-direction: column;
      background-color:RGBA(5, 31, 89, .6);
    }
  }
  .right {
    z-index: 1;
    width: 100%;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .right_box1 {
      width: 100%;
      height: 123px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size:100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 14px 22px 0 22px;
      display: flex;
      flex-direction: column;
      background-color:RGBA(5, 31, 89, .6);
    }
    .right_box2 {
      margin-top: 5px;
      width: 100%;
      height: 123px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size:100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 15px 0 10px 20px;
      display: flex;
      flex-direction: column;
      background-color:RGBA(5, 31, 89, .6);
    }
    .right_box3{
      margin-top: 5px;
      width: 100%;
      height: 222px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size:100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 15px 0 10px 20px;
      display: flex;
      flex-direction: column;
      background-color:RGBA(5, 31, 89, .6);
    }
  }
  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }
 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}

</style>
