import { createApp } from 'vue'
import App from './App.vue'
import router from "./router";
import "animate.css/animate.min.css";
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'amfe-flexible/index.js'
import '@/assets/styles/scrollbar.css'
import store from "./store";
import vue3SeamlessScroll from "vue3-seamless-scroll";
import * as ELIcons from '@element-plus/icons-vue'
import directives from '@/utils/directives/index.js'
import errorHandler from '@/utils/errorHandler.js'
// 导入真正的Cesium框架
import 'cesium/Build/Cesium/Widgets/widgets.css'
import * as Cesium from 'cesium'
import globalCesiumManager from '@/utils/cesium/globalManager.js'
import { waitForContainerAndInit } from '@/utils/compatibility/geowin3dCompat.js'
import { initGlobalCompatibility } from '@/utils/compatibility/globalCheck.js'

// 设置全局Cesium变量
window.Cesium = Cesium

// 配置Cesium以优化内存使用
if (typeof window !== 'undefined') {
  // 设置Cesium的基础URL
  window.CESIUM_BASE_URL = '/node_modules/cesium/Build/Cesium/'

  // 优化WebAssembly设置
  Cesium.Ion.defaultAccessToken = 'your-ion-access-token-here' // 如果有的话

  // 设置请求调度器以限制并发请求
  Cesium.RequestScheduler.maximumRequests = 10
  Cesium.RequestScheduler.maximumRequestsPerServer = 6
}

// 立即初始化全局兼容性检查
initGlobalCompatibility()

// 初始化兼容性层
waitForContainerAndInit().then(() => {
  console.log('geowin3d兼容性层初始化完成')
}).catch(error => {
  console.error('兼容性层初始化失败:', error)
})

const app = createApp(App)

// 设置全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info);
  errorHandler.reportError('vue', err, {
    componentInfo: info,
    componentName: vm?.$options?.name || 'unknown'
  });
};

// 设置全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Vue警告:', msg, trace);
};

// 注册图标组件
for (let iconName in ELIcons) {
	app.component(iconName, ELIcons[iconName])
}

// 全局属性 - 错误处理器
app.config.globalProperties.$errorHandler = errorHandler;
// 全局属性 - Cesium管理器
app.config.globalProperties.$cesium = globalCesiumManager;

// 挂载应用
app.use(vue3SeamlessScroll).use(router).use(store).use(ElementPlus).use(directives).mount('#app')
