import { createApp } from 'vue'
import App from './App.vue'
import router from "./router";
import "animate.css/animate.min.css";
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'amfe-flexible/index.js'
import '@/assets/styles/scrollbar.css'
import store from "./store";
import vue3SeamlessScroll from "vue3-seamless-scroll";
import * as ELIcons from '@element-plus/icons-vue'
import directives from '@/utils/directives/index.js'
import errorHandler from '@/utils/errorHandler.js'
// 暂时禁用Cesium以解决内存问题
// import 'cesium/Build/Cesium/Widgets/widgets.css'
// import * as Cesium from 'cesium'
// import globalCesiumManager from '@/utils/cesium/globalManager.js'
// import { waitForContainerAndInit } from '@/utils/compatibility/geowin3dCompat.js'
import { initGlobalCompatibility } from '@/utils/compatibility/globalCheck.js'

// 创建模拟的Cesium对象以防止错误
window.Cesium = {
  Viewer: class MockViewer {
    constructor(container, options = {}) {
      this.container = container;
      this.scene = {
        camera: { heading: 0, pitch: 0, roll: 0 },
        forceRender: () => {},
        globe: { ellipsoid: {} },
        pick: () => null,
        pickFromRay: () => null,
        drillPickFromRay: () => [],
        primitives: {
          add: () => {},
          remove: () => {}
        },
        canvas: document.createElement('canvas'),
        postProcessStages: {
          add: () => ({ selected: [] })
        }
      };
      this.camera = {
        position: { x: 0, y: 0, z: 0 },
        heading: 0,
        pitch: 0,
        roll: 0,
        setView: () => {},
        pickEllipsoid: () => null
      };
      this.entities = new window.Cesium.EntityCollection();
      this.flyTo = () => Promise.resolve();
      this.zoomTo = () => Promise.resolve();
      this.resolutionScale = 1.0;
    }
  },
  EntityCollection: class MockEntityCollection {
    constructor() {
      this._entities = [];
    }
    add(entity) {
      const mockEntity = {
        id: 'mock-entity-' + Math.random(),
        show: true,
        ...entity
      };
      this._entities.push(mockEntity);
      console.log('模拟EntityCollection.add调用');
      return mockEntity;
    }
    remove(entity) {
      console.log('模拟EntityCollection.remove调用');
      return true;
    }
    removeAll() {
      this._entities = [];
      console.log('模拟EntityCollection.removeAll调用');
    }
    getById(id) {
      return this._entities.find(e => e.id === id);
    }
    get length() {
      return this._entities.length;
    }
  },
  PrimitiveCollection: class MockPrimitiveCollection {
    constructor() {
      this._primitives = [];
    }
    add(primitive) {
      this._primitives.push(primitive);
      console.log('模拟PrimitiveCollection.add调用');
      return primitive;
    }
    remove(primitive) {
      const index = this._primitives.indexOf(primitive);
      if (index > -1) {
        this._primitives.splice(index, 1);
      }
      console.log('模拟PrimitiveCollection.remove调用');
      return true;
    }
    removeAll() {
      this._primitives = [];
      console.log('模拟PrimitiveCollection.removeAll调用');
    }
    get length() {
      return this._primitives.length;
    }
  },
  Cartesian3: {
    fromDegrees: (lon, lat, height) => ({ x: lon, y: lat, z: height || 0 }),
    distance: (p1, p2) => 1000,
    midpoint: (p1, p2, result) => ({ x: (p1.x + p2.x) / 2, y: (p1.y + p2.y) / 2, z: (p1.z + p2.z) / 2 }),
    subtract: (left, right, result) => ({ x: left.x - right.x, y: left.y - right.y, z: left.z - right.z }),
    dot: (left, right) => left.x * right.x + left.y * right.y + left.z * right.z,
    magnitude: (cartesian) => Math.sqrt(cartesian.x * cartesian.x + cartesian.y * cartesian.y + cartesian.z * cartesian.z)
  },
  Cartographic: {
    fromCartesian: (cartesian) => ({ longitude: 0, latitude: 0, height: 0 })
  },
  Math: {
    toDegrees: (radians) => radians * 180 / Math.PI,
    toRadians: (degrees) => degrees * Math.PI / 180,
    clamp: (value, min, max) => Math.min(Math.max(value, min), max)
  },
  Color: {
    WHITE: { r: 1, g: 1, b: 1, a: 1 },
    YELLOW: { r: 1, g: 1, b: 0, a: 1 },
    BLACK: { r: 0, g: 0, b: 0, a: 1 },
    fromCssColorString: (color) => ({ r: 1, g: 1, b: 1, a: 1 }),
    withAlpha: function(alpha) { return { ...this, a: alpha }; }
  },
  ScreenSpaceEventHandler: class MockHandler {
    constructor() {}
    setInputAction() {}
    removeInputAction() {}
    destroy() {}
  },
  ScreenSpaceEventType: {
    LEFT_CLICK: 'LEFT_CLICK',
    RIGHT_CLICK: 'RIGHT_CLICK',
    LEFT_DOUBLE_CLICK: 'LEFT_DOUBLE_CLICK'
  },
  Entity: class MockEntity {
    constructor(options = {}) {
      this.id = options.id || 'mock-entity-' + Math.random();
      this.show = options.show !== undefined ? options.show : true;
      Object.assign(this, options);
    }
  },
  EllipsoidTerrainProvider: class MockTerrainProvider {},
  HorizontalOrigin: { LEFT: 'LEFT', CENTER: 'CENTER', RIGHT: 'RIGHT' },
  VerticalOrigin: { TOP: 'TOP', CENTER: 'CENTER', BOTTOM: 'BOTTOM' },
  HeightReference: { CLAMP_TO_GROUND: 'CLAMP_TO_GROUND' },
  LabelStyle: { FILL_AND_OUTLINE: 'FILL_AND_OUTLINE' },
  Cartesian2: class MockCartesian2 { constructor(x, y) { this.x = x; this.y = y; } },
  PostProcessStage: class MockPostProcessStage {
    constructor(options = {}) {
      this.fragmentShader = options.fragmentShader;
      this.uniforms = options.uniforms;
      this.enabled = true;
      this.selected = [];
    }
  },
  PostProcessStageLibrary: {
    createSilhouetteStage: () => new window.Cesium.PostProcessStage()
  },
  Cesium3DTileset: class MockCesium3DTileset {
    constructor(options = {}) {
      this.url = options.url;
      this.show = true;
    }
  },
  defined: (obj) => obj !== undefined && obj !== null
}

// 立即初始化全局兼容性检查
initGlobalCompatibility()

console.log('Cesium已暂时禁用以解决内存问题，使用模拟对象')

const app = createApp(App)

// 设置全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info);
  errorHandler.reportError('vue', err, {
    componentInfo: info,
    componentName: vm?.$options?.name || 'unknown'
  });
};

// 设置全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Vue警告:', msg, trace);
};

// 注册图标组件
for (let iconName in ELIcons) {
	app.component(iconName, ELIcons[iconName])
}

// 全局属性 - 错误处理器
app.config.globalProperties.$errorHandler = errorHandler;
// 暂时禁用Cesium管理器
// app.config.globalProperties.$cesium = globalCesiumManager;

// 挂载应用
app.use(vue3SeamlessScroll).use(router).use(store).use(ElementPlus).use(directives).mount('#app')
