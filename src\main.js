import { createApp } from 'vue'
import App from './App.vue'
import router from "./router";
import "animate.css/animate.min.css";
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'amfe-flexible/index.js'
import '@/assets/styles/scrollbar.css'
import store from "./store";
import vue3SeamlessScroll from "vue3-seamless-scroll";
import * as ELIcons from '@element-plus/icons-vue'
import directives from '@/utils/directives/index.js'
import errorHandler from '@/utils/errorHandler.js'
// 暂时禁用Cesium以解决内存问题
// import 'cesium/Build/Cesium/Widgets/widgets.css'
// import * as Cesium from 'cesium'
// import globalCesiumManager from '@/utils/cesium/globalManager.js'
// import { waitForContainerAndInit } from '@/utils/compatibility/geowin3dCompat.js'
import { initGlobalCompatibility } from '@/utils/compatibility/globalCheck.js'

// 创建模拟的Cesium对象以防止错误
window.Cesium = {
  Viewer: class MockViewer {},
  Cartesian3: {
    fromDegrees: (lon, lat, height) => ({ x: lon, y: lat, z: height || 0 }),
    distance: (p1, p2) => 1000,
    midpoint: (p1, p2, result) => ({ x: (p1.x + p2.x) / 2, y: (p1.y + p2.y) / 2, z: (p1.z + p2.z) / 2 })
  },
  Cartographic: {
    fromCartesian: (cartesian) => ({ longitude: 0, latitude: 0, height: 0 })
  },
  Math: {
    toDegrees: (radians) => radians * 180 / Math.PI,
    toRadians: (degrees) => degrees * Math.PI / 180,
    clamp: (value, min, max) => Math.min(Math.max(value, min), max)
  },
  Color: {
    WHITE: { r: 1, g: 1, b: 1, a: 1 },
    YELLOW: { r: 1, g: 1, b: 0, a: 1 },
    BLACK: { r: 0, g: 0, b: 0, a: 1 },
    fromCssColorString: (color) => ({ r: 1, g: 1, b: 1, a: 1 }),
    withAlpha: function(alpha) { return { ...this, a: alpha }; }
  },
  ScreenSpaceEventHandler: class MockHandler {
    constructor() {}
    setInputAction() {}
    removeInputAction() {}
    destroy() {}
  },
  ScreenSpaceEventType: {
    LEFT_CLICK: 'LEFT_CLICK',
    RIGHT_CLICK: 'RIGHT_CLICK',
    LEFT_DOUBLE_CLICK: 'LEFT_DOUBLE_CLICK'
  },
  Entity: class MockEntity {},
  EllipsoidTerrainProvider: class MockTerrainProvider {},
  HorizontalOrigin: { LEFT: 'LEFT', CENTER: 'CENTER', RIGHT: 'RIGHT' },
  VerticalOrigin: { TOP: 'TOP', CENTER: 'CENTER', BOTTOM: 'BOTTOM' },
  HeightReference: { CLAMP_TO_GROUND: 'CLAMP_TO_GROUND' },
  LabelStyle: { FILL_AND_OUTLINE: 'FILL_AND_OUTLINE' },
  Cartesian2: class MockCartesian2 { constructor(x, y) { this.x = x; this.y = y; } },
  PostProcessStage: class MockPostProcessStage {},
  defined: (obj) => obj !== undefined && obj !== null
}

// 立即初始化全局兼容性检查
initGlobalCompatibility()

console.log('Cesium已暂时禁用以解决内存问题，使用模拟对象')

const app = createApp(App)

// 设置全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info);
  errorHandler.reportError('vue', err, {
    componentInfo: info,
    componentName: vm?.$options?.name || 'unknown'
  });
};

// 设置全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Vue警告:', msg, trace);
};

// 注册图标组件
for (let iconName in ELIcons) {
	app.component(iconName, ELIcons[iconName])
}

// 全局属性 - 错误处理器
app.config.globalProperties.$errorHandler = errorHandler;
// 暂时禁用Cesium管理器
// app.config.globalProperties.$cesium = globalCesiumManager;

// 挂载应用
app.use(vue3SeamlessScroll).use(router).use(store).use(ElementPlus).use(directives).mount('#app')
