import { createApp } from 'vue'
import App from './App.vue'
import router from "./router";
import "animate.css/animate.min.css";
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'amfe-flexible/index.js'
import store from "./store";
import vue3SeamlessScroll from "vue3-seamless-scroll";
import * as ELIcons from '@element-plus/icons-vue'
import directives from '@/utils/directives/index.js'
import errorHandler from '@/utils/errorHandler.js'

const app = createApp(App)

// 设置全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info);
  errorHandler.reportError('vue', err, {
    componentInfo: info,
    componentName: vm?.$options?.name || 'unknown'
  });
};

// 设置全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Vue警告:', msg, trace);
};

// 注册图标组件
for (let iconName in ELIcons) {
	app.component(iconName, ELIcons[iconName])
}

// 全局属性 - 错误处理器
app.config.globalProperties.$errorHandler = errorHandler;

// 挂载应用
app.use(vue3SeamlessScroll).use(router).use(store).use(ElementPlus).use(directives).mount('#app')
