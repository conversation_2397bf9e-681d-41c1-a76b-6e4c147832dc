{"name": "saas", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build ", "preview": "vite preview"}, "dependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.0.10", "@vitejs/plugin-vue": "^2.3.3", "@vueuse/core": "^8.5.0", "animate.css": "^4.1.1", "autoprefixer": "^10.4.12", "axios": "^0.27.2", "cesium": "^1.110.0", "crypto-js": "^4.1.1", "echarts": "^5.4.0", "echarts-gl": "^2.0.9", "element-plus": "^2.2.2", "file-saver": "^2.0.5", "jquery": "^3.6.1", "lodash": "^4.17.21", "pinia": "^2.0.14", "pinia-plugin-persist": "^1.0.0", "qs": "^6.11.0", "vite": "^2.9.9", "vue": "^3.2.25", "vue-loader": "^17.0.0", "vue-router": "^4.0.15", "vue3-seamless-scroll": "^2.0.1", "vue3-video-play": "^1.3.1-beta.6"}, "devDependencies": {"amfe-flexible": "^2.2.1", "less": "^4.1.3", "less-loader": "^11.1.0", "postcss-pxtorem": "^6.0.0", "unplugin-auto-import": "^0.8.5", "vite-plugin-require-transform": "^1.0.3"}}