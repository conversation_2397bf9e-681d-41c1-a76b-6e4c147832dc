import {
  defineStore
} from 'pinia'
import {
  ref,
  toRef,
  toRaw
} from "vue";
// defineStore 类等于 中的 moduel
export const useAmStore = defineStore({
  // 模块ID
  id: 'Am',
  state: () => {
    return {
      //am类列表
      amList: [],
      //未采样的点路径列表
      pathList: [],
      //是否循环
      loop: false,
      //小车速度，单位 米/小时
      speed: 1,
      //设置规划掘进距离
      planningDistance: 0,
      rateValue: 0,
    }
  },
  actions: {
    addAm(am) {
      this.amList.push(am);
      this.setAmValue(am)
    },
    removeAm() {
      this.amList = []
    },
    setAllValue() {
      for (var am of this.amList) {
        am.setPath(this.pathlist)
        am.setPlanningDistance(this.planningDistance)
        am.setSpeed(this.speed)
        am.setLoop(this.loop)
        am.setRate(this.rateValue)
      }
    },
    setAmValue(am){
        am.setPath(this.pathList)
        am.setSpeed(this.speed)
        am.setLoop(this.loop)
        am.setRate(this.rateValue)
        am.setPlanningDistance(this.planningDistance)
    },
    setPath(pathlist) {
      this.pathList = pathlist
      for (var am of this.amList) {
        am.setPath(pathlist)
      }
    },
    setPlanningDistance(value) {
      this.planningDistance = value
      for (var am of this.amList) {
        am.setPlanningDistance(this.planningDistance)
      }
    },
    setSpeed(speed) {
      this.speed = speed
      for (var am of this.amList) {
        am.setSpeed(this.speed)
      }
    },
    setLoop(bool) {
      this.loop = bool
      for (var am of this.amList) {
        am.setLoop(this.loop)
      }
    },
    setRate(value) {
      this.rateValue = value
      for (var am of this.amList) {
        am.setRate(this.rateValue)
      }
    },
    setOption(option){
      if(option.pathlist){
        this.pathList = option.pathlist
      }
      if(option.speed){
        this.speed = option.speed
      }
      if(option.loop){
        this.loop = option.loop
      }
      if(option.rateValue){
        this.rateValue = option.rateValue
      }
      if(option.planningDistance){
        this.planningDistance = option.planningDistance
      }
    },
    flyToModel() {
      for (var am of this.amList) {
        am.flyToModel()
      }
    }
  },
})