<!doctype html>

<title>CodeMirror: CMake mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="cmake.js"></script>
<style>
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      .cm-s-default span.cm-arrow { color: red; }
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">CMake</a>
  </ul>
</div>

<article>
<h2>CMake mode</h2>
<form><textarea id="code" name="code">
# vim: syntax=cmake
if(NOT CMAKE_BUILD_TYPE)
    # default to Release build for GCC builds
    set(CMAKE_BUILD_TYPE Release CACHE STRING
        "Choose the type of build, options are: None(CMAKE_CXX_FLAGS or CMAKE_C_FLAGS used) Debug Release RelWithDebInfo MinSizeRel."
        FORCE)
endif()
message(STATUS "cmake version ${CMAKE_VERSION}")
if(POLICY CMP0025)
    cmake_policy(SET CMP0025 OLD) # report Apple's Clang as just Clang
endif()
if(POLICY CMP0042)
    cmake_policy(SET CMP0042 NEW) # MACOSX_RPATH
endif()

project (x265)
cmake_minimum_required (VERSION 2.8.8) # OBJECT libraries require 2.8.8
include(CheckIncludeFiles)
include(CheckFunctionExists)
include(CheckSymbolExists)
include(CheckCXXCompilerFlag)

# X265_BUILD must be incremented each time the public API is changed
set(X265_BUILD 48)
configure_file("${PROJECT_SOURCE_DIR}/x265.def.in"
               "${PROJECT_BINARY_DIR}/x265.def")
configure_file("${PROJECT_SOURCE_DIR}/x265_config.h.in"
               "${PROJECT_BINARY_DIR}/x265_config.h")

SET(CMAKE_MODULE_PATH "${PROJECT_SOURCE_DIR}/cmake" "${CMAKE_MODULE_PATH}")

# System architecture detection
string(TOLOWER "${CMAKE_SYSTEM_PROCESSOR}" SYSPROC)
set(X86_ALIASES x86 i386 i686 x86_64 amd64)
list(FIND X86_ALIASES "${SYSPROC}" X86MATCH)
if("${SYSPROC}" STREQUAL "" OR X86MATCH GREATER "-1")
    message(STATUS "Detected x86 target processor")
    set(X86 1)
    add_definitions(-DX265_ARCH_X86=1)
    if("${CMAKE_SIZEOF_VOID_P}" MATCHES 8)
        set(X64 1)
        add_definitions(-DX86_64=1)
    endif()
elseif(${SYSPROC} STREQUAL "armv6l")
    message(STATUS "Detected ARM target processor")
    set(ARM 1)
    add_definitions(-DX265_ARCH_ARM=1 -DHAVE_ARMV6=1)
else()
    message(STATUS "CMAKE_SYSTEM_PROCESSOR value `${CMAKE_SYSTEM_PROCESSOR}` is unknown")
    message(STATUS "Please add this value near ${CMAKE_CURRENT_LIST_FILE}:${CMAKE_CURRENT_LIST_LINE}")
endif()

if(UNIX)
    list(APPEND PLATFORM_LIBS pthread)
    find_library(LIBRT rt)
    if(LIBRT)
        list(APPEND PLATFORM_LIBS rt)
    endif()
    find_package(Numa)
    if(NUMA_FOUND)
        list(APPEND CMAKE_REQUIRED_LIBRARIES ${NUMA_LIBRARY})
        check_symbol_exists(numa_node_of_cpu numa.h NUMA_V2)
        if(NUMA_V2)
            add_definitions(-DHAVE_LIBNUMA)
            message(STATUS "libnuma found, building with support for NUMA nodes")
            list(APPEND PLATFORM_LIBS ${NUMA_LIBRARY})
            link_directories(${NUMA_LIBRARY_DIR})
            include_directories(${NUMA_INCLUDE_DIR})
        endif()
    endif()
    mark_as_advanced(LIBRT NUMA_FOUND)
endif(UNIX)

if(X64 AND NOT WIN32)
    option(ENABLE_PIC "Enable Position Independent Code" ON)
else()
    option(ENABLE_PIC "Enable Position Independent Code" OFF)
endif(X64 AND NOT WIN32)

# Compiler detection
if(CMAKE_GENERATOR STREQUAL "Xcode")
  set(XCODE 1)
endif()
if (APPLE)
  add_definitions(-DMACOS)
endif()
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: "text/x-cmake",
        matchBrackets: true,
        indentUnit: 4
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-cmake</code>.</p>

  </article>
