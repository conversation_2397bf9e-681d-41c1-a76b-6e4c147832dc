import $http from '@/utils/request';

export function loginByJson(arg){
	return $http.get(`/api/getdemo?currentPage=${arg.currentPage}&pageSize=${arg.pageSize}`);
}

export function login(params){
	return $http.post('/api/send', params);
}

export function createToken(params){
	return $http.get(`/api/download?fileId=${params.fileId}`, { responseType: 'blob', })
}

/**
 * 使用  
 *   
 */
//  import {loginByJson} from "@/api/login/index";
// loginByJson(data).then((res)=>{
// 	console.log(res)
// })