<template>
  <div class="stoping">
    <!-- <div class="right" v-show="show">
        <div class="right_box5">
          <div class="box1_title">
            <img :src="titleImg" class="box_img" />
            预测预报
          </div>
          <div class="right_content">
            <div class="right_box5_table_head">
              <div class="right_box5_index right_box5_head_text">序号</div>
              <div class="right_box5_name right_box5_head_text">
                预测预报内容
              </div>
              <div class="right_box5_index right_box5_head_text">
                距离
              </div>
              <div class="right_box5_oper right_box5_head_text">操作</div>
            </div>
            <vue3-seamless-scroll :list="yichangqu" class="right_three_scroll" :step="0.5" v-model="scroll"
              :hover="hover" :limitScrollNum="limitScrollNum" wheel="true">
              <div class="" v-for="(item, index) in yichangqu" :key="index">
                <div class="right_box5_table_head right_box5_table_line" :class="
                  index % 2 == 0 ? 'right_table_radix' : 'right_table_even'
                ">
                  <div class="right_box5_index right_box5_line_text">
                    {{ index + 1 }}
                  </div>
                  <div class="right_box5_name right_box5_line_text" style="text-align: center">
                    {{ item.title }}
                  </div>
                  <div class="right_box5_index right_box5_line_text" style="text-align: center">
                    {{ item.distance }}
                  </div>
                  <div class="right_box5_oper right_box5_line_text">
                    <span class="right_box5_see" @click="handleOpen(item, true)" v-show="!item.show">查看</span>
                    <span class="right_box5_see" @click="handleOpen(item, false)" v-show="item.show"
                      style="color: yellow">隐藏</span>
                  </div>
                </div>
              </div>
            </vue3-seamless-scroll>
          </div>
        </div>
      </div> -->
    <div class="time">
      <div class="time_box">
        <div class="time_tool">
          <img :src="timeFlag ? startImg : pausedImg" class="time_tool_img" @click="timeChange(timeFlag)" />
        </div>
        <div class="time_right">
          <div class="time_line"></div>
          <div class="time_bottom" v-for="(item, index) in timeList" :key="index"
            :class="timeIndex == index ? 'time_active' : ''" @click="onChangeTime(index)">
            <div class="time_round"></div>
            <div class="time_title">{{ item }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { usePanelStore } from "@/store/panel";
import { initStore } from "@/utils/store";
import { storeToRefs } from "pinia";
import titleImg from "@/assets/img/home/<USER>";
import lineImg from "@/assets/img/home/<USER>";
import resImg from "@/assets/img/home/<USER>";
import upImg from "@/assets/img/home/<USER>";
import downImg from "@/assets/img/home/<USER>";
import FileSaver from "file-saver";
import leftImg1 from "@/assets/img/stoping/1-1.png";
import leftImg2 from "@/assets/img/stoping/1-2.png";
import leftImg3 from "@/assets/img/stoping/1-3.png";
import leftImg4 from "@/assets/img/stoping/1-4.png";
import * as echarts from "echarts";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { ref, toRef, reactive, onUnmounted, toRaw } from "vue";
import $ from "jquery";
import Animation2 from "@/utils/map/animation2";
import StopingBox from "@/utils/map/stopingBox";
import SetAlphaTool from "@/utils/map/setAlpha";
import { useToolsStore } from "@/store/tools";
// import YCQ from "@/utils/map/YCQ";
import InitModelShow from "@/utils/map/initModelShow";
import { InfoFilled } from '@element-plus/icons-vue'
import { getJuejinModel,getJYeyaModel } from "@/api/home/<USER>";
import { useLoadStore } from "@/store/load";
import startImg from "@/assets/img/home/<USER>";
import pausedImg from "@/assets/img/home/<USER>";

const loadStore = useLoadStore();
const { isLoadEnd } = storeToRefs(loadStore);


const toolsStore = useToolsStore();




let list = ref([
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
]);
const state = reactive({
  scroll: true,
  hover: true,
  limitScrollNum: 15,
});
let { scroll, hover, limitScrollNum } = toRefs(state);
const echart = echarts;
let myChart = null;
const panelStore = usePanelStore();
// 使用storeToRefs可以保证解构出来的数据也是响应式的
const { show } = storeToRefs(panelStore);
//生命周期
onMounted(() => {
  initStore(5);
  if (isLoadEnd.value == true) {
    init();
  }

});

watch(isLoadEnd, (newVal, oldVal) => {
  init();
}, 200);

onUnmounted(() => {
  removeTimeout()
  //移除物探图

  showGZM()
  setHangdao(true);
  if (am) {
    am.destroy();
  }
  if (window.ycq) {
    window.ycq.removeUpdataDistance()
  }
  if (window.dc) {
    window.dc.removeUpdataDistance()
  }
  
  viewer.scene.forceRender();
});

var timer1
var timer2
const removeTimeout = () => {
  if (timer1) {
    clearInterval(timer1);
  } if (timer2) {
    clearInterval(timer2);
  }
}

const init = () => {
  setHangdao(false);
  loadModel();
  initModelsShow()
  viewer.scene.forceRender();
};

var am;
var amRate = ref(0.65)
var amPositionList0 = [
  Cesium.Cartesian3.fromDegrees(116.9771865681275, 34.86098492771089, 1116.6161126308125),
  Cesium.Cartesian3.fromDegrees(116.97577440462048, 34.86218424474127, 1051.082545197303),
]
var amPositionList1 = [
  Cesium.Cartesian3.fromDegrees(116.97713676271479 ,34.86098819365874, 1116.0471665136192),
  Cesium.Cartesian3.fromDegrees(116.97575304364265 ,34.86214740649311, 1056.9035270079505),
]
var amPositionList2 = [
  Cesium.Cartesian3.fromDegrees(116.97712353289975 ,34.860979940363045, 1116.0683507453903),
  Cesium.Cartesian3.fromDegrees(116.97573897898106, 34.86213972101165 ,1056.8407321602726),
]
const loadModel = () => {
  am = new Animation2(viewer);
  var juejinModelUrl=getJuejinModel()
  am.setModel({ url:juejinModelUrl  });
  // am.setModel({ url: "http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1596741818243813376" });
  // am.setModel({ url: "http://172.19.7.226:48080/app-api/geo/function-server-gltf/get-code/1610659036567171072" });
  am.setPath(amPositionList0);
  var yeyaModelUrl=getJYeyaModel()
  am.setyeyaModel(yeyaModelUrl);
  // am.setyeyaModel("http://82.157.9.218:20005/app-api/geo/function-server-gltf/get-code/1596741845225771008");
  // am.setyeyaModel("http://172.19.7.226:48080/app-api/geo/function-server-gltf/get-code/1610658997031661568");
  timer1 = setTimeout(() => {
    am.setPlanningDistance(200);
    am.setSpeed(1);
    am.setLoop(true);
    am.setRate(amRate.value);
    am.play();
    am.flyToModel();
    am.unShowMeasurePathLine()
    am.showPathLine();
    am.addInputAction()
    window.ycq.updataDistanceByAm(am)
    window.dc.updataDistanceByAm(am)
    window.cameraVisual.setVisualByAm(window.location.hash, am)
  }, 1000);
  
};






//设置69巷道透明度
var setAlpha = new SetAlphaTool();
const setHangdao = (show) => {

  if (!show) {
    setAlpha.setHangdaoAlphaByID("71", 0.2);
    setAlpha.hideOthersHangdaoByIds(['71'])
  } else {
    setAlpha.setHangdaoAlphaByID("71", 1);
    setAlpha.showAllHangdao()
  }
};

//初始化图层列表和模型显隐
const initModelsShow = () => {
  var initModelShow = new InitModelShow()
  initModelShow.initShow()
}

// 时间轴 数据
let timeList = ref([
  "2022-12-10",
  "2022-12-11",
  "2022-12-12",
  "2022-12-13",
  "2022-12-14",
  "2022-12-15",
  "2022-12-16",
  "2022-12-17",
  "2022-12-18",
]);
let timeIndex = ref(0);
let timeFlag = ref(true);
let nowTime = ref(0);
// 手动选择时间
const onChangeTime = (index) => {
  timeIndex.value = index;
};
// 开始 暂停
const timeChange = (value) => {
  if (value) {
    initTime();
  } else {
    timePaused();
  }
  timeFlag.value = !value;
};
let timerLine = null;

//初始化时间
const initTime = () => {
  let currTime = new Date().getTime();
  //1000 毫秒运行一次
  if (currTime - nowTime.value > 1000) {
    nowTime.value = new Date().getTime();
    timeStart();
  }
  timerLine = requestAnimationFrame(initTime);
};
//   开始
const timeStart = () => {

  timeIndex.value += 1;
  if (timeIndex.value == timeList.value.length) {
    timeIndex.value = 0;
  }
};
//   暂停
const timePaused = () => {
  cancelAnimationFrame(timerLine);
  timerLine = null;
};

const getNewPath=(index)=>{
  var x1=(amPositionList2[0].x-amPositionList1[0].x)*index
  var y1=(amPositionList2[0].y-amPositionList1[0].y)*index
  var z1=(amPositionList2[0].z-amPositionList1[0].z)*index

  var x2=(amPositionList2[1].x-amPositionList1[1].x)*index
  var y2=(amPositionList2[1].y-amPositionList1[1].y)*index
  var z2=(amPositionList2[1].z-amPositionList1[1].z)*index

  var newx1=amPositionList0[0].x+x1
  var newy1=amPositionList0[0].y+y1
  var newz1=amPositionList0[0].z+z1

  var newx2=amPositionList0[1].x+x2
  var newy2=amPositionList0[1].y+y2
  var newz2=amPositionList0[1].z+z2

  return [new Cesium.Cartesian3(newx1,newy1,newz1),new Cesium.Cartesian3(newx2,newy2,newz2)]
  
}
const hideGZM=(index)=>{
  setAlpha.showAllModel("回采工作面")
  var List=['10','1','16','8','2','5','7','11','13']
  var hideList=[]

  for(var i=0;i<index;i++){
    hideList.push(List[i])
  }
  setAlpha.hideModelByIds("回采工作面", hideList);
}

const showGZM=(index)=>{
  setAlpha.showAllModel("回采工作面")
  var hideList=[]
  setAlpha.hideModelByIds("回采工作面", hideList);
}

//监听timeIndex，改变时执行
watch(timeIndex, (newVal, oldVal) => {
  // console.log(newVal, oldVal)
  var newpath=getNewPath(newVal)
  am.setPath(newpath);
  am.unShowMeasurePathLine()
  hideGZM(newVal)
  if (newVal >= oldVal) {
    // var startR = oldVal / timeList.value.length
    // var endR = newVal / timeList.value.length
    // var jiange=(endR-startR)/50
    // var startR = amRate.value + newVal / 300
    // var endR = amRate.value + oldVal / 300
    // console.log(amRate,amRate)
    // var jiange = (endR - startR) / 100
    // am.setPath(amPositionList2);
    var timer = setInterval(() => {
      // am.setRate(startR);
      // startR += jiange
      // if (startR >= endR) {
      //   clearInterval(timer)
      // }
    }, 10);
  } else {
    // am.setPath(amPositionList1)
    // am.setRate(amRate.value + newVal / 300);
    // setTimeout(() => {
    //   // window.amc.flyToModel()
    // }, 200);
  }
}, 200);


</script>
<style lang="less" scoped>
.stoping {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;

  .time {
    position: absolute;
    bottom: 60px;
    left: 0px;
    width: 100%;
    height: 100px;
    z-index: 99;
    display: flex;
    justify-content: center;

    .time_box {
      max-width: 1200px;
      overflow: auto;
      height: 100%;
      //  width: 100%;
      display: flex;
      font-size: 20px;
      color: #fff;
      position: relative;
      padding-top: 20px;
      box-sizing: border-box;

      .time_right {
        position: relative;
        display: flex;
      }

      .time_line {
        width: 100%;
        height: 6px;
        background-color: #0e5cae;
        position: absolute;
        top: 18px;
        left: 0px;
        border-radius: 6px;
      }

      .time_tool {
        width: 60px;
        font-size: 14px;
        display: flex;
        padding-top: 2px;
        padding-left: 10px;
        cursor: pointer;

        .time_tool_img {
          width: 40px;
          height: 40px;
        }
      }

      .time_bottom {
        position: relative;
        width: 120px;
        height: 40px;
        cursor: pointer;

        .time_round {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 8px;
          height: 8px;
          background-color: #fff;
          border-radius: 50%;
        }

        .time_title {
          position: absolute;
          left: 50%;
          top: 30px;
          transform: translateX(-50%);
          text-align: center;
          width: 100px;
          line-height: 20px;
          font-size: 16px;
          white-space: normal;
        }
      }

      .time_active {
        .time_round {
          background: #fabd63;
        }

        .time_title {
          color: #fabd63;
        }
      }
    }
  }

  // overflow: hidden;
  

  .right {
    position: absolute;
    bottom: 60px;
    left: 0px;
    
    z-index: 99;
    width: 344px;
    height: 100%;
    padding-bottom: 19px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .right_box1 {
      width: 100%;
      height: 178px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 2px 22px 0 12px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, .6);

      .right_content {
        width: 100%;
        height: 100%;
        padding-top: 8px;

        .right_btn_box {
          display: flex;
          width: 100%;
          align-items: center;
          margin-bottom: 4px;
          height: 22px;
          webkit-text-fill-color: red;
          /* 设置字体颜色 */
        }

        :deep(.el-input) {
          flex: 1;
          height: 22px;
        }

        :deep(.el-input__wrapper) {
          input::input-placeholder {
            color: red
          }
        }

        .right_content_btn {
          width: 70px;
          font-size: 14px;
          margin-right: 5px;
          color: #039ec4;
        }

        .unit {
          margin-left: 10px;
          width: 70px;
          display: block;
          font-size: 14px;
          margin-right: 2px;
          color: #039ec4;
        }

        .right_content_ul {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          margin-top: 8px;

          .right_content_li {
            width: 120px;
            height: 28px;
            margin-right: 10px;
            margin-bottom: 8px;
          }
        }

        .BTN {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #039ec4;
          border-radius: 6px;
          background-repeat: no-repeat;
          font-size: 14px;
          font-family: Source Han Sans SC;
          font-weight: 600;
          color: #ffffff;
          cursor: pointer;

          &:hover {
            transform: scale(1.03);
            border: 1px solid #ffd200;
          }
        }
      }

      .left_box1_li_active {
        border: 1px solid #ffd200 !important;
      }
    }

    .right_box2 {
      margin-top: 10px;
      width: 100%;
      height: 154px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 2px 22px 0 12px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, .6);

      .right_content {
        width: 100%;
        height: 100%;
        padding-top: 10px;

        .right_btn_box {
          display: flex;
          width: 100%;
          align-items: center;
          margin-bottom: 4px;
          height: 22px;
          webkit-text-fill-color: red;
          /* 设置字体颜色 */
        }

        :deep(.el-input) {
          flex: 1;
          height: 22px;
        }

        :deep(.el-input__wrapper) {
          input::input-placeholder {
            color: red
          }
        }

        .right_content_btn {
          width: 84px;
          font-size: 14px;
          margin-right: 5px;
          color: #039ec4;
        }

        .unit {
          margin-left: 10px;
          width: 70px;
          display: block;
          font-size: 14px;
          margin-right: 2px;
          color: #039ec4;
        }

        .right_btn_bottom {
          width: 100%;
          display: flex;
          justify-content: space-around;
          margin-top: 10px;
        }

        .BTN {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #039ec4;
          border-radius: 6px;
          background-repeat: no-repeat;
          font-size: 14px;
          font-family: Source Han Sans SC;
          font-weight: 600;
          color: #ffffff;
          width: 100px;
          height: 26px;
          cursor: pointer;

          &:hover {
            transform: scale(1.03);
            border: 1px solid #ffd200;
          }
        }
      }

      .left_box1_li_active {
        border: 1px solid #ffd200 !important;
      }
    }

    .right_box3 {
      margin-top: 10px;
      width: 100%;
      height: 150px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 4px 22px 0 12px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, .6);

      .right_content {
        width: 100%;
        height: 100%;
        padding-top: 8px;

        .right_btn_box {
          display: flex;
          width: 100%;
          align-items: center;
          margin-bottom: 6px;
          height: 22px;
          webkit-text-fill-color: red;
          /* 设置字体颜色 */
        }

        :deep(.el-input) {
          flex: 1;
          height: 22px;
        }

        :deep(.el-input__wrapper) {
          input {
            flex: 1;
          }

          input::input-placeholder {
            color: red
          }
        }

        .right_content_btn {
          width: 74px;
          font-size: 14px;
          margin-right: 5px;
          color: #039ec4;
        }

        .unit {
          margin-left: 10px;
          width: 70px;
          display: block;
          font-size: 14px;
          margin-right: 2px;
          color: #039ec4;
        }

        .right_btn_bottom {
          width: 100%;
          display: flex;
          justify-content: space-around;
          margin-top: 10px;
        }

        .BTN {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #039ec4;
          border-radius: 6px;
          background-repeat: no-repeat;
          font-size: 14px;
          font-family: Source Han Sans SC;
          font-weight: 600;
          color: #ffffff;
          width: 100px;
          height: 26px;
          cursor: pointer;

          &:hover {
            transform: scale(1.03);
            border: 1px solid #ffd200;
          }
        }
      }
      .left_box1_li_active {
        border: 1px solid #ffd200 !important;
      }
    }

    .right_box4 {
      margin-top: 10px;
      width: 100%;
      height: 130px;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 4px 22px 0 12px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, .6);

      .right_content {
        width: 100%;
        height: 100%;
        padding-top: 10px;

        .right_btn_box {
          display: flex;
          width: 100%;
          align-items: center;
          margin-bottom: 6px;
          height: 22px;
          webkit-text-fill-color: red;
          /* 设置字体颜色 */
        }

        :deep(.el-input) {
          flex: 1;
          height: 22px;
        }

        :deep(.el-input__wrapper) {
          input {
            flex: 1;
          }

          input::input-placeholder {
            color: red
          }
        }

        .right_content_btn {
          width: 100px;
          font-size: 14px;
          margin-right: 5px;
          color: #039ec4;
        }

        .unit {
          margin-left: 10px;
          width: 70px;
          display: block;
          font-size: 14px;
          margin-right: 2px;
          color: #039ec4;
        }

        .right_btn_bottom {
          width: 100%;
          display: flex;
          justify-content: space-around;
          margin-top: 10px;
        }

        .BTN {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #039ec4;
          border-radius: 6px;
          background-repeat: no-repeat;
          font-size: 14px;
          font-family: Source Han Sans SC;
          font-weight: 600;
          color: #ffffff;
          width: 100px;
          height: 26px;
          cursor: pointer;

          &:hover {
            transform: scale(1.03);
            border: 1px solid #ffd200;
          }
        }
      }

      .left_box1_li_active {
        border: 1px solid #ffd200 !important;
      }
    }

    .right_box5 {
      margin-top: 11px;
      width: 100%;
      height: 100%;
      background-image: url("../assets/img/home/<USER>");
      background-position: center center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 2px 0 10px 20px;
      display: flex;
      flex-direction: column;
      background-color: RGBA(5, 31, 89, 0.6);
      .right_content {
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 10px 20px 0 0;
        overflow: hidden;
        .right_three_scroll {
          height: 100%;
          overflow: hidden;
        }
        .right_box5_table_head {
          display: flex;
          justify-content: flex-start;
          height: 24px;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          align-items: center;
          background: linear-gradient(0deg, rgba(0, 222, 255, 0.24));
          box-shadow: 0px -1px 0px 0px rgba(51, 214, 255, 0.6);
          .right_box5_head_text {
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: #26c1d1;
            text-align: center;
            display: flex;
            justify-content: center;
          }
          .right_box5_line_text {
            display: flex;
            justify-content: center;
          }
          .right_box5_index {
            width: 40px;
          }
          .right_box5_name {
            width: 200px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: block;
            justify-content: flex-start;
          }
          .right_box5_type {
            width: 60px;
          }
          .right_box5_time {
            width: 120px;
          }
          .right_box5_upTime {
            width: 120px;
          }
          .right_box5_oper {
            width: 100px;
            display: flex;
            justify-content: center;
            span {
              margin-left: 10px;
              width: 49px;
              height: 20px;
              font-size: 12px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
              &:hover {
                filter: brightness(1.1);
              }
            }
            .right_box5_see {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
            .right_box5_down {
              background-image: url("../assets/img/home/<USER>");
              background-position: center center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }
        }
        .right_box5_table_line {
          height: 30px;
        }

        .right_table_even {
          background: rgba(4, 55, 105, 100);
        }

        .right_table_radix {
          background: rgba(4, 48, 98, 100);
        }
      }
    }
  }

  // 图框 标题
   .box1_title {
    position: relative;
    height: 38px;
    color: rgb(255, 255, 255);
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: undefined;
    line-height: 27px;
    letter-spacing: 0px;
    text-align: left;
    display: flex;
    // align-items: center;
   span{
      z-index: 1;
      padding-left: 19px;
      padding-top: 2px;
    }
  }

 .box_img {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 38px;
    width: 100%;
  }
}
</style>
