import yc from '@/assets/img/geology/yc.png'
import yc2 from '@/assets/img/geology/yc2.png'
import modelMoveHeight from "@/utils/map/modelMoveHeight"
import UnderPlane from "@/utils/map/underPlane";
import {
    storeToRefs
} from "pinia";
import {
    useLoadStore
} from "@/store/load";

const loadStore = useLoadStore();
const {
    loadData,
    isLoadEnd
} = storeToRefs(loadStore);
class Earth {
    layerManager = {}
    entities = []
    planeList = []

    constructor(_container) {
        this.initEarth(_container);
    }

    /**
     * 初始化加载地球
     * <AUTHOR>
     */
    initEarth(_container) {
        window.geoapp = this.geoapp = new Geowin3DAPP(
            _container, {
                sceneMode: Cesium.SceneMode.SCENE3D,
                shouldAnimate: true,
                infoBox: true,
                scene3DOnly: true,
                orderIndependentTranslucency: false,
                contextOptions: {
                    webgl: {
                        alpha: true,
                    },
                },
            }, {
                showMouseCenterIcon: false
            }
        );


        window.viewer = this.geoapp.getViewer();
        viewer.terrainProvider = new Geowin3D.GeowinTerrainProvider({
            tilename: "dem"
        });
        viewer.showLogo = false;
        window.camera = viewer.scene.camera;

        this.initEarthConfig();

    }

    /**
     * 初始化模型管理
     * @returns 
     */
    initModelManagerTools() {
        this.modelManager = new Geowin3D.GwModel.GwModelManager(viewer);
        return this.modelManager;
    }

    getViewer() {
        return viewer;
    }

    getGeoAPP() {
        return this.geoapp;
    }
    getEntities() {
        return this.entities;
    }
    /**
     * 初始化球配置
     * <AUTHOR>
     */
    initEarthConfig() {
        viewer.scene.globe.depthTestAgainstTerrain = true;
        
        //抗锯齿
        viewer.scene.fxaa = true;
        viewer.scene.postProcessStages.fxaa.enabled = true;
        //清除月亮太阳
        viewer.scene.moon.show = false
        viewer.scene.sun.show = false
        viewer.scene.globe.show = false;
        // viewer.scene.globe.enableLighting = false; //关闭光照
        // viewer.shadows = false; //关闭阴影
        this.updateSkyBox();
        viewer.scene.camera.changed.addEventListener(function () {
            viewer.scene.light = new Cesium.DirectionalLight({
                direction: viewer.scene.camera.directionWC,
            });
            viewer.scene.camera.changed.addEventListener(function (scene, time) {
                viewer.scene.light.direction = Cesium.Cartesian3.clone(
                    viewer.scene.camera.directionWC,
                    viewer.scene.light.direction
                );
            });
            viewer.scene.requestRender();
        })

        //设置鼠标进去地下
        viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;
        // this.undergroundSpace()

        // viewer.scene.screenSpaceCameraController.minimumZoomDistance = -8500+1900;//相机的高度的最小值
        // viewer.scene.screenSpaceCameraController.maximumZoomDistance = 10000+1900

        // var plane = viewer.entities.add({
        //     name: "plane",
        //     position: Cesium.Cartesian3.fromDegrees(lat, lng, -3500 + 1900),
        //     initposition: Cesium.Cartesian3.fromDegrees(lat, lng, -3500 + 1900),
        //     plane: {
        //         plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
        //         dimensions: new Cesium.Cartesian2(1400000, 1400000), //指定平面的宽度和高度。
        //         material: new Cesium.Color(5 / 255, 31 / 255, 85 / 255),
        //         fill: true, //是否填充
        //     },
        // });
        var lat = 116.992786
        var lng = 34.909266
        var underPlane = new UnderPlane(viewer, Cesium.Cartesian3.fromDegrees(lat, lng, -3500 + 1900))
        window.underPlane = underPlane
    }

    /**
     * 添加图层，支持3dtiles,wmts url,gltf等，后续所有图层类型均通过该方法进行扩展
     * tileset:3dtileset配置别名
     * tileset_url:3dtileset的服务地址
     * geowintms:二维瓦片配置别名
     * geowintms_url:二维瓦片的url服务地址
     * heatmap:热力图
     * @params {*}
     * {
     *  id:layerid,
     *  type:geowindem/tileset/geowintms,heatmap,
     *  position:
     *  options:
     *  bounds:
     *  tileName:
     * }
     *
     * <AUTHOR>
     */
    loadLayer(treeNode) {

        var id = treeNode.id;
        var layer = null;
        switch (treeNode.type) {
            //暂且未启用
            case "tileset":
                if (viewer.camera._mode == 2) return;
                layer = this.geoapp.load3DTileset({
                    tileName: treeNode.layername,
                    position: treeNode.position,
                    orientation: treeNode.orientation,
                });
                this.geoapp.show(layer);
                this.entities.push(layer);
                break;
            case "tileset_url":
                if (viewer.camera._mode == 2) return;
                layer = this.geoapp.load3DTileset({
                    url: treeNode.url,
                    position: treeNode.position,
                    orientation: treeNode.orientation,
                    // debugShowBoundingVolume:true
                });
                // layer.lightColor = new Cesium.Cartesian3(2,2,2);
                layer.imageBasedLightingFactor = new Cesium.Cartesian2(0.0, 0.0)
                this.geoapp.show(layer);
                this.entities.push(layer);
                break;
            case "geowintms": //暂且未启用
                layer = this.geoapp.addImageLayer({
                    url: treeNode.url,
                    bounds: treeNode["bounds"],
                });

                if (treeNode.options != null) {
                    for (var x in treeNode.options) {
                        layer[x] = treeNode.options[x];
                    }
                }
                break;

            case "geowindem": //暂且未启用
                layer = this.geoapp.addDefaultTerrain(treeNode.layername);
                break;
            case "dem":
                viewer.terrainProvider = new Geowin3D.Geowin3DTerrainProvider({
                    url: treeNode.url,
                });
                break;

            case "heatmap": //暂且未启用
                layer = this.geoapp.addHeatMapLayer(
                    treeNode.layername,
                    treeNode.options,
                    treeNode
                );
                break;
            default:
                if (viewer.camera._mode == 2) return;
                layer = this.geoapp.loadEntity(
                    treeNode.layername,
                    treeNode.type,
                    treeNode.options,
                    treeNode
                );
                break;
        }

        this.layerManager[id] = {
            treeNode: treeNode,
            layer: layer,
            features: [],
            isload: false

        };
        // var time = new Date().getTime()
        // console.log(treeNode.label,this.layerManager[id].isload,time)


        if (treeNode.type == "tileset_url" || treeNode.type == 'tileset') {
            layer.tileLoad.addEventListener((tile) => {
                // tile.debugShowBoundingVolume=true
                let content = tile.content;
                tile.pmid = id;
                let featuresLength = content.featuresLength;
                for (var i = 0; i < featuresLength; i++) {
                    // content.getFeature(i).dataLabel=treeNode.label
                    this.layerManager[id].features.push(content.getFeature(i))
                    // console.log(treeNode.label,i)
                }
            });
            layer.readyPromise.then((model) => {
                this.modelMovement(model)
                console.log(treeNode.label)
                model.dataLabel = treeNode.label
                this.layerManager[id].isload = true
                model.pmid = id;
                loadData.value[id] = true
                loadStore.setIsLoadEnd()
            })
            setTimeout(() => {
                if (loadData.value[id] == false) {
                    loadData.value[id] = true
                    console.log(treeNode.label + " 加载失败！")
                    loadStore.setIsLoadEnd()
                }
            }, 5000);

            layer.allTilesLoaded.addEventListener((tile) => {
                var time2 = new Date().getTime()
                // console.log(treeNode.label,this.layerManager[id].isload,time2,time2-time)
            })

        }

        if (treeNode.setStyle != null)
            window[treeNode.setStyle].call(this, layer, treeNode);

        if (treeNode.position != null)
            this.geoapp.flyToByPosition(treeNode.position);
    }

    /**
     * 移除图层
     * @param {*} treeNode
     * @returns
     */
    removeLayer(treeNode) {
        var id = treeNode.id;
        if (this.layerManager[id] == null) return;
        if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
            this.geoapp.hide(this.layerManager[id].layer);
            return;
        }
        if (treeNode.type == "geowindem") {
            this.geoapp.removeTerrain();
            return;
        }
        if (this.layerManager[id].layer)
            this.geoapp.removeEntity(this.layerManager[id].layer);
        delete this.layerManager[id];
    }

    /**
     * 创建相机信息条
     */
    getCameraInfo() {
        var cameraInfo = Geowin3D.GwCamera.getCurrentCameraInfo(window.camera);
        return {
            lontitude: cameraInfo[0],
            latitude: cameraInfo[1],
            height: cameraInfo[2] - 1900,
            heading: cameraInfo[3],
            pitch: cameraInfo[4],
            roll: cameraInfo[5],
            cameraInfo: cameraInfo,
        };
    }


    /**
     * 裁剪并填充
     */
    undergroundSpace() {
        let globe = viewer.scene.globe;
        globe.depthTestAgainstTerrain = true;
        // var lat = 117.003968;
        // var lng = 34.816295;
        // var lat=116.997840;
        // var lng=34.909760;
        var lat = 116.992786
        var lng = 34.909266
        let position = Cesium.Cartographic.toCartesian(
            new Cesium.Cartographic.fromDegrees(lat, lng, 0)
        );
        globe.clippingPlanes = new Cesium.ClippingPlaneCollection({
            modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(position),
            planes: [
                new Cesium.Plane(new Cesium.Cartesian3(1, 0.0, 0.0), -7000.0),
                new Cesium.Plane(new Cesium.Cartesian3(-1.0, 0.0, 0.0), -7000.0),
                new Cesium.Plane(new Cesium.Cartesian3(0.0, 1.0, 0.0), -7000.0),
                new Cesium.Plane(new Cesium.Cartesian3(0.0, -1.0, 0.0), -7000.0),
            ],
            edgeWidth: 1.0,
            edgeColor: Cesium.Color.BLACK,
        });


        var plane1 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, 7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,
                }),
                fill: true, //是否填充
            },
        });
        var plane2 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, -7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,

                }),
                fill: true, //是否填充
            },
        });
        var plane3 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Y, 7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,

                }),
                fill: true, //是否填充
            },
        });
        var plane4 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Y, -7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,

                }),
                fill: true, //是否填充
            },
        });
        var plane5 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -3000),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 14000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc2,

                }),
                fill: true, //是否填充
            },
        });

        var plane6 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -4000),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(1400000, 1400000), //指定平面的宽度和高度。
                material: new Cesium.Color(5 / 255, 31 / 255, 85 / 255),
                fill: true, //是否填充
            },
        });
        this.planeList.push(plane1)
        this.planeList.push(plane2)
        this.planeList.push(plane3)
        this.planeList.push(plane4)
        this.planeList.push(plane5)
    }

    setUndergroudSpaceShow(flag) {
        if (!flag) {
            if (this.planeList.length == 5) {
                for (var plane of this.planeList) {
                    plane.show = false
                }
            } else {
                setTimeout(() => {
                    this.setUndergroudSpaceShow(false)
                }, 200);
            }
        } else {
            for (var plane of this.planeList) {
                plane.show = true
            }
        }
    }


    /**
     * 移除图层
     * @param {*} treeNode 
     * @returns 
     */
    removeLayer(treeNode) {
        var id = treeNode.id;
        if (this.layerManager[id] == null) return;
        if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
            this.geoapp.hide(this.layerManager[id].layer);
            return;
        }
        if (treeNode.type == "geowindem") {
            this.geoapp.removeTerrain();
            return;
        }
        if (this.layerManager[id].layer)
            this.geoapp.removeEntity(this.layerManager[id].layer);
        delete this.layerManager[id];
    }

    /**
     * 设置tooltip
     */
    addToolTip(_options) {
        var bb = this.modelManager.add('TextBox2D', {
            position: [_options.lon, _options.lat, _options.height],
            height: _options.hheight, //广告牌的宽度
            width: _options.hwidth, //广告牌的高度,
            text: '测试文字',
            paddingLeft: 50,
            lineColor: 'rgba(255,255,0,1)',
            textColor: 'rgba(0,255,0,1)',
            lineWidth: 1,
            fontSize: 16,
            moveTop: 30 //数值不可以为负数
        });
        this.entities.push(bb);
    }

    /**
     * 模型旋转
     * @param {*} primitives 
     */
    trackModel(primitives) {
        this.trackManager = new Geowin3D.GwCamera.GwTrackManager(viewer.scene);
        var boundingSphere = this.getBoundingSphere(primitives);
        viewer.camera.flyToBoundingSphere(boundingSphere, {
            duration: 2
        });
        this.trackManager.trackByBoundingSphere(boundingSphere);
    }

    /**
     * 退出模型旋转
     */
    exitTrackModel() {
        this.trackManager.cancelTrack();
        this.trackManager = null;
    }

    /**
     * 获取模型包围盒
     * @param {*} primitives 
     * @returns 
     */
    getBoundingSphere(primitives) {
        var bss = [];
        for (var i = 0; i < primitives.length; i++) {
            if (primitives[i].ready)
                bss.push(primitives[i].boundingSphere);
        }

        return Geowin3D.BoundingSphere.fromBoundingSpheres(bss);
    }

    /**
     * 将单个模型上下移动一定距离
     */
    modelMovement(model) {
        const cartographic = Cesium.Cartographic.fromCartesian(
            model.boundingSphere.center
        );
        const surface = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            0.0
        );
        const offset = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            modelMoveHeight
        );
        const translation = Cesium.Cartesian3.subtract(
            offset,
            surface,
            new Cesium.Cartesian3()
        );
        model.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
    }

    /**
     * 将所有模型上下移动一定距离
     */
    modelsMovement() {
        for (var i = 0; i < this.entities.length; i++) {
            const cartographic = Cesium.Cartographic.fromCartesian(
                this.entities[i].boundingSphere.center
            );
            const surface = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                0.0
            );
            const offset = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                modelMoveHeight
            );
            const translation = Cesium.Cartesian3.subtract(
                offset,
                surface,
                new Cesium.Cartesian3()
            );
            this.entities[i].modelMatrix = Cesium.Matrix4.fromTranslation(translation);

        }
    }
    updateSkyBox(){
        //天空和设置
        var imagePaths = [
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png'
        ];
        Geowin3D.GwApp.setSkyBox(viewer.scene, {
            imagePaths: imagePaths
        });
        Geowin3D.GwApp.setSkyBoxOnGround(viewer.scene, imagePaths);
        viewer.scene.backgroundColor = new Cesium.Color(0.0, 0.0, 0.0, 0.0);
    }
}
export default Earth;