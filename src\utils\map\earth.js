import yc from '@/assets/img/geology/yc.png'
import yc2 from '@/assets/img/geology/yc2.png'
import modelMoveHeight from "@/utils/map/modelMoveHeight"
import UnderPlane from "@/utils/map/underPlane";
import {
    storeToRefs
} from "pinia";
import {
    useLoadStore
} from "@/store/load";

const loadStore = useLoadStore();
const {
    loadData,
    isLoadEnd
} = storeToRefs(loadStore);
class Earth {
    layerManager = {}
    entities = []
    planeList = []

    constructor(_container) {
        this.initEarth(_container);
    }

    /**
     * 初始化加载地球
     * <AUTHOR>
     */
    initEarth(_container) {
        // 使用Cesium创建viewer
        console.log('开始初始化Earth with Cesium');

        try {
            window.viewer = this.viewer = new window.Cesium.Viewer(_container, {
                animation: false,
                baseLayerPicker: false,
                fullscreenButton: false,
                geocoder: false,
                homeButton: false,
                infoBox: false,
                sceneModePicker: false,
                selectionIndicator: false,
                timeline: false,
                navigationHelpButton: false,
                scene3DOnly: true,
                shouldAnimate: false,
                terrainProvider: new window.Cesium.EllipsoidTerrainProvider(),
                imageryProvider: new window.Cesium.TileMapServiceImageryProvider({
                    url: window.Cesium.buildModuleUrl('Assets/Textures/NaturalEarthII')
                }),
                contextOptions: {
                    webgl: {
                        alpha: false,
                        depth: true,
                        stencil: false,
                        antialias: true,
                        premultipliedAlpha: true,
                        preserveDrawingBuffer: false,
                        failIfMajorPerformanceCaveat: false
                    }
                },
                orderIndependentTranslucency: false,
                shadows: false
            });

            // 隐藏Cesium logo
            this.viewer._cesiumWidget._creditContainer.style.display = "none";

            window.camera = this.viewer.scene.camera;

            console.log('Earth初始化完成');
            this.initEarthConfig();

        } catch (error) {
            console.error('Earth初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化模型管理
     * @returns 
     */
    initModelManagerTools() {
        this.modelManager = new Geowin3D.GwModel.GwModelManager(viewer);
        return this.modelManager;
    }

    getViewer() {
        return viewer;
    }

    getGeoAPP() {
        return this.geoapp;
    }
    getEntities() {
        return this.entities;
    }
    /**
     * 初始化球配置
     * <AUTHOR>
     */
    initEarthConfig() {
        viewer.scene.globe.depthTestAgainstTerrain = true;
        
        //抗锯齿
        viewer.scene.fxaa = true;
        viewer.scene.postProcessStages.fxaa.enabled = true;
        //清除月亮太阳
        viewer.scene.moon.show = false
        viewer.scene.sun.show = false
        viewer.scene.globe.show = false;
        // viewer.scene.globe.enableLighting = false; //关闭光照
        // viewer.shadows = false; //关闭阴影
        this.updateSkyBox();
        viewer.scene.camera.changed.addEventListener(function () {
            viewer.scene.light = new Cesium.DirectionalLight({
                direction: viewer.scene.camera.directionWC,
            });
            viewer.scene.camera.changed.addEventListener(function (scene, time) {
                viewer.scene.light.direction = Cesium.Cartesian3.clone(
                    viewer.scene.camera.directionWC,
                    viewer.scene.light.direction
                );
            });
            viewer.scene.requestRender();
        })

        //设置鼠标进去地下
        viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;
        // this.undergroundSpace()

        // viewer.scene.screenSpaceCameraController.minimumZoomDistance = -8500+1900;//相机的高度的最小值
        // viewer.scene.screenSpaceCameraController.maximumZoomDistance = 10000+1900

        // var plane = viewer.entities.add({
        //     name: "plane",
        //     position: Cesium.Cartesian3.fromDegrees(lat, lng, -3500 + 1900),
        //     initposition: Cesium.Cartesian3.fromDegrees(lat, lng, -3500 + 1900),
        //     plane: {
        //         plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
        //         dimensions: new Cesium.Cartesian2(1400000, 1400000), //指定平面的宽度和高度。
        //         material: new Cesium.Color(5 / 255, 31 / 255, 85 / 255),
        //         fill: true, //是否填充
        //     },
        // });
        var lat = 116.992786
        var lng = 34.909266
        var underPlane = new UnderPlane(viewer, Cesium.Cartesian3.fromDegrees(lat, lng, -3500 + 1900))
        window.underPlane = underPlane
    }

    /**
     * 添加图层，支持3dtiles,wmts url,gltf等，后续所有图层类型均通过该方法进行扩展
     * tileset:3dtileset配置别名
     * tileset_url:3dtileset的服务地址
     * geowintms:二维瓦片配置别名
     * geowintms_url:二维瓦片的url服务地址
     * heatmap:热力图
     * @params {*}
     * {
     *  id:layerid,
     *  type:geowindem/tileset/geowintms,heatmap,
     *  position:
     *  options:
     *  bounds:
     *  tileName:
     * }
     *
     * <AUTHOR>
     */
    loadLayer(treeNode) {
        console.log('加载图层:', treeNode);

        var id = treeNode.id;
        var layer = null;

        try {
            switch (treeNode.type) {
                case "tileset":
                case "tileset_url":
                    // 使用Cesium加载3D Tiles
                    const url = treeNode.url || treeNode.layer;
                    if (!url) {
                        console.warn('图层URL为空:', treeNode);
                        return null;
                    }

                    console.log('加载3D Tileset:', url);
                    layer = new window.Cesium.Cesium3DTileset({
                        url: url,
                        // 优化设置
                        maximumScreenSpaceError: 16,
                        maximumMemoryUsage: 512,
                        cullWithChildrenBounds: false,
                        dynamicScreenSpaceError: true,
                        dynamicScreenSpaceErrorDensity: 0.00278,
                        dynamicScreenSpaceErrorFactor: 4.0,
                        dynamicScreenSpaceErrorHeightFalloff: 0.25
                    });

                    // 设置光照
                    layer.imageBasedLightingFactor = new window.Cesium.Cartesian2(0.0, 0.0);

                    // 添加到场景
                    this.viewer.scene.primitives.add(layer);
                    this.entities.push(layer);

                    // 监听加载完成事件
                    layer.readyPromise.then(() => {
                        console.log('3D Tileset加载完成:', treeNode.label || treeNode.typeName);
                    }).catch(error => {
                        console.error('3D Tileset加载失败:', error);
                    });

                    break;
            case "geowintms":
            case "wmts":
                // 使用Cesium加载影像图层
                console.log('加载影像图层:', treeNode);
                try {
                    const imageryProvider = new window.Cesium.WebMapTileServiceImageryProvider({
                        url: treeNode.url,
                        layer: treeNode.layername || 'default',
                        style: 'default',
                        format: 'image/png',
                        tileMatrixSetID: 'default'
                    });
                    layer = this.viewer.imageryLayers.addImageryProvider(imageryProvider);

                    if (treeNode.options != null) {
                        for (var x in treeNode.options) {
                            layer[x] = treeNode.options[x];
                        }
                    }
                } catch (error) {
                    console.error('影像图层加载失败:', error);
                }
                break;

            case "geowindem":
            case "dem":
                // 使用Cesium地形提供者
                console.log('加载地形:', treeNode);
                try {
                    if (treeNode.url) {
                        this.viewer.terrainProvider = new window.Cesium.CesiumTerrainProvider({
                            url: treeNode.url
                        });
                    } else {
                        this.viewer.terrainProvider = new window.Cesium.EllipsoidTerrainProvider();
                    }
                } catch (error) {
                    console.error('地形加载失败:', error);
                    this.viewer.terrainProvider = new window.Cesium.EllipsoidTerrainProvider();
                }
                break;

            case "heatmap":
                // 热力图功能暂时简化
                console.log('热力图功能已简化:', treeNode);
                break;

            case "geojson":
                // 加载GeoJSON数据
                console.log('加载GeoJSON:', treeNode);
                try {
                    if (treeNode.url || treeNode.layer) {
                        const dataSource = window.Cesium.GeoJsonDataSource.load(treeNode.url || treeNode.layer);
                        this.viewer.dataSources.add(dataSource);
                        layer = dataSource;
                    }
                } catch (error) {
                    console.error('GeoJSON加载失败:', error);
                }
                break;

            default:
                break;
        } catch (error) {
            console.error('图层加载失败:', error, treeNode);
            layer = null;
        }

        // 存储图层信息
        this.layerManager[id] = {
            treeNode: treeNode,
            layer: layer,
            features: [],
            isload: false
        };

        // 处理3D Tileset的特殊逻辑
        if ((treeNode.type == "tileset_url" || treeNode.type == 'tileset') && layer) {
            // 监听瓦片加载事件
            if (layer.tileLoad) {
                layer.tileLoad.addEventListener((tile) => {
                    try {
                        let content = tile.content;
                        tile.pmid = id;
                        let featuresLength = content.featuresLength;
                        for (var i = 0; i < featuresLength; i++) {
                            this.layerManager[id].features.push(content.getFeature(i));
                        }
                    } catch (error) {
                        console.warn('瓦片特征提取失败:', error);
                    }
                });
            }

            // 监听加载完成事件
            if (layer.readyPromise) {
                layer.readyPromise.then((model) => {
                    try {
                        console.log('模型加载完成:', treeNode.label || treeNode.typeName);
                        model.dataLabel = treeNode.label || treeNode.typeName;
                        this.layerManager[id].isload = true;
                        model.pmid = id;
                        loadData.value[id] = true;
                        loadStore.setIsLoadEnd();

                        // 如果有模型移动功能，调用它
                        if (typeof this.modelMovement === 'function') {
                            this.modelMovement(model);
                        }
                    } catch (error) {
                        console.error('模型加载后处理失败:', error);
                    }
                }).catch(error => {
                    console.error('模型加载失败:', error);
                    loadData.value[id] = false;
                });
            }
        } else if (layer) {
            // 其他类型的图层直接标记为已加载
            this.layerManager[id].isload = true;
            loadData.value[id] = true;
            loadStore.setIsLoadEnd();
        }
            setTimeout(() => {
                if (loadData.value[id] == false) {
                    loadData.value[id] = true
                    console.log(treeNode.label + " 加载失败！")
                    loadStore.setIsLoadEnd()
                }
            }, 5000);

            layer.allTilesLoaded.addEventListener((tile) => {
                var time2 = new Date().getTime()
                // console.log(treeNode.label,this.layerManager[id].isload,time2,time2-time)
            })

        }

        if (treeNode.setStyle != null)
            window[treeNode.setStyle].call(this, layer, treeNode);

        if (treeNode.position != null)
            this.geoapp.flyToByPosition(treeNode.position);
    }

    /**
     * 移除图层
     * @param {*} treeNode
     * @returns
     */
    removeLayer(treeNode) {
        var id = treeNode.id;
        if (this.layerManager[id] == null) return;
        if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
            this.geoapp.hide(this.layerManager[id].layer);
            return;
        }
        if (treeNode.type == "geowindem") {
            this.geoapp.removeTerrain();
            return;
        }
        if (this.layerManager[id].layer)
            this.geoapp.removeEntity(this.layerManager[id].layer);
        delete this.layerManager[id];
    }

    /**
     * 创建相机信息条
     */
    getCameraInfo() {
        var cameraInfo = Geowin3D.GwCamera.getCurrentCameraInfo(window.camera);
        return {
            lontitude: cameraInfo[0],
            latitude: cameraInfo[1],
            height: cameraInfo[2] - 1900,
            heading: cameraInfo[3],
            pitch: cameraInfo[4],
            roll: cameraInfo[5],
            cameraInfo: cameraInfo,
        };
    }


    /**
     * 裁剪并填充
     */
    undergroundSpace() {
        let globe = viewer.scene.globe;
        globe.depthTestAgainstTerrain = true;
        // var lat = 117.003968;
        // var lng = 34.816295;
        // var lat=116.997840;
        // var lng=34.909760;
        var lat = 116.992786
        var lng = 34.909266
        let position = Cesium.Cartographic.toCartesian(
            new Cesium.Cartographic.fromDegrees(lat, lng, 0)
        );
        globe.clippingPlanes = new Cesium.ClippingPlaneCollection({
            modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(position),
            planes: [
                new Cesium.Plane(new Cesium.Cartesian3(1, 0.0, 0.0), -7000.0),
                new Cesium.Plane(new Cesium.Cartesian3(-1.0, 0.0, 0.0), -7000.0),
                new Cesium.Plane(new Cesium.Cartesian3(0.0, 1.0, 0.0), -7000.0),
                new Cesium.Plane(new Cesium.Cartesian3(0.0, -1.0, 0.0), -7000.0),
            ],
            edgeWidth: 1.0,
            edgeColor: Cesium.Color.BLACK,
        });


        var plane1 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, 7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,
                }),
                fill: true, //是否填充
            },
        });
        var plane2 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_X, -7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,

                }),
                fill: true, //是否填充
            },
        });
        var plane3 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Y, 7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,

                }),
                fill: true, //是否填充
            },
        });
        var plane4 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -1500),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Y, -7000), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 3000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc,

                }),
                fill: true, //是否填充
            },
        });
        var plane5 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -3000),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(14000, 14000), //指定平面的宽度和高度。
                material: new Cesium.ImageMaterialProperty({
                    image: yc2,

                }),
                fill: true, //是否填充
            },
        });

        var plane6 = viewer.entities.add({
            name: "plane",
            position: Cesium.Cartesian3.fromDegrees(lat, lng, -4000),
            plane: {
                plane: new Cesium.Plane(Cesium.Cartesian3.UNIT_Z, 0), //指定平面的法线和距离
                dimensions: new Cesium.Cartesian2(1400000, 1400000), //指定平面的宽度和高度。
                material: new Cesium.Color(5 / 255, 31 / 255, 85 / 255),
                fill: true, //是否填充
            },
        });
        this.planeList.push(plane1)
        this.planeList.push(plane2)
        this.planeList.push(plane3)
        this.planeList.push(plane4)
        this.planeList.push(plane5)
    }

    setUndergroudSpaceShow(flag) {
        if (!flag) {
            if (this.planeList.length == 5) {
                for (var plane of this.planeList) {
                    plane.show = false
                }
            } else {
                setTimeout(() => {
                    this.setUndergroudSpaceShow(false)
                }, 200);
            }
        } else {
            for (var plane of this.planeList) {
                plane.show = true
            }
        }
    }


    /**
     * 移除图层
     * @param {*} treeNode 
     * @returns 
     */
    removeLayer(treeNode) {
        var id = treeNode.id;
        if (this.layerManager[id] == null) return;
        if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
            this.geoapp.hide(this.layerManager[id].layer);
            return;
        }
        if (treeNode.type == "geowindem") {
            this.geoapp.removeTerrain();
            return;
        }
        if (this.layerManager[id].layer)
            this.geoapp.removeEntity(this.layerManager[id].layer);
        delete this.layerManager[id];
    }

    /**
     * 设置tooltip
     */
    addToolTip(_options) {
        var bb = this.modelManager.add('TextBox2D', {
            position: [_options.lon, _options.lat, _options.height],
            height: _options.hheight, //广告牌的宽度
            width: _options.hwidth, //广告牌的高度,
            text: '测试文字',
            paddingLeft: 50,
            lineColor: 'rgba(255,255,0,1)',
            textColor: 'rgba(0,255,0,1)',
            lineWidth: 1,
            fontSize: 16,
            moveTop: 30 //数值不可以为负数
        });
        this.entities.push(bb);
    }

    /**
     * 模型旋转
     * @param {*} primitives 
     */
    trackModel(primitives) {
        this.trackManager = new Geowin3D.GwCamera.GwTrackManager(viewer.scene);
        var boundingSphere = this.getBoundingSphere(primitives);
        viewer.camera.flyToBoundingSphere(boundingSphere, {
            duration: 2
        });
        this.trackManager.trackByBoundingSphere(boundingSphere);
    }

    /**
     * 退出模型旋转
     */
    exitTrackModel() {
        this.trackManager.cancelTrack();
        this.trackManager = null;
    }

    /**
     * 获取模型包围盒
     * @param {*} primitives 
     * @returns 
     */
    getBoundingSphere(primitives) {
        var bss = [];
        for (var i = 0; i < primitives.length; i++) {
            if (primitives[i].ready)
                bss.push(primitives[i].boundingSphere);
        }

        return Geowin3D.BoundingSphere.fromBoundingSpheres(bss);
    }

    /**
     * 将单个模型上下移动一定距离
     */
    modelMovement(model) {
        const cartographic = Cesium.Cartographic.fromCartesian(
            model.boundingSphere.center
        );
        const surface = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            0.0
        );
        const offset = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            modelMoveHeight
        );
        const translation = Cesium.Cartesian3.subtract(
            offset,
            surface,
            new Cesium.Cartesian3()
        );
        model.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
    }

    /**
     * 将所有模型上下移动一定距离
     */
    modelsMovement() {
        for (var i = 0; i < this.entities.length; i++) {
            const cartographic = Cesium.Cartographic.fromCartesian(
                this.entities[i].boundingSphere.center
            );
            const surface = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                0.0
            );
            const offset = Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                modelMoveHeight
            );
            const translation = Cesium.Cartesian3.subtract(
                offset,
                surface,
                new Cesium.Cartesian3()
            );
            this.entities[i].modelMatrix = Cesium.Matrix4.fromTranslation(translation);

        }
    }
    updateSkyBox(){
        //天空和设置
        var imagePaths = [
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png',
            '/bg/new2.png'
        ];
        Geowin3D.GwApp.setSkyBox(viewer.scene, {
            imagePaths: imagePaths
        });
        Geowin3D.GwApp.setSkyBoxOnGround(viewer.scene, imagePaths);
        viewer.scene.backgroundColor = new Cesium.Color(0.0, 0.0, 0.0, 0.0);
    }
}
export default Earth;