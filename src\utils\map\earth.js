import yc from '@/assets/img/geology/yc.png'
import yc2 from '@/assets/img/geology/yc2.png'
import modelMoveHeight from "@/utils/map/modelMoveHeight"
import UnderPlane from "@/utils/map/underPlane";
import {
    storeToRefs
} from "pinia";
import {
    useLoadStore
} from "@/store/load";

class Earth {
    layerManager = {}
    entities = []
    planeList = []

    constructor(_container) {
        // 初始化store
        this.loadStore = useLoadStore();
        const { loadData, isLoadEnd } = storeToRefs(this.loadStore);
        this.loadData = loadData;
        this.isLoadEnd = isLoadEnd;
        
        this.initEarth(_container);
    }

    /**
     * 初始化加载地球
     * <AUTHOR>
     */
    initEarth(_container) {
        // 使用Cesium创建viewer
        console.log('开始初始化Earth with Cesium');

        try {
            window.viewer = this.viewer = new window.Cesium.Viewer(_container, {
                animation: false,
                baseLayerPicker: false,
                fullscreenButton: false,
                geocoder: false,
                homeButton: false,
                infoBox: false,
                sceneModePicker: false,
                selectionIndicator: false,
                timeline: false,
                navigationHelpButton: false,
                scene3DOnly: true,
                shouldAnimate: false,
                contextOptions: {
                    webgl: {
                        alpha: true,
                        depth: true,
                        stencil: true,
                        antialias: true,
                        premultipliedAlpha: true,
                        preserveDrawingBuffer: true,
                        failIfMajorPerformanceCaveat: false
                    },
                    allowTextureFilterAnisotropic: false
                },
                targetFrameRate: 60,
                resolutionScale: 1.0,
                orderIndependentTranslucency: false,
                creditContainer: document.createElement("div"),
                dataSources: new window.Cesium.DataSourceCollection(),
                terrainProvider: window.Cesium.createWorldTerrain(),
                imageryProvider: new window.Cesium.OpenStreetMapImageryProvider({
                    url: 'https://a.tile.openstreetmap.org/'
                }),
                skyBox: new window.Cesium.SkyBox({
                    sources: {
                        positiveX: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
                        negativeX: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
                        positiveY: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
                        negativeY: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
                        positiveZ: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
                        negativeZ: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=='
                    }
                }),
                skyAtmosphere: new window.Cesium.SkyAtmosphere()
            });

            console.log('Earth初始化完成');
            this.initEarthConfig();

        } catch (error) {
            console.error('Earth初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化地球配置
     */
    initEarthConfig() {
        const viewer = this.viewer;
        
        // 设置相机位置到中国
        viewer.camera.setView({
            destination: window.Cesium.Cartesian3.fromDegrees(116.3974, 39.9093, 15000000.0)
        });

        // 初始化模型管理工具
        this.initModelManagerTools();
        
        // 设置光照
        if (viewer.scene.light) {
            viewer.scene.light = new window.Cesium.DirectionalLight({
                direction: viewer.scene.camera.directionWC,
            });
            viewer.scene.camera.changed.addEventListener(function (scene, time) {
                viewer.scene.light.direction = window.Cesium.Cartesian3.clone(
                    viewer.scene.camera.directionWC,
                    viewer.scene.light.direction
                );
            });
        }

        // 禁用默认的双击行为
        viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(window.Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
        
        // 设置地形
        viewer.terrainProvider = window.Cesium.createWorldTerrain();
        
        // 设置全局变量
        window.camera = viewer.camera;
        
        console.log('Earth配置完成');
    }

    /**
     * 初始化模型管理工具
     */
    initModelManagerTools() {
        console.log('模型管理工具已简化，原Geowin3D功能不可用');
        this.modelManager = {
            // 简化的模型管理器
            loadModel: (options) => {
                console.log('简化的模型加载:', options);
                return null;
            },
            removeModel: (model) => {
                console.log('简化的模型移除:', model);
            },
            clear: () => {
                console.log('简化的模型清理');
            }
        };
        return this.modelManager;
    }

    /**
     * 获取viewer
     */
    getViewer() {
        return this.viewer;
    }

    /**
     * 加载图层
     * @param {*} treeNode 
     * @returns 
     */
    loadLayer(treeNode) {
        console.log('加载图层:', treeNode);
        
        const id = treeNode.id;
        let layer = null;

        try {
            // 根据图层类型加载不同的图层
            switch (treeNode.type) {
                case "tileset":
                case "tileset_url":
                    layer = this.loadTileset(treeNode);
                    break;
                case "imagery":
                    layer = this.loadImagery(treeNode);
                    break;
                case "terrain":
                    layer = this.loadTerrain(treeNode);
                    break;
                case "geojson":
                    layer = this.loadGeoJSON(treeNode);
                    break;
                case "heatmap":
                    layer = this.loadHeatmap(treeNode);
                    break;
                default:
                    console.warn('未知图层类型:', treeNode.type);
                    break;
            }

            // 存储图层信息
            this.layerManager[id] = {
                treeNode: treeNode,
                layer: layer,
                features: [],
                isload: true
            };

            // 设置加载状态
            if (this.loadData) {
                this.loadData.value[id] = true;
                this.loadStore.setIsLoadEnd();
            }

        } catch (error) {
            console.error('图层加载失败:', error, treeNode);
            if (this.loadData) {
                this.loadData.value[id] = false;
            }
        }

        return layer;
    }

    /**
     * 加载3D Tileset
     */
    loadTileset(treeNode) {
        const url = treeNode.url || treeNode.layer;
        console.log('加载3D Tileset:', url);
        
        try {
            const tileset = new window.Cesium.Cesium3DTileset({
                url: url,
                maximumScreenSpaceError: 16,
                maximumNumberOfLoadedTiles: 1000
            });

            this.viewer.scene.primitives.add(tileset);

            tileset.readyPromise.then((model) => {
                console.log('3D Tileset加载完成:', treeNode.label || treeNode.typeName);
            }).catch(error => {
                console.error('3D Tileset加载失败:', error);
            });

            return tileset;
        } catch (error) {
            console.error('3D Tileset创建失败:', error);
            return null;
        }
    }

    /**
     * 加载影像图层
     */
    loadImagery(treeNode) {
        const url = treeNode.url || treeNode.layer;
        console.log('加载影像图层:', url);
        
        try {
            const imageryProvider = new window.Cesium.UrlTemplateImageryProvider({
                url: url
            });

            const imageryLayer = this.viewer.imageryLayers.addImageryProvider(imageryProvider);
            return imageryLayer;
        } catch (error) {
            console.error('影像图层加载失败:', error);
            return null;
        }
    }

    /**
     * 加载地形
     */
    loadTerrain(treeNode) {
        const url = treeNode.url || treeNode.layer;
        console.log('加载地形:', url);
        
        try {
            const terrainProvider = new window.Cesium.CesiumTerrainProvider({
                url: url
            });

            this.viewer.terrainProvider = terrainProvider;
            return terrainProvider;
        } catch (error) {
            console.error('地形加载失败:', error);
            return null;
        }
    }

    /**
     * 加载GeoJSON
     */
    loadGeoJSON(treeNode) {
        const url = treeNode.url || treeNode.layer;
        console.log('加载GeoJSON:', url);
        
        try {
            const dataSource = window.Cesium.GeoJsonDataSource.load(url);
            this.viewer.dataSources.add(dataSource);
            return dataSource;
        } catch (error) {
            console.error('GeoJSON加载失败:', error);
            return null;
        }
    }

    /**
     * 加载热力图
     */
    loadHeatmap(treeNode) {
        // 热力图功能暂时简化
        console.log('热力图功能已简化', treeNode);
        return null;
    }

    /**
     * 移除图层
     * @param {*} treeNode
     * @returns
     */
    removeLayer(treeNode) {
        var id = treeNode.id;
        if (this.layerManager[id] == null) return;
        
        try {
            const layerInfo = this.layerManager[id];
            const layer = layerInfo.layer;
            
            if (layer) {
                if (treeNode.type == "tileset" || treeNode.type == "tileset_url") {
                    this.viewer.scene.primitives.remove(layer);
                } else if (treeNode.type == "imagery") {
                    this.viewer.imageryLayers.remove(layer);
                } else if (treeNode.type == "geojson") {
                    this.viewer.dataSources.remove(layer);
                }
            }
            
            delete this.layerManager[id];
            console.log('图层移除成功:', treeNode.label);
        } catch (error) {
            console.error('图层移除失败:', error);
        }
    }

    /**
     * 获取相机信息
     */
    getCameraInfo() {
        if (!this.viewer || !this.viewer.camera) {
            return {
                longitude: 116.3974,
                latitude: 39.9093,
                height: 1000,
                heading: 0,
                pitch: -90,
                roll: 0,
                cameraInfo: [116.3974, 39.9093, 1000, 0, -90, 0]
            };
        }

        const camera = this.viewer.camera;
        const position = camera.positionCartographic;

        return {
            longitude: window.Cesium.Math.toDegrees(position.longitude),
            latitude: window.Cesium.Math.toDegrees(position.latitude),
            height: position.height,
            heading: window.Cesium.Math.toDegrees(camera.heading),
            pitch: window.Cesium.Math.toDegrees(camera.pitch),
            roll: window.Cesium.Math.toDegrees(camera.roll),
            cameraInfo: [
                window.Cesium.Math.toDegrees(position.longitude),
                window.Cesium.Math.toDegrees(position.latitude),
                position.height,
                window.Cesium.Math.toDegrees(camera.heading),
                window.Cesium.Math.toDegrees(camera.pitch),
                window.Cesium.Math.toDegrees(camera.roll)
            ]
        };
    }

    /**
     * 开挖功能
     */
    excavate(lat, lng) {
        console.log('开挖功能已简化，原Geowin3D功能不可用');
        try {
            const viewer = this.viewer;
            const globe = viewer.scene.globe;

            // 简化的开挖实现
            let position = window.Cesium.Cartographic.toCartesian(
                new window.Cesium.Cartographic.fromDegrees(lat, lng, 0)
            );
            globe.clippingPlanes = new window.Cesium.ClippingPlaneCollection({
                modelMatrix: window.Cesium.Transforms.eastNorthUpToFixedFrame(position),
                planes: [
                    new window.Cesium.Plane(new window.Cesium.Cartesian3(1, 0.0, 0.0), -7000.0),
                    new window.Cesium.Plane(new window.Cesium.Cartesian3(-1.0, 0.0, 0.0), -7000.0),
                    new window.Cesium.Plane(new window.Cesium.Cartesian3(0.0, 1.0, 0.0), -7000.0),
                    new window.Cesium.Plane(new window.Cesium.Cartesian3(0.0, -1.0, 0.0), -7000.0),
                ],
                edgeWidth: 1.0,
                edgeColor: window.Cesium.Color.BLACK,
            });

            // 添加地下平面
            var underPlane = new UnderPlane(viewer, window.Cesium.Cartesian3.fromDegrees(lat, lng, -3500 + 1900));

            // 添加岩层平面
            viewer.entities.add({
                position: window.Cesium.Cartesian3.fromDegrees(lat, lng, -1600),
                plane: {
                    plane: new window.Cesium.Plane(window.Cesium.Cartesian3.UNIT_Z, 0),
                    dimensions: new window.Cesium.Cartesian2(14000, 3000),
                    material: new window.Cesium.ImageMaterialProperty({
                        image: yc,
                    }),
                }
            });

            viewer.entities.add({
                position: window.Cesium.Cartesian3.fromDegrees(lat, lng, -2600),
                plane: {
                    plane: new window.Cesium.Plane(window.Cesium.Cartesian3.UNIT_Z, 0),
                    dimensions: new window.Cesium.Cartesian2(14000, 3000),
                    material: new window.Cesium.ImageMaterialProperty({
                        image: yc2,
                    }),
                }
            });

        } catch (error) {
            console.error('开挖功能执行失败:', error);
        }
    }

    /**
     * 模型跟踪
     */
    trackModel(primitives) {
        console.log('模型跟踪功能已简化，原Geowin3D功能不可用');
        try {
            // 简化的跟踪管理器
            this.trackManager = {
                trackByBoundingSphere: (boundingSphere) => {
                    console.log('简化的包围球跟踪', boundingSphere);
                },
                cancelTrack: () => {
                    console.log('取消跟踪');
                }
            };
        } catch (error) {
            console.error('模型跟踪失败:', error);
        }
    }

    /**
     * 退出模型跟踪
     */
    exitTrackModel() {
        console.log('退出模型跟踪');
        if (this.trackManager) {
            this.trackManager.cancelTrack();
        }
    }

    /**
     * 获取包围球
     */
    getBoundingSphere(primitives) {
        console.log('获取包围球功能已简化');
        try {
            if (primitives && primitives.boundingSphere) {
                return primitives.boundingSphere;
            }
            return null;
        } catch (error) {
            console.error('获取包围球失败:', error);
            return null;
        }
    }

    /**
     * 模型移动
     */
    modelMovement(model) {
        console.log('模型移动功能已简化，原Geowin3D功能不可用');
        try {
            if (model && typeof modelMoveHeight === 'function') {
                modelMoveHeight(model);
            }
        } catch (error) {
            console.error('模型移动失败:', error);
        }
    }

    /**
     * 批量模型移动
     */
    modelsMovement() {
        console.log('批量模型移动功能已简化，原Geowin3D功能不可用');
        try {
            // 简化实现
            Object.values(this.layerManager).forEach(layerInfo => {
                if (layerInfo.layer) {
                    this.modelMovement(layerInfo.layer);
                }
            });
        } catch (error) {
            console.error('批量模型移动失败:', error);
        }
    }

    /**
     * 更新天空盒
     */
    updateSkyBox() {
        console.log('天空盒功能已简化，原Geowin3D功能不可用');
        try {
            if (this.viewer && this.viewer.scene) {
                // 设置背景颜色
                this.viewer.scene.backgroundColor = window.Cesium.Color.fromCssColorString('#001122');
                console.log('天空盒背景颜色已设置');
            }
        } catch (error) {
            console.error('天空盒设置失败', error);
        }
    }
}

export default Earth;
