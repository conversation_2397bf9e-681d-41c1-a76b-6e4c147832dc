<!doctype html>

<head>
  <title>CodeMirror: Code Folding Demo</title>
  <meta charset="utf-8"/>
  <link rel=stylesheet href="../doc/docs.css">

  <style>
    .some-css {
      color: red;
      line-height: 2;
    }
  </style>

  <link rel="stylesheet" href="../lib/codemirror.css">
  <link rel="stylesheet" href="../addon/fold/foldgutter.css" />
  <script src="../lib/codemirror.js"></script>
  <script src="../addon/fold/foldcode.js"></script>
  <script src="../addon/fold/foldgutter.js"></script>
  <script src="../addon/fold/brace-fold.js"></script>
  <script src="../addon/fold/xml-fold.js"></script>
  <script src="../addon/fold/indent-fold.js"></script>
  <script src="../addon/fold/markdown-fold.js"></script>
  <script src="../addon/fold/comment-fold.js"></script>
  <script src="../mode/javascript/javascript.js"></script>
  <script src="../mode/xml/xml.js"></script>
  <script src="../mode/css/css.js"></script>
  <script src="../mode/htmlmixed/htmlmixed.js"></script>
  <script src="../mode/python/python.js"></script>
  <script src="../mode/markdown/markdown.js"></script>
  <style type="text/css">
    .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
  </style>
</head>

<body>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Code Folding</a>
  </ul>
</div>

<article>
  <h2>Code Folding Demo</h2>
  <form>
    <div style="max-width: 50em; margin-bottom: 1em">JavaScript:<br>
    <textarea id="code" name="code"></textarea></div>
    <div style="max-width: 50em; margin-bottom: 1em">HTML:<br>
    <textarea id="code-html" name="code-html"></textarea></div>
    <div style="max-width: 50em">Python:<br>
    <textarea id="code-python" name="code">
def foo():
  do_some_stuff()
  here
  return None

class Bar:
  __init__(self):
    if True:
      print("True")
    else:
      print("False")

  this_code_makes_no_sense():
    pass

# A comment</textarea></div>
    <div style="max-width: 50em">Markdown:<br>
    <textarea id="code-markdown" name="code"></textarea></div>
  </form>
  <script id="script">
/*
 * Demonstration of code folding
 */
window.onload = function() {
  var te = document.getElementById("code");
  var sc = document.getElementById("script");
  te.value = (sc.textContent || sc.innerText || sc.innerHTML).replace(/^\s*/, "");
  sc.innerHTML = "";
  var te_html = document.getElementById("code-html");
  te_html.value = document.documentElement.innerHTML;
  var te_python = document.getElementById("code-python");
  var te_markdown = document.getElementById("code-markdown");
  te_markdown.value = "# Foo\n## Bar\n\nblah blah\n\n## Baz\n\nblah blah\n\n# Quux\n\nblah blah\n"

  window.editor = CodeMirror.fromTextArea(te, {
    mode: "javascript",
    lineNumbers: true,
    lineWrapping: true,
    extraKeys: {"Ctrl-Q": function(cm){ cm.foldCode(cm.getCursor()); }},
    foldGutter: true,
    gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"]
  });
  editor.foldCode(CodeMirror.Pos(13, 0));

  window.editor_html = CodeMirror.fromTextArea(te_html, {
    mode: "text/html",
    lineNumbers: true,
    lineWrapping: true,
    extraKeys: {"Ctrl-Q": function(cm){ cm.foldCode(cm.getCursor()); }},
    foldGutter: true,
    gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"]
  });
  editor_html.foldCode(CodeMirror.Pos(0, 0));
  editor_html.foldCode(CodeMirror.Pos(34, 0));

  window.editor_python = CodeMirror.fromTextArea(te_python, {
    mode: "python",
    lineNumbers: true,
    extraKeys: {"Ctrl-Q": function(cm){ cm.foldCode(cm.getCursor()); }},
    foldGutter: true,
    gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"]
  });

  window.editor_markdown = CodeMirror.fromTextArea(te_markdown, {
    mode: "markdown",
    lineNumbers: true,
    lineWrapping: true,
    extraKeys: {"Ctrl-Q": function(cm){ cm.foldCode(cm.getCursor()); }},
    foldGutter: true,
    gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"]
  });
};
  </script>
</article>
</body>
