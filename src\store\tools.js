import { defineStore } from 'pinia'
export const useToolsStore = defineStore({
  // 模块ID
  id: 'toolIndex',
  state: () => {
    return {
      toolIndex:1,
      layerArr:[],
      disableTreeId:0,
      dataTree:undefined
    }
  },
  actions:{
  	setShow(toolIndex){
  		this.toolIndex = toolIndex;
  	},
    setLayerArr(layerList){
      this.layerArr = this.layerArr.push(layerList);
    },
    setDisableTreeId(disableTreeId){
      this.disableTreeId = disableTreeId;
    },
  }
})
