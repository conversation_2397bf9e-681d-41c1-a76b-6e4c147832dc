import {
    useToolsStore
} from "@/store/tools";
const toolsStore = useToolsStore();
class InitModelShow {

    constructor() {

    }
    initShow() {

        var labels;
        var underPlane=window.underPlane
        underPlane.resetPosition()
        switch (window.location.hash) {
            case '#/index/Home':
                labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '回采工作面', '陷落柱', '积水区', '7煤采空区','8煤采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层', '瞬变电磁顶板','瞬变电磁顺层','瞬变电磁底板', '电法异常区', '坑透异常区', '勘探钻孔'])
                break
            case '#/index/Monitoring':
                labels = window.layersManager.getIdsByLabels(["井巷模型",
                    "回采工作面",
                ])
                break
            case '#/index/Tunnel':
                labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '回采工作面', '陷落柱', '积水区', '7煤采空区','8煤采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层',  '电法异常区', '坑透异常区','瞬变电磁顶板','瞬变电磁顺层','瞬变电磁底板'])
                
                underPlane.setPosition(Cesium.Cartesian3.fromDegrees(116.992786, 34.909266, -1200 + 1900))
                break
            case '#/index/Deduction/DTunnel':
                labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '回采工作面', '陷落柱', '积水区', '7煤采空区','8煤采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层',  '电法异常区', '坑透异常区','瞬变电磁顶板','瞬变电磁顺层','瞬变电磁底板'])
               
                underPlane.setPosition(Cesium.Cartesian3.fromDegrees(116.992786, 34.909266, -1200 + 1900))
                break
            case '#/index/Deduction':
                labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '回采工作面', '陷落柱', '积水区', '7煤采空区','8煤采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层'])
                break
            case '#/index/Stoping':
                labels = window.layersManager.getIdsByLabels(['7号煤层', '8号煤层','井巷模型', '回采工作面',  '电法异常区', '坑透异常区','瞬变电磁顶板','瞬变电磁顺层','瞬变电磁底板'])
                
                underPlane.setPosition(Cesium.Cartesian3.fromDegrees(116.992786, 34.909266, -1350 + 1900))
                break
            case '#/index/Deduction/DStoping':
                labels = window.layersManager.getIdsByLabels(['7号煤层', '8号煤层','井巷模型', '回采工作面',  '电法异常区', '坑透异常区','瞬变电磁顶板','瞬变电磁顺层','瞬变电磁底板'])
                
                underPlane.setPosition(Cesium.Cartesian3.fromDegrees(116.992786, 34.909266, -1350 + 1900))
                break
            default:
                labels = window.layersManager.getIdsByLabels(['断层模型', '7号煤层', '8号煤层', '井巷模型', '回采工作面', '陷落柱', '积水区', '7煤采空区','8煤采空区', '第四系', '上侏罗～下白垩统', '二叠系地层', '石炭系地层',  '电法异常区', '坑透异常区', '勘探钻孔'])
        }

        toolsStore.$patch((state) => {
            var dataTree = state.dataTree
            dataTree.setCheckedKeys(labels);
        });


    }


    YCQshow(show) {
        var sid = window.ycq.getIndexByLabel('瞬变电磁顶板')
        window.ycq.show(sid, show)
        var sid = window.ycq.getIndexByLabel('瞬变电磁顺层')
        window.ycq.show(sid, show)
        var sid = window.ycq.getIndexByLabel('瞬变电磁底板')
        window.ycq.show(sid, show)
    }
}
export default InitModelShow