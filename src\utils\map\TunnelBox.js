/**
 * 透明掘进左下角BOX管理
 */
import {
  ref,
  toRaw
} from "vue";
import FileSaver from "file-saver";
class TunnelBox {

  //加载物探图贴图
  loadImage(item) {
    var url = item.imgUrl;
    var position = item.imgInfo[0];
    var Radians = item.imgInfo[1];
    var positionList = [];
    for (var p of position) {
      positionList.push(Cesium.Cartesian3.fromDegrees(...p));
    }
    var primitive = loadImages(url, positionList);
    this.imageList.value.push(primitive);
    item.imageIdx = this.imageList.value.length - 1;
    viewer.scene.forceRender();

    function loadImages(url, position) {
      let polygon = Cesium.PolygonGeometry.fromPositions({
        positions: position,
        stRotation: Cesium.Math.toRadians(Radians),
        perPositionHeight: true,
      });
      const primitive = new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({
          geometry: polygon,
        }),
        appearance: new Cesium.MaterialAppearance({
          faceForward: true,
          material: new Cesium.Material({
            fabric: {
              type: "Image",
              uniforms: {
                image: url,
              },
            },
          }),
        }),
        asynchronous: false,
        releaseMaterials: true,
      });

      viewer.scene.primitives.add(primitive);
      return primitive;
    }
  }

  //物探图显隐
  showImage(imageIdx, flag) {
    this.imageList.value[imageIdx].show = flag;
  }

  //下载操作
  downloadData(item) {
    var fileName = item.dwgUrl.split("/");
    fileName = fileName[fileName.length - 1];
    FileSaver.saveAs(item.dwgUrl, fileName);
  };

  downLoadDWG = (item) => {
    if (!item.dwgUrl) {
      // alert('该资源不存在！')
      return
    }
    var fileName = item.dwgUrl.split("/");
    fileName = fileName[fileName.length - 1];
    FileSaver.saveAs(item.dwgUrl, fileName);
  };
  downLoadDoc = (item) => {
    if (!item.docUrl) {
      // alert('该资源不存在！')
      return
    }
    var fileName = item.docUrl.split("/");
    fileName = fileName[fileName.length - 1];
    FileSaver.saveAs(item.docUrl, fileName);
  };


  //移除物探图内容
  remove() {
    for (var img of this.imageList.value) {
      viewer.scene.primitives.remove(toRaw(img));
    }
  }

  imageList = ref([]);

  tunnelBoxIndex = ref(0);

  tunnelBoxTitleList = ref(["掘进探放水", "孔中瞬变电磁", "电法"]);

  tunnelBoxInfoIist = ref([
    //掘进探放水
    [{
        title: "掘进探放水",
        check: false,
        index: 7431,
      }
      
    ],
    //孔中瞬变电磁
    [{
      title: "瞬变电磁",
      check: false,
      imageIdx: -1,
      index: 1,
      dwgUrl: "/TMJJ/doc/透明掘进/物探原始数据/7432工作面溜子道L16点前72.5m处超前探成果图2022.9.14 (1).dwg",
      docUrl:"/TMJJ/doc/透明掘进/物探原始数据/咨询徐庄7432溜子道L16点前72.5m处超前探报告 (1).doc",
      imgUrl: "/wtt/wtt.png",
      imgInfo: [
        [
          [
            116.95894611601241, 34.853256575177674, 1030.8193866189226 + 8 + 7
          ],
          [116.95935686428749, 34.85345234307486, 1030.3326654920625 + 8 + 7],
          [116.9596031118692, 34.853050773143245, 1055.6886364632994 - 14 + 7],
          [116.95922769148498, 34.852865505143264, 1055.633304579402 - 14 + 7]
        ],
        [45 + 180 + 5 + 10 - 2],
      ],
    }, ],
    //电法
    [{
        title: "电法1",
        check: false,
        imageIdx: -1,
        index: 7431,
        // dwgUrl: "/TMJJ/DWG/掘进规划用平面图.dwg",
        // imgUrl: "/wtt/DF1.png",
        imgInfo: [
          [
            [116.96890597149748, 34.85800925679701, 1057.131254735462 + 8],
            [116.97006237346214, 34.85672552902646, 1133.1225978605808 + 8],
            [116.98379106422597, 34.86502142455634, 1125.024220960616 + 8],
            [116.98244088576234, 34.86617760205918, 1096.0826687463912 + 8]
          ],
          [-36],
        ],
      },

      {
        title: "电法2",
        check: false,
        imageIdx: -1,
        index: 7431,
        // dwgUrl: "/TMJJ/DWG/掘进规划用平面图.dwg",
        // imgUrl: "/wtt/DF2.png",
        imgInfo: [
          [
            [116.96890597149748, 34.85800925679701, 1057.131254735462 + 8],
            [116.97006237346214, 34.85672552902646, 1133.1225978605808 + 8],
            [116.98379106422597, 34.86502142455634, 1125.024220960616 + 8],
            [116.98244088576234, 34.86617760205918, 1096.0826687463912 + 8]
          ],
          [-36],
        ],
      },

      {
        title: "电法3",
        check: false,
        imageIdx: -1,
        index: 7431,
        // dwgUrl: "/TMJJ/DWG/掘进规划用平面图.dwg",
        // imgUrl: "/wtt/DF3.png",
        imgInfo: [
          [
            [116.96890597149748, 34.85800925679701, 1057.131254735462 + 8],
            [116.97006237346214, 34.85672552902646, 1133.1225978605808 + 8],
            [116.98379106422597, 34.86502142455634, 1125.024220960616 + 8],
            [116.98244088576234, 34.86617760205918, 1096.0826687463912 + 8]
          ],
          [-36],
        ],
      }
    ],
  ]);
}

export default TunnelBox;