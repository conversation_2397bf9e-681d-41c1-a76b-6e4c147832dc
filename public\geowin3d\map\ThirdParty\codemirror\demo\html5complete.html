<!doctype html>

<head>
  <title>CodeMirror: HTML completion demo</title>
  <meta charset="utf-8"/>
  <link rel=stylesheet href="../doc/docs.css">

  <link rel="stylesheet" href="../lib/codemirror.css">
  <link rel="stylesheet" href="../addon/hint/show-hint.css">
  <script src="../lib/codemirror.js"></script>
  <script src="../addon/hint/show-hint.js"></script>
  <script src="../addon/hint/xml-hint.js"></script>
  <script src="../addon/hint/html-hint.js"></script>
  <script src="../mode/xml/xml.js"></script>
  <script src="../mode/javascript/javascript.js"></script>
  <script src="../mode/css/css.js"></script>
  <script src="../mode/htmlmixed/htmlmixed.js"></script>
  <style type="text/css">
    .CodeMirror {border-top: 1px solid #888; border-bottom: 1px solid #888;}
  </style>
</head>

<body>
  <div id=nav>
    <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

    <ul>
      <li><a href="../index.html">Home</a>
      <li><a href="../doc/manual.html">Manual</a>
      <li><a href="https://github.com/codemirror/codemirror">Code</a>
    </ul>
    <ul>
      <li><a class=active href="#">HTML completion</a>
    </ul>
  </div>

  <article>
    <h2>HTML completion demo</h2>

    <p>Shows the <a href="xmlcomplete.html">XML completer</a>
    parameterized with information about the tags in HTML.
    Press <strong>ctrl-space</strong> to activate completion.</p>

    <div id="code"></div>

    <script type="text/javascript">
      window.onload = function() {
        editor = CodeMirror(document.getElementById("code"), {
          mode: "text/html",
          extraKeys: {"Ctrl-Space": "autocomplete"},
          value: document.documentElement.innerHTML
        });
      };
    </script>
  </article>
</body>
