class Tooltip {

    worldPosition = undefined;
    tipContainer = undefined;
    tipTitle = "";
    tipText = "";
    tipTranslateX = 0;
    tipTranslateY = 0;
    tipShow = false;

    constructor(_viewer) {
        this.viewer = _viewer;
    }

    show(position, prop) {
        // this.viewer.camera.changed.addEventListener(this.onCameraChanged);
        this.tipShow = true;
        // 将当前点击的屏幕坐标转换为世界坐标，并保存（用于更新tooltip的位置）
        var worldPosition = this.viewer.scene.pickPosition(position);
        this.tipText = "";
        for (let key in prop) {
            if (!this.tipText) {
                this.tipText += key + "&nbsp;&nbsp;&nbsp;&nbsp" + prop[key];
            } else {
                this.tipText += "<br/>" + key + "&nbsp;&nbsp;&nbsp;&nbsp" + prop[key];
            }
        }
        this.updateTooltip(position);
        this.viewer.scene.requestRender();
    }

    createTooltip() {
        var container = document.createElement("div");
        container.setAttribute("class", "monomer-floor-tooltip_container");
        container.style.display = "none";
        container.style.position = "absolute";
        container.style.top = "0px";
        container.style.left = "0px";
        container.style.boxSizing = "border-box";

        var wrapper = document.createElement("div");
        wrapper.setAttribute("class", "monomer-floor-tooltip_wrapper");
        wrapper.style.maxWidth = "500px";
        wrapper.style.minWidth = "50px";
        wrapper.style.maxHeight = "300px";
        wrapper.style.minHeight = "28px";
        wrapper.style.padding = "12px 16px";
        wrapper.style.textAlign = "left";
        wrapper.style.borderRadius = "3px";
        wrapper.style.boxSizing = "border-box";
        wrapper.style.backgroundColor = "rgba(63, 72, 84, 0.9)";
        wrapper.style.overflowY = "auto";

        var close = document.createElement("div");
        close.setAttribute("class", "monomer-floor-tooltip_close");
        close.style.position = "absolute";
        close.style.top = "4px";
        close.style.right = "4px";
        close.style.width = "14px";
        close.style.height = "14px";
        close.style.font = "bold 16px/14px Tahoma, Verdana, sans-serif";
        close.style.color = "#fff";
        close.style.cursor = "pointer";
        close.innerText = "×";

        close.addEventListener("mouseover", function() {
            this.style.color = "#409eff";
        });
        close.addEventListener("mouseout", function() {
            this.style.color = "#fff";
        });
        close.addEventListener("click", function() {
            if (this.tipContainer && this.tipShow) {
                this.tipContainer.style.display = "none";
                this.tipShow = false;
                viewer.scene.requestRender();
            }
        });

        var header = document.createElement("div");
        header.setAttribute("class", "monomer-floor-tooltip_header");
        header.style.fontSize = "16px";
        header.style.color = "#fff";
        header.style.lineHeight = "24px";
        header.style.paddingBottom = "6px";
        header.style.borderBottom = "1px solid rgba(255, 255, 255, 0.2)";
        header.innerText = this.tipTitle;

        var content = document.createElement("div");
        content.setAttribute("class", "monomer-floor-tooltip_content");
        content.style.fontSize = "13px";
        content.style.lineHeight = "24px";
        content.style.color = "#e6e6e6";

        var bottom = document.createElement("div");
        bottom.setAttribute("class", "monomer-floor-tooltip_bottom");
        bottom.style.width = "0";
        bottom.style.height = "0";
        bottom.style.margin = "0 auto";
        bottom.style.padding = "0px";
        bottom.style.borderTop = "10px solid rgba(63, 72, 84, 0.9)";
        bottom.style.borderLeft = "10px solid transparent";
        bottom.style.borderRight = "10px solid transparent";
        bottom.style.borderBottom = "none";
        bottom.style.backgroundColor = "transparent";

        container.appendChild(wrapper);
        container.appendChild(close);
        container.appendChild(bottom);

        wrapper.appendChild(header);
        wrapper.appendChild(content);

        return container;
    }

    updateTooltip(position) {
        if (!this.tipContainer) {
            this.tipContainer = this.createTooltip();
            this.viewer.container.appendChild(this.tipContainer);
        }

        this.tipContainer.querySelector(".monomer-floor-tooltip_header").innerHTML = this.tipTitle;
        this.tipContainer.querySelector(".monomer-floor-tooltip_content").innerHTML = this.tipText;

        if (!this.tipShow) {
            this.tipContainer.style.display = "block";
            this.tipShow = true;
        }

        position && this.updateOffset(position);
    }

    updateOffset(position) {
        var width = window.getComputedStyle(this.tipContainer).getPropertyValue("width");
        var height = window.getComputedStyle(this.tipContainer).getPropertyValue("height");

        this.tipTranslateX = position.x - parseInt(width) / 2;
        this.tipTranslateY = position.y - parseInt(height);

        this.tipContainer.style.transform = `translate(${this.tipTranslateX}px, ${this.tipTranslateY}px)`;
    }


    disable() {
        if (this.tipShow && this.tipContainer) {
            this.tipContainer.style.display = "none";
            this.tipShow = false;
            this.viewer.camera.changed.removeEventListener(this.onCameraChanged);
        }
    }
    onCameraChanged() {
        if (!this.tipContainer || !this.worldPosition || !this.tipShow) {
            return;
        }

        let position = this.viewer.scene.cartesianToCanvasCoordinates(this.worldPosition, new Geowin3D.Cartesian2());

        position && this.updateOffset(this.position);
    }

}

export default Tooltip