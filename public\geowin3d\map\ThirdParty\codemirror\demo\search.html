<!doctype html>

<title>CodeMirror: Search/Replace Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<link rel="stylesheet" href="../addon/dialog/dialog.css">
<link rel="stylesheet" href="../addon/search/matchesonscrollbar.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/xml/xml.js"></script>
<script src="../addon/dialog/dialog.js"></script>
<script src="../addon/search/searchcursor.js"></script>
<script src="../addon/search/search.js"></script>
<script src="../addon/scroll/annotatescrollbar.js"></script>
<script src="../addon/search/matchesonscrollbar.js"></script>
<script src="../addon/search/jump-to-line.js"></script>
<style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      dt {font-family: monospace; color: #666;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Search/Replace</a>
  </ul>
</div>

<article>
<h2>Search/Replace Demo</h2>
<form><textarea id="code" name="code">
<dl>
  <dt id="option_indentWithTabs"><code><strong>indentWithTabs</strong>: boolean</code></dt>
  <dd>Whether, when indenting, the first N*<code>tabSize</code>
  spaces should be replaced by N tabs. Default is false.</dd>

  <dt id="option_electricChars"><code><strong>electricChars</strong>: boolean</code></dt>
  <dd>Configures whether the editor should re-indent the current
  line when a character is typed that might change its proper
  indentation (only works if the mode supports indentation).
  Default is true.</dd>

  <dt id="option_specialChars"><code><strong>specialChars</strong>: RegExp</code></dt>
  <dd>A regular expression used to determine which characters
  should be replaced by a
  special <a href="#option_specialCharPlaceholder">placeholder</a>.
  Mostly useful for non-printing special characters. The default
  is <code>/[\u0000-\u0019\u00ad\u200b\u2028\u2029\ufeff]/</code>.</dd>
  <dt id="option_specialCharPlaceholder"><code><strong>specialCharPlaceholder</strong>: function(char) → Element</code></dt>
  <dd>A function that, given a special character identified by
  the <a href="#option_specialChars"><code>specialChars</code></a>
  option, produces a DOM node that is used to represent the
  character. By default, a red dot (<span style="color: red">•</span>)
  is shown, with a title tooltip to indicate the character code.</dd>

  <dt id="option_rtlMoveVisually"><code><strong>rtlMoveVisually</strong>: boolean</code></dt>
  <dd>Determines whether horizontal cursor movement through
  right-to-left (Arabic, Hebrew) text is visual (pressing the left
  arrow moves the cursor left) or logical (pressing the left arrow
  moves to the next lower index in the string, which is visually
  right in right-to-left text). The default is <code>false</code>
  on Windows, and <code>true</code> on other platforms.</dd>
</dl>
</textarea></form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  mode: "text/html",
  lineNumbers: true,
  extraKeys: {"Alt-F": "findPersistent"}
});
</script>

    <p>Demonstration of primitive search/replace functionality. The
    keybindings (which can be configured with custom keymaps) are:</p>
    <dl>
      <dt>Ctrl-F / Cmd-F</dt><dd>Start searching</dd>
      <dt>Ctrl-G / Cmd-G</dt><dd>Find next</dd>
      <dt>Shift-Ctrl-G / Shift-Cmd-G</dt><dd>Find previous</dd>
      <dt>Shift-Ctrl-F / Cmd-Option-F</dt><dd>Replace</dd>
      <dt>Shift-Ctrl-R / Shift-Cmd-Option-F</dt><dd>Replace all</dd>
      <dt>Alt-F</dt><dd>Persistent search (dialog doesn't autoclose,
      enter to find next, Shift-Enter to find previous)</dd>
      <dt>Alt-G</dt><dd>Jump to line</dd>
    </dl>
    <p>Searching is enabled by
    including <a href="../addon/search/search.js">addon/search/search.js</a>
    and <a href="../addon/search/searchcursor.js">addon/search/searchcursor.js</a>.
    Jump to line - including <a href="../addon/search/jump-to-line.js">addon/search/jump-to-line.js</a>.</p>
    <p>For good-looking input dialogs, you also want to include
    <a href="../addon/dialog/dialog.js">addon/dialog/dialog.js</a>
    and <a href="../addon/dialog/dialog.css">addon/dialog/dialog.css</a>.</p>
  </article>
