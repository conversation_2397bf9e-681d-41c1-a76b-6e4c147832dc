/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

define(['./createTaskProcessorWorker', './Cartesian2-459c65b8', './when-4bbc8319', './Check-176aaa99', './IntersectionTests-1d02252c', './GeographicTilingScheme-1eda7af0', './Math-3fa78739', './Interval-b16f37de'], (function (createTaskProcessorWorker, Cartesian2, when, Check, IntersectionTests, GeographicTilingScheme, Math$1, Interval) { 'use strict';

  function loadTileBufferSynchronized(url, dataType) {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", url, false);
    xhr.overrideMimeType("text/plain; charset=x-user-defined");
    //xhr.responseType = 'arraybuffer';
    xhr.send(null);
    const responseContentType = xhr.getResponseHeader("Content-Type");
    // if (!!responseContentType && responseContentType.includes('null')) {
    //     return null;
    // }
    if (!!responseContentType && responseContentType.indexOf("null") !== -1) {
      return null;
    } else if (
      xhr.status === 404 &&
      responseContentType === "application/json" &&
      xhr.responseText &&
      xhr.responseText.length > 100
    ) {
      //服务器返回404,表示无此瓦片.
      return null;
    }

    if (
      xhr.status !== 200 ||
      !xhr.responseText ||
      xhr.responseText.length < 100
    ) {
      return undefined;
    }

    const binStr = xhr.responseText;
    const length = binStr.length;
    //var length = 45000;
    const arraybuffer = new ArrayBuffer(length);
    const byteArray = new Uint8Array(arraybuffer);
    for (let i = 0; i < length; ++i) {
      const c = binStr.charCodeAt(i);
      const byte = c & 0xff;
      byteArray[i] = byte;
    }
    if (when.defined(dataType) && dataType.toLowerCase() === "float32") {
      return new Float32Array(arraybuffer);
    } else {
      return new Int16Array(arraybuffer);
    }
  }

  const AltitudeResults = {
    FAILED: 0,
    INFERIOR: 1,
    DESIRABLE: 2,
  };

  function TileKey(options) {
    this.level = options.level;
    this.x = options.x;
    this.y = options.y;
    this.rectangle = options.rectangle;
  }
  TileKey.prototype.toString = function () {
    return this.level + "_" + this.x + "_" + this.y;
  };

  function ElevationLayer(layerId, manager, options) {
    this._samplesPerTile = when.defaultValue(options.samplesPerTile, 150);
    this._templateUrl = options.urlTemplate;
    this._minLevel = when.defaultValue(options.minLevel, 0);
    this._maxLevel = options.maxLevel;
    this._numTilesXAtMinLevel = options.numTilesXAtMinLevel;
    this._numTilesYAtMinLevel = options.numTilesYAtMinLevel;
    this._dataType = options.datatype;
    //this._rectangle = new Rectangle(options.west, options.south, options.east, options.north);
    this._rectangle = new Cartesian2.Rectangle(
      options.boundingBox.west,
      options.boundingBox.south,
      options.boundingBox.east,
      options.boundingBox.north
    );

    this._tilingScheme = new GeographicTilingScheme.GeographicTilingScheme({
      ellipsoid: Cartesian2.Ellipsoid.WGS84,
      rectangle: this._rectangle,
      numberOfLevelZeroTilesX: options.numTilesXAtMinLevel,
      numberOfLevelZeroTilesY: options.numTilesYAtMinLevel,
    });

    this._avaliablesByLevel = [];
    if (options.avaliables && options.avaliables.length > 0) {
      const lt = new Cartesian2.Rectangle();
      const rb = new Cartesian2.Rectangle();
      for (let i = 0; i < options.avaliables.length; ++i) {
        const avaliable = options.avaliables[i];
        const level = avaliable.level;
        if (!when.defined(this._avaliablesByLevel[level])) {
          this._avaliablesByLevel[level] = [];
        }
        this._tilingScheme.tileXYToRectangle(
          avaliable.minX,
          avaliable.minY,
          level,
          lt
        );
        this._tilingScheme.tileXYToRectangle(
          avaliable.maxX,
          avaliable.maxY,
          level,
          rb
        );
        this._avaliablesByLevel[level].push(
          new Cartesian2.Rectangle(lt.west, rb.south, rb.east, lt.north)
        );
      }
    }

    this._layerId = layerId;

    //this._queryParameters = options.queryParameters;

    // if(defined(options.queryParameters)){
    //     this._queryString = objectToQuery(this._queryParameters);
    // }
    this._blackList = [];

    this._manager = manager;

    this._bufferMap = new Map();
  }

  const scratchTile = new Cartesian2.Cartesian2();
  ElevationLayer.prototype.getTileKey = function (cartographic, level) {
    const tile = this._tilingScheme.positionToTileXY(
      cartographic,
      level,
      scratchTile
    );
    const x = parseInt(tile.x);
    const y = parseInt(tile.y);

    return new TileKey({
      level: level,
      x: x,
      y: y,
      rectangle: this._tilingScheme.tileXYToRectangle(x, y, level),
    });
  };

  function contains(arr, obj) {
    let i = arr.length;
    while (i--) {
      if (arr[i] === obj) {
        return true;
      }
    }
    return false;
  }
  ElevationLayer.prototype.getElevationAtTile = function (cartographic, tileKey) {
    const key = tileKey.toString();
    // if (this._blackList.includes(key)) {
    //     return false;
    // }
    if (contains(this._blackList, key)) {
      return false;
    }
    let buffer = this._bufferMap.get(key);
    if (!buffer) {
      //var url = this._baseUrl + '/' + tileKey.level + '/' + tileKey.x + '/' + tileKey.y + '.bil';
      // var url = this._templateUrl
      //     .replace('{TileMatrix}', tileKey.level)
      //     .replace('{TileRow}', tileKey.y)
      //     .replace('{TileCol}', tileKey.x);
      const url = this._templateUrl
        .replace("{TileMatrix}", tileKey.level)
        .replace("{TileRow}", tileKey.y)
        .replace("{TileCol}", tileKey.x);

      // //added by meisongjun 20180321
      // if (defined(this._queryString) ){
      //     url +='?' + this._queryString;
      // }

      const loadedBuffer = loadTileBufferSynchronized(url, this._dataType);
      if (loadedBuffer !== undefined) {
        if (!loadedBuffer) {
          this._blackList.push(key);
        } else {
          buffer = loadedBuffer;
          this._bufferMap.set(key, buffer);

          const timestamp = new Date().getTime();
          this._manager.insertBufferIndex(this._layerId, key, timestamp);
        }
      }
    } else {
      const timestamp = new Date().getTime();
      this._manager.updateBufferIndex(this._layerId, key, timestamp);
    }

    if (!buffer) {
      return false;
    }

    const x =
      ((this._samplesPerTile - 1) *
        (cartographic.longitude - tileKey.rectangle.west)) /
      Cartesian2.Rectangle.computeWidth(tileKey.rectangle);
    const y =
      ((this._samplesPerTile - 1) *
        (tileKey.rectangle.north - cartographic.latitude)) /
      Cartesian2.Rectangle.computeHeight(tileKey.rectangle);

    const x_min = Math.floor(x);
    const x_max = Math.min(x_min + 1, this._samplesPerTile - 1);
    const y_min = Math.floor(y);
    const y_max = Math.min(y_min + 1, this._samplesPerTile - 1);

    const top_left_height = buffer[y_min * this._samplesPerTile + x_min];
    const top_right_height = buffer[y_min * this._samplesPerTile + x_max];
    const bottom_left_height = buffer[y_max * this._samplesPerTile + x_min];
    const bottom_right_height = buffer[y_max * this._samplesPerTile + x_max];

    const horizontal_ratio = x - x_min;
    const vertical_ratio = y - y_min;
    cartographic.height =
      (top_left_height * (1 - horizontal_ratio) +
        top_right_height * horizontal_ratio) *
        (1 - vertical_ratio) +
      (bottom_left_height * (1 - horizontal_ratio) +
        bottom_right_height * horizontal_ratio) *
        vertical_ratio;

    return true;
  };

  ElevationLayer.prototype.getElevation = function (
    cartographic,
    samplesPerRadian,
    previousSamplesPerRadian
  ) {
    let result = AltitudeResults.FAILED;

    if (!Cartesian2.Rectangle.contains(this._rectangle, cartographic)) {
      return result;
    }

    let level = this._minLevel;
    let thisSamplesPerRadian =
      this._samplesPerTile /
      (Cartesian2.Rectangle.computeWidth(this._rectangle) /
        (this._numTilesXAtMinLevel * Math.pow(2, level)));

    for (; level <= this._maxLevel; ++level) {
      if (thisSamplesPerRadian >= samplesPerRadian) {
        result = AltitudeResults.DESIRABLE;
        break;
      }

      thisSamplesPerRadian *= 2;
    }

    if (level > this._maxLevel) {
      if (thisSamplesPerRadian <= previousSamplesPerRadian) {
        return AltitudeResults.FAILED;
      }
      level = this._maxLevel;
      result = AltitudeResults.INFERIOR;
    }

    let isAvaliable = false;
    const avaliables = this._avaliablesByLevel[level];
    for (let i = 0; i < avaliables.length; ++i) {
      if (Cartesian2.Rectangle.contains(avaliables[i], cartographic)) {
        isAvaliable = true;
        break;
      }
    }

    if (!isAvaliable) {
      return AltitudeResults.FAILED;
    }
    const key = this.getTileKey(cartographic, level);
    if (!this.getElevationAtTile(cartographic, key)) {
      return AltitudeResults.FAILED;
    }

    return result;
  };

  ElevationLayer.prototype.removeBufferData = function (tileKey) {
    const bufferMap = this._bufferMap;
    bufferMap.delete(tileKey);
  };

  function ElevationLayerManager() {
    this._layers = [];
    this._index = 0;
    this._maxBufferIndexArrayLength = 500;
    this._bufferIndexArray = [];
  }

  ElevationLayerManager.prototype.addLayer = function (options) {
    const layerId = this._index++;
    const newLayer = new ElevationLayer(layerId, this, options);
    this._layers.push(newLayer);
  };

  ElevationLayerManager.prototype.getElevation = function (
    cartographic,
    samplesPerRadian
  ) {
    let result = AltitudeResults.FAILED;
    const previousSamplesPerRadian = 0;
    for (let i = 0; i < this._layers.length; ++i) {
      const thisResult = this._layers[i].getElevation(
        cartographic,
        samplesPerRadian,
        previousSamplesPerRadian
      );
      if (thisResult === AltitudeResults.DESIRABLE) {
        return thisResult;
      } else if (thisResult === AltitudeResults.INFERIOR) {
        result = thisResult;
      }
    }
    return result;
  };

  function compareTimestamp(a, b) {
    if (a.timestamp < b.timestamp) {
      return -1;
    } else if (a.timestamp > b.timestamp) {
      return 1;
    }
    return 0;
  }

  ElevationLayerManager.prototype.insertBufferIndex = function (
    layerId,
    tileKey,
    timestamp
  ) {
    const indexArray = this._bufferIndexArray;
    const maxArrayLength = this._maxBufferIndexArrayLength;
    const currentArrayLength = indexArray.length;

    // if (currentArrayLength > maxArrayLength) {
    //     console.log('??????must NOT reach here!');
    //     // for (var i = maxArrayLength; i < currentArrayLength; i++) {
    //     //     this.removeBufferData(indexArray[i]);
    //     // }
    //     // indexArray.splice(maxArrayLength, currentArrayLength-maxArrayLength);
    // }
    if (currentArrayLength === maxArrayLength) {
      const removedIndex = indexArray.shift();
      this.removeBufferData(removedIndex);
    }
    indexArray.push({
      layerId: layerId,
      tileKey: tileKey,
      timestamp: timestamp,
    });
  };

  ElevationLayerManager.prototype.removeBufferData = function (bufferIndex) {
    const layers = this._layers;
    const layerCount = layers.length;
    for (let i = 0; i < layerCount; i++) {
      const layer = layers[i];
      if (layer._layerId === bufferIndex.layerId) {
        layer.removeBufferData(bufferIndex.tileKey);
        break;
      }
    }
  };

  ElevationLayerManager.prototype.updateBufferIndex = function (
    layerId,
    tileKey,
    timestamp
  ) {
    const indexArray = this._bufferIndexArray;
    const currentArrayLength = indexArray.length;
    for (let i = 0; i < currentArrayLength; i++) {
      const index = indexArray[i];
      if (index.layerId === layerId && index.tileKey === tileKey) {
        index.timestamp = timestamp;

        indexArray.sort(compareTimestamp);
        break;
      }
    }
  };

  const elevationLayerManager = new ElevationLayerManager();

  function createHeightmapFromElevationLayers(parameters, transferableObjects) {
    if (parameters.method === "addLayer") {
      elevationLayerManager.addLayer(parameters.layer);
      return {
        succeed: true,
      };
    }

    const rectangle = new Cartesian2.Rectangle(
      parameters.west,
      parameters.south,
      parameters.east,
      parameters.north
    );

    //added by meisongjun 20180521
    const constraintRegions = parameters.constraintRegions;
    const intersetConstraintRegionIndex = IntersectionTests.IntersectionTests.intersetRectangleWithConstraintRegion(
      rectangle,
      constraintRegions
    );

    const heightmapWidth = parameters.heightmapWidth;

    const samplesPerRadian = heightmapWidth / Cartesian2.Rectangle.computeWidth(rectangle);
    const stepX = Cartesian2.Rectangle.computeWidth(rectangle) / (heightmapWidth - 1);
    const stepY = Cartesian2.Rectangle.computeHeight(rectangle) / (heightmapWidth - 1);

    const bufferLength = heightmapWidth * heightmapWidth;
    const heightmap = new Float32Array(bufferLength);
    const cartographic = new Cartesian2.Cartographic();

    let row = 0,
      col = 0;

    //var hasDesirableResult = false, hasFailedResult = false;
    let index = 0;
    for (row = 0; row < heightmapWidth; row++) {
      for (col = 0; col < heightmapWidth; col++) {
        cartographic.longitude = rectangle.west + col * stepX;
        cartographic.latitude = rectangle.north - row * stepY;
        cartographic.height = 0.0;

        // var thisResult = elevationLayerManager.getElevation(cartographic, samplesPerRadian);
        // if (thisResult === AltitudeResults.DESIRABLE) {
        //     hasDesirableResult = true;
        // } else if (thisResult === AltitudeResults.FAILED) {
        //     hasFailedResult = true;
        //     break;
        // }
        //added by meisongjun 20180521
        let bInRegion = false;
        if (intersetConstraintRegionIndex !== -1) {
          const tempIndex = IntersectionTests.IntersectionTests.isCartographicInConstraintRegion(
            cartographic,
            intersetConstraintRegionIndex,
            constraintRegions
          );
          if (tempIndex !== -1) {
            if (constraintRegions[tempIndex].eraseType === "ERASE") {
              heightmap[index++] = constraintRegions[tempIndex].height;
            } else if (constraintRegions[tempIndex].eraseType === "OFFSET") {
              if (
                elevationLayerManager.getElevation(
                  cartographic,
                  samplesPerRadian
                ) === AltitudeResults.FAILED
              ) {
                //return null;
                cartographic.height = 0.0;
              }
              heightmap[index++] =
                cartographic.height + constraintRegions[tempIndex].height;
            } else if (constraintRegions[tempIndex].eraseType === "SMOOTH") {
              let height = IntersectionTests.IntersectionTests.interoplateCartographicWhenSmooth(
                cartographic,
                constraintRegions[tempIndex]
              );
              if (height < constraintRegions[tempIndex].height) {
                height = constraintRegions[tempIndex].height;
              }
              heightmap[index++] = height;
            }

            bInRegion = true;
          }
        }
        //end

        if (!bInRegion) {
          if (
            elevationLayerManager.getElevation(cartographic, samplesPerRadian) ===
            AltitudeResults.FAILED
          ) {
            //return null;
            cartographic.height = 0.0;
          }
          heightmap[index++] = cartographic.height;
        }
      }
      // if (hasFailedResult) {
      //     break;
      // }
    }
    // if (hasFailedResult) {
    //     return null;
    // }

    transferableObjects.push(heightmap.buffer);

    return {
      heightmap: heightmap.buffer,
    };
  }

  var createHeightmapFromElevationLayers$1 = createTaskProcessorWorker(createHeightmapFromElevationLayers);

  return createHeightmapFromElevationLayers$1;

}));
//# sourceMappingURL=createHeightmapFromElevationLayers.js.map
