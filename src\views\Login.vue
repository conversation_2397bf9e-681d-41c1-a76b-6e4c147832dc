<template>
    <Resize>
    <div class="login-container">
      <div class="login-text">
        智能矿山地质保障平台
      </div>
    <div class="login-box">
        <img class="login-form-bg"  src="../assets/img/login/linght.png" />
        <el-form
          ref="loginForm"
          class="login-form"
          :model="state.ruleForm"
          :rules="state.rules"
        >
          <el-form-item prop="username" label="" >
            <el-input v-model.trim="state.ruleForm.username" placeholder="请输入用户名">
              <img  class="login-form-pas" src="../assets/img/login/icon_my.png" />
            </el-input>
          </el-form-item>
          <el-form-item prop="password" label="" class="login-btn-item">
            <el-input type="password" v-model.trim="state.ruleForm.password" placeholder="请输入密码">
              <img class="login-form-pas" src="../assets/img/login/icon_password.png" />
            </el-input>
          </el-form-item>
          <el-button
            type="primary but"
            native-type="button"
            @click.prevent="submitForm"
            class="login-btn"
          >登录</el-button>
        </el-form>
    </div>
  </div>
  </Resize>
</template>

<script setup>
import Resize from '@/utils/Resize.vue'
import { ElMessage } from 'element-plus'
import {onBeforeMount,onMounted} from 'vue'
import { reactive, ref } from 'vue'
import {login,loginByJson} from '@/api/login/index.js'
import router from '../router'
const loginForm = ref(null)
const state = reactive({
  ruleForm: {
    username: '',
    password: ''
  },
  rules: {
    username: [
      { required: 'true', message: '用户名不能为空', trigger: 'blur' }
    ],
    password: [
      { required: 'true', message: '密码不能为空', trigger: 'blur' }
    ]
  }
})
const submitForm = async () => {
  loginForm.value.validate(async (valid) => {
    if (valid) {
      let data ={currentPage:1,pageSize:2}
      const res =  await loginByJson(data)
      console.log(res)
      if(res.data){
        router.replace({
            name: "Home",
        });
      }
    } else {
      console.log('error submit!!')
      return false;
    }
  })
}
//生命周期
onMounted(()=>{

})

// watchEffect(()=>{
//   openVn()
// })

</script>
<style lang="less" scoped>
.login-container {
  background-image: url("../assets/img/login/login.png");
  background-position: 50%;
  background-size:100% 100%;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .login-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    .login-form-user{
      height: 17px;
      width: 18px;
      background-image: url("../assets/img/login/icon_my.png");
      background-size: 428px 390px;
      background-repeat: no-repeat;
    }
    .login-btn{
      margin-top: 38px;
      width: 100%;
      height: 51px;
      background: #60CFF4;
      color: #004099;
      font-size: 22px;
      font-weight: bold;
    }
    .el-form-item{
      height: 59px;
      input{
        height: 59px;
        background: #1171B4;
        border: 1px solid #6EEFFC;
        margin-bottom: 18px;
        padding-left:58px;
        font-size: 17px;
        color: #fff;
      }
    }
  }
  
  .login-box {
    box-sizing: border-box;
    width: 428px;
    height: 390px;
    background-image: url("../assets/img/login/login_box.png");
    background-size: 428px 390px;
    background-repeat: no-repeat;
    box-sizing: border-box;
    padding: 62px 54px 0 56px;
    position: relative;
    .right-content {
      margin-bottom: 30px;
    }
    .login-form-bg{
      position: absolute;
      top: -175px;
      left: -100px;
      width: 619px;
      height: 349px;
    }
  }
  .login-container .login-box .login-text span:hover {
    border-bottom: 2px solid #4981f2;
  }
  .login-text {
      width: 428px;
      font-size: 40px;
      font-family: FZLanTingHei-R-GBK;
      font-weight: 400;
      line-height: 23px;
      margin-bottom: 37px;
      text-align: center;
      color: #fff;
    }
}
.login {
  padding: 20px;
  font-size: 29px;
  color: #fff;
  letter-spacing: 10px;
}
 .login-form-pas{
    height: 17px;
    margin-top: 20px;
    margin-left: 14px;
  }
</style>

