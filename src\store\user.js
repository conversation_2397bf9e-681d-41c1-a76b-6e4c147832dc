// user模块，类似于 vuex 中的moduel
import { defineStore } from 'pinia'

// defineStore 类等于 中的 moduel
// useUserStore ，类等于 中的 moduel ：{  useUserStore }
export const useUserStore = defineStore({
  // 模块ID
  id: 'user',
  state: () => {
    return {
      token: '',
      userInfo:{}
    }
  },
  actions:{
  	setToken( token ){
  		this.token = token;
  	}	,
    removeToken(){
      this.token = '';
    }
  },
  // 开启数据缓存
//   persist: {
//     enabled: true,
//     strategies: [{
//         // localStorage 的数据  相当于 setLocalStorage
//         key: 'data_home',
//         // 模式   
//         storage: window.sessionStorage,
//       //paths: ['token']
//     }]
//   }
})