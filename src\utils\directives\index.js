// import directives
/**
 * 自定义指令注册地
 */
import draggable from "../directives/modules/draggable";
 
const directivesList = {
    // Custom directives
    draggable,
};
 
const directives = {
    install: function (app) {
        Object.keys(directivesList).forEach(key => {
            // 注册自定义指令
            //console.log("key",key)
            app.directive(key, directivesList[key]);
        });
    }
};
 
export default directives;