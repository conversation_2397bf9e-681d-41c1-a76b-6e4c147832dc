import $http from '@/utils/request';
// const API = {
//   getdemo(arg) {
//     return $http.get(`/api/getdemo?currentPage=${arg.currentPage}&pageSize=${arg.pageSize}`);
//   },
//   get() {
//     return $http.get(`/api/getinfo?pageNo=1&pageSize=100`)
//   },
//   add(params) {
//     return $http.post('/api/send', params);
//   },
//   uploadfile(arg) {
//     return $http.post(`/api/upload`, arg)
//   },
//   downloadfile(params) {
//     return $http.get(`/api/download?fileId=${params.fileId}`, { responseType: 'blob', })
//   },
//   getShopCarList(){
//     return $http.get('/api/shopcar/getShopCarList')
//   }
// };
// export default API;
export function getShopCarList(){
  return $http.get('/api/getShopCarList')
}
