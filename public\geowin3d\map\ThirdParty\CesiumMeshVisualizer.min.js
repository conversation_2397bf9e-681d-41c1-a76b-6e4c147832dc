!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.CesiumMeshVisualizer=e()}}(function(){return function(){function e(t,n,i){function r(o,s){if(!n[o]){if(!t[o]){var l="function"==typeof require&&require;if(!s&&l)return l(o,!0);if(a)return a(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var d=n[o]={exports:{}};t[o][0].call(d.exports,function(e){return r(t[o][1][e]||e)},d,d.exports,e,t,n,i)}return n[o].exports}for(var a="function"==typeof require&&require,o=0;o<i.length;o++)r(i[o]);return r}return e}()({1:[function(e,t,n){"use strict";function i(e){e=Cesium.defaultValue(e,{}),this.length=Cesium.defaultValue(e.length,5e4),this.width=Cesium.defaultValue(e.width,125),this.headLength=Cesium.defaultValue(e.headLength,5e3),this.headWidth=Cesium.defaultValue(e.headWidth,1e3),this.reverse=Cesium.defaultValue(e.reverse,!1)}Object.defineProperty(n,"__esModule",{value:!0});var r=e("./GeometryUtils.js"),a=function(e){return e&&e.__esModule?e:{default:e}}(r);i.createGeometry=function(e){var t,n=e.length,i=e.width,r=e.headLength,o=e.headWidth,s=e.reverse,l=Cesium.CylinderGeometry.createGeometry(new Cesium.CylinderGeometry({length:n,topRadius:i,bottomRadius:i}));return s?(t=Cesium.CylinderGeometry.createGeometry(new Cesium.CylinderGeometry({length:r,topRadius:o,bottomRadius:0})),a.default.translate(t,[0,0,-(n+r)/2])):(t=Cesium.CylinderGeometry.createGeometry(new Cesium.CylinderGeometry({length:r,topRadius:0,bottomRadius:o})),a.default.translate(t,[0,0,(n+r)/2])),a.default.mergeGeometries([l,t])},n.default=i},{"./GeometryUtils.js":5}],2:[function(e,t,n){"use strict";function i(e){this.positions=e.positions,this.normals=e.normals,this.uvs=e.uvs,this.indices=e.indices}Object.defineProperty(n,"__esModule",{value:!0}),i.createGeometry=function(e){if(!e.positions)throw new Error("缺少positions参数");if(!e.indices)throw new Error("缺少indices参数");var t=e.positions,n=e.normals,i=e.uvs,r=e.indices instanceof Int32Array?e.indices:new Int32Array(e.indices),a={position:new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:t instanceof Float32Array?t:new Float32Array(e.positions)})};n&&(a.normal=new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:n instanceof Float32Array?n:new Float32Array(n)})),i&&(a.uv=new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:i instanceof Float32Array?i:new Float32Array(i)}));var o=Cesium.BoundingSphere.fromVertices(t);return new Cesium.Geometry({attributes:a,indices:new Int32Array(r),primitiveType:Cesium.PrimitiveType.TRIANGLES,boundingSphere:o})},n.default=i},{}],3:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(e){e=e?e:{},e.uniforms=e.uniforms?e.uniforms:{ambientColor:[0,0,0,1],emissionColor:[0,0,0,1],diffuseColor:[0,0,0,1],specularColor:[0,0,0,1],specularShininess:0,alpha:void 0,ambientColorMap:void 0,emissionColorMap:void 0,diffuseColorMap:void 0,specularColorMap:void 0,specularShininessMap:void 0,normalMap:void 0,alphaMap:void 0},e.uniforms.ambientColor=Cesium.defaultValue(e.uniforms.ambientColor,[0,0,0,1]),e.uniforms.emissionColor=Cesium.defaultValue(e.uniforms.emissionColor,[0,0,0,1]),e.uniforms.diffuseColor=Cesium.defaultValue(e.uniforms.diffuseColor,[0,0,0,1]),e.uniforms.specularColor=Cesium.defaultValue(e.uniforms.specularColor,[0,0,0,1]),e.uniforms.alpha=Cesium.defaultValue(e.uniforms.alpha,1),e.uniforms.specularShininess=Cesium.defaultValue(e.uniforms.specularShininess,0),e.side=Cesium.defaultValue(e.side,o.default.Sides.FRONT),o.default.apply(this,[e]),this.blendEnable=!1;var t=e.withTexture,n=e.withNormals;if(this.depthTest=!0,this.depthMask=!0,this.blending=!0,e.uniforms.diffuseColorMap){if("string"==typeof e.uniforms.diffuseColorMap){var i=e.uniforms.diffuseColorMap.toLowerCase(),r=d.default.GetExtension(i);".tif"==r||".png"==r?this.translucent=!0:"data:image/png"===i.slice(0,"data:image/png".length)?this.translucent=!0:"data:image/tif"===i.slice(0,"data:image/tif".length)&&(this.translucent=!0)}else(i instanceof HTMLCanvasElement||i instanceof HTMLVideoElement)&&(this.translucent=!0);if(t=!0,Cesium.defined(this.uniforms.diffuseColorMap.flipY)||(this.uniforms.diffuseColorMap.flipY=!1),!this.uniforms.diffuseColorMap.sampler){var a={};a.magnificationFilter=Cesium.WebGLConstants.LINEAR,a.minificationFilter=Cesium.WebGLConstants.NEAREST_MIPMAP_LINEAR,a.wrapS=Cesium.WebGLConstants.REPEAT,a.wrapT=Cesium.WebGLConstants.REPEAT,this.uniforms.diffuseColorMap.sampler=a}}else t=!1;var s=null,c=null;t&&n?(s=l.default.texture_normals_vert,c=l.default.texture_normals_frag):t&&!n?(s=l.default.texture_vert,c=l.default.texture_frag):!t&&n?(s=l.default.normals_vert,c=l.default.normals_frag):(s=l.default.none_vert,c=l.default.none_frag),this.vertexShader=s,this.fragmentShader=c}Object.defineProperty(n,"__esModule",{value:!0});var a=e("./MeshMaterial.js"),o=i(a),s=e("./Shaders/ShaderChunk.js"),l=i(s),c=e("../Util/Path.js"),d=i(c);r.prototype=Object.create(o.default.prototype),n.default=r},{"../Util/Path.js":35,"./MeshMaterial.js":9,"./Shaders/ShaderChunk.js":19}],4:[function(e,t,n){"use strict";function i(e,t,n){this.mesh=e,this.texture=t,this.depthTexture=n,this.framebuffer=null,this.ready=!1,this.readyPromise=Cesium.when.defer(),t&&t instanceof Cesium.Framebuffer?(this.framebuffer=t,this.texture=this.framebuffer._colorTextures[0],this.depthTexture=this.framebuffer._depthTexture,this.ready=!0,this.readyPromise.resolve(!0)):this.destroyAttachments=!0}Object.defineProperty(n,"__esModule",{value:!0}),i.prototype.destroy=function(){this.destroyAttachments&&(this.texture&&(this.texture.destroy(),delete this.texture),this.depthTexture&&(this.depthTexture.destroy(),delete this.depthTexture),this.framebuffer&&(this.framebuffer.destroy(),delete this.framebuffer),this.mesh&&(this.mesh.destroy(),delete this.mesh))},n.default=i},{}],5:[function(e,t,n){"use strict";function i(){}function r(e){var t=[];for(var n in e.attributes)e.attributes.hasOwnProperty(n)&&e.attributes[n]&&t.push(n);return t}function a(){p||(p=!0,l=new Cesium.Cartesian3,c=new Cesium.Matrix4,d=new Cesium.Matrix3,u=new Cesium.Cartesian3)}function o(e){for(var t,n,i,r,a=e.attributes.normal.values,o=0;o<a.length;o+=3)t=a[o],n=a[o+1],i=a[o+2],r=1/Math.sqrt(t*t+n*n+i*i),a[o]=t*r,a[o+1]=n*r,a[o+2]=i*r}function s(e){if(1==e.length)return e[0];var t,n=[],i=[],r=[],a=[],o=[],s=[],l=[],c=[],d=0,u=[],f=e[0];t=f.primitiveType;for(var m in f.attributes)if(f.attributes.hasOwnProperty(m)&&f.attributes[m]){var p=f.attributes[m];n.push(m),o.push(p.componentsPerAttribute),r.push(p.componentDatatype),a.push(p.values.constructor),s.push(p.normalize),u.push(0),l.push(0)}for(var h=0;h<e.length;h++)for(var v=e[h],_=0;_<n.length;_++){var g=n[_];u[_]+=v.attributes[g].values.length}for(var x=0;x<n.length;x++)i.push(new a[x](u[x]));for(var y=0;y<e.length;y++){for(var C=e[y],M=0;M<n.length;M++){var S=n[M],w=i[M],b=C.attributes[S].values;w.set(b,l[M]),l[M]+=b.length}for(var L=0;L<C.indices.length;L++){var E=C.indices[L];c.push(E+d)}d+=C.attributes.position.values.length/3}for(var P={},T=0;T<n.length;T++){P[n[T]]={values:i[T],componentsPerAttribute:o[T],componentDatatype:r[T],normalize:s[T]}}return c=i[0]/o[0]<65535?new Uint16Array(c):new Uint32Array(c),f=new Cesium.Geometry({attributes:P,indices:c,primitiveType:t})}Object.defineProperty(n,"__esModule",{value:!0});var l,c,d,u,f=e("../Util/CSG.js"),m=function(e){return e&&e.__esModule?e:{default:e}}(f),p=!1;i.rotateX=function(e,t){a();var n=e.attributes.position.values;Cesium.Matrix3.fromRotationX(t,d),Cesium.Matrix4.fromRotationTranslation(d,Cesium.Cartesian3.ZERO,c);for(var i=0;i<n.length;i+=3)l.x=n[i],l.y=n[i+1],l.z=n[i+2],Cesium.Matrix4.multiplyByPoint(c,l,l),n[i]=l.x,n[i+1]=l.y,n[i+2]=l.z},i.rotateY=function(e,t){a();var n=e.attributes.position.values;Cesium.Matrix3.fromRotationY(t,d),Cesium.Matrix4.fromRotationTranslation(d,Cesium.Cartesian3.ZERO,c);for(var i=0;i<n.length;i+=3)l.x=n[i],l.y=n[i+1],l.z=n[i+2],Cesium.Matrix4.multiplyByPoint(c,l,l),n[i]=l.x,n[i+1]=l.y,n[i+2]=l.z},i.rotateZ=function(e,t){a();var n=e.attributes.position.values;Cesium.Matrix3.fromRotationZ(t,d),Cesium.Matrix4.fromRotationTranslation(d,Cesium.Cartesian3.ZERO,c);for(var i=0;i<n.length;i+=3)l.x=n[i],l.y=n[i+1],l.z=n[i+2],Cesium.Matrix4.multiplyByPoint(c,l,l),n[i]=l.x,n[i+1]=l.y,n[i+2]=l.z},i.computeVertexNormals=function(e){var t=e.indices,n=e.attributes,i=t.length;if(n.position){var r=n.position.values;if(void 0===n.normal)n.normal=new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:new Float32Array(r.length)});else for(var a=n.normal.values,s=0;s<i;s++)a[s]=0;for(var l,c,d,u=n.normal.values,f=new Cesium.Cartesian3,m=new Cesium.Cartesian3,p=new Cesium.Cartesian3,h=new Cesium.Cartesian3,v=new Cesium.Cartesian3,s=0;s<i;s+=3)l=3*t[s+0],c=3*t[s+1],d=3*t[s+2],Cesium.Cartesian3.fromArray(r,l,f),Cesium.Cartesian3.fromArray(r,c,m),Cesium.Cartesian3.fromArray(r,d,p),Cesium.Cartesian3.subtract(p,m,h),Cesium.Cartesian3.subtract(f,m,v),Cesium.Cartesian3.cross(h,v,h),u[l]+=h.x,u[l+1]+=h.y,u[l+2]+=h.z,u[c]+=h.x,u[c+1]+=h.y,u[c+2]+=h.z,u[d]+=h.x,u[d+1]+=h.y,u[d+2]+=h.z;o(e),n.normal.needsUpdate=!0}return e},i.mergeGeometries=function(e){if(!e||!e.length)throw new Error("缺少geometries参数");if(1==e.length)return e[0];for(var t=[],n=!1,i=!1,a=e[0].primitiveType,o=0;o<e.length;o++){if(t[o]=r(e[o]),o>0){if(a!=e[o].primitiveType){i=!0;break}var l=t[o-1];if(!(n=l.length!=t[o].length))for(var c=0;c<l.length;c++)if(l[c]!=t[o][c]){n=!0;break}}if(a=e[o].primitiveType,n||i)break}if(i)throw new Error("待合并的几何体中primitiveType属性不完全一致");if(n)throw new Error("待合并的几何体中属性数量和和名称不完全一致");return s(e);var o,c,c,o},i.translate=function(e,t){a(),Array.isArray(t)?(u.x=t[0],u.y=t[1],u.z=t[2]):Cesium.Cartesian3.clone(t,u);for(var n=0;n<e.attributes.position.values.length;n+=3)e.attributes.position.values[n]+=u.x,e.attributes.position.values[n+1]+=u.y,e.attributes.position.values[n+2]+=u.z},i.getAttributeComponentType=function(e){var t=Cesium.ComponentDatatype.SHORT;return e instanceof Int8Array?t=Cesium.ComponentDatatype.BYTE:e instanceof Uint8Array||e instanceof Uint8ClampedArray?t=Cesium.ComponentDatatype.UNSIGNED_BYTE:e instanceof Int16Array?t=Cesium.ComponentDatatype.SHORT:e instanceof Uint16Array?t=Cesium.ComponentDatatype.UNSIGNED_SHORT:e instanceof Int32Array?t=Cesium.ComponentDatatype.INT:e instanceof Uint32Array?t=Cesium.ComponentDatatype.UNSIGNED_INT:e instanceof Float32Array?t=Cesium.ComponentDatatype.FLOAT:e instanceof Float64Array&&(t=Cesium.ComponentDatatype.DOUBLE),t},i.isGeometry3js=function(e){return"undefined"!=typeof THREE&&(e instanceof THREE.Geometry||e instanceof THREE.BufferGeometry)||e.attributes&&e.attributes.position&&e.index||e.vertices&&e.faces},i.parseBufferGeometry3js=function(e){var t={};e.attributes.normal||e.computeFaceNormals();for(var n in e.attributes)if(e.attributes.hasOwnProperty(n)){var r=e.getAttribute(n);r&&r.array.length>0&&(t[n]=new Cesium.GeometryAttribute({componentDatatype:i.getAttributeComponentType(r.array),componentsPerAttribute:r.itemSize,values:r.array,normalize:r.normalized}))}var a=[];return!e.index&&e.groups?(e.groups.forEach(function(e){for(var t=0;t<e.count;t++)a.push(t+e.start)}),a=new Int32Array(a)):a=e.index.array,new Cesium.Geometry({attributes:t,indices:a,primitiveType:Cesium.PrimitiveType.TRIANGLES})},i.fromGeometry3js=function(e){e.attributes&&(e.index||e.groups.length)||(e=(new THREE.BufferGeometry).fromGeometry(e));var t=i.parseBufferGeometry3js(e);return Cesium.GeometryPipeline.computeNormal(t),t},i.toGeometry3js=function(e){if("undefined"==typeof THREE)throw new Error("THREE 未加载");for(var t=e.attributes.position.values,n=0,i=new THREE.Geometry,r=0;r<t.length;r+=3)n=3*r,i.vertices.push(new THREE.Vector3(t[n],t[n+2],t[n+1]));for(var r=0;r<e.indices.length;r+=3){var a=e.indices[r],o=e.indices[r+1],s=e.indices[r+2];i.faces.push(new THREE.Face3(a,o,s))}return i},i.toCSG=function(e,t){if("undefined"!=typeof THREE&&e instanceof THREE.Geometry)return i._toCSG3js(e,t);if(t||(t={x:0,y:0,z:0}),e.attributes.normal||(e=Cesium.GeometryPipeline.computeNormal(e)),e.primitiveType!==Cesium.PrimitiveType.TRIANGLES)throw new Error("暂不支持此类几何体");if(!m.default)throw new Error("CSG 库未加载。请从 https://github.com/evanw/csg.js 获取");for(var n=(e.indices.length,[]),r=[],a=e.attributes.position.values,o=e.attributes.normal.values,s=0,l=0,c=0;c<e.indices.length;c+=3){r=[];var d=e.indices[c],u=e.indices[c+1],f=e.indices[c+2];l=3*d,s=3*d,r.push(new m.default.Vertex([a[l++]+t.x,a[l++]+t.y,a[l++]+t.z],[o[s++],o[s++],o[s++]])),l=3*u,s=3*u,r.push(new m.default.Vertex([a[l++]+t.x,a[l++]+t.y,a[l++]+t.z],[o[s++],o[s++],o[s++]])),l=3*f,s=3*f,r.push(new m.default.Vertex([a[l++]+t.x,a[l++]+t.y,a[l++]+t.z],[o[s++],o[s++],o[s++]])),n.push(new m.default.Polygon(r))}return m.default.fromPolygons(n)},i.fromCSG=function(e,t){if("undefined"!=typeof THREE&&geometry instanceof THREE.Geometry)return i._fromCSG3js(geometry,offset);var n,r,a,o=e.toPolygons();if(!m.default)throw new Error("CSG 库未加载。请从 https://github.com/evanw/csg.js 获取");var s=[],l=[],c=[];for(n=0;n<o.length;n++){for(a=[],r=0;r<o[n].vertices.length;r++)a.push(this.getGeometryVertice(s,l,o[n].vertices[r].pos,o[n].plane.normal));a[0]===a[a.length-1]&&a.pop();for(var r=2;r<a.length;r++)c.push(a[0],a[r-1],a[r])}s=new Float32Array(s),l=new Float32Array(l),c=new Int32Array(c);var d={};return d.position=new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:s}),d.normal=new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:l}),new Cesium.Geometry({attributes:d,indices:c,primitiveType:Cesium.PrimitiveType.TRIANGLES})},i._toCSG3js=function(e,t,n){if("undefined"==typeof THREE)throw new Error("THREE 未加载");var i,r,a,o,s;if(!m.default)throw"CSG library not loaded. Please get a copy from https://github.com/evanw/csg.js";if(e instanceof THREE.Mesh)r=e.geometry,t=t||e.position,n=n||e.rotation;else{if(!(e instanceof THREE.Geometry))throw"Model type not supported.";r=e,t=t||new THREE.Vector3(0,0,0),n=n||new THREE.Euler(0,0,0)}s=(new THREE.Matrix4).makeRotationFromEuler(n);var a=[];for(i=0;i<r.faces.length;i++)if(r.faces[i]instanceof THREE.Face3)o=[],o.push(new m.default.Vertex(r.vertices[r.faces[i].a].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),o.push(new m.default.Vertex(r.vertices[r.faces[i].b].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),o.push(new m.default.Vertex(r.vertices[r.faces[i].c].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),a.push(new m.default.Polygon(o));else{if(!(r.faces[i]instanceof THREE.Face4))throw"Model contains unsupported face.";o=[],o.push(new m.default.Vertex(r.vertices[r.faces[i].a].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),o.push(new m.default.Vertex(r.vertices[r.faces[i].b].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),o.push(new m.default.Vertex(r.vertices[r.faces[i].d].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),a.push(new m.default.Polygon(o)),o=[],o.push(new m.default.Vertex(r.vertices[r.faces[i].b].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),o.push(new m.default.Vertex(r.vertices[r.faces[i].c].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),o.push(new m.default.Vertex(r.vertices[r.faces[i].d].clone().add(t).applyMatrix4(s),[r.faces[i].normal.x,r.faces[i].normal.y,r.faces[i].normal.z])),a.push(new m.default.Polygon(o))}return m.default.fromPolygons(a)},i._fromCSG3js=function(e){if("undefined"==typeof THREE)throw new Error("THREE 未加载");var t,n,r,a,o=new THREE.Geometry,s=e.toPolygons();if(!m.default)throw"CSG library not loaded. Please get a copy from https://github.com/evanw/csg.js";for(t=0;t<s.length;t++){for(r=[],n=0;n<s[t].vertices.length;n++)r.push(i._getGeometryVertice3js(o,s[t].vertices[n].pos));r[0]===r[r.length-1]&&r.pop();for(var n=2;n<r.length;n++)a=new THREE.Face3(r[0],r[n-1],r[n],(new THREE.Vector3).copy(s[t].plane.normal)),o.faces.push(a),o.faceVertexUvs[0].push(new THREE.Vector2)}return o.computeBoundingBox(),o},i._getGeometryVertice3js=function(e,t){var n;for(n=0;n<e.vertices.length;n++)if(e.vertices[n].x===t.x&&e.vertices[n].y===t.y&&e.vertices[n].z===t.z)return n;return e.vertices.push(new THREE.Vector3(t.x,t.y,t.z)),e.vertices.length-1},n.default=i},{"../Util/CSG.js":34}],6:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(e){e=Cesium.defaultValue(e,{}),this.uuid=Cesium.createGuid(),this.show=Cesium.defaultValue(e.show,!0),this.maxAvailableDistance=Cesium.defaultValue(e.maxAvailableDistance,Number.MAX_VALUE),this._position=Cesium.defaultValue(e.position,new Cesium.Cartesian3(0,0,0)),this._scale=Cesium.defaultValue(e.scale,new Cesium.Cartesian3(1,1,1)),this._rotation=Cesium.defaultValue(e.rotation,{axis:new Cesium.Cartesian3(0,0,1),angle:0}),this._rotation=new s.default(this._rotation.axis,this._rotation.angle),this._boundingSphere=new Cesium.BoundingSphere,this._needsUpdate=!1,this._modelMatrixNeedsUpdate=!0,this._modelMatrix=new Cesium.Matrix4,Cesium.Matrix4.clone(Cesium.Matrix4.IDENTITY,this._modelMatrix),this._onNeedUpdateChanged=function(){this._modelMatrixNeedsUpdate=!0},this._rotation.paramChanged.removeEventListener(this._onNeedUpdateChanged),this._children=[],this._parent=null,this.type="LOD",Object.defineProperties(this,{levels:{enumerable:!0,value:[]}})}function a(e,t){for(var n=0;n<e.length;n++)if(e[n]==t){e.splice(n,1);break}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("./Rotation.js"),s=i(o),l=e("./RendererUtils.js"),c=i(l);r.prototype={constructor:r,setPosition:function(e,t,n){var i=!1;1==arguments.length&&("number"==typeof e?(e!=this._position.x&&(i=!0),this._position.x=e):e instanceof Cesium.Cartesian3&&(e==this._position.x&&t==this._position.y&&n==this._position.z||(i=!0),this._position.x=e.x,this._position.y=e.y,this._position.z=e.z)),2==arguments.length&&"number"==typeof t&&(t!=this._position.y&&(i=!0),this._position.y=t),3==arguments.length&&"number"==typeof n&&(n!=this._position.z&&(i=!0),this._position.z=n),i&&(this._modelMatrixNeedsUpdate=!0)},setScale:function(e,t,n){var i=!1;1==arguments.length&&("number"==typeof e?(e!=this._scale.x&&(i=!0),this._scale.x=e):e instanceof Cesium.Cartesian3&&(e==this._scale.x&&t==this._scale.y&&n==this._scale.z||(i=!0),this._scale.x=e.x,this._scale.y=e.y,this._scale.z=e.z)),2==arguments.length&&"number"==typeof t&&(t!=this._scale.y&&(i=!0),this._scale.y=t),3==arguments.length&&"number"==typeof n&&(n!=this._scale.z&&(i=!0),this._scale.z=n),i&&(this._modelMatrixNeedsUpdate=!0)},addLevel:function(e,t){void 0===t&&(t=0),t=Math.abs(t);for(var n=this.levels,i=0;i<n.length&&!(t<n[i].distance);i++);n.splice(i,0,{distance:t,object:e}),e.parent=this,this._children.push(e),this.levels[0].object.geometry?this._boundingSphere.radius=this.levels[0].object.geometry.boundingSphere.radius:this.levels[0].object.boundingSphere&&(this._boundingSphere.radius=this.levels[0].object.boundingSphere.radius)},update:function(){return function(e,t){var n=this.levels;if(n.length>1){this._modelMatrixNeedsUpdate&&(c.default.computeModelMatrix(e,this.position,this.rotation,this.scale,this.modelMatrix),this._modelMatrixNeedsUpdate=!1),Cesium.Matrix4.getTranslation(this.modelMatrix,this._boundingSphere.center);var i=this._boundingSphere,r=Math.max(0,Cesium.Cartesian3.distance(i.center,t.camera.positionWC)-i.radius),a=this.maxAvailableDistance>r;a=a&&t.cullingVolume.computeVisibility(this._boundingSphere)!==Cesium.Intersect.OUTSIDE,n[0].object.show=a;for(var o=1,s=n.length;o<s&&r>=n[o].distance;o++)n[o-1].object.show=!1,n[o].object.show=a;for(;o<s;o++)n[o].object.show=!1}}}(),getObjectForDistance:function(e){for(var t=this.levels,n=1,i=t.length;n<i&&!(e<t[n].distance);n++);return t[n-1].object}},Object.defineProperties(r.prototype,{modelMatrix:{get:function(){return this._modelMatrix}},parent:{get:function(){return this._parent},set:function(e){if(e&&(e._children&&Array.isArray(e._children)||e.children&&Array.isArray(e.children))){if(this._parent&&this._parent!=e){var t=this._parent._children?this._parent._children:this._parent.children;Array.isArray(t)&&a(t,this)}if(this._parent=e,"function"==typeof this._parent.add)this._parent.add(this);else{var t=e._children?e._children:e.children;t.push(this)}}this._needsUpdate=!0}},children:{get:function(){return this._children},set:function(e){this._children=e,this._needsUpdate=!0}},needsUpdate:{get:function(){return this._needsUpdate},set:function(e){this._needsUpdate=e}},rotation:{get:function(){return this._rotation},set:function(e){e!=this._rotation&&(this._rotation=e,this._needUpdate=!0),this._rotation.paramChanged.removeEventListener(this._onNeedUpdateChanged),this._rotation=e,this._rotation.paramChanged.addEventListener(this._onNeedUpdateChanged)}},position:{get:function(){return this._position},set:function(e){e.x==this._position.x&&e.y==this._position.y&&e.z==this._position.z||(this._position=e,this._needsUpdate=!0),this._position=e}},scale:{get:function(){return this._scale},set:function(e){e.x==this._scale.x&&e.y==this._scale.y&&e.z==this._scale.z||(this._scale=e,this._needsUpdate=!0),this._scale=e}}}),n.default=r},{"./RendererUtils.js":16,"./Rotation.js":17}],7:[function(e,t,n){"use strict";function i(){}function r(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){t[n]={value:{}};for(var i in e[n])"value"!==i&&(t[n][i]=e[n][i]);if(e[n].t)switch(e[n].t){}h(t[n],e[n].value)}return t}function a(e,t){if(h(e.opacity,t.opacity),h(e.diffuse,t.color),t.emissive){var n=(new t.emissive.constructor).copy(t.emissive).multiplyScalar(t.emissiveIntensity);h(e.emissive,n)}h(e.map,t.map),h(e.specularMap,t.specularMap),h(e.alphaMap,t.alphaMap),t.lightMap&&(h(e.lightMap,t.lightMap),h(e.lightMapIntensity,t.lightMapIntensity)),t.aoMap&&(h(e.aoMap,t.aoMap),h(e.aoMapIntensity,t.aoMapIntensity));var i;if(t.map?i=t.map:t.specularMap?i=t.specularMap:t.displacementMap?i=t.displacementMap:t.normalMap?i=t.normalMap:t.bumpMap?i=t.bumpMap:t.roughnessMap?i=t.roughnessMap:t.metalnessMap?i=t.metalnessMap:t.alphaMap?i=t.alphaMap:t.emissiveMap&&(i=t.emissiveMap),void 0!==i){i.isWebGLRenderTarget&&(i=i.texture);var r=i.offset;i.repeat;h(e.offsetRepeat,r)}h(e.envMap,t.envMap),h(e.flipEnvMap,t.envMap&&t.envMap.isCubeTexture?-1:1),h(e.reflectivity,t.reflectivity),h(e.refractionRatio,t.refractionRatio)}function o(e,t){h(e.diffuse,t.color),h(e.opacity,t.opacity)}function s(e,t){h(e.dashSize,t.dashSize),h(e.totalSize,t.dashSize+t.gapSize),h(e.scale,t.scale)}function l(e,t){if(h(e.diffuse,t.color),h(e.opacity,t.opacity),h(e.size,t.size*_pixelRatio),h(e.scale,.5*_height),h(e.map,t.map),null!==t.map){var n=t.map.offset,i=t.map.repeat;h(e.offsetRepeat.value.set(n.x,n.y,i.x,i.y))}}function c(e,t){t.emissiveMap&&h(e.emissiveMap,t.emissiveMap)}function d(e,t){h(e.specular,t.specular),h(e.shininess,Math.max(t.shininess,1e-4)),t.emissiveMap&&h(e.emissiveMap,t.emissiveMap),t.bumpMap&&(h(e.bumpMap,t.bumpMap),h(e.bumpScale,t.bumpScale)),t.normalMap&&(h(e.normalMap,t.normalMap),h(e.normalScale.value.copy(t.normalScale))),t.displacementMap&&(h(e.displacementMap,t.displacementMap),h(e.displacementScale,t.displacementScale),h(e.displacementBias,t.displacementBias))}function u(e,t){d(e,t),t.gradientMap&&h(e.gradientMap,t.gradientMap)}function f(e,t){h(e.roughness,t.roughness),h(e.metalness,t.metalness),t.roughnessMap&&h(e.roughnessMap,t.roughnessMap),t.metalnessMap&&h(e.metalnessMap,t.metalnessMap),t.emissiveMap&&h(e.emissiveMap,t.emissiveMap),t.bumpMap&&(h(e.bumpMap,t.bumpMap),h(e.bumpScale,t.bumpScale)),t.normalMap&&(h(e.normalMap,t.normalMap),h(e.normalScale.value.copy(t.normalScale))),t.displacementMap&&(h(e.displacementMap,t.displacementMap),h(e.displacementScale,t.displacementScale),h(e.displacementBias,t.displacementBias)),t.envMap&&h(e.envMapIntensity,t.envMapIntensity)}function m(e,t){h(e.clearCoat,t.clearCoat),h(e.clearCoatRoughness,t.clearCoatRoughness),f(e,t)}function p(e,t){t.bumpMap&&(h(e.bumpMap,t.bumpMap),h(e.bumpScale,t.bumpScale)),t.normalMap&&(h(e.normalMap,t.normalMap),h(e.normalScale.value.copy(t.normalScale))),t.displacementMap&&(h(e.displacementMap,t.displacementMap),h(e.displacementScale,t.displacementScale),h(e.displacementBias,t.displacementBias))}function h(e,t){var n=void 0===t?"undefined":v(t);if("undefined"===n)return void(e.value=void 0);if(null===t)return void(e.value=null);if(void 0!==e.value&&null!=e.value&&e.value.constructor&&e.value.constructor.clone&&t.constructor==e.value.constructor)e.value=e.value.constructor.clone(t);else switch(n){case"number":case"string":e.value=t;break;case"object":if(t instanceof THREE.Vector2&&(e.value.constructor.clone||(e.value=new Cesium.Cartesian2)),t instanceof THREE.Vector3&&(e.value.constructor.clone||(e.value=new Cesium.Cartesian3)),t instanceof THREE.Vector4&&(e.value.constructor.clone||(e.value=new Cesium.Cartesian4)),t instanceof THREE.Matrix3&&(e.value.constructor.clone||(e.value=new Cesium.Matrix3)),t instanceof THREE.Matrix4&&(e.value.constructor.clone||(e.value=new Cesium.Matrix4)),t instanceof THREE.Color)e.value.constructor.clone||(e.value=new Cesium.Color(t.r,t.g,t.b,t.a));else if(null!=e.value&&e.value.constructor.clone)e.value.constructor.clone(t,e.value);else if(t instanceof THREE.Texture){if(e.value!=t.image){e.value=t.image;var i={};i.magnificationFilter=Cesium.WebGLConstants.LINEAR,i.minificationFilter=Cesium.WebGLConstants.NEAREST_MIPMAP_LINEAR,i.wrapS=Cesium.WebGLConstants.REPEAT,i.wrapT=Cesium.WebGLConstants.REPEAT,e.sampler=i,e.flipY=t.flipY,e.needsUpdate=!0}}else e.value=t;break;default:console.log("未知uniform.value类型")}}Object.defineProperty(n,"__esModule",{value:!0});var v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_=e("./MeshMaterial.js"),g=function(e){return e&&e.__esModule?e:{default:e}}(_),x={MeshDepthMaterial:"depth",MeshNormalMaterial:"normal",MeshBasicMaterial:"basic",MeshLambertMaterial:"lambert",MeshPhongMaterial:"phong",MeshToonMaterial:"phong",MeshStandardMaterial:"physical",MeshPhysicalMaterial:"physical",LineBasicMaterial:"basic",LineDashedMaterial:"dashed",PointsMaterial:"points"};i.fromMaterial3js=function(e){if("undefined"==typeof THREE)throw new Error("Three.js is required.");var t=x[e.type];e["is"+e.type]=!0;var n=THREE.ShaderLib[t];n||(n=e);var a=new g.default({vertexShader:n.vertexShader,fragmentShader:n.fragmentShader,uniforms:r(n.uniforms)});return a.material3js=e,i.updateMaterialFrom3js(a),a},i.updateMaterialFrom3js=function(e){if(e&&e.material3js){var t=e.material3js;e.translucent=t.transparent,e.wireframe=t.wireframe;var n=e.uniforms,i=e.material3js;if((i.isMeshBasicMaterial||i.isMeshLambertMaterial||i.isMeshPhongMaterial||i.isMeshStandardMaterial||i.isMeshNormalMaterial||i.isMeshDepthMaterial)&&a(n,i),i.isLineBasicMaterial)o(n,i);else if(i.isLineDashedMaterial)o(n,i),s(n,i);else if(i.isPointsMaterial)l(n,i);else if(i.isMeshLambertMaterial)c(n,i);else if(i.isMeshToonMaterial)u(n,i);else if(i.isMeshPhongMaterial)d(n,i);else if(i.isMeshPhysicalMaterial)m(n,i);else if(i.isMeshStandardMaterial)f(n,i);else if(i.isMeshDepthMaterial)i.displacementMap&&(h(n.displacementMap,i.displacementMap),h(n.displacementScale,i.displacementScale),h(n.displacementBias,i.displacementBias));else if(i.isMeshNormalMaterial)p(n,i);else for(var r in i.uniforms)i.uniforms.hasOwnProperty(r)&&h(n[r],i.uniforms[r].value);n.ambientLightColor={value:new Cesium.Color(.06666666666666667,.06666666666666667,.06666666666666667)}}},i.isMaterial3js=function(e){return"undefined"!=typeof THREE&&e instanceof THREE.Material},n.default=i},{"./MeshMaterial.js":9}],8:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(e){if(r.isGeometrySupported(e)){e={geometry:e,material:arguments[1],instances:arguments[2],instancedAttributes:arguments[3]}}if(!e||!e.geometry)throw new Error("geometry是必须参数");if(!r.isGeometrySupported(e.geometry))throw new Error("暂不支持此类型的geometry");f.default.isGeometry3js(e.geometry)?e.geometry=f.default.fromGeometry3js(e.geometry):e.geometry instanceof l.default?(0==e.geometry.polygons.length&&(e.show=!1),e.geometry=l.default.fromCSG(e.geometry)):"function"==typeof e.geometry.constructor.createGeometry&&(e.geometry=e.geometry.constructor.createGeometry(e.geometry)),this.uuid=Cesium.createGuid(),this.show=Cesium.defaultValue(e.show,!0),this._geometry=e.geometry,this._material=Cesium.defaultValue(e.material,new d.default),this._position=Cesium.defaultValue(e.position,new Cesium.Cartesian3(0,0,0)),this._scale=Cesium.defaultValue(e.scale,new Cesium.Cartesian3(1,1,1)),this._rotation=Cesium.defaultValue(e.rotation,{axis:new Cesium.Cartesian3(0,0,1),angle:0}),this._rotation=new o.default(this._rotation.axis,this._rotation.angle),this._needsUpdate=!1,this._modelMatrix=new Cesium.Matrix4,Cesium.Matrix4.clone(Cesium.Matrix4.IDENTITY,this._modelMatrix),this.quaternion=null,this._modelMatrixNeedsUpdate=!0,this._onNeedUpdateChanged=function(){this.modelMatrixNeedsUpdate=!0},this._rotation.paramChanged.removeEventListener(this._onNeedUpdateChanged),this._drawCommand=null,this._children=[],this._parent=null,this._instances=null,this._center=this._position.clone(),this.instancedAttributes=e.instancedAttributes||[],e.instances&&e.instances.length&&(this._instances=[],e.instances.forEach(function(e){this.addInstance(e)},this)),this.userData={},!this._geometry.attributes.normal&&this.material instanceof p.default&&this._geometry.primitiveType==Cesium.PrimitiveType.TRIANGLES&&Cesium.GeometryPipeline.computeNormal(this._geometry),this._material&&this._material.addReference&&this._material.addReference()}Object.defineProperty(n,"__esModule",{value:!0});var a=e("./Rotation.js"),o=i(a),s=e("../Util/CSG.js"),l=i(s),c=e("./MeshMaterial.js"),d=i(c),u=e("./GeometryUtils.js"),f=i(u),m=e("./MeshPhongMaterial.js"),p=i(m);r.isGeometrySupported=function(e){return e instanceof Cesium.Geometry||e instanceof l.default||"function"==typeof e.constructor.createGeometry||f.default.isGeometry3js(e)},r.prototype.addInstance=function(e){return this._instances=this._instances||[],e.show=Cesium.defaultValue(e.show,!0),e.primitive=this,e.boundingSphere=new Cesium.BoundingSphere(new Cesium.Cartesian3,this.geometry.boundingSphere?this.geometry.boundingSphere.radius:0),Cesium.Matrix4.getTranslation(e.modelMatrix,e.boundingSphere.center),e.id=e.id||Cesium.createGuid(),e.instanceId=this._instances.length,this.instancedAttributes.forEach(function(t){e[t.name]||(e[t.name]=t.default)}),this._instances.push(e),e},r.traverse=function(e,t){t(e),e.children&&e.children.forEach(function(e){t(e)})},Object.defineProperties(r.prototype,{instances:{get:function(){return this._instances}},modelMatrix:{get:function(){return this._modelMatrix}},parent:{get:function(){return this._parent},set:function(e){this._parent=e,this.modelMatrixNeedsUpdate=!0}},modelMatrixNeedsUpdate:{get:function(){return this._modelMatrixNeedsUpdate},set:function(e){this._modelMatrixNeedsUpdate=e,this._modelMatrixNeedsUpdate&&r.traverse(this,function(t){t._modelMatrixNeedsUpdate=e})}},children:{get:function(){return this._children},set:function(e){this._children=e,this._needsUpdate=!0
}},geometry:{get:function(){return this._geometry},set:function(e){this._geometry=e,this._needsUpdate=!0,this.modelMatrixNeedsUpdate=!0}},material:{get:function(){return this._material},set:function(e){this._material=e,this._needsUpdate=!0}},needsUpdate:{get:function(){return this._needsUpdate},set:function(e){this._needsUpdate=e}},rotation:{get:function(){return this._rotation},set:function(e){e!=this._rotation&&(this._rotation=e,this.modelMatrixNeedsUpdate=!0),this._rotation.paramChanged.removeEventListener(this._onNeedUpdateChanged),this._rotation=e,this._rotation.paramChanged.addEventListener(this._onNeedUpdateChanged)}},position:{get:function(){return this._position},set:function(e){e.x==this._position.x&&e.y==this._position.y&&e.z==this._position.z||(this._position=e,this.modelMatrixNeedsUpdate=!0),this._position=e}},scale:{get:function(){return this._scale},set:function(e){e.x==this._scale.x&&e.y==this._scale.y&&e.z==this._scale.z||(this._scale=e,this.modelMatrixNeedsUpdate=!0),this._scale=e}}}),r.prototype.add=function(e){e.parent!==this&&(e.parent=this),this._children.push(e)},r.prototype.destroy=function(){this.material&&this.material.removeReference&&this.material.removeReference(),this.geometry&&(Cesium.destroyObject(this.geometry),delete this.geometry)},n.default=r},{"../Util/CSG.js":34,"./GeometryUtils.js":5,"./MeshMaterial.js":9,"./MeshPhongMaterial.js":10,"./Rotation.js":17}],9:[function(e,t,n){"use strict";function i(e){function t(e){var t={};for(var n in e)if(e.hasOwnProperty(n)&&Cesium.defined(e[n])){var i=e[n],r={};if(r.needsUpdate=!0,r._disposeCallbacks=[],r.onDispose=function(e){this._disposeCallbacks.indexOf(e)==-1&&this._disposeCallbacks.push(e)},r.destroy=function(){for(var e=0;e<this._disposeCallbacks.length;e++){this._disposeCallbacks[e].call(this)}this._disposeCallbacks.splice(0)},Array.isArray(i)&&i.length>=3&&i.length<=4&&"number"==typeof i[0])e[n]=new Cesium.Color(e[n][0],e[n][1],e[n][2],e[n][3]);else if(Cesium.defined(i.value))for(var o in i)i.hasOwnProperty(o)&&(r[o]=i[o]);e[n].hasOwnProperty("uuid")?(0,a.default)(r,"uuid",e[n].uuid,function(e,t){t.needsUpdate=e}):(0,a.default)(r,"uuid",Cesium.createGuid(),function(e,t){t.needsUpdate=e}),e[n].hasOwnProperty("value")?(0,a.default)(r,"value",e[n].value,function(e,t){t.needsUpdate=e}):(0,a.default)(r,"value",e[n],function(e,t){t.needsUpdate=e}),t[n]=r}return t}function n(e){r.needsUpdate=e}e=Cesium.defaultValue(e,{}),e.uniforms=Cesium.defaultValue(e.uniforms,{});var r=this;this.referenceCount=0,this._disposeCallbacks=[],this._uuid=Cesium.createGuid(),this._defaultColor=Cesium.defaultValue(e.defaultColor,Cesium.Color.WHITE),"string"==typeof this._defaultColor&&(this._defaultColor=Cesium.Color.fromCssColorString(this._defaultColor)),this._pickedColor=Cesium.defaultValue(e.pickedColor,Cesium.Color.YELLOW),"string"==typeof this._pickedColor&&(this._pickedColor=Cesium.Color.fromCssColorString(this._pickedColor)),this._picked=Cesium.defaultValue(e.picked,0),e.uniforms.pickedColor=this._pickedColor,e.uniforms.defaultColor=this._defaultColor,e.uniforms.picked=this._picked,this._uniforms=t(e.uniforms),(0,a.default)(this,"translucent",Cesium.defaultValue(e.translucent,!1),n),(0,a.default)(this,"wireframe",Cesium.defaultValue(e.wireframe,!1),n),(0,a.default)(this,"side",Cesium.defaultValue(e.side,i.Sides.DOUBLE),n),(0,a.default)(this,"uniformStateUsed",Cesium.defaultValue(e.uniformStateUsed,[{uniformStateName:"model",glslVarName:"modelMatrix"}]),n),(0,a.default)(this,"uniforms",this._uniforms,function(){r._uniforms=t(r._uniforms)}),this._vertexShader="//#inner\n void main() {\n\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n}",this._fragmentShader="//#inner"+this._uuid+"\n uniform float picked;\n uniform vec4  pickedColor;\n uniform vec4  defaultColor;\n void main() {\ngl_FragColor = defaultColor;\n if(picked!=0.0){\ngl_FragColor = pickedColor;}}",(0,a.default)(this,"vertexShader",Cesium.defaultValue(e.vertexShader,this._vertexShader),n),(0,a.default)(this,"fragmentShader",Cesium.defaultValue(e.fragmentShader,this._fragmentShader),n),this.depthTest=Cesium.defaultValue(e.depthTest,!0),this.depthMask=Cesium.defaultValue(e.depthMask,!0),this.blending=Cesium.defaultValue(e.blending,!0),this.allowPick=Cesium.defaultValue(e.allowPick,!0),this.pickColorQualifier=Cesium.defaultValue(e.pickColorQualifier,"uniform"),this.needsUpdate=!0}Object.defineProperty(n,"__esModule",{value:!0});var r=e("../Util/defineProperty.js"),a=function(e){return e&&e.__esModule?e:{default:e}}(r);Object.defineProperties(i.prototype,{uuid:{get:function(){return this._uuid}},defaultColor:{set:function(e){"string"==typeof e&&(e=Cesium.Color.fromCssColorString(e)),Cesium.Color.clone(e,this._defaultColor)},get:function(){return this._defaultColor}}}),i.Sides={FRONT:3,BACK:1,DOUBLE:2},i.prototype.onDispose=function(e){this._disposeCallbacks.indexOf(e)==-1&&this._disposeCallbacks.push(e)},i.prototype.addReference=function(){this.referenceCount++},i.prototype.removeReference=function(){if(--this.referenceCount<=0){for(var e=0;e<this._disposeCallbacks.length;e++){this._disposeCallbacks[e].call(this)}this._disposeCallbacks.splice(0),this.destroy()}},i.prototype.destroy=function(){for(var e in this._uniforms)this._uniforms.hasOwnProperty(e)&&this._uniforms[e].destroy&&this._uniforms[e].destroy()},n.default=i},{"../Util/defineProperty.js":36}],10:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(e){e=e?e:{},e.uniforms=e.uniforms?e.uniforms:{shininess:-1,emission:[0,0,0],specular:0},e.uniforms.shininess=Cesium.defaultValue(e.uniforms.shininess,0),e.uniforms.emission=Cesium.defaultValue(e.uniforms.emission,[.2,.2,.2]),e.uniforms.specular=Cesium.defaultValue(e.uniforms.specular,0),o.default.apply(this,arguments),this.vertexShader=d.default,this.fragmentShader=l.default}Object.defineProperty(n,"__esModule",{value:!0});var a=e("./MeshMaterial.js"),o=i(a),s=e("./Shaders/phong_frag.js"),l=i(s),c=e("./Shaders/phong_vert.js"),d=i(c);r.prototype=Object.create(o.default.prototype),n.default=r},{"./MeshMaterial.js":9,"./Shaders/phong_frag.js":25,"./Shaders/phong_vert.js":26}],11:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(){}Object.defineProperty(n,"__esModule",{value:!0});var a=e("./MaterialUtils.js"),o=i(a),s=e("./GeometryUtils.js"),l=i(s),c=e("./Mesh.js"),d=i(c);r.fromMesh3js=function(e){if(r.isMesh3js(e)){var t=e.geometry;l.default.isGeometry3js(t)&&(t=l.default.fromGeometry3js(t));var n=e.material;o.default.isMaterial3js(n)&&(n=o.default.fromMaterial3js(n));var i=new d.default({geometry:t,material:n,position:e.position,scale:e.scale});return i.quaternion=e.quaternion,i}},r.isMesh3js=function(e){return"undefined"!=typeof THREE&&e instanceof THREE.Mesh},n.default=r},{"./GeometryUtils.js":5,"./MaterialUtils.js":7,"./Mesh.js":8}],12:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(){pe||(pe=!0,p=Cesium.Matrix4,h=Cesium.DrawCommand,v=Cesium.defined,_=Cesium.GeometryPipeline,g=Cesium.BufferUsage,x=Cesium.BlendingState,y=Cesium.VertexArray,C=Cesium.ShaderProgram,M=Cesium.DepthFunction,S=Cesium.CullFace,w=Cesium.RenderState,b=Cesium.defaultValue,L=Cesium.Texture,E=Cesium.PixelFormat,P=Cesium.Cartesian3,T=Cesium.Cartesian2,D=Cesium.Cartesian4,R=Cesium.Math,A=Cesium.Color,U=Cesium.Buffer,I=Cesium.ComponentDatatype,N=Cesium.loadArrayBuffer||Cesium.Resource.fetchArrayBuffer,G=Cesium.loadImage||Cesium.Resource.fetchImage,Cesium.Cartesian3.prototype.set=function(e,t,n){this.x=e,this.y=t,this.z=n},Cesium.Cartesian3.prototype.copy=function(e){this.x=e.x,this.y=e.y,this.z=e.z},Cesium.Cartesian2.prototype.set=function(e,t){this.x=e,this.y=t},Cesium.Cartesian2.prototype.copy=function(e){this.x=e.x,this.y=e.y},Cesium.Quaternion.prototype.set=function(e,t,n,i){this.x=e,this.y=t,this.z=n,this.w=i},Cesium.Quaternion.prototype.copy=function(e){this.x=e.x,this.y=e.y,this.z=e.z,this.w=e.w},z=new p,O=new Cesium.Matrix4,F=new Cesium.Cartesian3,V=new Cesium.Cartesian3,B=new Cesium.Cartesian3,H=new Cesium.Cartesian3,k=new Cesium.Ray)}function a(e){var t=e._availableInstances,n=t.length,i=e._center,r=e._vertexBufferTypedArray;(!v(r)||12*n>r.length)&&(r=new Float32Array(12*n)),e._vertexBufferTypedArray=r;for(var a=0;a<n;++a){var o=t[a].modelMatrix,s=p.clone(o,z);s[12]-=i.x,s[13]-=i.y,s[14]-=i.z;var l=12*a;r[l+0]=s[0],r[l+1]=s[4],r[l+2]=s[8],r[l+3]=s[12],r[l+4]=s[1],r[l+5]=s[5],r[l+6]=s[9],r[l+7]=s[13],r[l+8]=s[2],r[l+9]=s[6],r[l+10]=s[10],r[l+11]=s[14]}return r}function o(e,t){var n,i=e._availableInstances,r=i.length,a=e._pickIdBufferTypedArray;for((!a||4*r>a.length)&&(a=new Uint8Array(4*r)),e._pickIdBufferTypedArray=a,n=0;n<r;++n){var o=i[n],s=e._pickIds[o.instanceId];s||(s=t.createPickId(o),e._pickIds[o.instanceId]=s);var l=s.color,c=4*n;a[c]=A.floatToByte(l.red),a[c+1]=A.floatToByte(l.green),a[c+2]=A.floatToByte(l.blue),a[c+3]=A.floatToByte(l.alpha)}return a}function s(e,t){var n,i,r=e._availableInstances,a=r.length,o=t.name,s=t.default instanceof A;"number"==typeof t.default?i=1:t.default instanceof T?i=2:t.default instanceof P?i=3:t.default instanceof D?i=4:s&&(i=4);var l=e["_"+o+"BufferTypedArray"];if((!l||a*i>l.length)&&(l=s?new Uint8Array(a*i):new Float32Array(a*i)),e["_"+o+"BufferTypedArray"]=l,s)for(n=0;n<a;++n){var c=r[n],d=c[o],u=n*i;l[u]=A.floatToByte(d.red),l[u+1]=A.floatToByte(d.green),l[u+2]=A.floatToByte(d.blue),l[u+3]=A.floatToByte(d.alpha)}else if("number"==typeof t.default)for(n=0;n<a;++n){var c=r[n],d=c[o];l[n]=d}else if(t.default instanceof T)for(n=0;n<a;++n){var c=r[n],d=c[o],u=n*i;l[u]=d.x,l[u+1]=d.y}else if(t.default instanceof P)for(n=0;n<a;++n){var c=r[n],d=c[o],u=n*i;l[u]=d.x,l[u+1]=d.y,l[u+2]=d.z}else if(t.default instanceof D)for(n=0;n<a;++n){var c=r[n],d=c[o],u=n*i;l[u]=d.x,l[u+1]=d.y,l[u+2]=d.z,l[u+3]=d.w}return l}function l(e,t,n,i,r){return e.instancedAttributes.forEach(function(a){i[a.name]=++r;var o=U.createVertexBuffer({context:t,typedArray:s(e,a),usage:g.STATIC_DRAW});a._buffer=o;var l={index:i[a.name],vertexBuffer:o,componentsPerAttribute:4,componentDatatype:I.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:0,instanceDivisor:1};"number"==typeof a.default?l.componentsPerAttribute=1:a.default instanceof T?l.componentsPerAttribute=2:a.default instanceof P?l.componentsPerAttribute=3:a.default instanceof D?l.componentsPerAttribute=4:a.default instanceof A&&(l.componentDatatype=I.UNSIGNED_BYTE,l.normalize=!0,l.componentsPerAttribute=4),n.push(l)}),r}function c(e,t){var n=o(e,t);e._pickIdBuffer=U.createVertexBuffer({context:t,typedArray:n,usage:g.STATIC_DRAW});var i=a(e);e._vertexBuffer=U.createVertexBuffer({context:t,typedArray:i,usage:g.STATIC_DRAW})}function d(e,t,n){n=n||0;var i=e._gl,r=e._bufferTarget;i.bindBuffer(r,e._buffer),i.bufferData(r,t,i.DYNAMIC_DRAW),i.bindBuffer(r,null)}function u(e,t){var n=a(e);d(e._vertexBuffer,n);var i=o(e,t);d(e._pickIdBuffer,i),e.instancedAttributes.forEach(function(t){var n=s(e,t);d(t._buffer,n)})}function f(e,t){for(var n=e._instances,i=n.length,r=new Array(i),a=0;a<i;++a)r[a]=t.createPickId(n[a]);return r}function m(e){r(),e=e||{},this._modelMatrix=b(e.modelMatrix,p.IDENTITY),this._actualModelMatrix=p.clone(this._modelMatrix),this._ready=!0,this._modelMatrixNeedsUpdate=!0,this._isWireframe=!1,this._up=b(e.up,new P(0,0,1)),this._position=b(e.position,new P(0,0,0)),this._scale=b(e.scale,new P(1,1,1)),this._rotation=b(e.rotation,{axis:new P(0,0,1),angle:0}),this._rotation=new K.default(this._rotation.axis,this._rotation.angle),this._rotation.paramChanged.addEventListener(this.onModelMatrixNeedUpdate,this),this._chidren=[],this._debug=!1,this._show=b(e.show,!0),this._center=new P,Cesium.Matrix4.getTranslation(this._modelMatrix,this._center),this._framebufferTextures={},this._uniformValueCache={},this._textureCache={},this._uniformMaps={},this.referenceMesh=new ie.default({axisParameter:b(e.referenceAxisParameter,{length:1e5}),show:b(e.showReference,!1)}),this.add(this.referenceMesh),this._pickIds=[],this.beforeUpdate=new Cesium.Event,this._scene=e.scene}Object.defineProperty(n,"__esModule",{value:!0});var p,h,v,_,g,x,y,C,M,S,w,b,L,E,P,T,D,R,A,U,I,N,G,z,O,F,V,B,H,k,j=e("./RendererUtils.js"),Y=i(j),W=e("./MeshMaterial.js"),X=i(W),q=e("./Shaders/ShaderChunk.js"),Z=i(q),Q=e("./Rotation.js"),K=i(Q),J=e("./FramebufferTexture.js"),$=i(J),ee=e("./LOD.js"),te=i(ee),ne=e("./ReferenceMesh.js"),ie=i(ne),re=e("../ThirdParty/tiff-js/tiff.js"),ae=i(re),oe=e("../Util/Path.js"),se=i(oe),le=e("./MaterialUtils.js"),ce=i(le),de=e("./MeshUtils.js"),ue=i(de),fe=e("./ShaderUtils.js"),me=i(fe),pe=!1;m.prototype={remove:function(e){function t(e){e&&(e.vertexArray=e.vertexArray.destroy(),e.shaderProgram=e.shaderProgram.destroy(),Cesium.destroyObject(e))}function n(e){e._drawCommand=t(e._drawCommand),e._pickCommand=t(e._pickCommand),e._textureCommand=t(e._textureCommand)}for(var i=0;i<this._chidren.length;i++)this._chidren[i]==e&&this._chidren.splice(i,1);m.traverse(e,function(){if(n(e),e._actualMesh&&e._actualMesh._drawCommand){var t=e._actualMesh;n(t),Cesium.destroyObject(t.geometry),e._actualMesh=t&&t.destroy(),Cesium.destroyObject(t)}},!1)},pickPosition:function(e,t){if(this._scene&&(this._scene.pickPosition(e,F),F))return this.worldCoordinatesToLocal(F,F),Cesium.Cartesian3.clone(F,t),t},getPickRay:function(e,t){if(this._scene&&(t||(t=Cesium.Ray()),this._scene.camera.getPickRay(e,k),this._scene.pickPosition(e,F),F))return Cesium.Cartesian3.clone(k.direction,V),this.worldCoordinatesToLocal(k.origin,H),this.worldCoordinatesToLocal(F,F),Cesium.Cartesian3.add(H,V,B),Cesium.Cartesian3.subtract(F,B,V),Cesium.Cartesian3.clone(F,t.origin),Cesium.Cartesian3.clone(V,t.direction),t},worldCoordinatesToLocal:function(e,t){return t||(t=new P),Cesium.Matrix4.inverseTransformation(this._actualModelMatrix,O),Cesium.Matrix4.multiplyByPoint(O,e,t),t},localToWorldCoordinates:function(e,t){return t||(t=new P),Cesium.Matrix4.multiplyByPoint(this._actualModelMatrix,e,t),t},onModelMatrixNeedUpdate:function(){this._modelMatrixNeedsUpdate=!0},setPosition:function(e,t,n){var i=!1;1==arguments.length&&("number"==typeof e?(e!=this._position.x&&(i=!0),this._position.x=e):e instanceof Cesium.Cartesian3&&(e==this._position.x&&t==this._position.y&&n==this._position.z||(i=!0),this._position.x=e.x,this._position.y=e.y,this._position.z=e.z)),2==arguments.length&&"number"==typeof t&&(t!=this._position.y&&(i=!0),this._position.y=t),3==arguments.length&&"number"==typeof n&&(n!=this._position.z&&(i=!0),this._position.z=n),i&&(this._modelMatrixNeedsUpdate=!0)},setScale:function(e,t,n){var i=!1;1==arguments.length&&("number"==typeof e?(e!=this._scale.x&&(i=!0),this._scale.x=e):e instanceof Cesium.Cartesian3&&(e==this._scale.x&&t==this._scale.y&&n==this._scale.z||(i=!0),this._scale.x=e.x,this._scale.y=e.y,this._scale.z=e.z)),2==arguments.length&&"number"==typeof t&&(t!=this._scale.y&&(i=!0),this._scale.y=t),3==arguments.length&&"number"==typeof n&&(n!=this._scale.z&&(i=!0),this._scale.z=n),i&&(this._modelMatrixNeedsUpdate=!0)},toWireframe:function(e){return e.primitiveType!==Cesium.PrimitiveType.TRIANGLES&&e.primitiveType!==Cesium.PrimitiveType.TRIANGLE_FAN&&e.primitiveType!==Cesium.PrimitiveType.TRIANGLE_STRIP?e:(e.triangleIndices||(e.triangleIndices=e.indices),e=_.toWireframe(e))},restoreFromWireframe:function(e){return e.primitiveType==Cesium.PrimitiveType.POINTS?e:(e.triangleIndices&&(e.indices=e.triangleIndices),e.primitiveType=Cesium.PrimitiveType.TRIANGLES,e)},createBoundingSphere:function(e){for(var t=e._instances.length,n=new Array(t),i=0;i<t;++i)n[i]=p.getTranslation(e._instances[i].modelMatrix,new P);e._boundingSphere=Cesium.BoundingSphere.fromPoints(n),P.clone(e._boundingSphere.center,e._center)},createDrawCommand:function(e,t){var n=this,i=t.context,r=e.geometry,a=e.material,o={primitive:this,id:e},s=i.createPickId(o);n._pickIds.push(s);var d,u=new Cesium.DrawCommand({modelMatrix:e._instances&&e._instances.length>0?void 0:p.clone(this.modelMatrix),owner:e,primitiveType:r.primitiveType,cull:!1,instanceCount:e._instances&&e._instances.length>0?e._availableInstances.length:void 0,pass:a.translucent?Cesium.Pass.TRANSLUCENT:Cesium.Pass.OPAQUE}),m=_.createAttributeLocations(r);if(e._instances&&e._instances.length){this.createBoundingSphere(e),d=[];var h=0;for(var v in m)m.hasOwnProperty(v)&&(h=Math.max(h,m[v]));var x=e;x._pickIds=f(x,t.context),c(x,t.context);var M=I.getSizeInBytes(I.FLOAT),S={czm_modelMatrixRow0:{index:h+1,vertexBuffer:x._vertexBuffer,componentsPerAttribute:4,componentDatatype:I.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:12*M,instanceDivisor:1},czm_modelMatrixRow1:{index:h+2,vertexBuffer:x._vertexBuffer,componentsPerAttribute:4,componentDatatype:I.FLOAT,normalize:!1,offsetInBytes:4*M,strideInBytes:12*M,instanceDivisor:1},czm_modelMatrixRow2:{index:h+3,vertexBuffer:x._vertexBuffer,componentsPerAttribute:4,componentDatatype:I.FLOAT,normalize:!1,offsetInBytes:8*M,strideInBytes:12*M,instanceDivisor:1}};S.a_pickColor={index:h+4,vertexBuffer:x._pickIdBuffer,componentsPerAttribute:4,componentDatatype:I.UNSIGNED_BYTE,normalize:!0,offsetInBytes:0,strideInBytes:0,instanceDivisor:1};for(var v in S)S.hasOwnProperty(v)&&(m[v]=++h,d.push(S[v]));h=l(e,t.context,d,m,h)}u.vertexArray=y.fromGeometry({context:i,geometry:r,attributeLocations:m,bufferUsage:g.STATIC_DRAW,vertexArrayAttributes:d}),d&&d.length&&(u._cacehVertexArrayAttributes=d.map(function(e){return e})),u.vertexArray._attributeLocations=m;var w=(s.color,{fragmentShader:this.getFragmentShaderSource(a),vertexShader:this.getVertexShaderSource(e,a)});if(a.material3js&&(w=me.default.processShader3js(a.material3js,w)),e._instances&&e._instances.length){var b=w.vertexShader;b="attribute vec4 czm_modelMatrixRow0;\nattribute vec4 czm_modelMatrixRow1;\nattribute vec4 czm_modelMatrixRow2;\nuniform mat4 czm_instanced_modifiedModelView;\nattribute vec4 a_pickColor;\nvarying vec4 czm_pickColor;\n"+Cesium.ShaderSource.replaceMain(b,"czm_instancing_main")+"void main()\n{\n    modelMatrix = mat4(czm_modelMatrixRow0.x, czm_modelMatrixRow1.x, czm_modelMatrixRow2.x, 0.0, czm_modelMatrixRow0.y, czm_modelMatrixRow1.y, czm_modelMatrixRow2.y, 0.0, czm_modelMatrixRow0.z, czm_modelMatrixRow1.z, czm_modelMatrixRow2.z, 0.0, czm_modelMatrixRow0.w, czm_modelMatrixRow1.w, czm_modelMatrixRow2.w, 1.0);\n    modelViewMatrix = czm_instanced_modifiedModelView * modelMatrix;\n    u_modelMatrix =modelMatrix;\n    u_modelViewMatrix = modelViewMatrix ;\n    czm_instancing_main();\n    czm_pickColor = a_pickColor;\n}\n",w.vertexShader=b}var b=new Cesium.ShaderSource({sources:[w.vertexShader]}),L=new Cesium.ShaderSource({sources:[w.fragmentShader]}),E=a.translucent;!E&&i.fragmentDepth&&L.defines.push("WRITE_DEPTH");u._sp=C.fromCache({context:i,fragmentShaderSource:L,vertexShaderSource:b,attributeLocations:m}),Cesium.defined(e.material.allowPick)||(e.material.allowPick=!0),e.material.allowPick,u.shaderProgram=u._sp,u.renderState=this.getRenderState(a),u._renderStateOptions=a._renderStateOptions,u.uniformMap=this.getUniformMap(a,t),u.uniformMap.czm_pickColor=function(){return s.color},u.uniformMap.czm_instanced_modifiedModelView=this.getModifiedModelViewCallback(i,e);var P=new Cesium.DrawCommand({owner:e,pickOnly:!0,instanceCount:e._instances&&e._instances.length>0?e._availableInstances.length:void 0,modelMatrix:e._instances&&e._instances.length>0?void 0:p.clone(this.modelMatrix),primitiveType:r.primitiveType,cull:a.cullFrustum,pass:a.translucent?Cesium.Pass.TRANSLUCENT:Cesium.Pass.OPAQUE});b=new Cesium.ShaderSource({sources:[w.vertexShader]}),L=new Cesium.ShaderSource({sources:[w.fragmentShader],pickColorQualifier:e._instances&&e._instances.length?"varying":a.pickColorQualifier||"uniform"}),L.defines.push("ONLY_SUN_LIGHTING"),E=a.translucent,!E&&i.fragmentDepth&&L.defines.push("WRITE_DEPTH"),b.defines.push("LOG_DEPTH"),L.defines.push("LOG_DEPTH"),L.sources.push("#ifdef GL_EXT_frag_depth \n#extension GL_EXT_frag_depth : enable \n#endif \n\n");var T=C.replaceCache({context:i,shaderProgram:T,vertexShaderSource:b,fragmentShaderSource:L,attributeLocations:m});return P.vertexArray=u.vertexArray,P.renderState=this.getRenderState(a),P.shaderProgram=T,P.uniformMap=u.uniformMap,P.executeInClosestFrustum=E,e._pickCommand=P,u},getModifiedModelViewCallback:function(e,t){return function(){return t._rtcTransform||(t._rtcTransform=new p),t._rtcModelView||(t._rtcModelView=new p),p.multiplyByTranslation(t.modelMatrix,t._center,t._rtcTransform),p.multiply(e.uniformState.view,t._rtcTransform,t._rtcModelView)}},getRenderState_old:function(e){var t={blending:e.blending?x.ALPHA_BLEND:x.DISABLED,depthTest:{enabled:e.depthTest,func:M.GREATER},cull:{enabled:!0,face:S.FRONT},depthRange:{near:0,far:1},colorMask:{red:!0,green:!0,blue:!0,alpha:!0},depthMask:e.depthMask};switch(t.cull.enabled=!0,t.blending.color={red:0,green:0,blue:0,alpha:0},e.side){case X.default.Sides.FRONT:t.cull.face=S.BACK;break;case X.default.Sides.BACK:t.cull.face=S.FRONT;break;default:t.cull.enabled=!1}return t=w.fromCache(t)},getRenderState:function(e){var t={blending:e.blending?x.ALPHA_BLEND:x.DISABLED,depthTest:{enabled:e.depthTest,func:M.LESS},cull:{enabled:!1,face:S.FRONT},depthRange:{near:0,far:1},colorMask:{red:!0,green:!0,blue:!0,alpha:!0},depthMask:e.depthMask};switch(t.cull.enabled=!0,e.side){case X.default.Sides.FRONT:t.cull.face=S.BACK;break;case X.default.Sides.BACK:t.cull.face=S.FRONT;break;default:t.cull.enabled=!1}return e._renderStateOptions=t,w.fromCache(t)},getUniformMap:function(e,t){function n(e,n,i){var r=function i(){if((!l._textureCache[n.uuid]||n.needsUpdate)&&!i.allLoaded&&!i.isLoading){for(var r=[],a=0;a<n.value.length;a++)if(n.value[a]instanceof HTMLCanvasElement||n.value[a]instanceof HTMLVideoElement||n.value[a]instanceof HTMLImageElement){var o=Cesium.when.defer();requestAnimationFrame(function(){o.resolve(n.value[a])}),r.push(o)}else{if("string"!=typeof n.value[a])throw Error(e+""+a+"给定值“ "+n[a]+"” 不是有效的纹理图片");r.push(G(n.value[a]))}i.isLoading=!0,n.needsUpdate=!1,Cesium.when.all(r,function(e){l._textureCache[n.uuid]=new Cesium.CubeMap({context:t.context,source:{positiveX:e[0],negativeX:e[1],positiveY:e[2],negativeY:e[3],positiveZ:e[4],negativeZ:e[5]}}),n.onDispose&&n.onDispose(function(){l._textureCache[n.uuid]&&(l._textureCache[n.uuid].destroy(),delete l._textureCache[n.uuid])}),i.allLoaded=!0,i.isLoading=!1})}return i.allLoaded?l._textureCache[n.uuid]:(l.defaultCubeMap||(l.defaultTextureImage||(l.defaultTextureImage=document.createElement("canvas"),l.defaultTextureImage.width=1,l.defaultTextureImage.height=1),l.defaultCubeMap=new Cesium.CubeMap({context:t.context,source:{positiveX:l.defaultTextureImage,negativeX:l.defaultTextureImage,positiveY:l.defaultTextureImage,negativeY:l.defaultTextureImage,positiveZ:l.defaultTextureImage,negativeZ:l.defaultTextureImage}})),l.defaultCubeMap)};return r.allLoaded&&(r.allLoaded=!1,r.isLoading=!1),r}function i(e,t){var n=Cesium.TextureMinificationFilter,i=Cesium.TextureWrap,r=e.sampler,a=r.minificationFilter===n.NEAREST_MIPMAP_NEAREST||r.minificationFilter===n.NEAREST_MIPMAP_LINEAR||r.minificationFilter===n.LINEAR_MIPMAP_NEAREST||r.minificationFilter===n.LINEAR_MIPMAP_LINEAR,o=a||r.wrapS===i.REPEAT||r.wrapS===i.MIRRORED_REPEAT||r.wrapT===i.REPEAT||r.wrapT===i.MIRRORED_REPEAT,s=e.source,l=!R.isPowerOfTwo(s.width)||!R.isPowerOfTwo(s.height);if(o&&l){var d=document.createElement("canvas");d.width=R.nextPowerOfTwo(s.width),d.height=R.nextPowerOfTwo(s.height);d.getContext("2d").drawImage(s,0,0,s.width,s.height,0,0,d.width,d.height),s=d}var u;return e.target===c.TEXTURE_2D&&(u=new L({context:t,source:s,width:e.width,height:e.height,pixelFormat:e.internalFormat,pixelDatatype:e.type,sampler:r,flipY:e.flipY})),a&&u.generateMipmap(),u}function r(e,n){var r;if(v(e.internalFormat))r=new L({context:t.context,pixelFormat:e.internalFormat,width:e.width,height:e.height,source:{arrayBufferView:e.bufferView},flipY:n.flipY});else{var a=Cesium.WebGLConstants.RGB;(e instanceof HTMLCanvasElement||e instanceof HTMLVideoElement||e.src&&e.src.toLocaleLowerCase().indexOf(".png")>=0)&&(a=Cesium.WebGLConstants.RGBA),r=n.sampler?i({context:t.context,source:e,target:c.TEXTURE_2D,width:n.width,height:n.height,pixelFormat:a,flipY:n.flipY,sampler:new Cesium.Sampler(n.sampler)},t.context):new L({context:t.context,source:e,target:c.TEXTURE_2D,width:n.width,height:n.height,pixelFormat:a,flipY:!Cesium.defined(n.flipY)||n.flipY})}return r}function a(e){return function n(){var i="string"==typeof e.value?e.value:null;if(i||(e.value instanceof HTMLImageElement||e.value instanceof HTMLCanvasElement||e.value instanceof HTMLVideoElement)&&(e.value.uuid||(e.value.uuid=e.uuid),i=e.value.uuid),!l._textureCache[i]||e.needsUpdate){if(e.value instanceof HTMLImageElement||e.value instanceof HTMLCanvasElement||e.value instanceof HTMLVideoElement){var a=e.value;return e.value.id||(e.value.id=e.uuid),l._textureCache[i]=r(a,e),e.onDispose&&e.onDispose(function(){l._textureCache[i]&&(l._textureCache[i].destroy(),delete l._textureCache[i])}),e.needsUpdate=!1,l._textureCache[i]}if("string"==typeof e.value&&!n.isLoading){n.isLoading=!0,e.needsUpdate=!1;var o=e.value.toLocaleLowerCase();"tif"==se.default.GetExtension(o).slice(1)?N(o).then(function(t){var a=new ae.default,o=a.parseTIFF(t);l._textureCache[i]&&l._textureCache[i].destroy&&l._textureCache[i].destroy(),l._textureCache[i]=r(o,e),e.onDispose&&e.onDispose(function(){l._textureCache[i]&&(l._textureCache[i].destroy(),delete l._textureCache[i])}),n.isLoading=!1}).otherwise(function(e){console.log(e)}):G(e.value).then(function(t){l._textureCache[i]&&l._textureCache[i].destroy&&l._textureCache[i].destroy(),l._textureCache[i]=r(t,e),e.onDispose&&e.onDispose(function(){l._textureCache[i]&&(l._textureCache[i].destroy(),delete l._textureCache[i])}),n.isLoading=!1}).otherwise(function(e){console.log(e)})}return l.defaultTextureImage||(l.defaultTextureImage=document.createElement("canvas"),l.defaultTextureImage.width=1,l.defaultTextureImage.height=1),l.defaultTexture||(l.defaultTexture=new L({context:t.context,source:l.defaultTextureImage})),l.defaultTexture}return l._textureCache[i]}}var o=this._uniformMaps;if(o[e.uuid]&&!e.needsUpdate)return o[e.uuid];var s={};o[e.uuid]=s,e.onDispose&&e.onDispose(function(){delete o[e.uuid]}),e.needsUpdate=!1,s.cameraPosition=function(){return t.camera.position},s.u_cameraPosition=function(){return t.camera.position},s.u_normalMatrix=function(){return t.context.uniformState.normal},s.u_projectionMatrix=function(){return t.context.uniformState.projection},s.u_modelViewMatrix=function(){return t.context.uniformState.modelView},s.normalMatrix=function(){return t.context.uniformState.normal},s.projectionMatrix=function(){return t.context.uniformState.projection},s.modelViewMatrix=function(){return t.context.uniformState.modelView},s.modelMatrix=function(){return t.context.uniformState.model},s.u_modelMatrix=function(){return t.context.uniformState.model},s.u_viewMatrix=function(){return t.context.uniformState.view},s.viewMatrix=function(){return t.context.uniformState.view},s.logDepthBufFC=function(){return 2/(Math.log(t.camera.frustum.far+1)/Math.LN2)},e.uniformStateUsed&&e.uniformStateUsed.length&&e.uniformStateUsed.forEach(function(e){if(!s[e.glslVarName]){if(!t.context.uniformState[e.uniformStateName])throw new Error(e.uniformStateName+"不是Cesium引擎的内置对象");s[e.glslVarName]=function(){return t.context.uniformState[e.uniformStateName]}}});var l=this,c=Cesium.WebGLConstants;if(e.uniforms){var d=e.uniforms;for(var u in d)if(d.hasOwnProperty(u)&&Cesium.defined(d[u].value)&&null!=d[u].value){if(Array.isArray(d[u].value)&&0==d[u].value.length)continue;var f=d[u];if(void 0==f||null==f)continue;!function(e,i){if(void 0!==i&&null!==i){var r="string"==typeof i.value,o="string"==typeof i.value;if("string"==typeof i.value){var c=i.value.toLocaleLowerCase();if(c.endsWith(".png")||c.endsWith(".jpg")||c.endsWith(".bmp")||c.endsWith(".gif")||c.endsWith(".tif")||c.endsWith(".tiff")||c.startsWith("data:"))r=!0,o=!1;else try{Cesium.Color.fromCssColorString(i.value),r=!0,o=!1}catch(e){r=!1,o=!1}}i.value instanceof Cesium.Cartesian2||i.value instanceof Cesium.Cartesian3||i.value instanceof Cesium.Cartesian4||i.value instanceof Cesium.Color||i.value instanceof Cesium.Matrix4||i.value instanceof Cesium.Matrix3||i.value instanceof Cesium.Matrix2||i.value instanceof Cesium.Texture||"number"==typeof i.value||"boolean"==typeof i.value||o||i.isColor||i.isCartesian2||i.isCartesian3||i.isCartesian4||i.value instanceof Cesium.Texture||i.value instanceof Array&&("number"==typeof i.value[0]||i.value[0]instanceof Cesium.Cartesian2||i.value[0]instanceof Cesium.Cartesian3||i.value[0]instanceof Cesium.Cartesian4)?(l._uniformValueCache||(l._uniformValueCache={}),l._uniformValueCache[i.uuid]=i,o&&(i.value=Cesium.Color.fromCssColorString(i.value)),s[e]=function(){return l._uniformValueCache[i.uuid].value},i.onDispose&&i.onDispose(function(){l._uniformValueCache[i.uuid]&&delete l._uniformValueCache[i.uuid]})):i.value instanceof Array&&6==i.value.length?s[e]=n(e,i):r||i.value instanceof HTMLImageElement||i.value instanceof HTMLCanvasElement||i.value instanceof HTMLVideoElement?s[e]=a(i):i.value instanceof $.default&&(l._renderToTextureCommands||(l._renderToTextureCommands=[]),l._framebufferTextures[i.uuid]||(l._framebufferTextures[i.uuid]=i),s[e]=function(){return l._framebufferTextures[i.uuid]&&l._framebufferTextures[i.uuid].value.texture?l._framebufferTextures[i.uuid].value.texture:t.context.defaultTexture})}}(u,f)}}return this._uniformMaps[e.uuid]},getVertexShaderSource:function(e,t){var n=e.geometry,i="\n        uniform mat4 modelViewMatrix;\n        uniform mat4 viewMatrix;\n        uniform mat4 modelMatrix;\n        uniform mat4 projectionMatrix;\n        uniform mat3 normalMatrix;\n        uniform mat4 u_modelViewMatrix;\n        uniform mat4 u_viewMatrix;\n        uniform mat4 u_modelMatrix;\n        uniform mat4 u_projectionMatrix;\n        uniform mat3 u_normalMatrix;\n        uniform vec3 cameraPosition;\n        uniform vec3 u_cameraPosition;\n",r=[e._instances&&e._instances.length>0?"mat4 modelViewMatrix":"uniform mat4 modelViewMatrix",e._instances&&e._instances.length>0?"mat4 modelMatrix":"uniform mat4 modelMatrix","uniform mat4 projectionMatrix","uniform mat3 normalMatrix",e._instances&&e._instances.length>0?"mat4 u_modelViewMatrix":"uniform mat4 u_modelViewMatrix",e._instances&&e._instances.length>0?"mat4 u_modelMatrix":"uniform mat4 u_modelMatrix","uniform mat4 u_projectionMatrix","uniform mat3 u_normalMatrix","uniform mat4 u_viewMatrix","uniform mat4 viewMatrix","uniform vec3 cameraPosition","uniform vec3 u_cameraPosition"];if(t.vertexShader){i="",r.forEach(function(e){t.vertexShader.indexOf(e)<0&&(i+=e+";\n")});var a=function(e){var t="",i=n.attributes;for(var r in i)if(i.hasOwnProperty(r)){var a=i[r];if(a){var o=null;switch(a.componentsPerAttribute){case 1:o="float";break;case 2:o="vec2";break;case 3:o="vec3";break;case 4:o="vec4"}if(o){if(e.indexOf("attribute "+o+" "+r)>=0)continue;t+="attribute "+o+" "+r+";\n"}}}return t}(t.vertexShader)+i+t.vertexShader;return a=Z.default.parseIncludes(a)}throw new Error("material.vertexShader 是必须参数")},getFragmentShaderSource:function(e){if(e.fragmentShader){return Z.default.parseIncludes(e.fragmentShader)}throw new Error("material.fragmentShader 是必须参数")}},m.prototype._computeModelMatrix=function(e,t){e._actualMesh&&(e=e._actualMesh);var n=this;if(e instanceof te.default||e instanceof ie.default||"function"==typeof e.update)e.parent?e.parent==n?e.update(n._actualModelMatrix,t):e.parent.modelMatrix?e.update(e.parent.modelMatrix,t):e.update(n._actualModelMatrix,t):e.update(n._actualModelMatrix,t);else{e.position;if(e.parent instanceof te.default)p.clone(e.parent.modelMatrix,e.modelMatrix);else if(e._modelMatrixNeedsUpdate){var i=e.quaternion?e.quaternion:e.rotation;if(e.parent&&e.parent.modelMatrix){
var r=e.parent.modelMatrix?e.parent.modelMatrix:e._drawCommand.modelMatrix;Y.default.computeModelMatrix(r,e.position,i,e.scale,e.modelMatrix)}else Y.default.computeModelMatrix(n._actualModelMatrix,e.position,i,e.scale,e.modelMatrix);e._modelMatrixNeedsUpdate=!1}}},m.prototype.update=function(e){if(this._scene||(this._scene=e.camera._scene),!(!this._ready||!this.show&&this._chidren.length>0)){this.beforeUpdate.raiseEvent(e);var t=this,n=!1,i=e.camera._scene._globe._surface.tileProvider._debug.wireframe;this.debug&&(i=!0),i!=this._isWireframe&&(n=!0),this._modelMatrixNeedsUpdate&&(this._actualModelMatrix=Y.default.computeModelMatrix(this._modelMatrix,this._position,this._rotation,this._scale,this._actualModelMatrix),this._up&&this._up.y&&(this._actualModelMatrix=Y.default.yUp2Zup(this._actualModelMatrix,this._actualModelMatrix)),Cesium.Cartesian3.clone(this._scale,this._oldScale),Cesium.Cartesian3.clone(this._position,this._oldPosition),this._modelMatrixNeedsUpdate=!1),m.traverse(this,function(r){if(r.show&&(!r._instances||!r._instances.length||(r._availableInstances=r._availableInstances||[],r._availableInstances.splice(0),r.geometry.boundingSphere||(r.geometry.boundingSphere=Cesium.BoundingSphere.fromVertices(r.geometry.attributes.position.values)),r._instances.forEach(function(t){if(t.show){p.getTranslation(t.modelMatrix,t.boundingSphere.center),t.boundingSphere.radius=r.geometry.boundingSphere.radius;e.cullingVolume.computeVisibility(t.boundingSphere)!=Cesium.Intersect.OUTSIDE&&r._availableInstances.push(t)}}),0!=r._availableInstances.length))){if(ue.default.isMesh3js(r)){if(!r._actualMesh||r.needsUpdate||r.geometry.needsUpdate)r._actualMesh=ue.default.fromMesh3js(r),r.modelMatrixNeedsUpdate=!0;else{for(var a in r.geometry.attributes)r.geometry.attributes.hasOwnProperty(a)&&(r._actualMesh.geometry.attributes[a].needsUpdate=r.geometry.attributes[a].needsUpdate);var o=r.geometry.index;o&&o.needsUpdate&&(r._actualMesh.geometry.needsUpdate=!0)}r._actualMesh.quaternion=Cesium.Quaternion.clone(r.quaternion),r._actualMesh.position=r.position,r._actualMesh.scale=r.scale,r._actualMesh.modelMatrixNeedsUpdate=r.modelMatrixNeedsUpdate,r=r._actualMesh,ce.default.updateMaterialFrom3js(r.material)}if(t._computeModelMatrix(r,e),"function"!=typeof r.update){if(e.passes.pick&&!r.material.allowPick)return;if(!r._drawCommand||r.needsUpdate||r.geometry.needsUpdate||n)i||r.material.wireframe?t.toWireframe(r.geometry):t.restoreFromWireframe(r.geometry),r._drawCommand&&r._drawCommand.destroy(),r._drawCommand=t.createDrawCommand(r,e),r.needsUpdate=!1,r.geometry.needsUpdate=!1;else{for(var s in r.geometry.attributes)if(r.geometry.attributes.hasOwnProperty(s)&&r.geometry.attributes[s]&&r.geometry.attributes[s].needsUpdate){var l=r._drawCommand.vertexArray._attributeLocations[s],c=r._drawCommand.vertexArray._attributes[l].vertexBuffer,d=r.geometry.attributes[s].values,f=c._gl,m=c._bufferTarget;f.bindBuffer(m,c._buffer),f.bufferData(m,d,g.STATIC_DRAW),f.bindBuffer(m,null)}if(r.geometry.indexNeedsUpdate){var c=r._drawCommand.vertexArray.indexBuffer,f=c._gl,m=c._bufferTarget;f.bindBuffer(m,c._buffer),f.bufferData(m,r.geometry.indices,g.STATIC_DRAW),f.bindBuffer(m,null),r.geometry.indexNeedsUpdate=!1}r._instances&&r._instances.length&&u(r,e.context)}if(r._drawCommand.modelMatrix=r.modelMatrix,r._instances&&r._instances.length?(r._drawCommand.boundingVolume=r._boundingSphere,r._drawCommand.instanceCount=r._availableInstances.length,r._pickCommand&&(r._pickCommand.instanceCount=r._availableInstances.length)):(r._drawCommand.boundingVolume||(r.geometry.boundingSphere||(r.geometry.boundingSphere=Cesium.BoundingSphere.fromVertices(r.geometry.attributes.position.values)),r._drawCommand.boundingVolume=Cesium.BoundingSphere.clone(r.geometry.boundingSphere)),Cesium.Matrix4.getTranslation(r.modelMatrix,r._drawCommand.boundingVolume.center)),r._pickCommand.boundingVolume=r._drawCommand.boundingVolume,r._drawCommand.uniformMap=t.getUniformMap(r.material,e),e.passes.pick){var h=r._pickCommand;e.commandList.push(h)}else r.material._renderStateOptions.depthTest.enabled=r.material.depthTest,r._drawCommand.renderState=w.fromCache(r.material._renderStateOptions),r._drawCommand.shaderProgram=r._drawCommand._sp,e.commandList.push(r._drawCommand)}else r.needsUpdate=!1}},!0);for(var r in t._framebufferTextures)if(t._framebufferTextures.hasOwnProperty(r)){var a=t._framebufferTextures[r].value;t.updateFrameBufferTexture(e,a)}this._isWireframe=i,n=!1,this._modelMatrixNeedsUpdate=!1,this._geometryChanged=!1}},m.prototype.initFrameBufferTexture=function(e,t,n){var i=this,r=t;r instanceof $.default&&(r.drawCommands=[],m.traverse(r.mesh,function(t){if(ue.default.isMesh3js(t)){var a=!t._actualMesh||t.needsUpdate||t.geometry.needsUpdate;if(a&&(t._actualMesh=ue.default.fromMesh3js(t)),!a){for(var o in t.geometry.attributes)t.geometry.attributes.hasOwnProperty(o)&&(t._actualMesh.geometry[o].needsUpdate=t.geometry.attributes[o].needsUpdate);var s=t.geometry.getIndex();s&&s.needsUpdate&&(t._actualMesh.geometry.needsUpdate=!0)}t._actualMesh.quaternion=Cesium.Quaternion.clone(t.quaternion),t._actualMesh.position=t.position,t._actualMesh.scale=t.scale,t._actualMesh.modelMatrixNeedsUpdate=t.modelMatrixNeedsUpdate,t=t._actualMesh,ce.default.updateMaterialFrom3js(t.material)}if(i._computeModelMatrix(t,e),!t._textureCommand||t.needsUpdate||t.geometry.needsUpdate)t.material.wireframe?i.toWireframe(t.geometry):i.restoreFromWireframe(t.geometry),t._textureCommand=i.createDrawCommand(t,e),t.needsUpdate=!1,t.material.needsUpdate=!1;else{var l=!1;for(var c in t.geometry.attributes)if(t.geometry.attributes.hasOwnProperty(c)&&t.geometry.attributes[c]&&t.geometry.attributes[c]&&t.geometry.attributes[c].needsUpdate){var d=t._textureCommand.vertexArray._attributeLocations[c],u=t._textureCommand.vertexArray._attributes[d].vertexBuffer,f=t.geometry.attributes[c].values,m=u._gl;if(u._sizeInBytes!=f*f.constructor.BYTES_PER_ELEMENT){l=!0;break}var p=u._bufferTarget;m.bindBuffer(p,u._buffer),m.bufferData(p,f,g.STATIC_DRAW),m.bindBuffer(p,null)}if(!l&&t.geometry.indexNeedsUpdate){var h=t.geometry.indices,u=t._textureCommand.vertexArray.indexBuffer;if(u._sizeInBytes!=h.length*h.constructor.BYTES_PER_ELEMENT){u.destroy();var v=U.createIndexBuffer({context:e.context,typedArray:h,usage:g.STATIC_DRAW,indexDatatype:h instanceof Uint16Array?Cesium.IndexDatatype.UNSIGNED_SHORT:Cesium.IndexDatatype.UNSIGNED_INT});t._textureCommand.vertexArray._indexBuffer=v}else{var m=u._gl,p=u._bufferTarget;m.bindBuffer(p,u._buffer),m.bufferData(p,h,g.STATIC_DRAW),m.bindBuffer(p,null)}t.geometry.indexNeedsUpdate=!1}if(l){var _=t._textureCommand,x=_.vertexArray._attributeLocations,C=_._cacehVertexArrayAttributes;_.vertexArray.destroy(),_.vertexArray=y.fromGeometry({context:e.context,geometry:t.geometry,attributeLocations:x,bufferUsage:g.STATIC_DRAW,vertexArrayAttributes:C}),C&&C.length&&(_._cacehVertexArrayAttributes=C.map(function(e){return e})),_.vertexArray._attributeLocations=x;for(var c in t.geometry.attributes)t.geometry.attributes.hasOwnProperty(c)&&t.geometry.attributes[c]&&(t.geometry.attributes[c].needsUpdate=!1)}}t._textureCommand.modelMatrix=t.modelMatrix;var M=e.context,S=M.drawingBufferWidth,b=M.drawingBufferHeight,P=!1;if(!r.texture||r.texture.width!=S||r.texture.height!=b){var T=r._notFullScreen||Cesium.defined(r.texture);T||(r.texture=new L({context:M,width:S,height:b,pixelFormat:E.RGBA}),P=!0),r._notFullScreen=T}r.depthTexture&&r.depthTexture.width==r.texture.width&&r.depthTexture.height==r.texture.height||(r.depthTexture=new Cesium.Texture({context:M,width:r.texture.width,height:r.texture.height,pixelFormat:Cesium.PixelFormat.DEPTH_COMPONENT,pixelDatatype:Cesium.PixelDatatype.UNSIGNED_SHORT}),P=!0),r.framebuffer&&!P||(r.framebuffer=new Cesium.Framebuffer({context:M,colorTextures:[r.texture],destroyAttachments:!1,depthTexture:r.depthTexture})),t.material._renderStateOptions.depthTest.enabled=t.material.depthTest,n&&(t.material._renderStateOptions.viewport=n),t._textureCommand.renderState=w.fromCache(t.material._renderStateOptions),r.drawCommands.push(t._textureCommand)},!0))},m.prototype.updateFrameBufferTexture=function(e,t,n){this.initFrameBufferTexture(e,t,n);var i=t;if(i.drawCommands&&i.drawCommands.length>0){Y.default.renderToTexture(i.drawCommands,e,i.framebuffer);for(var r=0;r<i.drawCommands.length;r++)i.drawCommands[r]._renderStateOptions.viewport=void 0,i.drawCommands[r].renderState=w.fromCache(i.drawCommands[r]._renderStateOptions)}t.ready||(t.ready=!0,t.readyPromise.resolve(t))},m.prototype.getPixels=function(e,t,n,i,r){n=n?n:{},n.x=n.x?n.x:0,n.y=n.y?n.y:0,n.width||(n.width=e.context._canvas.width),n.height||(n.height=e.context._canvas.height),this.initFrameBufferTexture(e,t,n);var a=t;if(a.drawCommands&&a.drawCommands.length>0){if(!a._computeTexture||a._computeTexture&&(a._computeTexture.width!=n.width||a._computeTexture.height!=n.height)){var o=n.pixelDatatype,s=n.pixelFormat;if(o==Cesium.PixelDatatype.FLOAT&&!e.context._gl.getExtension("OES_texture_float"))throw new Cesium.DeveloperError("此设备不支持浮点型纹理");a._computeTexture&&(a._computeTexture.destroy(),a._computeTexture=null),a._computeTexture=new Cesium.Texture({context:e.context,width:n.width,height:n.height,pixelFormat:s,pixelDatatype:o})}r=Y.default.renderToPixels(a.drawCommands,e,a._computeTexture,i?i:n,r);for(var l=0;l<a.drawCommands.length;l++)a.drawCommands[l].renderState.viewport=void 0;return r}return null},m.prototype.add=function(e){this._chidren.push(e)},m.prototype.destroy=function(){this._ready=!1,m.traverse(this,function(e){e._drawCommand&&delete e._drawCommand},!1);for(var e in this._uniformValueCache)this._uniformValueCache.hasOwnProperty(e)&&delete this._uniformValueCache[e];for(var e in this._textureCache)this._textureCache.hasOwnProperty(e)&&delete this._textureCache[e];for(var e in this._uniformMaps)this._uniformMaps.hasOwnProperty(e)&&delete this._uniformMaps[e];for(var e in this._framebufferTextures)this._framebufferTextures.hasOwnProperty(e)&&delete this._framebufferTextures[e];if(this._uniformValueCache={},this._textureCache={},this._uniformMaps={},this._framebufferTextures={},this._pickIds)for(e=0;e<this._pickIds.length;++e)this._pickIds[e].destroy&&this._pickIds[e].destroy()},m.traverse=function(e,t,n,i){if(e&&(i||(i={cancelCurrent:!1,cancelAll:!1}),i.cancelCurrent=!1,(!n||e.show||e.visible)&&((e.geometry&&e.material||e instanceof te.default||e instanceof ie.default)&&t(e,i),e.children)))for(var r=0;r<e.children.length;r++)if(!i.cancelCurrent){if(i.cancelAll)break;m.traverse(e.children[r],t,n,i)}},Object.defineProperties(m.prototype,{scene:{set:function(e){this._scene=e},get:function(){return this._scene}},frameState:{get:function(){if(this._scene)return this._scene.frameState}},modelMatrixNeedsUpdate:{get:function(){return this._modelMatrixNeedsUpdate},set:function(e){this._modelMatrixNeedsUpdate=e,e&&m.traverse(this,function(t){t._modelMatrixNeedsUpdate=e},!1)}},showReference:{get:function(){return this.referenceMesh.show},set:function(e){this.referenceMesh.show=e}},children:{get:function(){return this._chidren},set:function(e){this._chidren=e}},show:{get:function(){return this._show},set:function(e){this._show=e}},debug:{get:function(){return this._debug},set:function(e){this._debug=e}},ready:{get:function(){return this._ready}},modelMatrix:{get:function(){return this._modelMatrix},set:function(e){this._modelMatrix=e,this._modelMatrixNeedsUpdate=!0}},rotation:{get:function(){return this._rotation},set:function(e){e!=this._rotation&&(this._rotation=e,this._needUpdate=!0),this._rotation.paramChanged.removeEventListener(this._onNeedUpdateChanged),this._rotation=e,this._rotation.paramChanged.addEventListener(this._onNeedUpdateChanged)}},position:{get:function(){return this._position},set:function(e){e.x==this._position.x&&e.y==this._position.y&&e.z==this._position.z||(this._position=e,this._modelMatrixNeedsUpdate=!0),this._position=e}},scale:{get:function(){return this._scale},set:function(e){e.x==this._scale.x&&e.y==this._scale.y&&e.z==this._scale.z||(this._scale=e,this._modelMatrixNeedsUpdate=!0),this._scale=e}}}),n.default=m},{"../ThirdParty/tiff-js/tiff.js":33,"../Util/Path.js":35,"./FramebufferTexture.js":4,"./LOD.js":6,"./MaterialUtils.js":7,"./MeshMaterial.js":9,"./MeshUtils.js":11,"./ReferenceMesh.js":15,"./RendererUtils.js":16,"./Rotation.js":17,"./ShaderUtils.js":18,"./Shaders/ShaderChunk.js":19}],13:[function(e,t,n){"use strict";function i(e,t,n,i){this.width=e,this.height=t,this.widthSegments=n,this.heightSegments=i}Object.defineProperty(n,"__esModule",{value:!0});var r=e("./BasicGeometry.js"),a=function(e){return e&&e.__esModule?e:{default:e}}(r);i.createGeometry=function(e){var t=e.width,n=e.height,i=e.widthSegments,r=e.heightSegments;t=t||1,n=n||1;var o,s,l=t/2,c=n/2,d=Math.floor(i)||1,u=Math.floor(r)||1,f=d+1,m=u+1,p=t/d,h=n/u,v=[],_=[],g=[],x=[];for(s=0;s<m;s++){var y=s*h-c;for(o=0;o<f;o++){var C=o*p-l;_.push(C,-y,0),g.push(0,0,1),x.push(o/d),x.push(1-s/u)}}for(s=0;s<u;s++)for(o=0;o<d;o++){var M=o+f*s,S=o+f*(s+1),w=o+1+f*(s+1),b=o+1+f*s;v.push(M,S,b),v.push(S,w,b)}return a.default.createGeometry({positions:new Float32Array(_),normals:new Float32Array(g),uvs:new Float32Array(x),indices:new Int32Array(v)})},n.default=i},{"./BasicGeometry.js":2}],14:[function(e,t,n){"use strict";function i(e){if(this.type="PlaneGeometry",!e||!e.positions)throw new Error("缺少positions参数");if(4!=e.positions.length&&e.positions.length/3!=4)throw new Error("positions参数必须包含四个顶点的位置坐标");this.positions=e.positions}Object.defineProperty(n,"__esModule",{value:!0}),i.createGeometry=function(e){var t,n=e.positions;if(!Array.isArray(n))throw new Error("positions参数必须是数组类型");if(n[0]instanceof Cesium.Cartesian3){t=new Float32Array(12);for(var i=0;i<n.length;i++){var r=n[i];t[3*i]=r.x,t[3*i+1]=r.y,t[3*i+2]=r.z}}else{if("number"!=typeof n[0])throw new Error("positions参数有误");t=new Float32Array(t)}var a=new Int32Array([0,1,3,1,2,3]),o={position:new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:n})},s=Cesium.BoundingSphere.fromVertices(n);return new Cesium.Geometry({attributes:o,indices:new Int32Array(a),primitiveType:Cesium.PrimitiveType.TRIANGLES,boundingSphere:s})},n.default=i},{}],15:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(e){o=o||Cesium.defaultValue,e=Cesium.defaultValue(e,{}),this._axisParameter=new l.default(e.axisParameter),this._axisParameterY=new l.default(e.axisParameter),this._axisParameterY.reverse=!0;var t=new f.default({defaultColor:"rgba(255,0,0,1)",wireframe:!1,side:f.default.Sides.DOUBLE,translucent:!1}),n=new f.default({defaultColor:"rgba(0,255,0,1)",wireframe:!1,side:f.default.Sides.DOUBLE,translucent:!0}),i=new f.default({defaultColor:"rgba(0,0,255,1)",wireframe:!1,side:f.default.Sides.DOUBLE,translucent:!1}),r=l.default.createGeometry(new l.default(this._axisParameter)),a=l.default.createGeometry(new l.default(this._axisParameterY)),s=new d.default(r,t),c=new d.default(a,n),u=new d.default(r,i);s.position.z=this._axisParameter.length/2,c.position.y=-this._axisParameter.length/2,c.rotation.axis.y=1,c.rotation.angle=-180,u.position.x=this._axisParameter.length/2,u.rotation.axis.x=1,u.rotation.angle=-180,u.parent=this,c.parent=this,s.parent=this,this._children=[u,c,s],this.x=u,this.y=c,this.z=s,this.uuid=Cesium.createGuid(),this.show=o(e.show,!0),this._position=o(e.position,new Cesium.Cartesian3(0,0,0)),this._scale=o(e.scale,new Cesium.Cartesian3(1,1,1)),this._rotation=o(e.rotation,{axis:new Cesium.Cartesian3(0,0,1),angle:0}),this._rotation=new p.default(this._rotation.axis,this._rotation.angle),this._needsUpdate=!0,this._modelMatrixNeedsUpdate=!0,this._modelMatrix=new Cesium.Matrix4,Cesium.Matrix4.clone(Cesium.Matrix4.IDENTITY,this._modelMatrix),this._onNeedUpdateChanged=function(){this._modelMatrixNeedsUpdate=!0},this._rotation.paramChanged.removeEventListener(this._onNeedUpdateChanged),this._parent=null}function a(e,t){for(var n=0;n<e.length;n++)if(e[n]==t){e.splice(n,1);break}}Object.defineProperty(n,"__esModule",{value:!0});var o,s=e("./ArrowGeometry.js"),l=i(s),c=e("./Mesh.js"),d=i(c),u=e("./MeshMaterial.js"),f=i(u),m=e("./Rotation.js"),p=i(m),h=e("./RendererUtils.js"),v=i(h);Object.defineProperties(r.prototype,{modelMatrix:{get:function(){return this._modelMatrix}},parent:{get:function(){return this._parent},set:function(e){if(e&&(e._children&&Array.isArray(e._children)||e.children&&Array.isArray(e.children))){if(this._parent&&this._parent!=e){var t=this._parent._children?this._parent._children:this._parent.children;Array.isArray(t)&&a(t,this)}if(this._parent=e,"function"==typeof this._parent.add)this._parent.add(this);else{var t=e._children?e._children:e.children;t.push(this)}}this.modelMatrixNeedsUpdate=!0}},modelMatrixNeedsUpdate:{get:function(){return this._modelMatrixNeedsUpdate},set:function(e){this._modelMatrixNeedsUpdate=e,this._modelMatrixNeedsUpdate&&d.default.traverse(this,function(t){t._modelMatrixNeedsUpdate=e})}},children:{get:function(){return this._children}},needsUpdate:{get:function(){return this._needsUpdate},set:function(e){this._needsUpdate=e}},rotation:{get:function(){return this._rotation},set:function(e){e!=this._rotation&&(this._rotation=e,this.modelMatrixNeedsUpdate=!0),this._rotation.paramChanged.removeEventListener(this._onNeedUpdateChanged),this._rotation=e,this._rotation.paramChanged.addEventListener(this._onNeedUpdateChanged)}},position:{get:function(){return this._position},set:function(e){e.x==this._position.x&&e.y==this._position.y&&e.z==this._position.z||(this._position=e,this.modelMatrixNeedsUpdate=!0),this._position=e}},scale:{get:function(){return this._scale},set:function(e){e.x==this._scale.x&&e.y==this._scale.y&&e.z==this._scale.z||(this._scale=e,this.modelMatrixNeedsUpdate=!0),this._scale=e}}}),r.prototype.update=function(e,t){(this._modelMatrixNeedsUpdate||this._needsUpdate)&&(v.default.computeModelMatrix(e,this.position,this.rotation,this.scale,this.modelMatrix),this._modelMatrixNeedsUpdate=!1)},n.default=r},{"./ArrowGeometry.js":1,"./Mesh.js":8,"./MeshMaterial.js":9,"./RendererUtils.js":16,"./Rotation.js":17}],16:[function(e,t,n){"use strict";function i(){}function r(e){if(e.validateFramebuffer){var t=e._gl,n=t.checkFramebufferStatus(t.FRAMEBUFFER);if(n!==t.FRAMEBUFFER_COMPLETE){var i;switch(n){case t.FRAMEBUFFER_INCOMPLETE_ATTACHMENT:i="Framebuffer is not complete.  Incomplete attachment: at least one attachment point with a renderbuffer or texture attached has its attached object no longer in existence or has an attached image with a width or height of zero, or the color attachment point has a non-color-renderable image attached, or the depth attachment point has a non-depth-renderable image attached, or the stencil attachment point has a non-stencil-renderable image attached.  Color-renderable formats include GL_RGBA4, GL_RGB5_A1, and GL_RGB565. GL_DEPTH_COMPONENT16 is the only depth-renderable format. GL_STENCIL_INDEX8 is the only stencil-renderable format.";break;case t.FRAMEBUFFER_INCOMPLETE_DIMENSIONS:i="Framebuffer is not complete.  Incomplete dimensions: not all attached images have the same width and height.";break;case t.FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT:i="Framebuffer is not complete.  Missing attachment: no images are attached to the framebuffer.";break;case t.FRAMEBUFFER_UNSUPPORTED:i="Framebuffer is not complete.  Unsupported: the combination of internal formats of the attached images violates an implementation-dependent set of restrictions."}throw new DeveloperError(i)}}}function a(e,t){if(t!==e._currentFramebuffer){"undefined"!=typeof WebGLRenderingContext&&(f=[Cesium.WebGLConstants.BACK]),e._currentFramebuffer=t;var n=f;if(Cesium.defined(t))t._bind(),r(e),n=t._getActiveColorAttachments();else{var i=e._gl;i.bindFramebuffer(i.FRAMEBUFFER,null)}e.drawBuffers&&e.glDrawBuffers(n)}}Object.defineProperty(n,"__esModule",{value:!0});var o,s,l,c,d,u;i.renderToTexture=function(e,t,n,i){var r=Array.isArray(e)?e:[e],a=t.context,o=null,s=!1;n instanceof Cesium.Framebuffer&&(o=n),o||(i&&i.width==n.width&&i.height==n.height||(i=new Cesium.Texture({context:a,width:n.width,height:n.height,pixelFormat:Cesium.PixelFormat.DEPTH_COMPONENT,pixelDatatype:Cesium.PixelDatatype.UNSIGNED_SHORT})),o=new Cesium.Framebuffer({context:a,colorTextures:[n],destroyAttachments:!1,depthTexture:i}),s=!0),u||(u=new Cesium.ClearCommand({color:new Cesium.Color(0,0,0,0)}));var l=u;l.framebuffer=o,l.renderState=t.renderState,l.execute(a),r.forEach(function(e){e.framebuffer=o,e.execute(a)}),s&&o.destroy()},i.renderToPixels=function(e,t,n,r,a){var o=Array.isArray(e)?e:[e],s=t.context,l=null,c=!1;if(n instanceof Cesium.Framebuffer&&(l=n),!l){var d=new Cesium.Texture({context:s,width:n.width,height:n.height,pixelFormat:Cesium.PixelFormat.DEPTH_COMPONENT,pixelDatatype:Cesium.PixelDatatype.UNSIGNED_SHORT});l=new Cesium.Framebuffer({context:s,colorTextures:[n],depthTexture:s.depthTexture?d:void 0,destroyAttachments:!1}),c=!0}u||(u=new Cesium.ClearCommand({color:new Cesium.Color(0,0,0,0)}));var f=u;return f.framebuffer=l,f.renderState=t.renderState,f.execute(s),o.forEach(function(e){e.framebuffer=l,e.execute(s)}),r=r?r:{},a=i.readPixels(t,Object.assign(r,{framebuffer:l}),a),delete r.framebuffer,c&&l.destroy(),a};var f,m,p;i.readPixels=function(e,t,n){var i=e.context._gl;t=t||{};var r=Math.max(t.x||0,0),o=Math.max(t.y||0,0),s=t.width||i.drawingBufferWidth,l=t.height||i.drawingBufferHeight,c=t.pixelDatatype||Cesium.PixelDatatype.UNSIGNED_BYTE,d=t.pixelFormat||Cesium.PixelFormat.RGBA,u=t.framebuffer;if(s<=0)throw new Cesium.DeveloperError("readState.width must be greater than zero.");if(l<=0)throw new Cesium.DeveloperError("readState.height must be greater than zero.");a(this,u);var f=4;return d==Cesium.PixelFormat.RGB?f=3:d==Cesium.PixelFormat.ALPHA&&(f=1),n||(c==Cesium.PixelDatatype.FLOAT?(c=i.FLOAT,n=new Float32Array(f*s*l)):n=c==Cesium.PixelDatatype.UNSIGNED_BYTE?new Uint8Array(f*s*l):new Uint16Array(f*s*l)),i.readPixels(r,o,s,l,d,c,n),m=d,p=c,n},i.yUp2Zup=function(e,t){return o||(o=Cesium.Matrix4.fromRotationTranslation(Cesium.Matrix3.fromRotationX(Cesium.Math.PI_OVER_TWO))),Cesium.Matrix4.multiplyTransformation(e,o,t)},i.computeModelMatrix=function(e,t,n,i,r){if(0==arguments.length)return e;s||(s=new Cesium.Cartesian3),l||(l=new Cesium.Quaternion),c||(c=new Cesium.Cartesian3),d||(d=new Cesium.Matrix4);var a=Cesium.Matrix4;if(r||(r=new a),a.clone(e,r),t||(s.x=0,s.y=0,s.z=0),s.x=t.x,s.y=t.y,s.z=t.z,i||(c.x=0,c.y=0,c.z=0),c.x=i.x,c.y=i.y,c.z=i.z,n instanceof Cesium.Quaternion)Cesium.Quaternion.clone(n,l);else{var o=n.axis,u=n.angle;Cesium.Quaternion.fromAxisAngle(new Cesium.Cartesian3(o.x,o.y,o.z),Cesium.Math.toRadians(u),l)}return a.fromTranslationQuaternionRotationScale(s,l,c,d),a.multiplyTransformation(r,d,r),r},n.default=i},{}],17:[function(e,t,n){"use strict";function i(e,t){this._axis=e,this._angle=t,this.paramChanged=new Cesium.Event}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperties(i.prototype,{axis:{set:function(e){e.x==this._axis.x&&e.y==this._axis.y&&e.z==this._axis.z||(this._axis=e,this.paramChanged.raiseEvent()),this._axis=e},get:function(){return this._axis}},angle:{set:function(e){e!=this._angle&&(this._angle=e,this.paramChanged.raiseEvent()),this._angle=e},get:function(){return this._angle}}}),n.default=i},{}],18:[function(e,t,n){"use strict";function i(){}if(Object.defineProperty(n,"__esModule",{value:!0}),i.processShader3js=function(e,t){return new WebGLProgram(e,t)},"undefined"!=typeof THREE){THREE.ShaderChunk,THREE.ShaderLib,THREE.BackSide,THREE.DoubleSide,THREE.FlatShading,THREE.CubeUVRefractionMapping,THREE.CubeUVReflectionMapping,THREE.GammaEncoding,THREE.LinearEncoding,THREE.NoToneMapping,THREE.AddOperation,THREE.MixOperation,THREE.MultiplyOperation,THREE.EquirectangularRefractionMapping,THREE.CubeRefractionMapping,THREE.SphericalReflectionMapping,THREE.EquirectangularReflectionMapping,THREE.CubeReflectionMapping,THREE.PCFSoftShadowMap,THREE.PCFShadowMap,THREE.CineonToneMapping,THREE.Uncharted2ToneMapping,THREE.ReinhardToneMapping,THREE.LinearToneMapping,THREE.GammaEncoding,THREE.RGBDEncoding,THREE.RGBM16Encoding,THREE.RGBM7Encoding,THREE.RGBEEncoding,THREE.sRGBEncoding}n.default=i},{}],19:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0});var r=e("./none_frag.js"),a=i(r),o=e("./none_vert.js"),s=i(o),l=e("./normals_frag.js"),c=i(l),d=e("./normals_vert.js"),u=i(d),f=e("./texture_frag.js"),m=i(f),p=e("./texture_vert.js"),h=i(p),v=e("./texture_normals_frag.js"),_=i(v),g=e("./texture_normals_vert.js"),x=i(g),y={alphamap_fragment:"#ifdef USE_ALPHAMAP\n\tdiffuseColor.a *= texture2D( alphaMap, vUv ).g;\n#endif\n",alphamap_pars_fragment:"#ifdef USE_ALPHAMAP\n\tuniform sampler2D alphaMap;\n#endif\n",alphatest_fragment:"#ifdef ALPHATEST\n\tif ( diffuseColor.a < ALPHATEST ) discard;\n#endif\n",aomap_fragment:"#ifdef USE_AOMAP\n\tfloat ambientOcclusion = ( texture2D( aoMap, vUv2 ).r - 1.0 ) * aoMapIntensity + 1.0;\n\treflectedLight.indirectDiffuse *= ambientOcclusion;\n\t#if defined( USE_ENVMAP ) && defined( PHYSICAL )\n\t\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\t\treflectedLight.indirectSpecular *= computeSpecularOcclusion( dotNV, ambientOcclusion, material.specularRoughness );\n\t#endif\n#endif\n",aomap_pars_fragment:"#ifdef USE_AOMAP\n\tuniform sampler2D aoMap;\n\tuniform float aoMapIntensity;\n#endif",begin_vertex:"\nvec3 transformed = vec3( position );\n",beginnormal_vertex:"\nvec3 objectNormal = vec3( normal );\n",bsdfs:"float punctualLightIntensityToIrradianceFactor( const in float lightDistance, const in float cutoffDistance, const in float decayExponent ) {\n\tif( decayExponent > 0.0 ) {\n#if defined ( PHYSICALLY_CORRECT_LIGHTS )\n\t\tfloat distanceFalloff = 1.0 / max( pow( lightDistance, decayExponent ), 0.01 );\n\t\tfloat maxDistanceCutoffFactor = pow2( saturate( 1.0 - pow4( lightDistance / cutoffDistance ) ) );\n\t\treturn distanceFalloff * maxDistanceCutoffFactor;\n#else\n\t\treturn pow( saturate( -lightDistance / cutoffDistance + 1.0 ), decayExponent );\n#endif\n\t}\n\treturn 1.0;\n}\nvec3 BRDF_Diffuse_Lambert( const in vec3 diffuseColor ) {\n\treturn RECIPROCAL_PI * diffuseColor;\n}\nvec3 F_Schlick( const in vec3 specularColor, const in float dotLH ) {\n\tfloat fresnel = exp2( ( -5.55473 * dotLH - 6.98316 ) * dotLH );\n\treturn ( 1.0 - specularColor ) * fresnel + specularColor;\n}\nfloat G_GGX_Smith( const in float alpha, const in float dotNL, const in float dotNV ) {\n\tfloat a2 = pow2( alpha );\n\tfloat gl = dotNL + sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNL ) );\n\tfloat gv = dotNV + sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNV ) );\n\treturn 1.0 / ( gl * gv );\n}\nfloat G_GGX_SmithCorrelated( const in float alpha, const in float dotNL, const in float dotNV ) {\n\tfloat a2 = pow2( alpha );\n\tfloat gv = dotNL * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNV ) );\n\tfloat gl = dotNV * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNL ) );\n\treturn 0.5 / max( gv + gl, EPSILON );\n}\nfloat D_GGX( const in float alpha, const in float dotNH ) {\n\tfloat a2 = pow2( alpha );\n\tfloat denom = pow2( dotNH ) * ( a2 - 1.0 ) + 1.0;\n\treturn RECIPROCAL_PI * a2 / pow2( denom );\n}\nvec3 BRDF_Specular_GGX( const in IncidentLight incidentLight, const in GeometricContext geometry, const in vec3 specularColor, const in float roughness ) {\n\tfloat alpha = pow2( roughness );\n\tvec3 halfDir = normalize( incidentLight.direction + geometry.viewDir );\n\tfloat dotNL = saturate( dot( geometry.normal, incidentLight.direction ) );\n\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\tfloat dotNH = saturate( dot( geometry.normal, halfDir ) );\n\tfloat dotLH = saturate( dot( incidentLight.direction, halfDir ) );\n\tvec3 F = F_Schlick( specularColor, dotLH );\n\tfloat G = G_GGX_SmithCorrelated( alpha, dotNL, dotNV );\n\tfloat D = D_GGX( alpha, dotNH );\n\treturn F * ( G * D );\n}\nvec2 LTC_Uv( const in vec3 N, const in vec3 V, const in float roughness ) {\n\tconst float LUT_SIZE  = 64.0;\n\tconst float LUT_SCALE = ( LUT_SIZE - 1.0 ) / LUT_SIZE;\n\tconst float LUT_BIAS  = 0.5 / LUT_SIZE;\n\tfloat theta = acos( dot( N, V ) );\n\tvec2 uv = vec2(\n\t\tsqrt( saturate( roughness ) ),\n\t\tsaturate( theta / ( 0.5 * PI ) ) );\n\tuv = uv * LUT_SCALE + LUT_BIAS;\n\treturn uv;\n}\nfloat LTC_ClippedSphereFormFactor( const in vec3 f ) {\n\tfloat l = length( f );\n\treturn max( ( l * l + f.z ) / ( l + 1.0 ), 0.0 );\n}\nvec3 LTC_EdgeVectorFormFactor( const in vec3 v1, const in vec3 v2 ) {\n\tfloat x = dot( v1, v2 );\n\tfloat y = abs( x );\n\tfloat a = 0.86267 + (0.49788 + 0.01436 * y ) * y;\n\tfloat b = 3.45068 + (4.18814 + y) * y;\n\tfloat v = a / b;\n\tfloat theta_sintheta = (x > 0.0) ? v : 0.5 * inversesqrt( 1.0 - x * x ) - v;\n\treturn cross( v1, v2 ) * theta_sintheta;\n}\nvec3 LTC_Evaluate( const in vec3 N, const in vec3 V, const in vec3 P, const in mat3 mInv, const in vec3 rectCoords[ 4 ] ) {\n\tvec3 v1 = rectCoords[ 1 ] - rectCoords[ 0 ];\n\tvec3 v2 = rectCoords[ 3 ] - rectCoords[ 0 ];\n\tvec3 lightNormal = cross( v1, v2 );\n\tif( dot( lightNormal, P - rectCoords[ 0 ] ) < 0.0 ) return vec3( 0.0 );\n\tvec3 T1, T2;\n\tT1 = normalize( V - N * dot( V, N ) );\n\tT2 = - cross( N, T1 );\n\tmat3 mat = mInv * transpose( mat3( T1, T2, N ) );\n\tvec3 coords[ 4 ];\n\tcoords[ 0 ] = mat * ( rectCoords[ 0 ] - P );\n\tcoords[ 1 ] = mat * ( rectCoords[ 1 ] - P );\n\tcoords[ 2 ] = mat * ( rectCoords[ 2 ] - P );\n\tcoords[ 3 ] = mat * ( rectCoords[ 3 ] - P );\n\tcoords[ 0 ] = normalize( coords[ 0 ] );\n\tcoords[ 1 ] = normalize( coords[ 1 ] );\n\tcoords[ 2 ] = normalize( coords[ 2 ] );\n\tcoords[ 3 ] = normalize( coords[ 3 ] );\n\tvec3 vectorFormFactor = vec3( 0.0 );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 0 ], coords[ 1 ] );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 1 ], coords[ 2 ] );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 2 ], coords[ 3 ] );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 3 ], coords[ 0 ] );\n\tvec3 result = vec3( LTC_ClippedSphereFormFactor( vectorFormFactor ) );\n\treturn result;\n}\nvec3 BRDF_Specular_GGX_Environment( const in GeometricContext geometry, const in vec3 specularColor, const in float roughness ) {\n\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\tconst vec4 c0 = vec4( - 1, - 0.0275, - 0.572, 0.022 );\n\tconst vec4 c1 = vec4( 1, 0.0425, 1.04, - 0.04 );\n\tvec4 r = roughness * c0 + c1;\n\tfloat a004 = min( r.x * r.x, exp2( - 9.28 * dotNV ) ) * r.x + r.y;\n\tvec2 AB = vec2( -1.04, 1.04 ) * a004 + r.zw;\n\treturn specularColor * AB.x + AB.y;\n}\nfloat G_BlinnPhong_Implicit( ) {\n\treturn 0.25;\n}\nfloat D_BlinnPhong( const in float shininess, const in float dotNH ) {\n\treturn RECIPROCAL_PI * ( shininess * 0.5 + 1.0 ) * pow( dotNH, shininess );\n}\nvec3 BRDF_Specular_BlinnPhong( const in IncidentLight incidentLight, const in GeometricContext geometry, const in vec3 specularColor, const in float shininess ) {\n\tvec3 halfDir = normalize( incidentLight.direction + geometry.viewDir );\n\tfloat dotNH = saturate( dot( geometry.normal, halfDir ) );\n\tfloat dotLH = saturate( dot( incidentLight.direction, halfDir ) );\n\tvec3 F = F_Schlick( specularColor, dotLH );\n\tfloat G = G_BlinnPhong_Implicit( );\n\tfloat D = D_BlinnPhong( shininess, dotNH );\n\treturn F * ( G * D );\n}\nfloat GGXRoughnessToBlinnExponent( const in float ggxRoughness ) {\n\treturn ( 2.0 / pow2( ggxRoughness + 0.0001 ) - 2.0 );\n}\nfloat BlinnExponentToGGXRoughness( const in float blinnExponent ) {\n\treturn sqrt( 2.0 / ( blinnExponent + 2.0 ) );\n}\n",
bumpmap_pars_fragment:"#ifdef USE_BUMPMAP\n\tuniform sampler2D bumpMap;\n\tuniform float bumpScale;\n\tvec2 dHdxy_fwd() {\n\t\tvec2 dSTdx = dFdx( vUv );\n\t\tvec2 dSTdy = dFdy( vUv );\n\t\tfloat Hll = bumpScale * texture2D( bumpMap, vUv ).x;\n\t\tfloat dBx = bumpScale * texture2D( bumpMap, vUv + dSTdx ).x - Hll;\n\t\tfloat dBy = bumpScale * texture2D( bumpMap, vUv + dSTdy ).x - Hll;\n\t\treturn vec2( dBx, dBy );\n\t}\n\tvec3 perturbNormalArb( vec3 surf_pos, vec3 surf_norm, vec2 dHdxy ) {\n\t\tvec3 vSigmaX = vec3( dFdx( surf_pos.x ), dFdx( surf_pos.y ), dFdx( surf_pos.z ) );\n\t\tvec3 vSigmaY = vec3( dFdy( surf_pos.x ), dFdy( surf_pos.y ), dFdy( surf_pos.z ) );\n\t\tvec3 vN = surf_norm;\n\t\tvec3 R1 = cross( vSigmaY, vN );\n\t\tvec3 R2 = cross( vN, vSigmaX );\n\t\tfloat fDet = dot( vSigmaX, R1 );\n\t\tvec3 vGrad = sign( fDet ) * ( dHdxy.x * R1 + dHdxy.y * R2 );\n\t\treturn normalize( abs( fDet ) * surf_norm - vGrad );\n\t}\n#endif\n",clipping_planes_fragment:"#if NUM_CLIPPING_PLANES > 0\n\tfor ( int i = 0; i < UNION_CLIPPING_PLANES; ++ i ) {\n\t\tvec4 plane = clippingPlanes[ i ];\n\t\tif ( dot( vViewPosition, plane.xyz ) > plane.w ) discard;\n\t}\n\t\t\n\t#if UNION_CLIPPING_PLANES < NUM_CLIPPING_PLANES\n\t\tbool clipped = true;\n\t\tfor ( int i = UNION_CLIPPING_PLANES; i < NUM_CLIPPING_PLANES; ++ i ) {\n\t\t\tvec4 plane = clippingPlanes[ i ];\n\t\t\tclipped = ( dot( vViewPosition, plane.xyz ) > plane.w ) && clipped;\n\t\t}\n\t\tif ( clipped ) discard;\n\t\n\t#endif\n#endif\n",clipping_planes_pars_fragment:"#if NUM_CLIPPING_PLANES > 0\n\t#if ! defined( PHYSICAL ) && ! defined( PHONG )\n\t\tvarying vec3 vViewPosition;\n\t#endif\n\tuniform vec4 clippingPlanes[ NUM_CLIPPING_PLANES ];\n#endif\n",clipping_planes_pars_vertex:"#if NUM_CLIPPING_PLANES > 0 && ! defined( PHYSICAL ) && ! defined( PHONG )\n\tvarying vec3 vViewPosition;\n#endif\n",clipping_planes_vertex:"#if NUM_CLIPPING_PLANES > 0 && ! defined( PHYSICAL ) && ! defined( PHONG )\n\tvViewPosition = - mvPosition.xyz;\n#endif\n",color_fragment:"#ifdef USE_COLOR\n\tdiffuseColor.rgb *= vColor;\n#endif",color_pars_fragment:"#ifdef USE_COLOR\n\tvarying vec3 vColor;\n#endif\n",color_pars_vertex:"#ifdef USE_COLOR\n\tvarying vec3 vColor;\n#endif",color_vertex:"#ifdef USE_COLOR\n\tvColor.xyz = color.xyz;\n#endif",common:"#define PI 3.14159265359\n#define PI2 6.28318530718\n#define PI_HALF 1.5707963267949\n#define RECIPROCAL_PI 0.31830988618\n#define RECIPROCAL_PI2 0.15915494\n#define LOG2 1.442695\n#define EPSILON 1e-6\n#define saturate(a) clamp( a, 0.0, 1.0 )\n#define whiteCompliment(a) ( 1.0 - saturate( a ) )\nfloat pow2( const in float x ) { return x*x; }\nfloat pow3( const in float x ) { return x*x*x; }\nfloat pow4( const in float x ) { float x2 = x*x; return x2*x2; }\nfloat average( const in vec3 color ) { return dot( color, vec3( 0.3333 ) ); }\nhighp float rand( const in vec2 uv ) {\n\tconst highp float a = 12.9898, b = 78.233, c = 43758.5453;\n\thighp float dt = dot( uv.xy, vec2( a,b ) ), sn = mod( dt, PI );\n\treturn fract(sin(sn) * c);\n}\nstruct IncidentLight {\n\tvec3 color;\n\tvec3 direction;\n\tbool visible;\n};\nstruct ReflectedLight {\n\tvec3 directDiffuse;\n\tvec3 directSpecular;\n\tvec3 indirectDiffuse;\n\tvec3 indirectSpecular;\n};\nstruct GeometricContext {\n\tvec3 position;\n\tvec3 normal;\n\tvec3 viewDir;\n};\nvec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n\treturn normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n}\nvec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n\treturn normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n}\nvec3 projectOnPlane(in vec3 point, in vec3 pointOnPlane, in vec3 planeNormal ) {\n\tfloat distance = dot( planeNormal, point - pointOnPlane );\n\treturn - distance * planeNormal + point;\n}\nfloat sideOfPlane( in vec3 point, in vec3 pointOnPlane, in vec3 planeNormal ) {\n\treturn sign( dot( point - pointOnPlane, planeNormal ) );\n}\nvec3 linePlaneIntersect( in vec3 pointOnLine, in vec3 lineDirection, in vec3 pointOnPlane, in vec3 planeNormal ) {\n\treturn lineDirection * ( dot( planeNormal, pointOnPlane - pointOnLine ) / dot( planeNormal, lineDirection ) ) + pointOnLine;\n}\nmat3 transpose( const in mat3 v ) {\n\tmat3 tmp;\n\ttmp[0] = vec3(v[0].x, v[1].x, v[2].x);\n\ttmp[1] = vec3(v[0].y, v[1].y, v[2].y);\n\ttmp[2] = vec3(v[0].z, v[1].z, v[2].z);\n\treturn tmp;\n}\n",cube_uv_reflection_fragment:"#ifdef ENVMAP_TYPE_CUBE_UV\n#define cubeUV_textureSize (1024.0)\nint getFaceFromDirection(vec3 direction) {\n\tvec3 absDirection = abs(direction);\n\tint face = -1;\n\tif( absDirection.x > absDirection.z ) {\n\t\tif(absDirection.x > absDirection.y )\n\t\t\tface = direction.x > 0.0 ? 0 : 3;\n\t\telse\n\t\t\tface = direction.y > 0.0 ? 1 : 4;\n\t}\n\telse {\n\t\tif(absDirection.z > absDirection.y )\n\t\t\tface = direction.z > 0.0 ? 2 : 5;\n\t\telse\n\t\t\tface = direction.y > 0.0 ? 1 : 4;\n\t}\n\treturn face;\n}\n#define cubeUV_maxLods1  (log2(cubeUV_textureSize*0.25) - 1.0)\n#define cubeUV_rangeClamp (exp2((6.0 - 1.0) * 2.0))\nvec2 MipLevelInfo( vec3 vec, float roughnessLevel, float roughness ) {\n\tfloat scale = exp2(cubeUV_maxLods1 - roughnessLevel);\n\tfloat dxRoughness = dFdx(roughness);\n\tfloat dyRoughness = dFdy(roughness);\n\tvec3 dx = dFdx( vec * scale * dxRoughness );\n\tvec3 dy = dFdy( vec * scale * dyRoughness );\n\tfloat d = max( dot( dx, dx ), dot( dy, dy ) );\n\td = clamp(d, 1.0, cubeUV_rangeClamp);\n\tfloat mipLevel = 0.5 * log2(d);\n\treturn vec2(floor(mipLevel), fract(mipLevel));\n}\n#define cubeUV_maxLods2 (log2(cubeUV_textureSize*0.25) - 2.0)\n#define cubeUV_rcpTextureSize (1.0 / cubeUV_textureSize)\nvec2 getCubeUV(vec3 direction, float roughnessLevel, float mipLevel) {\n\tmipLevel = roughnessLevel > cubeUV_maxLods2 - 3.0 ? 0.0 : mipLevel;\n\tfloat a = 16.0 * cubeUV_rcpTextureSize;\n\tvec2 exp2_packed = exp2( vec2( roughnessLevel, mipLevel ) );\n\tvec2 rcp_exp2_packed = vec2( 1.0 ) / exp2_packed;\n\tfloat powScale = exp2_packed.x * exp2_packed.y;\n\tfloat scale = rcp_exp2_packed.x * rcp_exp2_packed.y * 0.25;\n\tfloat mipOffset = 0.75*(1.0 - rcp_exp2_packed.y) * rcp_exp2_packed.x;\n\tbool bRes = mipLevel == 0.0;\n\tscale =  bRes && (scale < a) ? a : scale;\n\tvec3 r;\n\tvec2 offset;\n\tint face = getFaceFromDirection(direction);\n\tfloat rcpPowScale = 1.0 / powScale;\n\tif( face == 0) {\n\t\tr = vec3(direction.x, -direction.z, direction.y);\n\t\toffset = vec2(0.0+mipOffset,0.75 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;\n\t}\n\telse if( face == 1) {\n\t\tr = vec3(direction.y, direction.x, direction.z);\n\t\toffset = vec2(scale+mipOffset, 0.75 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;\n\t}\n\telse if( face == 2) {\n\t\tr = vec3(direction.z, direction.x, direction.y);\n\t\toffset = vec2(2.0*scale+mipOffset, 0.75 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;\n\t}\n\telse if( face == 3) {\n\t\tr = vec3(direction.x, direction.z, direction.y);\n\t\toffset = vec2(0.0+mipOffset,0.5 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;\n\t}\n\telse if( face == 4) {\n\t\tr = vec3(direction.y, direction.x, -direction.z);\n\t\toffset = vec2(scale+mipOffset, 0.5 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;\n\t}\n\telse {\n\t\tr = vec3(direction.z, -direction.x, direction.y);\n\t\toffset = vec2(2.0*scale+mipOffset, 0.5 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;\n\t}\n\tr = normalize(r);\n\tfloat texelOffset = 0.5 * cubeUV_rcpTextureSize;\n\tvec2 s = ( r.yz / abs( r.x ) + vec2( 1.0 ) ) * 0.5;\n\tvec2 base = offset + vec2( texelOffset );\n\treturn base + s * ( scale - 2.0 * texelOffset );\n}\n#define cubeUV_maxLods3 (log2(cubeUV_textureSize*0.25) - 3.0)\nvec4 textureCubeUV(vec3 reflectedDirection, float roughness ) {\n\tfloat roughnessVal = roughness* cubeUV_maxLods3;\n\tfloat r1 = floor(roughnessVal);\n\tfloat r2 = r1 + 1.0;\n\tfloat t = fract(roughnessVal);\n\tvec2 mipInfo = MipLevelInfo(reflectedDirection, r1, roughness);\n\tfloat s = mipInfo.y;\n\tfloat level0 = mipInfo.x;\n\tfloat level1 = level0 + 1.0;\n\tlevel1 = level1 > 5.0 ? 5.0 : level1;\n\tlevel0 += min( floor( s + 0.5 ), 5.0 );\n\tvec2 uv_10 = getCubeUV(reflectedDirection, r1, level0);\n\tvec4 color10 = envMapTexelToLinear(texture2D(envMap, uv_10));\n\tvec2 uv_20 = getCubeUV(reflectedDirection, r2, level0);\n\tvec4 color20 = envMapTexelToLinear(texture2D(envMap, uv_20));\n\tvec4 result = mix(color10, color20, t);\n\treturn vec4(result.rgb, 1.0);\n}\n#endif\n",defaultnormal_vertex:"vec3 transformedNormal = normalMatrix * objectNormal;\n#ifdef FLIP_SIDED\n\ttransformedNormal = - transformedNormal;\n#endif\n",displacementmap_pars_vertex:"#ifdef USE_DISPLACEMENTMAP\n\tuniform sampler2D displacementMap;\n\tuniform float displacementScale;\n\tuniform float displacementBias;\n#endif\n",displacementmap_vertex:"#ifdef USE_DISPLACEMENTMAP\n\ttransformed += normalize( objectNormal ) * ( texture2D( displacementMap, uv ).x * displacementScale + displacementBias );\n#endif\n",emissivemap_fragment:"#ifdef USE_EMISSIVEMAP\n\tvec4 emissiveColor = texture2D( emissiveMap, vUv );\n\temissiveColor.rgb = emissiveMapTexelToLinear( emissiveColor ).rgb;\n\ttotalEmissiveRadiance *= emissiveColor.rgb;\n#endif\n",emissivemap_pars_fragment:"#ifdef USE_EMISSIVEMAP\n\tuniform sampler2D emissiveMap;\n#endif\n",encodings_fragment:"  gl_FragColor = linearToOutputTexel( gl_FragColor );\n",encodings_pars_fragment:"\nvec4 LinearToLinear( in vec4 value ) {\n\treturn value;\n}\nvec4 GammaToLinear( in vec4 value, in float gammaFactor ) {\n\treturn vec4( pow( value.xyz, vec3( gammaFactor ) ), value.w );\n}\nvec4 LinearToGamma( in vec4 value, in float gammaFactor ) {\n\treturn vec4( pow( value.xyz, vec3( 1.0 / gammaFactor ) ), value.w );\n}\nvec4 sRGBToLinear( in vec4 value ) {\n\treturn vec4( mix( pow( value.rgb * 0.9478672986 + vec3( 0.0521327014 ), vec3( 2.4 ) ), value.rgb * 0.0773993808, vec3( lessThanEqual( value.rgb, vec3( 0.04045 ) ) ) ), value.w );\n}\nvec4 LinearTosRGB( in vec4 value ) {\n\treturn vec4( mix( pow( value.rgb, vec3( 0.41666 ) ) * 1.055 - vec3( 0.055 ), value.rgb * 12.92, vec3( lessThanEqual( value.rgb, vec3( 0.0031308 ) ) ) ), value.w );\n}\nvec4 RGBEToLinear( in vec4 value ) {\n\treturn vec4( value.rgb * exp2( value.a * 255.0 - 128.0 ), 1.0 );\n}\nvec4 LinearToRGBE( in vec4 value ) {\n\tfloat maxComponent = max( max( value.r, value.g ), value.b );\n\tfloat fExp = clamp( ceil( log2( maxComponent ) ), -128.0, 127.0 );\n\treturn vec4( value.rgb / exp2( fExp ), ( fExp + 128.0 ) / 255.0 );\n}\nvec4 RGBMToLinear( in vec4 value, in float maxRange ) {\n\treturn vec4( value.xyz * value.w * maxRange, 1.0 );\n}\nvec4 LinearToRGBM( in vec4 value, in float maxRange ) {\n\tfloat maxRGB = max( value.x, max( value.g, value.b ) );\n\tfloat M      = clamp( maxRGB / maxRange, 0.0, 1.0 );\n\tM            = ceil( M * 255.0 ) / 255.0;\n\treturn vec4( value.rgb / ( M * maxRange ), M );\n}\nvec4 RGBDToLinear( in vec4 value, in float maxRange ) {\n\treturn vec4( value.rgb * ( ( maxRange / 255.0 ) / value.a ), 1.0 );\n}\nvec4 LinearToRGBD( in vec4 value, in float maxRange ) {\n\tfloat maxRGB = max( value.x, max( value.g, value.b ) );\n\tfloat D      = max( maxRange / maxRGB, 1.0 );\n\tD            = min( floor( D ) / 255.0, 1.0 );\n\treturn vec4( value.rgb * ( D * ( 255.0 / maxRange ) ), D );\n}\nconst mat3 cLogLuvM = mat3( 0.2209, 0.3390, 0.4184, 0.1138, 0.6780, 0.7319, 0.0102, 0.1130, 0.2969 );\nvec4 LinearToLogLuv( in vec4 value )  {\n\tvec3 Xp_Y_XYZp = value.rgb * cLogLuvM;\n\tXp_Y_XYZp = max(Xp_Y_XYZp, vec3(1e-6, 1e-6, 1e-6));\n\tvec4 vResult;\n\tvResult.xy = Xp_Y_XYZp.xy / Xp_Y_XYZp.z;\n\tfloat Le = 2.0 * log2(Xp_Y_XYZp.y) + 127.0;\n\tvResult.w = fract(Le);\n\tvResult.z = (Le - (floor(vResult.w*255.0))/255.0)/255.0;\n\treturn vResult;\n}\nconst mat3 cLogLuvInverseM = mat3( 6.0014, -2.7008, -1.7996, -1.3320, 3.1029, -5.7721, 0.3008, -1.0882, 5.6268 );\nvec4 LogLuvToLinear( in vec4 value ) {\n\tfloat Le = value.z * 255.0 + value.w;\n\tvec3 Xp_Y_XYZp;\n\tXp_Y_XYZp.y = exp2((Le - 127.0) / 2.0);\n\tXp_Y_XYZp.z = Xp_Y_XYZp.y / value.y;\n\tXp_Y_XYZp.x = value.x * Xp_Y_XYZp.z;\n\tvec3 vRGB = Xp_Y_XYZp.rgb * cLogLuvInverseM;\n\treturn vec4( max(vRGB, 0.0), 1.0 );\n}\n",envmap_fragment:"#ifdef USE_ENVMAP\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\t\tvec3 cameraToVertex = normalize( vWorldPosition - cameraPosition );\n\t\tvec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n\t\t#ifdef ENVMAP_MODE_REFLECTION\n\t\t\tvec3 reflectVec = reflect( cameraToVertex, worldNormal );\n\t\t#else\n\t\t\tvec3 reflectVec = refract( cameraToVertex, worldNormal, refractionRatio );\n\t\t#endif\n\t#else\n\t\tvec3 reflectVec = vReflect;\n\t#endif\n\t#ifdef ENVMAP_TYPE_CUBE\n\t\tvec4 envColor = textureCube( envMap, flipNormal * vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n\t#elif defined( ENVMAP_TYPE_EQUIREC )\n\t\tvec2 sampleUV;\n\t\tsampleUV.y = asin( flipNormal * reflectVec.y ) * RECIPROCAL_PI + 0.5;\n\t\tsampleUV.x = atan( flipNormal * reflectVec.z, flipNormal * reflectVec.x ) * RECIPROCAL_PI2 + 0.5;\n\t\tvec4 envColor = texture2D( envMap, sampleUV );\n\t#elif defined( ENVMAP_TYPE_SPHERE )\n\t\tvec3 reflectView = flipNormal * normalize( ( viewMatrix * vec4( reflectVec, 0.0 ) ).xyz + vec3( 0.0, 0.0, 1.0 ) );\n\t\tvec4 envColor = texture2D( envMap, reflectView.xy * 0.5 + 0.5 );\n\t#else\n\t\tvec4 envColor = vec4( 0.0 );\n\t#endif\n\tenvColor = envMapTexelToLinear( envColor );\n\t#ifdef ENVMAP_BLENDING_MULTIPLY\n\t\toutgoingLight = mix( outgoingLight, outgoingLight * envColor.xyz, specularStrength * reflectivity );\n\t#elif defined( ENVMAP_BLENDING_MIX )\n\t\toutgoingLight = mix( outgoingLight, envColor.xyz, specularStrength * reflectivity );\n\t#elif defined( ENVMAP_BLENDING_ADD )\n\t\toutgoingLight += envColor.xyz * specularStrength * reflectivity;\n\t#endif\n#endif\n",envmap_pars_fragment:"#if defined( USE_ENVMAP ) || defined( PHYSICAL )\n\tuniform float reflectivity;\n\tuniform float envMapIntensity;\n#endif\n#ifdef USE_ENVMAP\n\t#if ! defined( PHYSICAL ) && ( defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) )\n\t\tvarying vec3 vWorldPosition;\n\t#endif\n\t#ifdef ENVMAP_TYPE_CUBE\n\t\tuniform samplerCube envMap;\n\t#else\n\t\tuniform sampler2D envMap;\n\t#endif\n\tuniform float flipEnvMap;\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) || defined( PHYSICAL )\n\t\tuniform float refractionRatio;\n\t#else\n\t\tvarying vec3 vReflect;\n\t#endif\n#endif\n",envmap_pars_vertex:"#ifdef USE_ENVMAP\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\t\tvarying vec3 vWorldPosition;\n\t#else\n\t\tvarying vec3 vReflect;\n\t\tuniform float refractionRatio;\n\t#endif\n#endif\n",envmap_vertex:"#ifdef USE_ENVMAP\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\t\tvWorldPosition = worldPosition.xyz;\n\t#else\n\t\tvec3 cameraToVertex = normalize( worldPosition.xyz - cameraPosition );\n\t\tvec3 worldNormal = inverseTransformDirection( transformedNormal, viewMatrix );\n\t\t#ifdef ENVMAP_MODE_REFLECTION\n\t\t\tvReflect = reflect( cameraToVertex, worldNormal );\n\t\t#else\n\t\t\tvReflect = refract( cameraToVertex, worldNormal, refractionRatio );\n\t\t#endif\n\t#endif\n#endif\n",fog_vertex:"\n#ifdef USE_FOG\nfogDepth = -mvPosition.z;\n#endif",fog_pars_vertex:"#ifdef USE_FOG\n  varying float fogDepth;\n#endif\n",fog_fragment:"#ifdef USE_FOG\n\t#ifdef FOG_EXP2\n\t\tfloat fogFactor = whiteCompliment( exp2( - fogDensity * fogDensity * fogDepth * fogDepth * LOG2 ) );\n\t#else\n\t\tfloat fogFactor = smoothstep( fogNear, fogFar, fogDepth );\n\t#endif\n\tgl_FragColor.rgb = mix( gl_FragColor.rgb, fogColor, fogFactor );\n#endif\n",fog_pars_fragment:"#ifdef USE_FOG\n\tuniform vec3 fogColor;\n\tvarying float fogDepth;\n\t#ifdef FOG_EXP2\n\t\tuniform float fogDensity;\n\t#else\n\t\tuniform float fogNear;\n\t\tuniform float fogFar;\n\t#endif\n#endif\n",gradientmap_pars_fragment:"#ifdef TOON\n\tuniform sampler2D gradientMap;\n\tvec3 getGradientIrradiance( vec3 normal, vec3 lightDirection ) {\n\t\tfloat dotNL = dot( normal, lightDirection );\n\t\tvec2 coord = vec2( dotNL * 0.5 + 0.5, 0.0 );\n\t\t#ifdef USE_GRADIENTMAP\n\t\t\treturn texture2D( gradientMap, coord ).rgb;\n\t\t#else\n\t\t\treturn ( coord.x < 0.7 ) ? vec3( 0.7 ) : vec3( 1.0 );\n\t\t#endif\n\t}\n#endif\n",lightmap_fragment:"#ifdef USE_LIGHTMAP\n\treflectedLight.indirectDiffuse += PI * texture2D( lightMap, vUv2 ).xyz * lightMapIntensity;\n#endif\n",lightmap_pars_fragment:"#ifdef USE_LIGHTMAP\n\tuniform sampler2D lightMap;\n\tuniform float lightMapIntensity;\n#endif",lights_lambert_vertex:"vec3 diffuse = vec3( 1.0 );\nGeometricContext geometry;\ngeometry.position = mvPosition.xyz;\ngeometry.normal = normalize( transformedNormal );\ngeometry.viewDir = normalize( -mvPosition.xyz );\nGeometricContext backGeometry;\nbackGeometry.position = geometry.position;\nbackGeometry.normal = -geometry.normal;\nbackGeometry.viewDir = geometry.viewDir;\nvLightFront = vec3( 0.0 );\n#ifdef DOUBLE_SIDED\n\tvLightBack = vec3( 0.0 );\n#endif\nIncidentLight directLight;\nfloat dotNL;\nvec3 directLightColor_Diffuse;\n#if NUM_POINT_LIGHTS > 0\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tgetPointDirectLightIrradiance( pointLights[ i ], geometry, directLight );\n\t\tdotNL = dot( geometry.normal, directLight.direction );\n\t\tdirectLightColor_Diffuse = PI * directLight.color;\n\t\tvLightFront += saturate( dotNL ) * directLightColor_Diffuse;\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += saturate( -dotNL ) * directLightColor_Diffuse;\n\t\t#endif\n\t}\n#endif\n#if NUM_SPOT_LIGHTS > 0\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tgetSpotDirectLightIrradiance( spotLights[ i ], geometry, directLight );\n\t\tdotNL = dot( geometry.normal, directLight.direction );\n\t\tdirectLightColor_Diffuse = PI * directLight.color;\n\t\tvLightFront += saturate( dotNL ) * directLightColor_Diffuse;\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += saturate( -dotNL ) * directLightColor_Diffuse;\n\t\t#endif\n\t}\n#endif\n#if NUM_DIR_LIGHTS > 0\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tgetDirectionalDirectLightIrradiance( directionalLights[ i ], geometry, directLight );\n\t\tdotNL = dot( geometry.normal, directLight.direction );\n\t\tdirectLightColor_Diffuse = PI * directLight.color;\n\t\tvLightFront += saturate( dotNL ) * directLightColor_Diffuse;\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += saturate( -dotNL ) * directLightColor_Diffuse;\n\t\t#endif\n\t}\n#endif\n#if NUM_HEMI_LIGHTS > 0\n\tfor ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n\t\tvLightFront += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry );\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += getHemisphereLightIrradiance( hemisphereLights[ i ], backGeometry );\n\t\t#endif\n\t}\n#endif\n",lights_pars:"uniform vec3 ambientLightColor;\nvec3 getAmbientLightIrradiance( const in vec3 ambientLightColor ) {\n\tvec3 irradiance = ambientLightColor;\n\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\tirradiance *= PI;\n\t#endif\n\treturn irradiance;\n}\n#if NUM_DIR_LIGHTS > 0\n\tstruct DirectionalLight {\n\t\tvec3 direction;\n\t\tvec3 color;\n\t\tint shadow;\n\t\tfloat shadowBias;\n\t\tfloat shadowRadius;\n\t\tvec2 shadowMapSize;\n\t};\n\tuniform DirectionalLight directionalLights[ NUM_DIR_LIGHTS ];\n\tvoid getDirectionalDirectLightIrradiance( const in DirectionalLight directionalLight, const in GeometricContext geometry, out IncidentLight directLight ) {\n\t\tdirectLight.color = directionalLight.color;\n\t\tdirectLight.direction = directionalLight.direction;\n\t\tdirectLight.visible = true;\n\t}\n#endif\n#if NUM_POINT_LIGHTS > 0\n\tstruct PointLight {\n\t\tvec3 position;\n\t\tvec3 color;\n\t\tfloat distance;\n\t\tfloat decay;\n\t\tint shadow;\n\t\tfloat shadowBias;\n\t\tfloat shadowRadius;\n\t\tvec2 shadowMapSize;\n\t};\n\tuniform PointLight pointLights[ NUM_POINT_LIGHTS ];\n\tvoid getPointDirectLightIrradiance( const in PointLight pointLight, const in GeometricContext geometry, out IncidentLight directLight ) {\n\t\tvec3 lVector = pointLight.position - geometry.position;\n\t\tdirectLight.direction = normalize( lVector );\n\t\tfloat lightDistance = length( lVector );\n\t\tdirectLight.color = pointLight.color;\n\t\tdirectLight.color *= punctualLightIntensityToIrradianceFactor( lightDistance, pointLight.distance, pointLight.decay );\n\t\tdirectLight.visible = ( directLight.color != vec3( 0.0 ) );\n\t}\n#endif\n#if NUM_SPOT_LIGHTS > 0\n\tstruct SpotLight {\n\t\tvec3 position;\n\t\tvec3 direction;\n\t\tvec3 color;\n\t\tfloat distance;\n\t\tfloat decay;\n\t\tfloat coneCos;\n\t\tfloat penumbraCos;\n\t\tint shadow;\n\t\tfloat shadowBias;\n\t\tfloat shadowRadius;\n\t\tvec2 shadowMapSize;\n\t};\n\tuniform SpotLight spotLights[ NUM_SPOT_LIGHTS ];\n\tvoid getSpotDirectLightIrradiance( const in SpotLight spotLight, const in GeometricContext geometry, out IncidentLight directLight  ) {\n\t\tvec3 lVector = spotLight.position - geometry.position;\n\t\tdirectLight.direction = normalize( lVector );\n\t\tfloat lightDistance = length( lVector );\n\t\tfloat angleCos = dot( directLight.direction, spotLight.direction );\n\t\tif ( angleCos > spotLight.coneCos ) {\n\t\t\tfloat spotEffect = smoothstep( spotLight.coneCos, spotLight.penumbraCos, angleCos );\n\t\t\tdirectLight.color = spotLight.color;\n\t\t\tdirectLight.color *= spotEffect * punctualLightIntensityToIrradianceFactor( lightDistance, spotLight.distance, spotLight.decay );\n\t\t\tdirectLight.visible = true;\n\t\t} else {\n\t\t\tdirectLight.color = vec3( 0.0 );\n\t\t\tdirectLight.visible = false;\n\t\t}\n\t}\n#endif\n#if NUM_RECT_AREA_LIGHTS > 0\n\tstruct RectAreaLight {\n\t\tvec3 color;\n\t\tvec3 position;\n\t\tvec3 halfWidth;\n\t\tvec3 halfHeight;\n\t};\n\tuniform sampler2D ltcMat;\tuniform sampler2D ltcMag;\n\tuniform RectAreaLight rectAreaLights[ NUM_RECT_AREA_LIGHTS ];\n#endif\n#if NUM_HEMI_LIGHTS > 0\n\tstruct HemisphereLight {\n\t\tvec3 direction;\n\t\tvec3 skyColor;\n\t\tvec3 groundColor;\n\t};\n\tuniform HemisphereLight hemisphereLights[ NUM_HEMI_LIGHTS ];\n\tvec3 getHemisphereLightIrradiance( const in HemisphereLight hemiLight, const in GeometricContext geometry ) {\n\t\tfloat dotNL = dot( geometry.normal, hemiLight.direction );\n\t\tfloat hemiDiffuseWeight = 0.5 * dotNL + 0.5;\n\t\tvec3 irradiance = mix( hemiLight.groundColor, hemiLight.skyColor, hemiDiffuseWeight );\n\t\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\t\tirradiance *= PI;\n\t\t#endif\n\t\treturn irradiance;\n\t}\n#endif\n#if defined( USE_ENVMAP ) && defined( PHYSICAL )\n\tvec3 getLightProbeIndirectIrradiance( const in GeometricContext geometry, const in int maxMIPLevel ) {\n\t\tvec3 worldNormal = inverseTransformDirection( geometry.normal, viewMatrix );\n\t\t#ifdef ENVMAP_TYPE_CUBE\n\t\t\tvec3 queryVec = vec3( flipEnvMap * worldNormal.x, worldNormal.yz );\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = textureCubeLodEXT( envMap, queryVec, float( maxMIPLevel ) );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = textureCube( envMap, queryVec, float( maxMIPLevel ) );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#elif defined( ENVMAP_TYPE_CUBE_UV )\n\t\t\tvec3 queryVec = vec3( flipEnvMap * worldNormal.x, worldNormal.yz );\n\t\t\tvec4 envMapColor = textureCubeUV( queryVec, 1.0 );\n\t\t#else\n\t\t\tvec4 envMapColor = vec4( 0.0 );\n\t\t#endif\n\t\treturn PI * envMapColor.rgb * envMapIntensity;\n\t}\n\tfloat getSpecularMIPLevel( const in float blinnShininessExponent, const in int maxMIPLevel ) {\n\t\tfloat maxMIPLevelScalar = float( maxMIPLevel );\n\t\tfloat desiredMIPLevel = maxMIPLevelScalar - 0.79248 - 0.5 * log2( pow2( blinnShininessExponent ) + 1.0 );\n\t\treturn clamp( desiredMIPLevel, 0.0, maxMIPLevelScalar );\n\t}\n\tvec3 getLightProbeIndirectRadiance( const in GeometricContext geometry, const in float blinnShininessExponent, const in int maxMIPLevel ) {\n\t\t#ifdef ENVMAP_MODE_REFLECTION\n\t\t\tvec3 reflectVec = reflect( -geometry.viewDir, geometry.normal );\n\t\t#else\n\t\t\tvec3 reflectVec = refract( -geometry.viewDir, geometry.normal, refractionRatio );\n\t\t#endif\n\t\treflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n\t\tfloat specularMIPLevel = getSpecularMIPLevel( blinnShininessExponent, maxMIPLevel );\n\t\t#ifdef ENVMAP_TYPE_CUBE\n\t\t\tvec3 queryReflectVec = vec3( flipEnvMap * reflectVec.x, reflectVec.yz );\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = textureCubeLodEXT( envMap, queryReflectVec, specularMIPLevel );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = textureCube( envMap, queryReflectVec, specularMIPLevel );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#elif defined( ENVMAP_TYPE_CUBE_UV )\n\t\t\tvec3 queryReflectVec = vec3( flipEnvMap * reflectVec.x, reflectVec.yz );\n\t\t\tvec4 envMapColor = textureCubeUV(queryReflectVec, BlinnExponentToGGXRoughness(blinnShininessExponent));\n\t\t#elif defined( ENVMAP_TYPE_EQUIREC )\n\t\t\tvec2 sampleUV;\n\t\t\tsampleUV.y = saturate( reflectVec.y * 0.5 + 0.5 );\n\t\t\tsampleUV.x = atan( reflectVec.z, reflectVec.x ) * RECIPROCAL_PI2 + 0.5;\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = texture2DLodEXT( envMap, sampleUV, specularMIPLevel );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = texture2D( envMap, sampleUV, specularMIPLevel );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#elif defined( ENVMAP_TYPE_SPHERE )\n\t\t\tvec3 reflectView = normalize( ( viewMatrix * vec4( reflectVec, 0.0 ) ).xyz + vec3( 0.0,0.0,1.0 ) );\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = texture2DLodEXT( envMap, reflectView.xy * 0.5 + 0.5, specularMIPLevel );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = texture2D( envMap, reflectView.xy * 0.5 + 0.5, specularMIPLevel );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#endif\n\t\treturn envMapColor.rgb * envMapIntensity;\n\t}\n#endif\n",lights_phong_fragment:"BlinnPhongMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;\nmaterial.specularColor = specular;\nmaterial.specularShininess = shininess;\nmaterial.specularStrength = specularStrength;\n",lights_phong_pars_fragment:"varying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\nstruct BlinnPhongMaterial {\n\tvec3\tdiffuseColor;\n\tvec3\tspecularColor;\n\tfloat\tspecularShininess;\n\tfloat\tspecularStrength;\n};\nvoid RE_Direct_BlinnPhong( const in IncidentLight directLight, const in GeometricContext geometry, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n\t#ifdef TOON\n\t\tvec3 irradiance = getGradientIrradiance( geometry.normal, directLight.direction ) * directLight.color;\n\t#else\n\t\tfloat dotNL = saturate( dot( geometry.normal, directLight.direction ) );\n\t\tvec3 irradiance = dotNL * directLight.color;\n\t#endif\n\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\tirradiance *= PI;\n\t#endif\n\treflectedLight.directDiffuse += irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n\treflectedLight.directSpecular += irradiance * BRDF_Specular_BlinnPhong( directLight, geometry, material.specularColor, material.specularShininess ) * material.specularStrength;\n}\nvoid RE_IndirectDiffuse_BlinnPhong( const in vec3 irradiance, const in GeometricContext geometry, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n\treflectedLight.indirectDiffuse += irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n}\n#define RE_Direct\t\t\t\tRE_Direct_BlinnPhong\n#define RE_IndirectDiffuse\t\tRE_IndirectDiffuse_BlinnPhong\n#define Material_LightProbeLOD( material )\t(0)\n",lights_physical_fragment:"PhysicalMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb * ( 1.0 - metalnessFactor );\nmaterial.specularRoughness = clamp( roughnessFactor, 0.04, 1.0 );\n#ifdef STANDARD\n\tmaterial.specularColor = mix( vec3( DEFAULT_SPECULAR_COEFFICIENT ), diffuseColor.rgb, metalnessFactor );\n#else\n\tmaterial.specularColor = mix( vec3( MAXIMUM_SPECULAR_COEFFICIENT * pow2( reflectivity ) ), diffuseColor.rgb, metalnessFactor );\n\tmaterial.clearCoat = saturate( clearCoat );\tmaterial.clearCoatRoughness = clamp( clearCoatRoughness, 0.04, 1.0 );\n#endif\n",
lights_physical_pars_fragment:"struct PhysicalMaterial {\n\tvec3\tdiffuseColor;\n\tfloat\tspecularRoughness;\n\tvec3\tspecularColor;\n\t#ifndef STANDARD\n\t\tfloat clearCoat;\n\t\tfloat clearCoatRoughness;\n\t#endif\n};\n#define MAXIMUM_SPECULAR_COEFFICIENT 0.16\n#define DEFAULT_SPECULAR_COEFFICIENT 0.04\nfloat clearCoatDHRApprox( const in float roughness, const in float dotNL ) {\n\treturn DEFAULT_SPECULAR_COEFFICIENT + ( 1.0 - DEFAULT_SPECULAR_COEFFICIENT ) * ( pow( 1.0 - dotNL, 5.0 ) * pow( 1.0 - roughness, 2.0 ) );\n}\n#if NUM_RECT_AREA_LIGHTS > 0\n\tvoid RE_Direct_RectArea_Physical( const in RectAreaLight rectAreaLight, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\t\tvec3 normal = geometry.normal;\n\t\tvec3 viewDir = geometry.viewDir;\n\t\tvec3 position = geometry.position;\n\t\tvec3 lightPos = rectAreaLight.position;\n\t\tvec3 halfWidth = rectAreaLight.halfWidth;\n\t\tvec3 halfHeight = rectAreaLight.halfHeight;\n\t\tvec3 lightColor = rectAreaLight.color;\n\t\tfloat roughness = material.specularRoughness;\n\t\tvec3 rectCoords[ 4 ];\n\t\trectCoords[ 0 ] = lightPos - halfWidth - halfHeight;\t\trectCoords[ 1 ] = lightPos + halfWidth - halfHeight;\n\t\trectCoords[ 2 ] = lightPos + halfWidth + halfHeight;\n\t\trectCoords[ 3 ] = lightPos - halfWidth + halfHeight;\n\t\tvec2 uv = LTC_Uv( normal, viewDir, roughness );\n\t\tfloat norm = texture2D( ltcMag, uv ).a;\n\t\tvec4 t = texture2D( ltcMat, uv );\n\t\tmat3 mInv = mat3(\n\t\t\tvec3(   1,   0, t.y ),\n\t\t\tvec3(   0, t.z,   0 ),\n\t\t\tvec3( t.w,   0, t.x )\n\t\t);\n\t\treflectedLight.directSpecular += lightColor * material.specularColor * norm * LTC_Evaluate( normal, viewDir, position, mInv, rectCoords );\n\t\treflectedLight.directDiffuse += lightColor * material.diffuseColor * LTC_Evaluate( normal, viewDir, position, mat3( 1 ), rectCoords );\n\t}\n#endif\nvoid RE_Direct_Physical( const in IncidentLight directLight, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\tfloat dotNL = saturate( dot( geometry.normal, directLight.direction ) );\n\tvec3 irradiance = dotNL * directLight.color;\n\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\tirradiance *= PI;\n\t#endif\n\t#ifndef STANDARD\n\t\tfloat clearCoatDHR = material.clearCoat * clearCoatDHRApprox( material.clearCoatRoughness, dotNL );\n\t#else\n\t\tfloat clearCoatDHR = 0.0;\n\t#endif\n\treflectedLight.directSpecular += ( 1.0 - clearCoatDHR ) * irradiance * BRDF_Specular_GGX( directLight, geometry, material.specularColor, material.specularRoughness );\n\treflectedLight.directDiffuse += ( 1.0 - clearCoatDHR ) * irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n\t#ifndef STANDARD\n\t\treflectedLight.directSpecular += irradiance * material.clearCoat * BRDF_Specular_GGX( directLight, geometry, vec3( DEFAULT_SPECULAR_COEFFICIENT ), material.clearCoatRoughness );\n\t#endif\n}\nvoid RE_IndirectDiffuse_Physical( const in vec3 irradiance, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\treflectedLight.indirectDiffuse += irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectSpecular_Physical( const in vec3 radiance, const in vec3 clearCoatRadiance, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\t#ifndef STANDARD\n\t\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\t\tfloat dotNL = dotNV;\n\t\tfloat clearCoatDHR = material.clearCoat * clearCoatDHRApprox( material.clearCoatRoughness, dotNL );\n\t#else\n\t\tfloat clearCoatDHR = 0.0;\n\t#endif\n\treflectedLight.indirectSpecular += ( 1.0 - clearCoatDHR ) * radiance * BRDF_Specular_GGX_Environment( geometry, material.specularColor, material.specularRoughness );\n\t#ifndef STANDARD\n\t\treflectedLight.indirectSpecular += clearCoatRadiance * material.clearCoat * BRDF_Specular_GGX_Environment( geometry, vec3( DEFAULT_SPECULAR_COEFFICIENT ), material.clearCoatRoughness );\n\t#endif\n}\n#define RE_Direct\t\t\t\tRE_Direct_Physical\n#define RE_Direct_RectArea\t\tRE_Direct_RectArea_Physical\n#define RE_IndirectDiffuse\t\tRE_IndirectDiffuse_Physical\n#define RE_IndirectSpecular\t\tRE_IndirectSpecular_Physical\n#define Material_BlinnShininessExponent( material )   GGXRoughnessToBlinnExponent( material.specularRoughness )\n#define Material_ClearCoat_BlinnShininessExponent( material )   GGXRoughnessToBlinnExponent( material.clearCoatRoughness )\nfloat computeSpecularOcclusion( const in float dotNV, const in float ambientOcclusion, const in float roughness ) {\n\treturn saturate( pow( dotNV + ambientOcclusion, exp2( - 16.0 * roughness - 1.0 ) ) - 1.0 + ambientOcclusion );\n}\n",lights_template:"\nGeometricContext geometry;\ngeometry.position = - vViewPosition;\ngeometry.normal = normal;\ngeometry.viewDir = normalize( vViewPosition );\nIncidentLight directLight;\n#if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )\n\tPointLight pointLight;\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tpointLight = pointLights[ i ];\n\t\tgetPointDirectLightIrradiance( pointLight, geometry, directLight );\n\t\t#ifdef USE_SHADOWMAP\n\t\tdirectLight.color *= all( bvec2( pointLight.shadow, directLight.visible ) ) ? getPointShadow( pointShadowMap[ i ], pointLight.shadowMapSize, pointLight.shadowBias, pointLight.shadowRadius, vPointShadowCoord[ i ] ) : 1.0;\n\t\t#endif\n\t\tRE_Direct( directLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )\n\tSpotLight spotLight;\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tspotLight = spotLights[ i ];\n\t\tgetSpotDirectLightIrradiance( spotLight, geometry, directLight );\n\t\t#ifdef USE_SHADOWMAP\n\t\tdirectLight.color *= all( bvec2( spotLight.shadow, directLight.visible ) ) ? getShadow( spotShadowMap[ i ], spotLight.shadowMapSize, spotLight.shadowBias, spotLight.shadowRadius, vSpotShadowCoord[ i ] ) : 1.0;\n\t\t#endif\n\t\tRE_Direct( directLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )\n\tDirectionalLight directionalLight;\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tdirectionalLight = directionalLights[ i ];\n\t\tgetDirectionalDirectLightIrradiance( directionalLight, geometry, directLight );\n\t\t#ifdef USE_SHADOWMAP\n\t\tdirectLight.color *= all( bvec2( directionalLight.shadow, directLight.visible ) ) ? getShadow( directionalShadowMap[ i ], directionalLight.shadowMapSize, directionalLight.shadowBias, directionalLight.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n\t\t#endif\n\t\tRE_Direct( directLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )\n\tRectAreaLight rectAreaLight;\n\tfor ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {\n\t\trectAreaLight = rectAreaLights[ i ];\n\t\tRE_Direct_RectArea( rectAreaLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if defined( RE_IndirectDiffuse )\n\tvec3 irradiance = getAmbientLightIrradiance( ambientLightColor );\n\t#ifdef USE_LIGHTMAP\n\t\tvec3 lightMapIrradiance = texture2D( lightMap, vUv2 ).xyz * lightMapIntensity;\n\t\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\t\tlightMapIrradiance *= PI;\n\t\t#endif\n\t\tirradiance += lightMapIrradiance;\n\t#endif\n\t#if ( NUM_HEMI_LIGHTS > 0 )\n\t\tfor ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n\t\t\tirradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry );\n\t\t}\n\t#endif\n\t#if defined( USE_ENVMAP ) && defined( PHYSICAL ) && defined( ENVMAP_TYPE_CUBE_UV )\n\t\tirradiance += getLightProbeIndirectIrradiance( geometry, 8 );\n\t#endif\n\tRE_IndirectDiffuse( irradiance, geometry, material, reflectedLight );\n#endif\n#if defined( USE_ENVMAP ) && defined( RE_IndirectSpecular )\n\tvec3 radiance = getLightProbeIndirectRadiance( geometry, Material_BlinnShininessExponent( material ), 8 );\n\t#ifndef STANDARD\n\t\tvec3 clearCoatRadiance = getLightProbeIndirectRadiance( geometry, Material_ClearCoat_BlinnShininessExponent( material ), 8 );\n\t#else\n\t\tvec3 clearCoatRadiance = vec3( 0.0 );\n\t#endif\n\tRE_IndirectSpecular( radiance, clearCoatRadiance, geometry, material, reflectedLight );\n#endif\n",logdepthbuf_fragment:"#if defined(USE_LOGDEPTHBUF) && defined(USE_LOGDEPTHBUF_EXT)\n\tgl_FragDepthEXT = log2(vFragDepth) * logDepthBufFC * 0.5;\n#endif",logdepthbuf_pars_fragment:"#ifdef USE_LOGDEPTHBUF\n\tuniform float logDepthBufFC;\n\t#ifdef USE_LOGDEPTHBUF_EXT\n\t\tvarying float vFragDepth;\n\t#endif\n#endif\n",logdepthbuf_pars_vertex:"#ifdef USE_LOGDEPTHBUF\n\t#ifdef USE_LOGDEPTHBUF_EXT\n\t\tvarying float vFragDepth;\n\t#endif\n\tuniform float logDepthBufFC;\n#endif",logdepthbuf_vertex:"#ifdef USE_LOGDEPTHBUF\n\tgl_Position.z = log2(max( EPSILON, gl_Position.w + 1.0 )) * logDepthBufFC;\n\t#ifdef USE_LOGDEPTHBUF_EXT\n\t\tvFragDepth = 1.0 + gl_Position.w;\n\t#else\n\t\tgl_Position.z = (gl_Position.z - 1.0) * gl_Position.w;\n\t#endif\n#endif\n",map_fragment:"#ifdef USE_MAP\n\tvec4 texelColor = texture2D( map, vUv );\n\ttexelColor = mapTexelToLinear( texelColor );\n\tdiffuseColor *= texelColor;\n#endif\n",map_pars_fragment:"#ifdef USE_MAP\n\tuniform sampler2D map;\n#endif\n",map_particle_fragment:"#ifdef USE_MAP\n\tvec4 mapTexel = texture2D( map, vec2( gl_PointCoord.x, 1.0 - gl_PointCoord.y ) * offsetRepeat.zw + offsetRepeat.xy );\n\tdiffuseColor *= mapTexelToLinear( mapTexel );\n#endif\n",map_particle_pars_fragment:"#ifdef USE_MAP\n\tuniform vec4 offsetRepeat;\n\tuniform sampler2D map;\n#endif\n",metalnessmap_fragment:"float metalnessFactor = metalness;\n#ifdef USE_METALNESSMAP\n\tvec4 texelMetalness = texture2D( metalnessMap, vUv );\n\tmetalnessFactor *= texelMetalness.b;\n#endif\n",metalnessmap_pars_fragment:"#ifdef USE_METALNESSMAP\n\tuniform sampler2D metalnessMap;\n#endif",morphnormal_vertex:"#ifdef USE_MORPHNORMALS\n\tobjectNormal += ( morphNormal0 - normal ) * morphTargetInfluences[ 0 ];\n\tobjectNormal += ( morphNormal1 - normal ) * morphTargetInfluences[ 1 ];\n\tobjectNormal += ( morphNormal2 - normal ) * morphTargetInfluences[ 2 ];\n\tobjectNormal += ( morphNormal3 - normal ) * morphTargetInfluences[ 3 ];\n#endif\n",morphtarget_pars_vertex:"#ifdef USE_MORPHTARGETS\n\t#ifndef USE_MORPHNORMALS\n\tuniform float morphTargetInfluences[ 8 ];\n\t#else\n\tuniform float morphTargetInfluences[ 4 ];\n\t#endif\n#endif",morphtarget_vertex:"#ifdef USE_MORPHTARGETS\n\ttransformed += ( morphTarget0 - position ) * morphTargetInfluences[ 0 ];\n\ttransformed += ( morphTarget1 - position ) * morphTargetInfluences[ 1 ];\n\ttransformed += ( morphTarget2 - position ) * morphTargetInfluences[ 2 ];\n\ttransformed += ( morphTarget3 - position ) * morphTargetInfluences[ 3 ];\n\t#ifndef USE_MORPHNORMALS\n\ttransformed += ( morphTarget4 - position ) * morphTargetInfluences[ 4 ];\n\ttransformed += ( morphTarget5 - position ) * morphTargetInfluences[ 5 ];\n\ttransformed += ( morphTarget6 - position ) * morphTargetInfluences[ 6 ];\n\ttransformed += ( morphTarget7 - position ) * morphTargetInfluences[ 7 ];\n\t#endif\n#endif\n",normal_flip:"#ifdef DOUBLE_SIDED\n\tfloat flipNormal = ( float( gl_FrontFacing ) * 2.0 - 1.0 );\n#else\n\tfloat flipNormal = 1.0;\n#endif\n",normal_fragment:"#ifdef FLAT_SHADED\n\tvec3 fdx = vec3( dFdx( vViewPosition.x ), dFdx( vViewPosition.y ), dFdx( vViewPosition.z ) );\n\tvec3 fdy = vec3( dFdy( vViewPosition.x ), dFdy( vViewPosition.y ), dFdy( vViewPosition.z ) );\n\tvec3 normal = normalize( cross( fdx, fdy ) );\n#else\n\tvec3 normal = normalize( vNormal ) * flipNormal;\n#endif\n#ifdef USE_NORMALMAP\n\tnormal = perturbNormal2Arb( -vViewPosition, normal );\n#elif defined( USE_BUMPMAP )\n\tnormal = perturbNormalArb( -vViewPosition, normal, dHdxy_fwd() );\n#endif\n",normalmap_pars_fragment:"#ifdef USE_NORMALMAP\n\tuniform sampler2D normalMap;\n\tuniform vec2 normalScale;\n\tvec3 perturbNormal2Arb( vec3 eye_pos, vec3 surf_norm ) {\n\t\tvec3 q0 = vec3( dFdx( eye_pos.x ), dFdx( eye_pos.y ), dFdx( eye_pos.z ) );\n\t\tvec3 q1 = vec3( dFdy( eye_pos.x ), dFdy( eye_pos.y ), dFdy( eye_pos.z ) );\n\t\tvec2 st0 = dFdx( vUv.st );\n\t\tvec2 st1 = dFdy( vUv.st );\n\t\tvec3 S = normalize( q0 * st1.t - q1 * st0.t );\n\t\tvec3 T = normalize( -q0 * st1.s + q1 * st0.s );\n\t\tvec3 N = normalize( surf_norm );\n\t\tvec3 mapN = texture2D( normalMap, vUv ).xyz * 2.0 - 1.0;\n\t\tmapN.xy = normalScale * mapN.xy;\n\t\tmat3 tsn = mat3( S, T, N );\n\t\treturn normalize( tsn * mapN );\n\t}\n#endif\n",packing:"vec3 packNormalToRGB( const in vec3 normal ) {\n\treturn normalize( normal ) * 0.5 + 0.5;\n}\nvec3 unpackRGBToNormal( const in vec3 rgb ) {\n\treturn 1.0 - 2.0 * rgb.xyz;\n}\nconst float PackUpscale = 256. / 255.;const float UnpackDownscale = 255. / 256.;\nconst vec3 PackFactors = vec3( 256. * 256. * 256., 256. * 256.,  256. );\nconst vec4 UnpackFactors = UnpackDownscale / vec4( PackFactors, 1. );\nconst float ShiftRight8 = 1. / 256.;\nvec4 packDepthToRGBA( const in float v ) {\n\tvec4 r = vec4( fract( v * PackFactors ), v );\n\tr.yzw -= r.xyz * ShiftRight8;\treturn r * PackUpscale;\n}\nfloat unpackRGBAToDepth( const in vec4 v ) {\n\treturn dot( v, UnpackFactors );\n}\nfloat viewZToOrthographicDepth( const in float viewZ, const in float near, const in float far ) {\n\treturn ( viewZ + near ) / ( near - far );\n}\nfloat orthographicDepthToViewZ( const in float linearClipZ, const in float near, const in float far ) {\n\treturn linearClipZ * ( near - far ) - near;\n}\nfloat viewZToPerspectiveDepth( const in float viewZ, const in float near, const in float far ) {\n\treturn (( near + viewZ ) * far ) / (( far - near ) * viewZ );\n}\nfloat perspectiveDepthToViewZ( const in float invClipZ, const in float near, const in float far ) {\n\treturn ( near * far ) / ( ( far - near ) * invClipZ - far );\n}\n",premultiplied_alpha_fragment:"#ifdef PREMULTIPLIED_ALPHA\n\tgl_FragColor.rgb *= gl_FragColor.a;\n#endif\n",project_vertex:"vec4 mvPosition = modelViewMatrix * vec4( transformed, 1.0 );\ngl_Position = projectionMatrix * mvPosition;\n",dithering_fragment:"#if defined( DITHERING )\n  gl_FragColor.rgb = dithering( gl_FragColor.rgb );\n#endif\n",dithering_pars_fragment:"#if defined( DITHERING )\n\tvec3 dithering( vec3 color ) {\n\t\tfloat grid_position = rand( gl_FragCoord.xy );\n\t\tvec3 dither_shift_RGB = vec3( 0.25 / 255.0, -0.25 / 255.0, 0.25 / 255.0 );\n\t\tdither_shift_RGB = mix( 2.0 * dither_shift_RGB, -2.0 * dither_shift_RGB, grid_position );\n\t\treturn color + dither_shift_RGB;\n\t}\n#endif\n",roughnessmap_fragment:"float roughnessFactor = roughness;\n#ifdef USE_ROUGHNESSMAP\n\tvec4 texelRoughness = texture2D( roughnessMap, vUv );\n\troughnessFactor *= texelRoughness.g;\n#endif\n",roughnessmap_pars_fragment:"#ifdef USE_ROUGHNESSMAP\n\tuniform sampler2D roughnessMap;\n#endif",shadowmap_pars_fragment:"#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\t\tuniform sampler2D directionalShadowMap[ NUM_DIR_LIGHTS ];\n\t\tvarying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHTS ];\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\t\tuniform sampler2D spotShadowMap[ NUM_SPOT_LIGHTS ];\n\t\tvarying vec4 vSpotShadowCoord[ NUM_SPOT_LIGHTS ];\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\t\tuniform sampler2D pointShadowMap[ NUM_POINT_LIGHTS ];\n\t\tvarying vec4 vPointShadowCoord[ NUM_POINT_LIGHTS ];\n\t#endif\n\tfloat texture2DCompare( sampler2D depths, vec2 uv, float compare ) {\n\t\treturn step( compare, unpackRGBAToDepth( texture2D( depths, uv ) ) );\n\t}\n\tfloat texture2DShadowLerp( sampler2D depths, vec2 size, vec2 uv, float compare ) {\n\t\tconst vec2 offset = vec2( 0.0, 1.0 );\n\t\tvec2 texelSize = vec2( 1.0 ) / size;\n\t\tvec2 centroidUV = floor( uv * size + 0.5 ) / size;\n\t\tfloat lb = texture2DCompare( depths, centroidUV + texelSize * offset.xx, compare );\n\t\tfloat lt = texture2DCompare( depths, centroidUV + texelSize * offset.xy, compare );\n\t\tfloat rb = texture2DCompare( depths, centroidUV + texelSize * offset.yx, compare );\n\t\tfloat rt = texture2DCompare( depths, centroidUV + texelSize * offset.yy, compare );\n\t\tvec2 f = fract( uv * size + 0.5 );\n\t\tfloat a = mix( lb, lt, f.y );\n\t\tfloat b = mix( rb, rt, f.y );\n\t\tfloat c = mix( a, b, f.x );\n\t\treturn c;\n\t}\n\tfloat getShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord ) {\n\t\tfloat shadow = 1.0;\n\t\tshadowCoord.xyz /= shadowCoord.w;\n\t\tshadowCoord.z += shadowBias;\n\t\tbvec4 inFrustumVec = bvec4 ( shadowCoord.x >= 0.0, shadowCoord.x <= 1.0, shadowCoord.y >= 0.0, shadowCoord.y <= 1.0 );\n\t\tbool inFrustum = all( inFrustumVec );\n\t\tbvec2 frustumTestVec = bvec2( inFrustum, shadowCoord.z <= 1.0 );\n\t\tbool frustumTest = all( frustumTestVec );\n\t\tif ( frustumTest ) {\n\t\t#if defined( SHADOWMAP_TYPE_PCF )\n\t\t\tvec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n\t\t\tfloat dx0 = - texelSize.x * shadowRadius;\n\t\t\tfloat dy0 = - texelSize.y * shadowRadius;\n\t\t\tfloat dx1 = + texelSize.x * shadowRadius;\n\t\t\tfloat dy1 = + texelSize.y * shadowRadius;\n\t\t\tshadow = (\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy1 ), shadowCoord.z )\n\t\t\t) * ( 1.0 / 9.0 );\n\t\t#elif defined( SHADOWMAP_TYPE_PCF_SOFT )\n\t\t\tvec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n\t\t\tfloat dx0 = - texelSize.x * shadowRadius;\n\t\t\tfloat dy0 = - texelSize.y * shadowRadius;\n\t\t\tfloat dx1 = + texelSize.x * shadowRadius;\n\t\t\tfloat dy1 = + texelSize.y * shadowRadius;\n\t\t\tshadow = (\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( 0.0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx1, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx0, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy, shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx1, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( 0.0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx1, dy1 ), shadowCoord.z )\n\t\t\t) * ( 1.0 / 9.0 );\n\t\t#else\n\t\t\tshadow = texture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z );\n\t\t#endif\n\t\t}\n\t\treturn shadow;\n\t}\n\tvec2 cubeToUV( vec3 v, float texelSizeY ) {\n\t\tvec3 absV = abs( v );\n\t\tfloat scaleToCube = 1.0 / max( absV.x, max( absV.y, absV.z ) );\n\t\tabsV *= scaleToCube;\n\t\tv *= scaleToCube * ( 1.0 - 2.0 * texelSizeY );\n\t\tvec2 planar = v.xy;\n\t\tfloat almostATexel = 1.5 * texelSizeY;\n\t\tfloat almostOne = 1.0 - almostATexel;\n\t\tif ( absV.z >= almostOne ) {\n\t\t\tif ( v.z > 0.0 )\n\t\t\t\tplanar.x = 4.0 - v.x;\n\t\t} else if ( absV.x >= almostOne ) {\n\t\t\tfloat signX = sign( v.x );\n\t\t\tplanar.x = v.z * signX + 2.0 * signX;\n\t\t} else if ( absV.y >= almostOne ) {\n\t\t\tfloat signY = sign( v.y );\n\t\t\tplanar.x = v.x + 2.0 * signY + 2.0;\n\t\t\tplanar.y = v.z * signY - 2.0;\n\t\t}\n\t\treturn vec2( 0.125, 0.25 ) * planar + vec2( 0.375, 0.75 );\n\t}\n\tfloat getPointShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord ) {\n\t\tvec2 texelSize = vec2( 1.0 ) / ( shadowMapSize * vec2( 4.0, 2.0 ) );\n\t\tvec3 lightToPosition = shadowCoord.xyz;\n\t\tvec3 bd3D = normalize( lightToPosition );\n\t\tfloat dp = ( length( lightToPosition ) - shadowBias ) / 1000.0;\n\t\t#if defined( SHADOWMAP_TYPE_PCF ) || defined( SHADOWMAP_TYPE_PCF_SOFT )\n\t\t\tvec2 offset = vec2( - 1, 1 ) * shadowRadius * texelSize.y;\n\t\t\treturn (\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyx, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyx, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxx, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxx, texelSize.y ), dp )\n\t\t\t) * ( 1.0 / 9.0 );\n\t\t#else\n\t\t\treturn texture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp );\n\t\t#endif\n\t}\n#endif\n",shadowmap_pars_vertex:"#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\t\tuniform mat4 directionalShadowMatrix[ NUM_DIR_LIGHTS ];\n\t\tvarying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHTS ];\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\t\tuniform mat4 spotShadowMatrix[ NUM_SPOT_LIGHTS ];\n\t\tvarying vec4 vSpotShadowCoord[ NUM_SPOT_LIGHTS ];\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\t\tuniform mat4 pointShadowMatrix[ NUM_POINT_LIGHTS ];\n\t\tvarying vec4 vPointShadowCoord[ NUM_POINT_LIGHTS ];\n\t#endif\n#endif\n",shadowmap_vertex:"#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tvDirectionalShadowCoord[ i ] = directionalShadowMatrix[ i ] * worldPosition;\n\t}\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tvSpotShadowCoord[ i ] = spotShadowMatrix[ i ] * worldPosition;\n\t}\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tvPointShadowCoord[ i ] = pointShadowMatrix[ i ] * worldPosition;\n\t}\n\t#endif\n#endif\n",shadowmask_pars_fragment:"float getShadowMask() {\n\tfloat shadow = 1.0;\n\t#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\tDirectionalLight directionalLight;\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tdirectionalLight = directionalLights[ i ];\n\t\tshadow *= bool( directionalLight.shadow ) ? getShadow( directionalShadowMap[ i ], directionalLight.shadowMapSize, directionalLight.shadowBias, directionalLight.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n\t}\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\tSpotLight spotLight;\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tspotLight = spotLights[ i ];\n\t\tshadow *= bool( spotLight.shadow ) ? getShadow( spotShadowMap[ i ], spotLight.shadowMapSize, spotLight.shadowBias, spotLight.shadowRadius, vSpotShadowCoord[ i ] ) : 1.0;\n\t}\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\tPointLight pointLight;\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tpointLight = pointLights[ i ];\n\t\tshadow *= bool( pointLight.shadow ) ? getPointShadow( pointShadowMap[ i ], pointLight.shadowMapSize, pointLight.shadowBias, pointLight.shadowRadius, vPointShadowCoord[ i ] ) : 1.0;\n\t}\n\t#endif\n\t#endif\n\treturn shadow;\n}\n",skinbase_vertex:"#ifdef USE_SKINNING\n\tmat4 boneMatX = getBoneMatrix( skinIndex.x );\n\tmat4 boneMatY = getBoneMatrix( skinIndex.y );\n\tmat4 boneMatZ = getBoneMatrix( skinIndex.z );\n\tmat4 boneMatW = getBoneMatrix( skinIndex.w );\n#endif",skinning_pars_vertex:"#ifdef USE_SKINNING\n\tuniform mat4 bindMatrix;\n\tuniform mat4 bindMatrixInverse;\n\t#ifdef BONE_TEXTURE\n\t\tuniform sampler2D boneTexture;\n\t\tuniform int boneTextureSize;\n\t\tmat4 getBoneMatrix( const in float i ) {\n\t\t\tfloat j = i * 4.0;\n\t\t\tfloat x = mod( j, float( boneTextureSize ) );\n\t\t\tfloat y = floor( j / float( boneTextureSize ) );\n\t\t\tfloat dx = 1.0 / float( boneTextureSize );\n\t\t\tfloat dy = 1.0 / float( boneTextureSize );\n\t\t\ty = dy * ( y + 0.5 );\n\t\t\tvec4 v1 = texture2D( boneTexture, vec2( dx * ( x + 0.5 ), y ) );\n\t\t\tvec4 v2 = texture2D( boneTexture, vec2( dx * ( x + 1.5 ), y ) );\n\t\t\tvec4 v3 = texture2D( boneTexture, vec2( dx * ( x + 2.5 ), y ) );\n\t\t\tvec4 v4 = texture2D( boneTexture, vec2( dx * ( x + 3.5 ), y ) );\n\t\t\tmat4 bone = mat4( v1, v2, v3, v4 );\n\t\t\treturn bone;\n\t\t}\n\t#else\n\t\tuniform mat4 boneMatrices[ MAX_BONES ];\n\t\tmat4 getBoneMatrix( const in float i ) {\n\t\t\tmat4 bone = boneMatrices[ int(i) ];\n\t\t\treturn bone;\n\t\t}\n\t#endif\n#endif\n",skinning_vertex:"#ifdef USE_SKINNING\n\tvec4 skinVertex = bindMatrix * vec4( transformed, 1.0 );\n\tvec4 skinned = vec4( 0.0 );\n\tskinned += boneMatX * skinVertex * skinWeight.x;\n\tskinned += boneMatY * skinVertex * skinWeight.y;\n\tskinned += boneMatZ * skinVertex * skinWeight.z;\n\tskinned += boneMatW * skinVertex * skinWeight.w;\n\ttransformed = ( bindMatrixInverse * skinned ).xyz;\n#endif\n",skinnormal_vertex:"#ifdef USE_SKINNING\n\tmat4 skinMatrix = mat4( 0.0 );\n\tskinMatrix += skinWeight.x * boneMatX;\n\tskinMatrix += skinWeight.y * boneMatY;\n\tskinMatrix += skinWeight.z * boneMatZ;\n\tskinMatrix += skinWeight.w * boneMatW;\n\tskinMatrix  = bindMatrixInverse * skinMatrix * bindMatrix;\n\tobjectNormal = vec4( skinMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n#endif\n",specularmap_fragment:"float specularStrength;\n#ifdef USE_SPECULARMAP\n\tvec4 texelSpecular = texture2D( specularMap, vUv );\n\tspecularStrength = texelSpecular.r;\n#else\n\tspecularStrength = 1.0;\n#endif",specularmap_pars_fragment:"#ifdef USE_SPECULARMAP\n\tuniform sampler2D specularMap;\n#endif",tonemapping_fragment:"#if defined( TONE_MAPPING )\n  gl_FragColor.rgb = toneMapping( gl_FragColor.rgb );\n#endif\n",tonemapping_pars_fragment:"#define saturate(a) clamp( a, 0.0, 1.0 )\nuniform float toneMappingExposure;\nuniform float toneMappingWhitePoint;\nvec3 LinearToneMapping( vec3 color ) {\n\treturn toneMappingExposure * color;\n}\nvec3 ReinhardToneMapping( vec3 color ) {\n\tcolor *= toneMappingExposure;\n\treturn saturate( color / ( vec3( 1.0 ) + color ) );\n}\n#define Uncharted2Helper( x ) max( ( ( x * ( 0.15 * x + 0.10 * 0.50 ) + 0.20 * 0.02 ) / ( x * ( 0.15 * x + 0.50 ) + 0.20 * 0.30 ) ) - 0.02 / 0.30, vec3( 0.0 ) )\nvec3 Uncharted2ToneMapping( vec3 color ) {\n\tcolor *= toneMappingExposure;\n\treturn saturate( Uncharted2Helper( color ) / Uncharted2Helper( vec3( toneMappingWhitePoint ) ) );\n}\nvec3 OptimizedCineonToneMapping( vec3 color ) {\n\tcolor *= toneMappingExposure;\n\tcolor = max( vec3( 0.0 ), color - 0.004 );\n\treturn pow( ( color * ( 6.2 * color + 0.5 ) ) / ( color * ( 6.2 * color + 1.7 ) + 0.06 ), vec3( 2.2 ) );\n}\n",uv_pars_fragment:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP ) || defined( USE_EMISSIVEMAP ) || defined( USE_ROUGHNESSMAP ) || defined( USE_METALNESSMAP )\n\tvarying vec2 vUv;\n#endif",uv_pars_vertex:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP ) || defined( USE_EMISSIVEMAP ) || defined( USE_ROUGHNESSMAP ) || defined( USE_METALNESSMAP )\n\tvarying vec2 vUv;\n\tuniform vec4 offsetRepeat;\n#endif\n",uv_vertex:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP ) || defined( USE_EMISSIVEMAP ) || defined( USE_ROUGHNESSMAP ) || defined( USE_METALNESSMAP )\n\tvUv = uv * offsetRepeat.zw + offsetRepeat.xy;\n#endif",uv2_pars_fragment:"#if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n\tvarying vec2 vUv2;\n#endif",uv2_pars_vertex:"#if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n\tattribute vec2 uv2;\n\tvarying vec2 vUv2;\n#endif",uv2_vertex:"#if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n\tvUv2 = uv2;\n#endif",worldpos_vertex:"#if defined( USE_ENVMAP ) || defined( PHONG ) || defined( PHYSICAL ) || defined( LAMBERT ) || defined ( USE_SHADOWMAP )\n\tvec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n#endif\n",cube_frag:"uniform samplerCube tCube;\nuniform float tFlip;\nuniform float opacity;\nvarying vec3 vWorldPosition;\n#include <common>\nvoid main() {\n\tgl_FragColor = textureCube( tCube, vec3( tFlip * vWorldPosition.x, vWorldPosition.yz ) );\n\tgl_FragColor.a *= opacity;\n}\n",cube_vert:"varying vec3 vWorldPosition;\n#include <common>\nvoid main() {\n\tvWorldPosition = transformDirection( position, modelMatrix );\n\t#include <begin_vertex>\n\t#include <project_vertex>\n}\n",depth_frag:"#if DEPTH_PACKING == 3200\n\tuniform float opacity;\n#endif\n#include <common>\n#include <packing>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( 1.0 );\n\t#if DEPTH_PACKING == 3200\n\t\tdiffuseColor.a = opacity;\n\t#endif\n\t#include <map_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <logdepthbuf_fragment>\n\t#if DEPTH_PACKING == 3200\n\t\tgl_FragColor = vec4( vec3( gl_FragCoord.z ), opacity );\n\t#elif DEPTH_PACKING == 3201\n\t\tgl_FragColor = packDepthToRGBA( gl_FragCoord.z );\n\t#endif\n}\n",depth_vert:"#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <skinbase_vertex>\n\t#ifdef USE_DISPLACEMENTMAP\n\t\t#include <beginnormal_vertex>\n\t\t#include <morphnormal_vertex>\n\t\t#include <skinnormal_vertex>\n\t#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n}\n",distanceRGBA_frag:"uniform vec3 lightPos;\nvarying vec4 vWorldPosition;\n#include <common>\n#include <packing>\n#include <clipping_planes_pars_fragment>\nvoid main () {\n\t#include <clipping_planes_fragment>\n\tgl_FragColor = packDepthToRGBA( length( vWorldPosition.xyz - lightPos.xyz ) / 1000.0 );\n}\n",distanceRGBA_vert:"varying vec4 vWorldPosition;\n#include <common>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <skinbase_vertex>\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <project_vertex>\n\t#include <worldpos_vertex>\n\t#include <clipping_planes_vertex>\n\tvWorldPosition = worldPosition;\n}\n",equirect_frag:"uniform sampler2D tEquirect;\nuniform float tFlip;\nvarying vec3 vWorldPosition;\n#include <common>\nvoid main() {\n\tvec3 direction = normalize( vWorldPosition );\n\tvec2 sampleUV;\n\tsampleUV.y = saturate( tFlip * direction.y * -0.5 + 0.5 );\n\tsampleUV.x = atan( direction.z, direction.x ) * RECIPROCAL_PI2 + 0.5;\n\tgl_FragColor = texture2D( tEquirect, sampleUV );\n}\n",equirect_vert:"varying vec3 vWorldPosition;\n#include <common>\nvoid main() {\n\tvWorldPosition = transformDirection( position, modelMatrix );\n\t#include <begin_vertex>\n\t#include <project_vertex>\n}\n",
linedashed_frag:"uniform vec3 diffuse;\nuniform float opacity;\nuniform float dashSize;\nuniform float totalSize;\nvarying float vLineDistance;\n#include <common>\n#include <color_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tif ( mod( vLineDistance, totalSize ) > dashSize ) {\n\t\tdiscard;\n\t}\n\tvec3 outgoingLight = vec3( 0.0 );\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\t#include <logdepthbuf_fragment>\n\t#include <color_fragment>\n\toutgoingLight = diffuseColor.rgb;\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <premultiplied_alpha_fragment>\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n}\n",linedashed_vert:"uniform float scale;\nattribute float lineDistance;\nvarying float vLineDistance;\n#include <common>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <color_vertex>\n\tvLineDistance = scale * lineDistance;\n\tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n\tgl_Position = projectionMatrix * mvPosition;\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <fog_vertex>\n}\n",meshbasic_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <specularmap_fragment>\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\t#ifdef USE_LIGHTMAP\n\t\treflectedLight.indirectDiffuse += texture2D( lightMap, vUv2 ).xyz * lightMapIntensity;\n\t#else\n\t\treflectedLight.indirectDiffuse += vec3( 1.0 );\n\t#endif\n\t#include <aomap_fragment>\n\treflectedLight.indirectDiffuse *= diffuseColor.rgb;\n\tvec3 outgoingLight = reflectedLight.indirectDiffuse;\n\t#include <normal_flip>\n\t#include <envmap_fragment>\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <premultiplied_alpha_fragment>\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n}\n",meshbasic_vert:"#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <skinbase_vertex>\n\t#ifdef USE_ENVMAP\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n\t#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <worldpos_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <envmap_vertex>\n\t#include <fog_vertex>\n}\n",meshlambert_frag:"uniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float opacity;\nvarying vec3 vLightFront;\n#ifdef DOUBLE_SIDED\n\tvarying vec3 vLightBack;\n#endif\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_pars_fragment>\n#include <bsdfs>\n#include <lights_pars>\n#include <fog_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <shadowmask_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\tvec3 totalEmissiveRadiance = emissive;\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <specularmap_fragment>\n\t#include <emissivemap_fragment>\n\treflectedLight.indirectDiffuse = getAmbientLightIrradiance( ambientLightColor );\n\t#include <lightmap_fragment>\n\treflectedLight.indirectDiffuse *= BRDF_Diffuse_Lambert( diffuseColor.rgb );\n\t#ifdef DOUBLE_SIDED\n\t\treflectedLight.directDiffuse = ( gl_FrontFacing ) ? vLightFront : vLightBack;\n\t#else\n\t\treflectedLight.directDiffuse = vLightFront;\n\t#endif\n\treflectedLight.directDiffuse *= BRDF_Diffuse_Lambert( diffuseColor.rgb ) * getShadowMask();\n\t#include <aomap_fragment>\n\tvec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + totalEmissiveRadiance;\n\t#include <normal_flip>\n\t#include <envmap_fragment>\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n\t#include <premultiplied_alpha_fragment>\n\t#include <dithering_fragment>\n}\n",meshlambert_vert:"#define LAMBERT\nvarying vec3 vLightFront;\n#ifdef DOUBLE_SIDED\n\tvarying vec3 vLightBack;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <envmap_pars_vertex>\n#include <bsdfs>\n#include <lights_pars>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <worldpos_vertex>\n\t#include <envmap_vertex>\n\t#include <lights_lambert_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",meshphong_frag:"#define PHONG\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform vec3 specular;\nuniform float shininess;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_pars_fragment>\n#include <gradientmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars>\n#include <lights_phong_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\tvec3 totalEmissiveRadiance = emissive;\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <specularmap_fragment>\n\t#include <normal_flip>\n\t#include <normal_fragment>\n\t#include <emissivemap_fragment>\n\t#include <lights_phong_fragment>\n\t#include <lights_template>\n\t#include <aomap_fragment>\n\tvec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;\n\t#include <envmap_fragment>\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n\t#include <premultiplied_alpha_fragment>\n\t#include <dithering_fragment>\n}\n",meshphong_vert:"#define PHONG\nvarying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n#ifndef FLAT_SHADED\n\tvNormal = normalize( transformedNormal );\n#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\tvViewPosition = - mvPosition.xyz;\n\t#include <worldpos_vertex>\n\t#include <envmap_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",meshphysical_frag:"#define PHYSICAL\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float roughness;\nuniform float metalness;\nuniform float opacity;\n#ifndef STANDARD\n\tuniform float clearCoat;\n\tuniform float clearCoatRoughness;\n#endif\nvarying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <cube_uv_reflection_fragment>\n#include <lights_pars>\n#include <lights_physical_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <roughnessmap_pars_fragment>\n#include <metalnessmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\tvec3 totalEmissiveRadiance = emissive;\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <roughnessmap_fragment>\n\t#include <metalnessmap_fragment>\n\t#include <normal_flip>\n\t#include <normal_fragment>\n\t#include <emissivemap_fragment>\n\t#include <lights_physical_fragment>\n\t#include <lights_template>\n\t#include <aomap_fragment>\n\tvec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n\t#include <premultiplied_alpha_fragment>\n\t#include <dithering_fragment>\n}\n",meshphysical_vert:"#define PHYSICAL\nvarying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n#ifndef FLAT_SHADED\n\tvNormal = normalize( transformedNormal );\n#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\tvViewPosition = - mvPosition.xyz;\n\t#include <worldpos_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",normal_frag:"#define NORMAL\nuniform float opacity;\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP )\n\tvarying vec3 vViewPosition;\n#endif\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <packing>\n#include <uv_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\nvoid main() {\n\t#include <logdepthbuf_fragment>\n\t#include <normal_flip>\n\t#include <normal_fragment>\n\tgl_FragColor = vec4( packNormalToRGB( normal ), opacity );\n}\n",normal_vert:"#define NORMAL\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP )\n\tvarying vec3 vViewPosition;\n#endif\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n#ifndef FLAT_SHADED\n\tvNormal = normalize( transformedNormal );\n#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP )\n\tvViewPosition = - mvPosition.xyz;\n#endif\n}\n",points_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <color_pars_fragment>\n#include <map_particle_pars_fragment>\n#include <fog_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec3 outgoingLight = vec3( 0.0 );\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\t#include <logdepthbuf_fragment>\n\t#include <map_particle_fragment>\n\t#include <color_fragment>\n\t#include <alphatest_fragment>\n\toutgoingLight = diffuseColor.rgb;\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <premultiplied_alpha_fragment>\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n}\n",points_vert:"uniform float size;\nuniform float scale;\n#include <common>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <color_vertex>\n\t#include <begin_vertex>\n\t#include <project_vertex>\n\t#ifdef USE_SIZEATTENUATION\n\t\tgl_PointSize = size * ( scale / - mvPosition.z );\n\t#else\n\t\tgl_PointSize = size;\n\t#endif\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <worldpos_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",shadow_frag:"uniform float opacity;\n#include <common>\n#include <packing>\n#include <bsdfs>\n#include <lights_pars>\n#include <shadowmap_pars_fragment>\n#include <shadowmask_pars_fragment>\nvoid main() {\n\tgl_FragColor = vec4( 0.0, 0.0, 0.0, opacity * ( 1.0 - getShadowMask() ) );\n}\n",shadow_vert:"#include <shadowmap_pars_vertex>\nvoid main() {\n\t#include <begin_vertex>\n\t#include <project_vertex>\n\t#include <worldpos_vertex>\n\t#include <shadowmap_vertex>\n}\n",none_frag:a.default,none_vert:s.default,normals_frag:c.default,normals_vert:u.default,texture_frag:m.default,texture_vert:h.default,texture_normals_frag:_.default,texture_normals_vert:x.default};y.parseIncludes=function(e){function t(e,t){var n=y[t];if(void 0===n)throw new Error("Can not resolve #include <"+t+">");return y.parseIncludes(n)}return e.replace(/#include +<([\w\d.]+)>/g,t)},n.default=y},{"./none_frag.js":21,"./none_vert.js":22,"./normals_frag.js":23,"./normals_vert.js":24,"./texture_frag.js":27,"./texture_normals_frag.js":28,"./texture_normals_vert.js":29,"./texture_vert.js":30}],20:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=e("./ShaderChunk.js"),r=function(e){return e&&e.__esModule?e:{default:e}}(i),a={common:{diffuse:{value:{isColor:!0,red:1,green:1,blue:1,alpha:1}},opacity:{value:1},map:{value:null},offsetRepeat:{value:{isCartesian4:!0,x:0,y:0,z:1,w:1}},specularMap:{value:null},alphaMap:{value:null},envMap:{value:null},flipEnvMap:{value:-1},reflectivity:{value:1},refractionRatio:{value:.98}},aomap:{aoMap:{value:null},aoMapIntensity:{value:1}},lightmap:{lightMap:{value:null},lightMapIntensity:{value:1}},emissivemap:{emissiveMap:{value:null}},bumpmap:{bumpMap:{value:null},bumpScale:{value:1}},normalmap:{normalMap:{value:null},normalScale:{value:{isCartesian2:!0,x:1,y:1}}},displacementmap:{displacementMap:{value:null},displacementScale:{value:1},displacementBias:{value:0}},roughnessmap:{roughnessMap:{value:null}},metalnessmap:{metalnessMap:{value:null}},gradientmap:{gradientMap:{value:null}},fog:{fogDensity:{value:25e-5},fogNear:{value:1},fogFar:{value:2e3},fogColor:{value:{isColor:!0,red:0,green:0,blue:1,alpha:1}}},lights:{ambientLightColor:{value:[]},directionalLights:{value:[],properties:{direction:{},color:{},shadow:{},shadowBias:{},shadowRadius:{},shadowMapSize:{}}},directionalShadowMap:{value:[]},directionalShadowMatrix:{value:[]},spotLights:{value:[],properties:{color:{},position:{},direction:{},distance:{},coneCos:{},penumbraCos:{},decay:{},shadow:{},shadowBias:{},shadowRadius:{},shadowMapSize:{}}},spotShadowMap:{value:[]},spotShadowMatrix:{value:[]},pointLights:{value:[],properties:{color:{},position:{},decay:{},distance:{},shadow:{},shadowBias:{},shadowRadius:{},shadowMapSize:{}}},pointShadowMap:{value:[]},pointShadowMatrix:{value:[]},hemisphereLights:{value:[],properties:{direction:{},skyColor:{},groundColor:{}}},rectAreaLights:{value:[],properties:{color:{},position:{},width:{},height:{}}}},points:{diffuse:{value:{isColor:!0,red:1,green:1,blue:1,alpha:1}},opacity:{value:1},size:{value:1},scale:{value:1},map:{value:null},offsetRepeat:{value:{isCartesian4:!0,x:0,y:0,z:1,w:1}}}},o={merge:function(e){for(var t={},n=0;n<e.length;n++){var i=this.clone(e[n]);for(var r in i)t[r]=i[r]}return t},clone:function(e){var t={};for(var n in e){t[n]={};for(var i in e[n]){var r=e[n][i];"undefined"!=typeof Cesium&&r&&(r instanceof Cesium.Color||r instanceof Cesium.Matrix3||r instanceof Cesium.Matrix4||r instanceof Cesium.Cartesian2||r instanceof Cesium.Cartesian3||r instanceof Cesium.Cartesian4)?t[n][i]=r.constructor.clone(r):Array.isArray(r)?t[n][i]=r.slice():"undefined"!=typeof Cesium&&r?r.isColor?t[n][i]=Cesium.Color.clone(r):r.isCartesian2?t[n][i]=Cesium.Cartesian2.clone(r):r.isCartesian3?t[n][i]=Cesium.Cartesian3.clone(r):r.isCartesian4?t[n][i]=Cesium.Cartesian4.clone(r):t[n][i]=r:t[n][i]=r}}return t}},s={basic:{uniforms:o.merge([a.common,a.aomap,a.lightmap,a.fog]),vertexShader:r.default.meshbasic_vert,fragmentShader:r.default.meshbasic_frag},lambert:{uniforms:o.merge([a.common,a.aomap,a.lightmap,a.emissivemap,a.fog,a.lights,{emissive:{value:{isColor:!0,red:1,green:1,blue:1,alpha:1}}}]),vertexShader:r.default.meshlambert_vert,fragmentShader:r.default.meshlambert_frag},phong:{uniforms:o.merge([a.common,a.aomap,a.lightmap,a.emissivemap,a.bumpmap,a.normalmap,a.displacementmap,a.gradientmap,a.fog,a.lights,{emissive:{value:{isColor:!0,red:1,green:1,blue:1,alpha:1}},specular:{value:{isColor:!0,red:1,green:1,blue:1,alpha:1}},shininess:{value:30}}]),vertexShader:r.default.meshphong_vert,fragmentShader:r.default.meshphong_frag},standard:{uniforms:o.merge([a.common,a.aomap,a.lightmap,a.emissivemap,a.bumpmap,a.normalmap,a.displacementmap,a.roughnessmap,a.metalnessmap,a.fog,a.lights,{emissive:{value:{isColor:!0,red:0,green:0,blue:0,alpha:1}},roughness:{value:.5},metalness:{value:.5},envMapIntensity:{value:1}}]),vertexShader:r.default.meshphysical_vert,fragmentShader:r.default.meshphysical_frag},points:{uniforms:o.merge([a.points,a.fog]),vertexShader:r.default.points_vert,fragmentShader:r.default.points_frag},dashed:{uniforms:o.merge([a.common,a.fog,{scale:{value:1},dashSize:{value:1},totalSize:{value:2}}]),vertexShader:r.default.linedashed_vert,fragmentShader:r.default.linedashed_frag},depth:{uniforms:o.merge([a.common,a.displacementmap]),vertexShader:r.default.depth_vert,fragmentShader:r.default.depth_frag},normal:{uniforms:o.merge([a.common,a.bumpmap,a.normalmap,a.displacementmap,{opacity:{value:1}}]),vertexShader:r.default.normal_vert,fragmentShader:r.default.normal_frag},cube:{uniforms:{tCube:{value:null},tFlip:{value:-1},opacity:{value:1}},vertexShader:r.default.cube_vert,fragmentShader:r.default.cube_frag},equirect:{uniforms:{tEquirect:{value:null},tFlip:{value:-1}},vertexShader:r.default.equirect_vert,fragmentShader:r.default.equirect_frag},distanceRGBA:{uniforms:{lightPos:{value:{isCartesian3:!0,x:0,y:0,z:0}}},vertexShader:r.default.distanceRGBA_vert,fragmentShader:r.default.distanceRGBA_frag}};s.physical={uniforms:o.merge([s.standard.uniforms,{clearCoat:{value:0},clearCoatRoughness:{value:0}}]),vertexShader:r.default.meshphysical_vert,fragmentShader:r.default.meshphysical_frag},n.default=s},{"./ShaderChunk.js":19}],21:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\nvarying vec3 v_position;\n\nuniform vec4 ambientColor;\nuniform vec4 diffuseColor;\nuniform vec4 specularColor;\nuniform float specularShininess;\nuniform float picked;\nuniform vec4  pickedColor;\n\nvoid main(void) \n{\n    vec4 color = vec4(0.0, 0.0, 0.0, 0.0);\n    vec4 ambient = ambientColor;\n    vec4 diffuse = diffuseColor;\n    vec4 specular = specularColor;\n    color.xyz += ambient.xyz;\n    color.xyz += diffuse.xyz;\n    color.xyz += specular.xyz;\n    color = vec4(color.rgb * diffuse.a, diffuse.a);\n    gl_FragColor = color;\n    if(picked!=0.0){\n        gl_FragColor =mix(color, pickedColor*0.5,1.0);\n    }\n}"},{}],22:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\n\n\nvarying vec3 v_position;\n\nvoid main(void) \n{\n    vec4 pos =  modelViewMatrix * vec4( position,1.0);\n    v_position = pos.xyz;\n    gl_Position =  projectionMatrix * pos;\n}"},{}],23:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\nvarying vec3 v_position;\nvarying vec3 v_normal;\n\nuniform vec4 ambientColor;\nuniform vec4 diffuseColor;\nuniform vec4 specularColor;\nuniform float specularShininess;\nuniform float alpha;\nuniform float picked;\nuniform vec4  pickedColor;\n\nvarying vec3 v_light0Direction;\n\nvoid main(void) \n{\n    vec3 normal = normalize(v_normal);\n    vec4 color = vec4(0.0, 0.0, 0.0, 0.0);\n    vec3 diffuseLight = vec3(0.0, 0.0, 0.0);\n    vec3 lightColor = vec3(1.0,1.0,1.0);\nvec4 ambient = ambientColor;\n    vec4 diffuse = diffuseColor;\n    vec4 specular = specularColor;\n\n    vec3 specularLight = vec3(0.0, 0.0, 0.0);\n    {\n        float specularIntensity = 0.0;\n        float attenuation = 1.0;\n        vec3 l = normalize(v_light0Direction);\n        vec3 viewDir = -normalize(v_position);\n        vec3 h = normalize(l+viewDir);\n        specularIntensity = max(0.0, pow(max(dot(normal,h), 0.0) , specularShininess)) * attenuation;\n        specularLight += lightColor * specularIntensity;\n        diffuseLight += lightColor * max(dot(normal,l), 0.0) * attenuation;\n    }\n    //specular.xyz *= specularLight;\n    //diffuse.xyz *= diffuseLight;\n    color.xyz += ambient.xyz;\n    color.xyz += diffuse.xyz;\n    color.xyz += specular.xyz;\n    color = vec4(color.rgb * diffuse.a, diffuse.a*alpha);\n    gl_FragColor = color;\n    if(picked!=0.0){\n        gl_FragColor =mix(color, pickedColor*0.5,1.0);\n    }\n}"},{}],24:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\n\n\nvarying vec3 v_position;\nvarying vec3 v_normal;\n\nvarying vec3 v_light0Direction;\n\nvoid main(void) \n{\n    vec4 pos =  modelViewMatrix * vec4( position,1.0);\n    v_normal =  normalMatrix *  normal;\n    v_position = pos.xyz;\n    v_light0Direction = mat3( modelViewMatrix) * vec3(1.0,1.0,1.0);\n    gl_Position =  projectionMatrix * pos;\n}"},{}],25:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\nvarying vec3 v_position;\nvarying vec3 v_normal;\nuniform float picked;\nuniform vec4  pickedColor;\nuniform vec4  defaultColor;\nuniform float specular;\nuniform float shininess;\nuniform vec3  emission;\nuniform vec3  u_cameraPosition;\nvoid main() {\n    vec3 positionToEyeEC = -v_position; \n    vec3 normalEC =normalize(v_normal);\n    vec4 color=defaultColor;\n    if(picked!=0.0){\n        color = pickedColor;\n    }\n    czm_material material;\n    material.specular = specular;\n    material.shininess = shininess;\n    material.normal =  normalEC;\n    material.emission =emission;//vec3(0.2,0.2,0.2);\n    material.diffuse = color.rgb ;\n    material.alpha =  color.a;\n    vec3 lightDirectionEC=-u_cameraPosition;\n    gl_FragColor =  czm_phong(normalize(positionToEyeEC), material,lightDirectionEC);\n}"},{}],26:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\n\n\nvarying vec3 v_position;\nvarying vec3 v_normal;\n\nvarying vec3 v_light0Direction;\n\nvoid main(void) \n{\n    vec4 pos =  modelViewMatrix * vec4( position,1.0);\n    v_normal =  normalMatrix *  normal;\n    v_position = pos.xyz;\n    v_light0Direction = mat3( modelViewMatrix) * vec3(1.0,1.0,1.0);\n    gl_Position =  projectionMatrix * pos;\n}"},{}],27:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\nvarying vec3 v_position;\nvarying vec2 v_texcoord0;\n\nuniform vec4 ambientColor;\nuniform sampler2D diffuseColorMap;\nuniform vec4 specularColor;\nuniform float specularShininess;\nuniform float picked;\nuniform vec4  pickedColor;\n\nuniform float alpha;\n\nvoid main(void) \n{\n    vec4 color = vec4(0.0, 0.0, 0.0, 0.0);\n    vec3 diffuseLight = vec3(0.0, 0.0, 0.0);\n    vec3 lightColor = vec3(1.0,1.0,1.0);\n    vec4 ambient = ambientColor;\n    vec4 diffuse = texture2D(diffuseColorMap, v_texcoord0);\n    vec4 specular = specularColor;\n    color.xyz += ambient.xyz;\n    color.xyz += diffuse.xyz;\n    color.xyz += specular.xyz;\n    color = vec4(diffuse.rgb * diffuse.a, diffuse.a*alpha);\n    gl_FragColor = color;\n    if(picked!=0.0){\n        gl_FragColor =mix(color, pickedColor*0.5,1.0);\n    }\n}"},{}],28:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\nvarying vec3 v_position;\nvarying vec2 v_texcoord0;\nvarying vec3 v_normal;\n\nuniform vec4 ambientColor;\nuniform sampler2D diffuseColorMap;\nuniform vec4 specularColor;\nuniform float specularShininess;\nuniform float picked;\nuniform vec4  pickedColor;\n\nvarying vec3 v_light0Direction;\n\nvoid main(void) \n{\n    vec3 normal = normalize(v_normal);\n    vec4 color = vec4(0.0, 0.0, 0.0, 0.0);\n    vec3 diffuseLight = vec3(0.0, 0.0, 0.0);\n    vec3 lightColor = vec3(1.0,1.0,1.0);\n    vec4 ambient = ambientColor;\n    vec4 diffuse = texture2D(diffuseColorMap, v_texcoord0);\n    vec4 specular = specularColor;\n\n    vec3 specularLight = vec3(0.0, 0.0, 0.0);\n    {\n        float specularIntensity = 0.0;\n        float attenuation = 1.0;\n        vec3 l = normalize(v_light0Direction);\n        vec3 viewDir = -normalize(v_position);\n        vec3 h = normalize(l+viewDir);\n        specularIntensity = max(0.0, pow(max(dot(normal,h), 0.0) , specularShininess)) * attenuation;\n        specularLight += lightColor * specularIntensity;\n        diffuseLight += lightColor * max(dot(normal,l), 0.0) * attenuation;\n    }\n    //specular.xyz *= specularLight;\n    //diffuse.xyz *= diffuseLight;\n    color.xyz += ambient.xyz;\n    color.xyz += diffuse.xyz;\n    color.xyz += specular.xyz;\n    color = vec4(diffuse.rgb * diffuse.a, diffuse.a);\n    gl_FragColor = color;\n    if(picked!=0.0){\n        gl_FragColor = pickedColor*color;\n    }\n}"},{}],29:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\n\n\nvarying vec3 v_position;\nvarying vec2 v_texcoord0;\nvarying vec3 v_normal;\n\nvarying vec3 v_light0Direction;\n\nvoid main(void) \n{\n    vec4 pos =  modelViewMatrix * vec4( position,1.0);\n    v_normal =  normalMatrix *  normal;\n    v_texcoord0 =uv;\n    v_position = pos.xyz;\n    v_light0Direction = mat3( modelViewMatrix) * vec3(1.0,1.0,1.0);\n    gl_Position =  projectionMatrix * pos;\n}"},{}],30:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.default="\n#ifdef GL_ES\n    precision highp float;\n#endif\n\n\n\nvarying vec3 v_position;\nvarying vec2 v_texcoord0;\n\nvoid main(void) \n{\n    vec4 pos =  modelViewMatrix * vec4( position,1.0);\n    v_texcoord0 =  uv;\n    v_position = pos.xyz;\n    gl_Position =  projectionMatrix * pos;\n}"},{}],31:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0}),n.defineProperty=n.ShaderUtils=n.MaterialUtils=n.MeshPhongMaterial=n.CSG=n.PlaneBufferGeometry=n.BasicGeometry=n.BasicMeshMaterial=n.ReferenceMesh=n.Rotation=n.PlaneGeometry=n.LOD=n.GeometryUtils=n.FramebufferTexture=n.MeshVisualizer=n.ShaderLib=n.ShaderChunk=n.MeshMaterial=n.Mesh=n.RendererUtils=void 0
;var r=e("./Core/RendererUtils.js"),a=i(r),o=e("./Core/Mesh.js"),s=i(o),l=e("./Core/MeshMaterial.js"),c=i(l),d=e("./Core/Shaders/ShaderChunk.js"),u=i(d),f=e("./Core/MeshVisualizer.js"),m=i(f),p=e("./Core/FramebufferTexture.js"),h=i(p),v=e("./Core/GeometryUtils.js"),_=i(v),g=e("./Core/LOD.js"),x=i(g),y=e("./Core/PlaneGeometry.js"),C=i(y),M=e("./Core/Rotation.js"),S=i(M),w=e("./Core/ReferenceMesh.js"),b=i(w),L=e("./Core/BasicMeshMaterial.js"),E=i(L),P=e("./Core/BasicGeometry.js"),T=i(P),D=e("./Core/Shaders/ShaderLib.js"),R=i(D),A=e("./Core/PlaneBufferGeometry.js"),U=i(A),I=e("./Util/CSG.js"),N=i(I),G=e("./Core/MeshPhongMaterial.js"),z=i(G),O=e("./Core/MaterialUtils.js"),F=i(O),V=e("./Core/ShaderUtils.js"),B=i(V),H=e("./Util/defineProperty.js"),k=i(H),j={};"undefined"!=typeof Cesium&&(j=Cesium),j.defineProperty=k.default,j.RendererUtils=a.default,j.MaterialUtils=F.default,j.ShaderUtils=B.default,j.GeometryUtils=_.default,j.Mesh=s.default,j.MeshMaterial=c.default,j.ShaderChunk=u.default,j.ShaderLib=R.default,j.MeshVisualizer=m.default,j.FramebufferTexture=h.default,j.LOD=x.default,j.PlaneGeometry=C.default,j.Rotation=S.default,j.ReferenceMesh=b.default,j.BasicMeshMaterial=E.default,j.BasicGeometry=T.default,j.PlaneBufferGeometry=U.default,j.CSG=N.default,j.MeshPhongMaterial=z.default,j.MeshVisualizerVERSION="1.0.1",n.RendererUtils=a.default,n.Mesh=s.default,n.MeshMaterial=c.default,n.ShaderChunk=u.default,n.ShaderLib=R.default,n.MeshVisualizer=m.default,n.FramebufferTexture=h.default,n.GeometryUtils=_.default,n.LOD=x.default,n.PlaneGeometry=C.default,n.Rotation=S.default,n.ReferenceMesh=b.default,n.BasicMeshMaterial=E.default,n.BasicGeometry=T.default,n.PlaneBufferGeometry=U.default,n.CSG=N.default,n.MeshPhongMaterial=z.default,n.MaterialUtils=F.default,n.ShaderUtils=B.default,n.defineProperty=k.default,n.default=j,void 0!==t&&(t.exports=j)},{"./Core/BasicGeometry.js":2,"./Core/BasicMeshMaterial.js":3,"./Core/FramebufferTexture.js":4,"./Core/GeometryUtils.js":5,"./Core/LOD.js":6,"./Core/MaterialUtils.js":7,"./Core/Mesh.js":8,"./Core/MeshMaterial.js":9,"./Core/MeshPhongMaterial.js":10,"./Core/MeshVisualizer.js":12,"./Core/PlaneBufferGeometry.js":13,"./Core/PlaneGeometry.js":14,"./Core/ReferenceMesh.js":15,"./Core/RendererUtils.js":16,"./Core/Rotation.js":17,"./Core/ShaderUtils.js":18,"./Core/Shaders/ShaderChunk.js":19,"./Core/Shaders/ShaderLib.js":20,"./Util/CSG.js":34,"./Util/defineProperty.js":36}],32:[function(e,t,n){"use strict";function i(){this.polygons=[]}Object.defineProperty(n,"__esModule",{value:!0}),i.fromPolygons=function(e){var t=new i;return t.polygons=e,t},i.prototype={clone:function(){var e=new i;return e.polygons=this.polygons.map(function(e){return e.clone()}),e},toPolygons:function(){return this.polygons},union:function(e){var t=new i.Node(this.clone().polygons),n=new i.Node(e.clone().polygons);return t.clipTo(n),n.clipTo(t),n.invert(),n.clipTo(t),n.invert(),t.build(n.allPolygons()),i.fromPolygons(t.allPolygons())},subtract:function(e){var t=new i.Node(this.clone().polygons),n=new i.Node(e.clone().polygons);return t.invert(),t.clipTo(n),n.clipTo(t),n.invert(),n.clipTo(t),n.invert(),t.build(n.allPolygons()),t.invert(),i.fromPolygons(t.allPolygons())},intersect:function(e){var t=new i.Node(this.clone().polygons),n=new i.Node(e.clone().polygons);return t.invert(),n.clipTo(t),n.invert(),t.clipTo(n),n.clipTo(t),t.build(n.allPolygons()),t.invert(),i.fromPolygons(t.allPolygons())},inverse:function(){var e=this.clone();return e.polygons.map(function(e){e.flip()}),e}},i.cube=function(e){e=e||{};var t=new i.Vector(e.center||[0,0,0]),n=e.radius?e.radius.length?e.radius:[e.radius,e.radius,e.radius]:[1,1,1];return i.fromPolygons([[[0,4,6,2],[-1,0,0]],[[1,3,7,5],[1,0,0]],[[0,1,5,4],[0,-1,0]],[[2,6,7,3],[0,1,0]],[[0,2,3,1],[0,0,-1]],[[4,5,7,6],[0,0,1]]].map(function(e){return new i.Polygon(e[0].map(function(r){var a=new i.Vector(t.x+n[0]*(2*!!(1&r)-1),t.y+n[1]*(2*!!(2&r)-1),t.z+n[2]*(2*!!(4&r)-1));return new i.Vertex(a,new i.Vector(e[1]))}))}))},i.sphere=function(e){function t(e,t){e*=2*Math.PI,t*=Math.PI;var o=new i.Vector(Math.cos(e)*Math.sin(t),Math.cos(t),Math.sin(e)*Math.sin(t));n.push(new i.Vertex(r.plus(o.times(a)),o))}e=e||{};for(var n,r=new i.Vector(e.center||[0,0,0]),a=e.radius||1,o=e.slices||16,s=e.stacks||8,l=[],c=0;c<o;c++)for(var d=0;d<s;d++)n=[],t(c/o,d/s),d>0&&t((c+1)/o,d/s),d<s-1&&t((c+1)/o,(d+1)/s),t(c/o,(d+1)/s),l.push(new i.Polygon(n));return i.fromPolygons(l)},i.cylinder=function(e){function t(e,t,r){var s=t*Math.PI*2,c=d.times(Math.cos(s)).plus(u.times(Math.sin(s))),f=n.plus(a.times(e)).plus(c.times(o)),m=c.times(1-Math.abs(r)).plus(l.times(r));return new i.Vertex(f,m)}e=e||{};for(var n=new i.Vector(e.start||[0,-1,0]),r=new i.Vector(e.end||[0,1,0]),a=r.minus(n),o=e.radius||1,s=e.slices||16,l=a.unit(),c=Math.abs(l.y)>.5,d=new i.Vector(c,!c,0).cross(l).unit(),u=d.cross(l).unit(),f=new i.Vertex(n,l.negated()),m=new i.Vertex(r,l.unit()),p=[],h=0;h<s;h++){var v=h/s,_=(h+1)/s;p.push(new i.Polygon([f,t(0,v,-1),t(0,_,-1)])),p.push(new i.Polygon([t(0,_,0),t(0,v,0),t(1,v,0),t(1,_,0)])),p.push(new i.Polygon([m,t(1,_,1),t(1,v,1)]))}return i.fromPolygons(p)},i.Vector=function(e,t,n){3==arguments.length?(this.x=e,this.y=t,this.z=n):"x"in e?(this.x=e.x,this.y=e.y,this.z=e.z):(this.x=e[0],this.y=e[1],this.z=e[2])},i.Vector.prototype={clone:function(){return new i.Vector(this.x,this.y,this.z)},negated:function(){return new i.Vector(-this.x,-this.y,-this.z)},plus:function(e){return new i.Vector(this.x+e.x,this.y+e.y,this.z+e.z)},minus:function(e){return new i.Vector(this.x-e.x,this.y-e.y,this.z-e.z)},times:function(e){return new i.Vector(this.x*e,this.y*e,this.z*e)},dividedBy:function(e){return new i.Vector(this.x/e,this.y/e,this.z/e)},dot:function(e){return this.x*e.x+this.y*e.y+this.z*e.z},lerp:function(e,t){return this.plus(e.minus(this).times(t))},length:function(){return Math.sqrt(this.dot(this))},unit:function(){return this.dividedBy(this.length())},cross:function(e){return new i.Vector(this.y*e.z-this.z*e.y,this.z*e.x-this.x*e.z,this.x*e.y-this.y*e.x)}},i.Vertex=function(e,t){this.pos=new i.Vector(e),this.normal=new i.Vector(t)},i.Vertex.prototype={clone:function(){return new i.Vertex(this.pos.clone(),this.normal.clone())},flip:function(){this.normal=this.normal.negated()},interpolate:function(e,t){return new i.Vertex(this.pos.lerp(e.pos,t),this.normal.lerp(e.normal,t))}},i.Plane=function(e,t){this.normal=e,this.w=t},i.Plane.EPSILON=1e-5,i.Plane.fromPoints=function(e,t,n){var r=t.minus(e).cross(n.minus(e)).unit();return new i.Plane(r,r.dot(e))},i.Plane.prototype={clone:function(){return new i.Plane(this.normal.clone(),this.w)},flip:function(){this.normal=this.normal.negated(),this.w=-this.w},splitPolygon:function(e,t,n,r,a){for(var o=0,s=[],l=0;l<e.vertices.length;l++){var c=this.normal.dot(e.vertices[l].pos)-this.w,d=c<-i.Plane.EPSILON?2:c>i.Plane.EPSILON?1:0;o|=d,s.push(d)}switch(o){case 0:(this.normal.dot(e.plane.normal)>0?t:n).push(e);break;case 1:r.push(e);break;case 2:a.push(e);break;case 3:for(var u=[],f=[],l=0;l<e.vertices.length;l++){var m=(l+1)%e.vertices.length,p=s[l],h=s[m],v=e.vertices[l],_=e.vertices[m];if(2!=p&&u.push(v),1!=p&&f.push(2!=p?v.clone():v),3==(p|h)){var c=(this.w-this.normal.dot(v.pos))/this.normal.dot(_.pos.minus(v.pos)),g=v.interpolate(_,c);u.push(g),f.push(g.clone())}}u.length>=3&&r.push(new i.Polygon(u,e.shared)),f.length>=3&&a.push(new i.Polygon(f,e.shared))}}},i.Polygon=function(e,t){this.vertices=e,this.shared=t,this.plane=i.Plane.fromPoints(e[0].pos,e[1].pos,e[2].pos)},i.Polygon.prototype={clone:function(){var e=this.vertices.map(function(e){return e.clone()});return new i.Polygon(e,this.shared)},flip:function(){this.vertices.reverse().map(function(e){e.flip()}),this.plane.flip()}},i.Node=function(e){this.plane=null,this.front=null,this.back=null,this.polygons=[],e&&this.build(e)},i.Node.prototype={clone:function(){var e=new i.Node;return e.plane=this.plane&&this.plane.clone(),e.front=this.front&&this.front.clone(),e.back=this.back&&this.back.clone(),e.polygons=this.polygons.map(function(e){return e.clone()}),e},invert:function(){for(var e=0;e<this.polygons.length;e++)this.polygons[e].flip();this.plane.flip(),this.front&&this.front.invert(),this.back&&this.back.invert();var t=this.front;this.front=this.back,this.back=t},clipPolygons:function(e){if(!this.plane)return e.slice();for(var t=[],n=[],i=0;i<e.length;i++)this.plane.splitPolygon(e[i],t,n,t,n);return this.front&&(t=this.front.clipPolygons(t)),n=this.back?this.back.clipPolygons(n):[],t.concat(n)},clipTo:function(e){this.polygons=e.clipPolygons(this.polygons),this.front&&this.front.clipTo(e),this.back&&this.back.clipTo(e)},allPolygons:function(){var e=this.polygons.slice();return this.front&&(e=e.concat(this.front.allPolygons())),this.back&&(e=e.concat(this.back.allPolygons())),e},build:function(e){if(e.length){this.plane||(this.plane=e[0].plane.clone());for(var t=[],n=[],r=0;r<e.length;r++)this.plane.splitPolygon(e[r],this.polygons,this.polygons,t,n);t.length&&(this.front||(this.front=new i.Node),this.front.build(t)),n.length&&(this.back||(this.back=new i.Node),this.back.build(n))}}},n.default=i},{}],33:[function(e,t,n){"use strict";function i(){this.tiffDataView=void 0,this.littleEndian=void 0,this.fileDirectories=[]}Object.defineProperty(n,"__esModule",{value:!0}),i.prototype={isLittleEndian:function(){var e=this.getBytes(2,0);if(18761===e)this.littleEndian=!0;else{if(19789!==e)throw console.log(e),TypeError("Invalid byte order value.");this.littleEndian=!1}return this.littleEndian},hasTowel:function(){if(42!==this.getBytes(2,2))throw RangeError("You forgot your towel!");return!0},getFieldTagName:function(e){var t={315:"Artist",258:"BitsPerSample",265:"CellLength",264:"CellWidth",320:"ColorMap",259:"Compression",33432:"Copyright",306:"DateTime",338:"ExtraSamples",266:"FillOrder",289:"FreeByteCounts",288:"FreeOffsets",291:"GrayResponseCurve",290:"GrayResponseUnit",316:"HostComputer",270:"ImageDescription",257:"ImageLength",256:"ImageWidth",271:"Make",281:"MaxSampleValue",280:"MinSampleValue",272:"Model",254:"NewSubfileType",274:"Orientation",262:"PhotometricInterpretation",284:"PlanarConfiguration",296:"ResolutionUnit",278:"RowsPerStrip",277:"SamplesPerPixel",305:"Software",279:"StripByteCounts",273:"StripOffsets",255:"SubfileType",263:"Threshholding",282:"XResolution",283:"YResolution",326:"BadFaxLines",327:"CleanFaxData",343:"ClipPath",328:"ConsecutiveBadFaxLines",433:"Decode",434:"DefaultImageColor",269:"DocumentName",336:"DotRange",321:"HalftoneHints",346:"Indexed",347:"JPEGTables",285:"PageName",297:"PageNumber",317:"Predictor",319:"PrimaryChromaticities",532:"ReferenceBlackWhite",339:"SampleFormat",559:"StripRowCounts",330:"SubIFDs",292:"T4Options",293:"T6Options",325:"TileByteCounts",323:"TileLength",324:"TileOffsets",322:"TileWidth",301:"TransferFunction",318:"WhitePoint",344:"XClipPathUnits",286:"XPosition",529:"YCbCrCoefficients",531:"YCbCrPositioning",530:"YCbCrSubSampling",345:"YClipPathUnits",287:"YPosition",37378:"ApertureValue",40961:"ColorSpace",36868:"DateTimeDigitized",36867:"DateTimeOriginal",34665:"Exif IFD",36864:"ExifVersion",33434:"ExposureTime",41728:"FileSource",37385:"Flash",40960:"FlashpixVersion",33437:"FNumber",42016:"ImageUniqueID",37384:"LightSource",37500:"MakerNote",37377:"ShutterSpeedValue",37510:"UserComment",33723:"IPTC",34675:"ICC Profile",700:"XMP",42112:"GDAL_METADATA",42113:"GDAL_NODATA",34377:"Photoshop"};return e in t?t[e]:"Tag"+e},getFieldTypeName:function(e){var t,n={1:"BYTE",2:"ASCII",3:"SHORT",4:"LONG",5:"RATIONAL",6:"SBYTE",7:"UNDEFINED",8:"SSHORT",9:"SLONG",10:"SRATIONAL",11:"FLOAT",12:"DOUBLE"};return e in n&&(t=n[e]),t},getFieldTypeLength:function(e){var t;return["BYTE","ASCII","SBYTE","UNDEFINED"].indexOf(e)!==-1?t=1:["SHORT","SSHORT"].indexOf(e)!==-1?t=2:["LONG","SLONG","FLOAT"].indexOf(e)!==-1?t=4:["RATIONAL","SRATIONAL","DOUBLE"].indexOf(e)!==-1&&(t=8),t},getBits:function(e,t,n){n=n||0;var i=Math.floor(n/8),r=t+i,a=n+e,o=32-e;if(a<=0)throw console.log(e,t,n),RangeError("No bits requested");if(a<=8)var s=24+n,l=this.tiffDataView.getUint8(r,this.littleEndian);else if(a<=16)var s=16+n,l=this.tiffDataView.getUint16(r,this.littleEndian);else{if(!(a<=32))throw console.log(e,t,n),RangeError("Too many bits requested");var s=n,l=this.tiffDataView.getUint32(r,this.littleEndian)}return{bits:l<<s>>>o,byteOffset:r+Math.floor(a/8),bitOffset:a%8}},getBytes:function(e,t){if(e<=0)throw console.log(e,t),RangeError("No bytes requested");if(e<=1)return this.tiffDataView.getUint8(t,this.littleEndian);if(e<=2)return this.tiffDataView.getUint16(t,this.littleEndian);if(e<=3)return this.tiffDataView.getUint32(t,this.littleEndian)>>>8;if(e<=4)return this.tiffDataView.getUint32(t,this.littleEndian);throw console.log(e,t),RangeError("Too many bytes requested")},getFieldValues:function(e,t,n,i){var r=[],a=this.getFieldTypeLength(t),o=a*n;if(o<=4){if(this.littleEndian===!1)var s=i>>>8*(4-a);else var s=i;r.push(s)}else for(var l=0;l<n;l++){var c=a*l;if(a>=8){if(["RATIONAL","SRATIONAL"].indexOf(t)===-1)throw console.log(t,n,o),TypeError("Can't handle this field type or size");r.push(this.getBytes(4,i+c)),r.push(this.getBytes(4,i+c+4))}else r.push(this.getBytes(a,i+c))}return"ASCII"===t&&r.forEach(function(e,t,n){n[t]=String.fromCharCode(e)}),r},clampColorSample:function(e,t){var n=Math.pow(2,8-t);return Math.floor(e*n+(n-1))},makeRGBAFillValue:function(e,t,n,i){return void 0===i&&(i=1),"rgba("+e+", "+t+", "+n+", "+i+")"},parseFileDirectory:function(e){for(var t=this.getBytes(2,e),n=[],i=e+2,r=0;r<t;i+=12,r++){var a=this.getBytes(2,i),o=this.getBytes(2,i+2),s=this.getBytes(4,i+4),l=this.getBytes(4,i+8),c=this.getFieldTagName(a),d=this.getFieldTypeName(o),u=this.getFieldValues(c,d,s,l);n[c]={type:d,values:u}}this.fileDirectories.push(n);var f=this.getBytes(4,i);return 0===f?this.fileDirectories:this.parseFileDirectory(f)},parseTIFF:function(e,t){if(t=t||document.createElement("canvas"),this.tiffDataView=new DataView(e),this.canvas=t,this.littleEndian=this.isLittleEndian(this.tiffDataView),this.hasTowel(this.tiffDataView,this.littleEndian)){var n=this.getBytes(4,4);this.fileDirectories=this.parseFileDirectory(n);var i=this.fileDirectories[0],r=i.ImageWidth.values[0],a=i.ImageLength.values[0];this.canvas.width=r,this.canvas.height=a;var o=[],s=i.Compression?i.Compression.values[0]:1,l=i.SamplesPerPixel.values[0],c=[],d=0,u=!1;if(i.BitsPerSample.values.forEach(function(e,t,n){c[t]={bitsPerSample:e,hasBytesPerSample:!1,bytesPerSample:void 0},e%8==0&&(c[t].hasBytesPerSample=!0,c[t].bytesPerSample=e/8),d+=e},this),d%8==0){u=!0;var f=d/8}var m=i.StripOffsets.values,p=m.length;if(i.StripByteCounts)var h=i.StripByteCounts.values;else{if(console.log("Missing StripByteCounts!"),1!==p)throw Error("Cannot recover from missing StripByteCounts");var h=[Math.ceil(r*a*d/8)]}for(var v=0;v<p;v++){var _=m[v];o[v]=[];for(var g=h[v],x=0,y=0,C=1,M=!0,S=[],w=0,b=0,L=0;x<g;x+=C)switch(s){case 1:for(var E=0,S=[];E<l;E++){if(!c[E].hasBytesPerSample){var P=this.getBits(c[E].bitsPerSample,_+x,y);throw S.push(P.bits),x=P.byteOffset-_,y=P.bitOffset,RangeError("Cannot handle sub-byte bits per sample")}var T=c[E].bytesPerSample*E;S.push(this.getBytes(c[E].bytesPerSample,_+x+T))}if(o[v].push(S),!u)throw C=0,RangeError("Cannot handle sub-byte bits per pixel");C=f;break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:break;case 7:break;case 32773:if(M){M=!1;var D=1,R=1,A=this.tiffDataView.getInt8(_+x,this.littleEndian);A>=0&&A<=127?D=A+1:A>=-127&&A<=-1?R=1-A:M=!0}else{for(var U=this.getBytes(1,_+x),E=0;E<R;E++){if(!c[b].hasBytesPerSample)throw RangeError("Cannot handle sub-byte bits per sample");L=L<<8*w|U,++w===c[b].bytesPerSample&&(S.push(L),L=w=0,b++),b===l&&(o[v].push(S),S=[],b=0)}D--,0===D&&(M=!0)}C=1}}if(t.getContext){var I=this.canvas.getContext("2d");if(I.fillStyle=this.makeRGBAFillValue(255,255,255,0),i.RowsPerStrip)var N=i.RowsPerStrip.values[0];else var N=a;var G=o.length,z=a%N,O=0===z?N:z,F=N,V=0,B=i.PhotometricInterpretation.values[0],H=[],k=0;if(i.ExtraSamples&&(H=i.ExtraSamples.values,k=H.length),i.ColorMap)var j=i.ColorMap.values,Y=Math.pow(2,c[0].bitsPerSample);for(var v=0;v<G;v++){v+1===G&&(F=O);for(var W=o[v].length,X=V*v,q=0,Z=0;Z<W;q++)for(var Q=0;Q<r;Q++,Z++){var K=o[v][Z],J=0,$=0,ee=0,te=1;if(k>0)for(var ne=0;ne<k;ne++)if(1===H[ne]||2===H[ne]){te=K[3+ne]/256;break}switch(B){case 0:if(c[0].hasBytesPerSample)var ie=Math.pow(16,2*c[0].bytesPerSample);K.forEach(function(e,t,n){n[t]=ie-e});case 1:J=$=ee=this.clampColorSample(K[0],c[0].bitsPerSample);break;case 2:J=this.clampColorSample(K[0],c[0].bitsPerSample),$=this.clampColorSample(K[1],c[1].bitsPerSample),ee=this.clampColorSample(K[2],c[2].bitsPerSample);break;case 3:if(void 0===j)throw Error("Palette image missing color map");var re=K[0];J=this.clampColorSample(j[re],16),$=this.clampColorSample(j[Y+re],16),ee=this.clampColorSample(j[2*Y+re],16);break;case 4:throw RangeError("Not Yet Implemented: Transparency mask");case 5:throw RangeError("Not Yet Implemented: CMYK");case 6:throw RangeError("Not Yet Implemented: YCbCr");case 8:throw RangeError("Not Yet Implemented: CIELab");default:throw RangeError("Unknown Photometric Interpretation:",B)}I.fillStyle=this.makeRGBAFillValue(J,$,ee,te),I.fillRect(Q,X+q,1,1)}V=F}}return this.canvas}}},n.default=i},{}],34:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=e("../ThirdParty/csg/csg.js"),r=function(e){return e&&e.__esModule?e:{default:e}}(i);r.default.toCSG=function(e,t){if(t||(t={x:0,y:0,z:0}),e.attributes.normal||(e=Cesium.GeometryPipeline.computeNormal(e)),e.primitiveType!==Cesium.PrimitiveType.TRIANGLES)throw new Error("暂不支持此类几何体");for(var n=[],i=[],a=e.attributes.position.values,o=e.attributes.normal.values,s=0,l=0,c=0;c<e.indices.length;c+=3){i=[];var d=e.indices[c],u=e.indices[c+1],f=e.indices[c+2];l=3*d,s=3*d,i.push(new r.default.Vertex([a[l++]+t.x,a[l++]+t.y,a[l++]+t.z],[o[s++],o[s++],o[s++]])),l=3*u,s=3*u,i.push(new r.default.Vertex([a[l++]+t.x,a[l++]+t.y,a[l++]+t.z],[o[s++],o[s++],o[s++]])),l=3*f,s=3*f,i.push(new r.default.Vertex([a[l++]+t.x,a[l++]+t.y,a[l++]+t.z],[o[s++],o[s++],o[s++]])),n.push(new r.default.Polygon(i))}return r.default.fromPolygons(n)},r.default.fromCSG=function(e){var t,n,i,a=e.toPolygons();if(!r.default)throw new Error("CSG 库未加载。请从 https://github.com/evanw/csg.js 获取");var o=[],s=[],l=[];for(t=0;t<a.length;t++){for(i=[],n=0;n<a[t].vertices.length;n++)i.push(this.getGeometryVertice(o,s,a[t].vertices[n].pos,a[t].plane.normal));i[0]===i[i.length-1]&&i.pop();for(var n=2;n<i.length;n++)l.push(i[0],i[n-1],i[n])}o=new Float32Array(o),s=new Float32Array(s),l=new Int32Array(l);var c={};return c.position=new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:o}),c.normal=new Cesium.GeometryAttribute({componentDatatype:Cesium.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:s}),new Cesium.Geometry({attributes:c,indices:l,primitiveType:Cesium.PrimitiveType.TRIANGLES})},r.default.getGeometryVertice=function(e,t,n,i){var r,a=0;for(r=0;r<e.length;r+=3){if(e[r]===n.x&&e[r+1]===n.y&&e[r+2]===n.z)return a;a++}return e.push(n.x,n.y,n.z),t.push(i.x,i.y,i.z),a},n.default=r.default},{"../ThirdParty/csg/csg.js":32}],35:[function(e,t,n){"use strict";function i(){}Object.defineProperty(n,"__esModule",{value:!0}),i.GetExtension=function(e){var t=e.lastIndexOf(".");return t>=0?e.substring(t,e.length):""},i.GetFileName=function(e){var t=e.lastIndexOf("/");return t<0?e:e.substring(t+1,e.length)},i.GetDirectoryName=function(e){var t=e.lastIndexOf("/");return t<0?"":e.substring(0,t)},i.Combine=function(e,t){return e+t},i.ChangeExtension=function(e,t){return e.replace(i.GetExtension(e),t)},n.default=i},{}],36:[function(e,t,n){"use strict";function i(e,t,n,i){e["_"+t]=n;var r={get:function(){return this["_"+t]},set:function(n){var r=n!=this["_"+t];this["_"+t]&&this["_"+t].equals&&n&&(r=this["_"+t].equals(n));var a=this["_"+t];this["_"+t]=n,"function"==typeof i&&r&&i(r,e,n,a)}},a={};a[t]=r,Object.defineProperties(e,a)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=i},{}]},{},[31])(31)});
//# sourceMappingURL=CesiumMeshVisualizer.min.js.map