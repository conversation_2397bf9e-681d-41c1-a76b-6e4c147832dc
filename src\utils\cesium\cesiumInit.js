/**
 * Cesium初始化和工具函数
 * 替代原来的geowin3d库
 */
import * as Cesium from 'cesium'

// 设置Cesium的基础URL和访问令牌
// Cesium.Ion.defaultAccessToken = 'your-cesium-ion-access-token-here' // 需要替换为实际的token

// 全局Cesium配置
window.Cesium = Cesium
window.CESIUM_BASE_URL = '/node_modules/cesium/Build/Cesium/'

// 创建地形提供者
function createTerrainProvider() {
  try {
    // 使用椭球体地形（不需要token）
    return new Cesium.EllipsoidTerrainProvider()
  } catch (error) {
    console.warn('无法创建地形提供者:', error)
    return undefined
  }
}

// 创建影像提供者
function createImageryProvider() {
  try {
    // 使用OpenStreetMap（免费）
    return new Cesium.OpenStreetMapImageryProvider({
      url: 'https://a.tile.openstreetmap.org/'
    })
  } catch (error) {
    console.warn('无法创建影像提供者:', error)
    return undefined
  }
}

/**
 * 创建Cesium Viewer
 * @param {string} containerId - 容器ID
 * @param {Object} options - 配置选项
 * @returns {Cesium.Viewer}
 */
export function createViewer(containerId, options = {}) {
  const defaultOptions = {
    animation: false,
    baseLayerPicker: false,
    fullscreenButton: false,
    geocoder: false,
    homeButton: false,
    infoBox: false,
    sceneModePicker: false,
    selectionIndicator: false,
    timeline: false,
    navigationHelpButton: false,
    scene3DOnly: true,
    shouldAnimate: false, // 减少动画以节省内存
    // 使用简单的地形提供者以减少内存使用
    terrainProvider: new Cesium.EllipsoidTerrainProvider(),
    // 使用简单的影像提供者
    imageryProvider: new Cesium.TileMapServiceImageryProvider({
      url: Cesium.buildModuleUrl('Assets/Textures/NaturalEarthII')
    }),
    // 优化渲染设置以节省内存
    contextOptions: {
      webgl: {
        alpha: false,
        depth: true,
        stencil: false,
        antialias: true,
        premultipliedAlpha: true,
        preserveDrawingBuffer: false,
        failIfMajorPerformanceCaveat: false
      }
    },
    // 禁用一些高内存消耗的功能
    orderIndependentTranslucency: false,
    shadows: false,
    ...options
  }

  try {
    const viewer = new Cesium.Viewer(containerId, defaultOptions)

    // 禁用默认的双击行为
    if (viewer.cesiumWidget && viewer.cesiumWidget.screenSpaceEventHandler) {
      try {
        viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)
      } catch (error) {
        console.warn('无法移除双击事件:', error)
      }
    }

    // 设置地球参数
    if (viewer.scene && viewer.scene.globe) {
      try {
        viewer.scene.globe.enableLighting = false
        viewer.scene.globe.depthTestAgainstTerrain = true
      } catch (error) {
        console.warn('无法设置地球参数:', error)
      }
    }

    // 设置相机参数
    if (viewer.scene && viewer.scene.screenSpaceCameraController) {
      try {
        viewer.scene.screenSpaceCameraController.minimumZoomDistance = 1.0
        viewer.scene.screenSpaceCameraController.maximumZoomDistance = 50000000.0
      } catch (error) {
        console.warn('无法设置相机参数:', error)
      }
    }

    console.log('Cesium Viewer创建成功')
    return viewer
  } catch (error) {
    console.error('创建Cesium Viewer失败:', error)
    throw error
  }
}

/**
 * 测量工具类
 */
export class MeasurementTools {
  constructor(viewer) {
    this.viewer = viewer
    this.activePoints = []
    this.activeEntity = null
    this.handler = null
    this.measurementType = null
  }

  /**
   * 开始距离测量
   */
  startDistanceMeasurement() {
    this.cleanup()
    this.measurementType = 'distance'
    this.activePoints = []
    
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas)
    
    this.handler.setInputAction((event) => {
      const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid)
      if (!position) return
      
      this.activePoints.push(position)
      
      if (this.activePoints.length === 1) {
        // 第一个点
        this.viewer.entities.add({
          position: position,
          point: {
            pixelSize: 8,
            color: Cesium.Color.YELLOW,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        })
      } else if (this.activePoints.length === 2) {
        // 第二个点，完成测量
        this.viewer.entities.add({
          position: position,
          point: {
            pixelSize: 8,
            color: Cesium.Color.YELLOW,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        })
        
        // 添加线段
        const distance = Cesium.Cartesian3.distance(this.activePoints[0], this.activePoints[1])
        const midpoint = Cesium.Cartesian3.midpoint(this.activePoints[0], this.activePoints[1], new Cesium.Cartesian3())
        
        this.activeEntity = this.viewer.entities.add({
          polyline: {
            positions: this.activePoints,
            width: 3,
            color: Cesium.Color.YELLOW,
            clampToGround: true
          },
          position: midpoint,
          label: {
            text: `${distance.toFixed(2)} m`,
            font: '14pt sans-serif',
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -40),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        })
        
        this.cleanup()
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
  }

  /**
   * 开始面积测量
   */
  startAreaMeasurement() {
    this.cleanup()
    this.measurementType = 'area'
    this.activePoints = []
    
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas)
    
    this.handler.setInputAction((event) => {
      const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid)
      if (!position) return
      
      this.activePoints.push(position)
      
      // 添加点
      this.viewer.entities.add({
        position: position,
        point: {
          pixelSize: 8,
          color: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      })
      
      if (this.activePoints.length >= 3) {
        // 更新多边形
        if (this.activeEntity) {
          this.viewer.entities.remove(this.activeEntity)
        }
        
        const area = this.calculatePolygonArea(this.activePoints)
        const center = this.calculatePolygonCenter(this.activePoints)
        
        this.activeEntity = this.viewer.entities.add({
          polygon: {
            hierarchy: this.activePoints,
            material: Cesium.Color.YELLOW.withAlpha(0.3),
            outline: true,
            outlineColor: Cesium.Color.YELLOW,
            height: 0
          },
          position: center,
          label: {
            text: `${area.toFixed(2)} m²`,
            font: '14pt sans-serif',
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -40),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        })
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    
    // 右键完成测量
    this.handler.setInputAction(() => {
      this.cleanup()
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
  }

  /**
   * 计算多边形面积
   */
  calculatePolygonArea(positions) {
    if (positions.length < 3) return 0
    
    const cartographics = positions.map(pos => Cesium.Cartographic.fromCartesian(pos))
    let area = 0
    
    for (let i = 0; i < cartographics.length; i++) {
      const j = (i + 1) % cartographics.length
      area += cartographics[i].longitude * cartographics[j].latitude
      area -= cartographics[j].longitude * cartographics[i].latitude
    }
    
    area = Math.abs(area) / 2.0
    area = area * 6378137 * 6378137 // 转换为平方米
    
    return area
  }

  /**
   * 计算多边形中心点
   */
  calculatePolygonCenter(positions) {
    let x = 0, y = 0, z = 0
    
    positions.forEach(pos => {
      x += pos.x
      y += pos.y
      z += pos.z
    })
    
    return new Cesium.Cartesian3(
      x / positions.length,
      y / positions.length,
      z / positions.length
    )
  }

  /**
   * 清理测量工具
   */
  cleanup() {
    if (this.handler) {
      this.handler.destroy()
      this.handler = null
    }
  }

  /**
   * 清除所有测量结果
   */
  clearAll() {
    this.cleanup()
    this.viewer.entities.removeAll()
    this.activePoints = []
    this.activeEntity = null
  }
}

/**
 * 图层管理器
 */
export class LayerManager {
  constructor() {
    this.layers = new Map()
    this.layerCounter = 0
  }

  /**
   * 添加图层
   */
  addLayer(label, layer) {
    const id = `layer_${this.layerCounter++}`
    this.layers.set(id, {
      id,
      label,
      layer,
      visible: true
    })
    return id
  }

  /**
   * 根据标签获取图层ID
   */
  getIdByLabel(label) {
    for (const [id, layerInfo] of this.layers) {
      if (layerInfo.label === label) {
        return id
      }
    }
    return null
  }

  /**
   * 获取图层
   */
  getLayer(id) {
    const layerInfo = this.layers.get(id)
    return layerInfo ? layerInfo.layer : null
  }

  /**
   * 设置图层可见性
   */
  setLayerVisible(id, visible) {
    const layerInfo = this.layers.get(id)
    if (layerInfo) {
      layerInfo.visible = visible
      if (layerInfo.layer.show !== undefined) {
        layerInfo.layer.show = visible
      }
    }
  }

  /**
   * 移除图层
   */
  removeLayer(id) {
    this.layers.delete(id)
  }
}

// 导出默认配置
export const defaultCesiumConfig = {
  terrainProvider: createTerrainProvider(),
  imageryProvider: createImageryProvider()
}


