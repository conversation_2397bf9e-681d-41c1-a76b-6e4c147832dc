/**
 * 
 */
class Section {
    startPoint = null;
    stopPoint = null;

    constructor(viewer) {
        this.viewer = viewer;
        this.fas = new Geowin3D.GwTools.GwSection(viewer);
        this.ple = new Geowin3D.GwMicroApp.GwPolylineEditor(viewer);
        this.ple.setHeightOffset(0.02);
        this.init();
    }

    init() {
        const that = this;
        this.ple.setHeightOffset(0.02);

        this.ple.on(function (eventType, eventArg) {
            if (eventType == "PointAdded") {
                var pos = eventArg.position;
                if (!Geowin3D.defined(that.startPoint)) {
                    that.startPoint = Geowin3D.Cartesian3.fromDegrees(pos[0], pos[1], pos[2]);
                } else if (!Geowin3D.defined(that.stopPoint)) {
                    that.stopPoint = Geowin3D.Cartesian3.fromDegrees(pos[0], pos[1], pos[2]);

                    // 添加剖面数据
                    that.fas.add(that.startPoint, that.stopPoint);
                    // 开始剖面分析
                    that.fas.start();

                    that.startPoint = undefined;
                    that.stopPoint = undefined;

                    that.ple.cancel();

                    that.ple.start();
                }
            }
        })
    }

    start() {
        if (this.fas.haveSection()) {
            this.fas.start();
          } else {
            this.ple.start();
          }
    }

    remove() {
        this.fas.remove();
    }

    cancle() {
        this.fas.cancel();
        this.ple.cancel();
        this.remove()
    }
}

export default Section